@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400&display=swap');

:root {
  --app-height: 100%;
}

html,
body,
#root,
#root > div {
  width: 100%;
  height: var(--app-height);
}

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: 'Inter', sans-serif;
}

[data-testid='all-transactions-search'] {
  height: 48px;
}

[data-tooltip] {
  position: relative;
  cursor: pointer;
}

[data-tooltip]:before,
[data-tooltip]:after {
  line-height: 1;
  font-size: 0.9em;
  pointer-events: none;
  position: absolute;
  box-sizing: border-box;
  display: none;
  z-index: 999999;
}

[data-tooltip]:before {
  content: '';
  border: 5px solid transparent;
}

[data-tooltip]:after {
  content: attr(data-tooltip);
  text-align: center;
  min-width: 3em;
  width: 16em;
  /*white-space: nowrap;*/
  /*overflow: hidden;*/
  /*text-overflow: ellipsis;*/
  padding: 6px 8px;
  border-radius: 3px;
  background: #2f4858;
  color: #ffffff;
}

[data-tooltip]:hover:before,
[data-tooltip]:hover:after {
  display: block;
  opacity: 1;
  z-index: 999999;
}

[data-tooltip]:not([data-flow])::before,
[data-tooltip][data-flow='top']::before {
  bottom: 100%;
  border-bottom-width: 0;
  border-top-color: #2f4858;
}

[data-tooltip]:not([data-flow])::after,
[data-tooltip][data-flow='top']::after {
  bottom: calc(100% + 5px);
}

[data-tooltip]:not([data-flow])::before,
[tooltip]:not([data-flow])::after,
[data-tooltip][data-flow='top']::before,
[data-tooltip][data-flow='top']::after {
  left: 50%;
  -webkit-transform: translate(-50%, -4px);
  transform: translate(-50%, -4px);
}

[data-tooltip][data-flow='bottom']::before {
  top: 100%;
  border-top-width: 0;
  border-bottom-color: #2f4858;
}

[data-tooltip][data-flow='bottom']::after {
  top: calc(100% + 5px);
}

[data-tooltip][data-flow='bottom']::before,
[data-tooltip][data-flow='bottom']::after {
  left: 50%;
  -webkit-transform: translate(-50%, 8px);
  transform: translate(-50%, 8px);
}

[data-tooltip][data-flow='left']::before {
  top: 50%;
  border-right-width: 0;
  border-left-color: #2f4858;
  left: calc(0em - 5px);
  -webkit-transform: translate(-8px, -50%);
  transform: translate(-8px, -50%);
}

[data-tooltip][data-flow='left']::after {
  top: 50%;
  right: calc(100% + 5px);
  -webkit-transform: translate(-8px, -50%);
  transform: translate(-8px, -50%);
}

[data-tooltip][data-flow='right']::before {
  top: 50%;
  border-left-width: 0;
  border-right-color: #2f4858;
  right: calc(0em - 5px);
  -webkit-transform: translate(8px, -50%);
  transform: translate(8px, -50%);
}

[data-tooltip][data-flow='right']::after {
  top: 50%;
  left: calc(100% + 5px);
  -webkit-transform: translate(8px, -50%);
  transform: translate(8px, -50%);
}

[data-tooltip='']::after,
[data-tooltip='']::before {
  display: none !important;
}

body > #finicityConnectIframe {
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 20000;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

div[aria-modal] {
  z-index: 100 !important;
}

#hubspot-chat-container {
  position: absolute;
  bottom: 0;
  right: 100px;
  width: 300px;
  /*height: 500px;*/
  visibility: hidden;
}

#hubspot-chat-close-button {
  position: absolute;
  bottom: 455px;
  right: 16px;
  background-color: white;
  border-radius: 6px;
  padding: 6px 12px;
  border: none;
  z-index: 99;
  font-family: Helvetica, Arial, sans-serif;
  font-size: 16px;
  cursor: pointer;
  font-weight: 500;
  visibility: hidden;
  color: #425b76;
}

#hubspot-conversations-inline-iframe {
  width: 400px;
  height: 500px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 19px 38px rgba(0, 0, 0, 0.3), 0 15px 12px rgba(0, 0, 0, 0.22);
}

input:focus {
  outline: none !important;
  outline-width: 0px !important;
}

div[data-testid='text-input-underline'] {
  background-color: #fff !important;
}

textarea,
textarea:focus {
  outline: none !important;
  outline-width: 0px !important;
  -webkit-appearance: none;
}

:focus-visible {
  outline: none !important;
}
