import { makeAutoObservable } from 'mobx'
import { OwnerTypes } from '@linqpal/models/src/dictionaries/UnifiedApplication'
import { ICoOwner } from '../../../../../../models/src/applications/unified/IUnifiedApplicationDraft'

export class CoOwner implements ICoOwner {
  // common
  private _id = ''

  private _type: typeof OwnerTypes.INDIVIDUAL | typeof OwnerTypes.ENTITY =
    OwnerTypes.INDIVIDUAL

  private _percentOwned = 0

  private _address = ''

  private _city = ''

  private _state = ''

  private _zip = ''

  private _phone = ''

  private _email = ''

  // individual owner
  private _firstName = ''

  private _lastName = ''

  private _birthday = ''

  private _ssn = ''

  // entity
  private _entityName = ''

  private _ein = ''

  constructor(data?: Partial<ICoOwner>) {
    if (data) {
      Object.assign(this, data)
    }
    makeAutoObservable(this)
  }

  // Getters and Setters
  get id() {
    return this._id
  }

  set id(value: string) {
    this._id = value
  }

  get type() {
    return this._type
  }

  set type(value: typeof OwnerTypes.INDIVIDUAL | typeof OwnerTypes.ENTITY) {
    this.setType(value)
  }

  get percentOwned() {
    return this._percentOwned
  }

  set percentOwned(value: number) {
    this._percentOwned = value
  }

  get address() {
    return this._address
  }

  set address(value: string) {
    this._address = value
  }

  get city() {
    return this._city
  }

  set city(value: string) {
    this._city = value
  }

  get state() {
    return this._state
  }

  set state(value: string) {
    this._state = value
  }

  get zip() {
    return this._zip
  }

  set zip(value: string) {
    this._zip = value
  }

  get phone() {
    return this._phone
  }

  set phone(value: string) {
    this.setPhone(value)
  }

  get email() {
    return this._email
  }

  set email(value: string) {
    this.setEmail(value)
  }

  get firstName() {
    return this._firstName
  }

  set firstName(value: string) {
    this.setFirstName(value)
  }

  get lastName() {
    return this._lastName
  }

  set lastName(value: string) {
    this.setLastName(value)
  }

  get birthday() {
    return this._birthday
  }

  set birthday(value: string) {
    this.setBirthday(value)
  }

  get ssn() {
    return this._ssn
  }

  set ssn(value: string) {
    this.setSSN(value)
  }

  get entityName() {
    return this._entityName
  }

  set entityName(value: string) {
    this.setEntityName(value)
  }

  get ein() {
    return this._ein
  }

  set ein(value: string) {
    this.setEIN(value)
  }

  // common actions
  setType(type: typeof OwnerTypes.INDIVIDUAL | typeof OwnerTypes.ENTITY) {
    if (type === OwnerTypes.ENTITY) {
      this._firstName = ''
      this._lastName = ''
      this._birthday = ''
      this._ssn = ''
    } else {
      this._entityName = ''
      this._ein = ''
    }
    this._type = type
  }

  setPercentage(percentage: number) {
    this._percentOwned = percentage
  }

  setPhone(phone: string) {
    this._phone = phone
  }

  setEmail(email: string) {
    this._email = email
  }

  // individual owner actions
  setFirstName(firstName: string) {
    this._firstName = firstName
  }

  setLastName(lastName: string) {
    this._lastName = lastName
  }

  setBirthday(birthday: string) {
    this._birthday = birthday
  }

  setSSN(ssn: string) {
    this._ssn = ssn
  }

  // entity owner actions
  setEntityName(name: string) {
    this._entityName = name
  }

  setEIN(ein: string) {
    this._ein = ein
  }
}
