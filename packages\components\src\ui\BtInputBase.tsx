import React, { FC, useCallback, useEffect, useState } from 'react'
import {
  Input as UIKittenInput,
  InputProps as UIKittenInputProps,
} from '@ui-kitten/components'
import { BtInputLabel } from './BtInputLabel'
import { NativeSyntheticEvent, TextInputFocusEventData } from 'react-native'

export interface BtInputBaseProps
  extends Omit<UIKittenInputProps, 'label' | 'caption'> {
  label?: string
  required?: boolean
  validate?: (value: string) => string | undefined
  testID?: string
  labelStyle?: any
  validateEmptyStateOnBlur?: boolean
  keepValidationStateOnFocus?: boolean
  captionComponent?: React.ReactElement
}

export const BtInputBase: FC<BtInputBaseProps> = ({
  label,
  required,
  validate,
  labelStyle,
  validateEmptyStateOnBlur,
  keepValidationStateOnFocus,
  captionComponent,
  ...uiKittenInputProps
}) => {
  const [validationState, setValidationState] = useState<{
    status?: string
    caption?: React.ReactElement | string
  }>({})

  const handleValidation = useCallback(
    (value: string | undefined) => {
      if (validate) {
        const errorMessage = validate(value ?? '')
        const status = errorMessage
          ? 'danger'
          : uiKittenInputProps.status ?? 'basic'

        const updatedCaptionComponent = captionComponent
          ? React.cloneElement(captionComponent, {
              text: errorMessage || '',
            })
          : undefined

        setValidationState((prevState) => {
          if (
            prevState.status !== status ||
            prevState.caption !== (updatedCaptionComponent || errorMessage)
          ) {
            return {
              status,
              caption: updatedCaptionComponent || errorMessage,
            }
          }
          return prevState
        })
      }
    },
    [validate, captionComponent, uiKittenInputProps.status],
  )

  useEffect(() => {
    if (uiKittenInputProps.value) {
      handleValidation(uiKittenInputProps.value.replace(/[^\d]/g, ''))
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [uiKittenInputProps.value])

  const handleFocus = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
    if (validate && !keepValidationStateOnFocus) {
      setValidationState({})
    }

    if (uiKittenInputProps.onFocus) {
      uiKittenInputProps.onFocus(e)
    }
  }

  const handleChange = (text: string) => {
    handleValidation(text)

    if (uiKittenInputProps.onChangeText) {
      uiKittenInputProps.onChangeText(text)
    }
  }

  const handleBlur = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
    if (validate && (uiKittenInputProps.value || validateEmptyStateOnBlur)) {
      handleValidation(uiKittenInputProps.value)
    }

    if (uiKittenInputProps.onBlur) {
      uiKittenInputProps.onBlur(e)
    }
  }

  return (
    <UIKittenInput
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      autoComplete={'new-password'}
      {...uiKittenInputProps}
      accessoryRight={<>{uiKittenInputProps.accessoryRight}</>}
      label={
        label && (
          <BtInputLabel labelStyle={labelStyle} required={required}>
            {label}
          </BtInputLabel>
        )
      }
      onFocus={handleFocus}
      onBlur={handleBlur}
      onChangeText={handleChange}
      {...validationState}
    />
  )
}
