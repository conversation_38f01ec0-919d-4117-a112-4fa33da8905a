import { StyleSheet, View } from 'react-native'
import { BtPlainText } from '@linqpal/components/src/ui'
import React, { FC } from 'react'
import { useUnifiedApplication } from '../UnifiedApplicationContext'
import { useTranslation } from 'react-i18next'
import { observer } from 'mobx-react-lite'
import { colors } from '@linqpal/components/src/theme'
import { ProgressBar } from './WizardProgressBar'
import { useResponsive } from '../../../../utils/hooks'
import { getGroupTitle } from '../../../GeneralApplication/Application/groupTitles'

// TODO: VK: Unified: calculate progress

export const WizardGroupTitle: FC = observer(() => {
  const { sm } = useResponsive()
  const { t } = useTranslation('application')

  const store = useUnifiedApplication()

  if (!store.stepOptions.showGroupTitle) return null

  const titleKey = getGroupTitle(store.currentGroup)
  const title = t(titleKey as any)

  return title && sm ? (
    <>
      <View style={styles.titleWrapper}>
        <BtPlainText style={styles.title} testID="ApplicationTitle">
          {title}
        </BtPlainText>
      </View>
      <ProgressBar />
    </>
  ) : null
})

const styles = StyleSheet.create({
  titleWrapper: {
    padding: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.accentText,
  },
})
