import CIcon from '@coreui/icons-react'
import {
  CButton,
  CButtonGroup,
  CCard,
  CCardBody,
  CCardFooter,
  CCardHeader,
  CCardTitle,
  CCol,
  CDataTable,
  CDropdown,
  CDropdownItem,
  CDropdownMenu,
  CDropdownToggle,
  CInput,
  CInputCheckbox,
  CInputGroup,
  CInputGroupAppend,
  CPagination,
  CRow,
} from '@coreui/react'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { Link, useHistory, useParams } from 'react-router-dom'
import { ReactComponent as BackArrow } from '../../../assets/icons/back-arrow.svg'
import _ from 'lodash'
import { routes } from '@linqpal/models'
import { CustomerAccountInfo } from '@linqpal/models/src/dtos/CustomerAccounts'
import { ICustomerSettings, ISupplier } from '@linqpal/models/src/types/routes'
import { CustomerSettingsPopup } from './CustomerSettingsPopup'
import xlsx from 'xlsx'
import { ConfirmationModal } from '../../../components'
import { useToasts } from 'react-toast-notifications'
import { GetLabelWithInfo } from '../../loan/LoanStatus'
import { LoanPricingPackagesProvider } from '../../config/LoanPricingPackages'
import { useGetDebtInvestors } from './hooks/useGetDebtInvestors'

interface IParams {
  tradeCredit: string
  loanPaymentCollection: string
}

interface SlotCallback {
  showSettings: (row: CustomerAccountInfo) => void
  openDeleteModal: (row: CustomerAccountInfo) => void
}

const fields = [
  { key: 'name', label: 'Business Name' },
  { key: 'email', label: 'E-mail' },
  { key: 'phone', label: 'Cell phone' },
  { key: 'ownerName', label: 'Owner of the company' },
  {
    key: 'autoTradeCreditEnabled',
    label: (
      <GetLabelWithInfo info="Auto BNPL">
        Automated Trade Credit
      </GetLabelWithInfo>
    ),
  },
  { key: 'loanPaymentCollection', label: 'Loan Payment Collection' },
  { key: 'isDeleted', label: 'Is Deleted' },
  { key: 'delete', label: 'Delete Customer' },
  { key: 'settings', label: 'Settings' },
]

const pageSize = 10

const TRADE_CREDIT_OPTIONS = [
  { label: 'All', value: '' },
  { label: 'On', value: 'on' },
  { label: 'Off', value: 'off' },
]

const COLLECTION_OPTIONS = [
  { label: 'All', value: '' },
  { label: 'Supplier', value: 'supplier' },
  { label: 'Borrower', value: 'borrower' },
]

const CONFIRMATION_MESSAGE = (name: string) =>
  `Do you really want to delete ${name}?`

const CONFIRMATION_WIHT_INVOCE_MESSAGE = (name: string) =>
  `${name} has invoices. ${CONFIRMATION_MESSAGE('')}`

const getSlots = (callbacks: SlotCallback) => ({
  name: (row: CustomerAccountInfo) => <td>{row.name || row.companyName}</td>,
  isDeleted: (row: CustomerAccountInfo) => (
    <td>
      <CInputCheckbox
        style={{ marginLeft: 0 }}
        checked={row.isDeleted}
        disabled
      />
    </td>
  ),
  delete: (row: CustomerAccountInfo) => (
    <td>
      <CButton
        onClick={() => callbacks.openDeleteModal(row)}
        variant="ghost"
        size="sm"
        color="danger"
      >
        Delete
      </CButton>
    </td>
  ),
  settings: (row: CustomerAccountInfo) => (
    <td>
      <CButton
        color="primary"
        variant="outline"
        size="sm"
        onClick={() => callbacks.showSettings(row)}
      >
        Edit
      </CButton>
    </td>
  ),
  ownerName: (row: CustomerAccountInfo) => (
    <td>{[row.first_name, row.last_name].join(' ')}</td>
  ),
  autoTradeCreditEnabled: (row: CustomerAccountInfo) => (
    <td>{row.settings?.autoTradeCreditEnabled ? 'On' : 'Off'}</td>
  ),
  loanPaymentCollection: (row: CustomerAccountInfo) => (
    <td>
      {_.has(row, 'loanPaymentCollection')
        ? row.settings?.loanPaymentCollection
        : 'borrower'}
    </td>
  ),
})

const SupplierCustomers: React.FC = () => {
  const routePrams = useParams()
  const history = useHistory()
  const { addToast } = useToasts()
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [showDeleteConfirmationModal, setShowDeleteConfirmationModal] =
    useState(false)
  const [search, setSearch] = useState('')
  const [items, setItems] = useState<CustomerAccountInfo[]>([])
  const [total, setTotal] = useState(0)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [page, setPage] = useState(0)
  const [companyName, setCompanyName] = useState('')
  const [params, setParams] = useState<IParams>({
    tradeCredit: '',
    loanPaymentCollection: '',
  })

  const [settingsToEdit, setSettingsToEdit] = useState<
    ICustomerSettings | undefined
  >(undefined)

  const [supplier, setSupplier] = useState<ISupplier>()

  const [editingCustomer, setEditingCustomer] = useState<
    CustomerAccountInfo | undefined
  >(undefined)

  const [deletingCustomer, setDeletingCustomer] = useState<
    CustomerAccountInfo | undefined
  >(undefined)

  useEffect(() => {
    if (routePrams.companyId) {
      routes.admin
        .getCompany(routePrams.companyId)
        .then((res) => setSupplier(res))
    } else {
      history.push('/suppliers')
    }
  }, [routePrams.companyId, history])

  const { debtInvestors, loading: debtInvestorsLoading } = useGetDebtInvestors()

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const refreshTable = useCallback(
    _.debounce((currentSearch: string, currentPage: number) => {
      setLoading(true)
      routes.admin
        .getSupplierCustomers(
          routePrams.companyId,
          currentSearch,
          currentPage,
          pageSize,
          params,
        )
        .then((resp) => {
          setError('')
          setItems(resp.items)
          setTotal(resp.total)
          setCompanyName(resp.companyName)
        })
        .catch(() => {
          setError('Failed to load suppliers')
        })
        .finally(() => setLoading(false))
    }, 500),
    [routePrams.companyId, params],
  )

  const exportCsv = useCallback(
    (currentSearch: string) => {
      setLoading(true)
      routes.admin
        .getSupplierCustomers(
          routePrams.companyId,
          currentSearch,
          0,
          2000,
          params,
        )
        .then((resp) => {
          setError('')
          const book = xlsx.utils.book_new()
          xlsx.utils.book_append_sheet(
            book,
            xlsx.utils.json_to_sheet(
              resp.items.map((x) => ({
                'Business Name': x.name || x.companyName,
                'E-mail': x.email,
                'Cell phone': x.phone,
                'Owner of the company': [x.first_name, x.last_name].join(' '),
              })),
            ),
          )
          xlsx.writeFile(book, 'supplier-customers.csv', {
            bookType: 'csv',
          })
        })
        .catch(() => {
          setError('Failed to load suppliers')
        })
        .finally(() => setLoading(false))
    },
    [routePrams.companyId, params],
  )

  useEffect(() => {
    refreshTable(search, page)
  }, [search, page, refreshTable])

  const slots = useMemo(
    () =>
      getSlots({
        showSettings: (row) => {
          setEditingCustomer(row)
          const customerSettings = row.settings
          setSettingsToEdit({
            loanPlans: customerSettings?.loanPlans ?? [],
            loanPaymentCollection:
              customerSettings?.loanPaymentCollection ?? 'borrower',
            acceptCheckPayment: customerSettings?.acceptCheckPayment ?? false,
            acceptAchPayment: customerSettings?.acceptAchPayment ?? false,
            autoDebit: customerSettings?.autoDebit ?? false,
            autoTradeCreditEnabled:
              customerSettings?.autoTradeCreditEnabled ?? false,
            autoTradeCreditPaymentPlan:
              customerSettings?.autoTradeCreditPaymentPlan ?? '',
            autoTradeCreditMaxInvoiceAmount:
              customerSettings?.autoTradeCreditMaxInvoiceAmount ?? 0,
            acceptCreditPayment: customerSettings?.acceptCreditPayment ?? true,
            inHouseCredit: customerSettings?.inHouseCredit ?? undefined,
            automatedDrawApproval: customerSettings?.automatedDrawApproval,
            sendFinalPaymentWhenLoanIsPaid:
              customerSettings?.sendFinalPaymentWhenLoanIsPaid ?? false,
            debtInvestorTradeCredit:
              customerSettings?.debtInvestorTradeCredit ?? null,
          })
        },
        openDeleteModal: (row) => {
          setDeletingCustomer(row)
          setShowDeleteModal(true)
        },
      }),
    [setSettingsToEdit, setEditingCustomer],
  )

  const deleteIfCustomerHasNoInvoices = async (): Promise<void> => {
    if (!deletingCustomer?.id) {
      return
    }

    let invoicesCount = 0

    try {
      setLoading(true)
      ;({ count: invoicesCount } = await routes.admin.getCustomerInvoicesCount(
        deletingCustomer.id,
      ))
    } finally {
      setLoading(false)
    }

    if (invoicesCount === 0) {
      await deleteCustomer()
    } else {
      setShowDeleteConfirmationModal(true)
    }
  }

  const deleteCustomer = async (): Promise<void> => {
    if (!deletingCustomer?.id) {
      return
    }

    try {
      setLoading(true)

      await routes.admin.deleteSupplierCustomers(deletingCustomer.id)

      setShowDeleteModal(false)
      setShowDeleteConfirmationModal(false)
      refreshTable(search, page)
      addToast('Customer has been deleted', {
        appearance: 'info',
        autoDismiss: true,
      })
    } catch (err) {
      addToast('Customer has not been deleted', {
        appearance: 'error',
        autoDismiss: true,
      })
    } finally {
      setShowDeleteModal(false)
      setShowDeleteConfirmationModal(false)
      setLoading(false)
    }
  }

  return (
    <LoanPricingPackagesProvider>
      <Link
        to="/suppliers"
        style={{
          textDecoration: 'none',
          color: '#3F4B62',
          marginBottom: 10,
          display: 'block',
        }}
      >
        <BackArrow /> Back to Suppliers
      </Link>
      <CCard>
        <CCardHeader className="d-flex flex-row align-items-center">
          <CCardTitle className="mb-0 mr-2 flex-grow-1">
            {companyName}'s Customers
          </CCardTitle>
          <CButtonGroup>
            <CButton onClick={() => exportCsv(search)}>
              <CIcon name="cil-cloud-download" /> Download CSV
            </CButton>
            <CButton onClick={() => refreshTable(search, page)}>
              <CIcon name="cil-reload" /> Refresh
            </CButton>
          </CButtonGroup>
        </CCardHeader>
        <CCardBody>
          <CRow className="mb-2 align-items-center">
            <CCol xs={12} sm={8} md={6} lg={4}>
              <CInputGroup>
                <CInput
                  value={search}
                  onChange={(ev: React.ChangeEvent<HTMLInputElement>) =>
                    setSearch(ev.target.value)
                  }
                />
                <CInputGroupAppend>
                  <CButton
                    color="light"
                    onClick={() => refreshTable(search, page)}
                  >
                    Search
                  </CButton>
                </CInputGroupAppend>
              </CInputGroup>
            </CCol>
            <CCol xs={6} md="auto">
              <CDropdown>
                <CDropdownToggle caret color="light">
                  {`Trade Credit ${
                    TRADE_CREDIT_OPTIONS.find(
                      (t) => t.value === params.tradeCredit,
                    )?.label || TRADE_CREDIT_OPTIONS[0].label
                  }`}
                </CDropdownToggle>
                <CDropdownMenu>
                  {TRADE_CREDIT_OPTIONS.map((t) => (
                    <CDropdownItem
                      key={t.value}
                      active={t.value === params.tradeCredit}
                      onClick={() =>
                        setParams((prev) => ({ ...prev, tradeCredit: t.value }))
                      }
                    >
                      {t.label}
                    </CDropdownItem>
                  ))}
                </CDropdownMenu>
              </CDropdown>
            </CCol>
            <CCol xs={6} md="auto">
              <CDropdown>
                <CDropdownToggle caret color="light">
                  {`Loan payment collection - ${
                    COLLECTION_OPTIONS.find(
                      (t) => t.value === params.loanPaymentCollection,
                    )?.label || COLLECTION_OPTIONS[0].label
                  }`}
                </CDropdownToggle>
                <CDropdownMenu>
                  {COLLECTION_OPTIONS.map((t) => (
                    <CDropdownItem
                      key={t.value}
                      active={t.value === params.loanPaymentCollection}
                      onClick={() =>
                        setParams((prev) => ({
                          ...prev,
                          loanPaymentCollection: t.value,
                        }))
                      }
                    >
                      {t.label}
                    </CDropdownItem>
                  ))}
                </CDropdownMenu>
              </CDropdown>
            </CCol>
          </CRow>
          <CDataTable
            fields={fields}
            items={items}
            loading={loading}
            captionSlot={
              error ? <div className="text-danger">{error}</div> : null
            }
            scopedSlots={slots}
            responsive
            striped
            size="sm"
            hover
          />
        </CCardBody>
        <CCardFooter>
          <CPagination
            activePage={page}
            onActivePageChange={setPage}
            pages={Math.ceil(total / pageSize)}
          />
          {settingsToEdit && editingCustomer && (
            <CustomerSettingsPopup
              settings={settingsToEdit}
              supplier={supplier}
              onModalClose={() => {
                setSettingsToEdit(undefined)
                setEditingCustomer(undefined)
              }}
              debtInvestors={debtInvestors ?? []}
              debtInvestorsLoading={debtInvestorsLoading}
              refresh={() => refreshTable(search, page)}
              customer={editingCustomer}
            />
          )}

          <ConfirmationModal
            confirmationMessage={CONFIRMATION_MESSAGE(
              deletingCustomer?.name || 'this customer',
            )}
            show={showDeleteModal}
            close={() => setShowDeleteModal(false)}
          >
            <CButton
              disabled={loading}
              color="danger"
              onClick={() => deleteIfCustomerHasNoInvoices()}
            >
              Delete
            </CButton>{' '}
            <CButton
              color="secondary"
              onClick={() => setShowDeleteModal(false)}
            >
              Cancel
            </CButton>
          </ConfirmationModal>
          <ConfirmationModal
            confirmationMessage={CONFIRMATION_WIHT_INVOCE_MESSAGE(
              deletingCustomer?.name || 'this customer',
            )}
            show={showDeleteConfirmationModal}
            close={() => setShowDeleteConfirmationModal(false)}
          >
            <CButton
              disabled={loading}
              color="danger"
              onClick={() => deleteCustomer()}
            >
              Confirm
            </CButton>{' '}
            <CButton
              color="secondary"
              onClick={() => setShowDeleteConfirmationModal(false)}
            >
              Cancel
            </CButton>
          </ConfirmationModal>
        </CCardFooter>
      </CCard>
    </LoanPricingPackagesProvider>
  )
}

export default SupplierCustomers
