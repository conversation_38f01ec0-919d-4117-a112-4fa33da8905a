import { observer } from 'mobx-react'
import { useTranslation } from 'react-i18next'
import { useUnifiedApplication } from '../../../../UnifiedApplicationContext'
import { BtButton } from '@linqpal/components/src/ui'
import React from 'react'
import { StyleSheet } from 'react-native'
import { UnifiedApplicationReviewStep } from '../../../../Store/UnifiedApplicationReviewStore'
import { useResponsive } from '../../../../../../../utils/hooks'
import RootStore from '../../../../../../../store/RootStore'

export const GoToAgreementButton = observer(() => {
  const { t } = useTranslation('application')
  const { sm } = useResponsive()

  const store = useUnifiedApplication()

  if (
    !store.isInReview ||
    store.reviewStore.currentStep === UnifiedApplicationReviewStep.AGREEMENT
  ) {
    return null
  }

  return (
    <BtButton
      disabled={
        !store.reviewStore.isAgreementAccepted ||
        !store.canSubmit ||
        store.isSubmitting ||
        RootStore.isBusy
      }
      style={sm ? styles.button : styles.mobileButton}
      onPress={() => {
        store.reviewStore.currentStep = UnifiedApplicationReviewStep.AGREEMENT
      }}
      testID="UnifiedApplication.Wizard.Review.GoToAgreementButton"
      loading={store.isSubmitting}
    >
      {t('Review.Continue')}
    </BtButton>
  )
})

const styles = StyleSheet.create({
  button: {
    width: 320,
    marginBottom: 36,
  },
  mobileButton: {
    width: 160,
  },
})
