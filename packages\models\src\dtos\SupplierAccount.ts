import { BillingContact, IEncrypted } from '.'
import {
  CustomerStatus,
  TradeCreditStatusType,
  InHouseCreditStatusType,
} from '../dictionaries'
import { IBankAccountModel } from '../mst'

export interface SupplierAccountHouseCreditInfo {
  account_open_date: string | null
  ar_forward_terms: string | null
  ar_forward_available_credit: string | null
  processing_payments: string | null
  max_credit_limit: string | null
  payment_history: string | null
  avg_days_to_pay: string | null
  open_a_r: string | null
  current_balance: string | null
  highest_credit: string | null
  billing_past_due_1: string | null
  billing_past_due_2: string | null
  billing_past_due_3: string | null
  billing_past_due_more: string | null
  total_past_due: string | null
  average_invoices: string | null
  rejected_ach_count: string | null
  ein_ssn: IEncrypted | null
}

export interface BlueTapeCreditInfo {
  maximum_credit_amount: number | undefined
  available_credit: number
  held_amount: number
  outstanding_amount: number
  past_due_amount: number
  processing_amount: number
  account_status: TradeCreditStatusType | string
}

export interface InHouseCreditInfo {
  maximum_credit_amount: number
  available_credit: number
  held_amount: number
  outstanding_amount: number
  past_due_amount: number
  processing_amount: number
  account_status: InHouseCreditStatusType
  credit_terms: string
  total_invoices_due: number
}

export interface TradeCreditDrawsInfo {
  activeDrawsCount: number
  inactiveDrawsCount: number
  pastDueDrawsCount: number
  pastDueWorstStatus: string
}

export interface TradeCreditTransactionsInfo {
  pastDueAmount: number
  processingAmount: number
  dueThisMonthAmount: number
  dueThisWeekAmount: number
}

export interface SupplierAccount {
  id: string
  company_id: string
  name: string
  contacts: BillingContact[]
  dateAdded: string
  customer: { id: string } | null
  status: CustomerStatus
  credit_status: string
  credit_status_valid_till: any
  customer_credit_limit: number
  house_credit_info?: SupplierAccountHouseCreditInfo
  first_name: string | null
  last_name: string | null
  display_name: string | null
  phone: string
  email: string
  address: string
  business_phone: string
  invited: boolean
  bankAccounts?: IBankAccountModel[]
  invoice_import_enabled?: boolean
  last_purchase_date?: Date
  contact_source?: string
  connector?: {
    quickBooksSettings?: {
      invoice_import_enabled?: boolean
    }
  }
}
