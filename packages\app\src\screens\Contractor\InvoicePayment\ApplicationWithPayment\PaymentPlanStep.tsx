import React, { FC } from 'react'
import { StyleSheet, View } from 'react-native'
import { BtPlainText } from '@linqpal/components/src/ui'
import { BButton } from '../../../../ui/atoms/builder-2.0/Button'
import ApplicationStore from '../../../GeneralApplication/Application/ApplicationStore'
import { useTranslation } from 'react-i18next'
import { PaymentPlansList } from '../Credit/PaymentPlan/PaymentPlansList'
import { observer } from 'mobx-react'
import RootStore from '../../../../store/RootStore'
import { ILoanPaymentPlanModel, routes } from '@linqpal/models'
import { IDownPaymentDetails } from '@linqpal/models/src/types/routes'
import { PaymentStepContainer } from './PaymentStepContainer'
import { frequency } from '@linqpal/models/src/dictionaries'

interface IProps {
  invoices: any[]
  paymentPlan: ILoanPaymentPlanModel | null
  onSelect: (
    paymentPlan: ILoanPaymentPlanModel,
    downPayment: IDownPaymentDetails | null,
  ) => void
  onContinue: () => void
  onClose?: () => void
}

export const PaymentPlanStep: FC<IProps> = observer(
  ({ invoices, paymentPlan, onSelect, onContinue, onClose }) => {
    const { t } = useTranslation('application')

    // using app store for backward compatibility only, to be removed
    const { paymentPlans, loadingPlans } = ApplicationStore

    const document = RootStore.userStore.document

    const hasSinglePayment = paymentPlans?.some(
      (p) => p.frequency === frequency.SINGLE,
    )

    const handleSelect = (
      paymentPlanDetails: ILoanPaymentPlanModel,
      downPaymentDetails: IDownPaymentDetails | null,
    ) => {
      onSelect(paymentPlanDetails, downPaymentDetails)
    }

    const handleContinue = () => {
      if (!paymentPlan) return

      routes.user
        .updatePlan(
          invoices.map((i) => i._id),
          paymentPlan.id,
          document?.loanApplicationId,
        )
        .then(onContinue)
    }

    return (
      <PaymentStepContainer
        title={t('CreditRequestPaymentPlan.title')}
        onClose={onClose}
      >
        <BtPlainText style={styles.heading}>
          {t('ChooseAPaymentPlan.Heading')}
        </BtPlainText>

        {!hasSinglePayment && paymentPlans?.length ? (
          <BtPlainText style={styles.subHeading}>
            {t('ChooseAPaymentPlan.SubHeading')}
          </BtPlainText>
        ) : null}

        <PaymentPlansList
          invoices={invoices}
          paymentPlan={paymentPlan}
          onSelect={handleSelect}
        />

        {!loadingPlans ? (
          <View style={styles.continueButtonWrapper}>
            <BButton
              onPress={handleContinue}
              buttonStyle={styles.continueButton}
              loading={RootStore.isBusy}
              disabled={RootStore.isBusy || !paymentPlan}
            >
              {t('Continue')}
            </BButton>
          </View>
        ) : null}
      </PaymentStepContainer>
    )
  },
)

const styles = StyleSheet.create({
  heading: {
    marginTop: 40,
    fontWeight: '600',
    fontSize: 16,
    lineHeight: 24,
    alignSelf: 'center',
  },
  subHeading: {
    marginTop: 4,
    marginBottom: 9,
    fontWeight: '500',
    fontSize: 12,
    lineHeight: 20,
    color: '#335C75',
    width: 250,
    textAlign: 'center',
  },
  continueButtonWrapper: {
    width: '100%',
    alignItems: 'flex-end',
    marginVertical: 30,
  },
  continueButton: {
    width: 150,
  },
})
