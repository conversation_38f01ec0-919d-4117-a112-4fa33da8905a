import React, { FC } from 'react'
import { NumberFormatValues, PatternFormat } from 'react-number-format'
import { BtInputBaseProps } from './BtInputBase'
import { BtInput_v1 } from './BtInput_v1'

export type BtDateInputFormat = 'YYYY' | 'MM/YYYY' | 'MM/DD/YYYY'

export interface BtDateInputProps extends BtInputBaseProps {
  format?: BtDateInputFormat
}

export const BtDateInput_v1: FC<BtDateInputProps> = ({
  size = 'large',
  value,
  label,
  format = 'MM/DD/YYYY',
  required,
  disabled,
  placeholder,
  validate,
  onChangeText,
  testID,
}) => {
  const handleValueChange = (formatValues: NumberFormatValues) => {
    if (onChangeText) {
      onChangeText(formatValues.formattedValue)
    }
  }

  const patternFormat = format.replace(/([A-Z])/g, '#')

  return (
    <PatternFormat
      format={patternFormat}
      placeholder={placeholder || format}
      size={size}
      label={label}
      value={value}
      required={required}
      disabled={disabled}
      keyboardType="number-pad"
      customInput={BtInput_v1}
      validate={validate}
      allowEmptyFormatting={false}
      onValueChange={handleValueChange}
      testID={testID}
    />
  )
}
