import React, { FC } from 'react'
import { BtPlainText } from '@linqpal/components/src/ui'
import { useLenderApplication } from '../LenderApplicationContext'
import { StyleSheet, View } from 'react-native'

export const WizardHeader: FC = () => {
  const store = useLenderApplication()
  const groups = store.getFlowGroups()

  return (
    <View style={styles.wrapper}>
      {groups.map((group) => {
        return <BtPlainText key={group}>{group}</BtPlainText>
      })}
    </View>
  )
}

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    gap: 32,
  },
})
