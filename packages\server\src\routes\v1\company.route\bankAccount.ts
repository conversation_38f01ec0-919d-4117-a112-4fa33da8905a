import { authRequired } from '../../../services/auth.service'
import { bankAccountsService, Company } from '@linqpal/common-backend'
import transactional from '../../../services/transactional.service'
import { ControllerItem } from 'src/routes/controllerItem'

export default {
  middlewares: {
    pre: [authRequired(), ...transactional.pre],
    post: transactional.post,
  },
  get: async (req, res) => {
    const { id } = req.query
    if (!id || typeof id !== 'string') {
      res.status(400).send({ error: 'Id not provided' })
      return
    }

    const bankAccountObj = await bankAccountsService.getBankAccount(
      id as string,
      req.company!.id,
    )
    res.send({ result: 'ok', bankAccount: bankAccountObj })
  },
  post: async (req, res, next) => {
    const company = await Company.findOne({ _id: req.company!._id }).session(
      req.session,
    )

    const { _id, status, ...data } = req.body

    const id = await bankAccountsService.addOrUpdateBankAccount(
      company,
      null,
      _id,
      data,
      req.user!,
      req.session,
    )

    res.locals.result = { id: id }

    next()
  },
  delete: async (req, res, next) => {
    const { id } = req.query
    if (!id || typeof id !== 'string') {
      res.status(400).send({ error: 'Id not provided' })
      return
    }
    await bankAccountsService.deleteBankAccount(id, req.company!.id)
    next()
  },
} as ControllerItem
