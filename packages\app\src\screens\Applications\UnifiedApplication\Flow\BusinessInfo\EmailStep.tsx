import React, { FC } from 'react'
import { BtInput } from '@linqpal/components/src/ui'
import { observer } from 'mobx-react-lite'
import { useTranslation } from 'react-i18next'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { runInAction } from 'mobx'
import { IUnifiedApplicationEditor } from '../getUnifiedApplicationEditor'

const EmailEditor: FC = observer(() => {
  const { t } = useTranslation('application')
  const store = useUnifiedApplication()

  const handleChange = (email: string) => {
    runInAction(() => (store.draft.data.businessInfo.email = email))
  }

  return (
    <BtInput
      value={store.draft.data?.businessInfo?.email || ''}
      onChangeText={handleChange}
      label={t('Business.EmailLabel')}
      testID="UnifiedApplication.BusinessInfo.Email"
    />
  )
})

export const EmailStep: IUnifiedApplicationEditor = {
  options: {
    title: 'Business.Email',
  },
  component: EmailEditor,
}
