import React, { FC } from 'react'
import { Icon } from '@ui-kitten/components'
import { theme } from '@linqpal/common-frontend'

interface OwnerIconProps {
  isOwnerFilled?: boolean
}

export const OwnerIcon: FC<OwnerIconProps> = ({ isOwnerFilled = true }) => {
  return (
    <Icon
      fill={
        isOwnerFilled
          ? theme.colors.accent.secondary
          : theme.colors.tertiary.light
      }
      style={{ marginTop: 12 }}
      width={20}
      height={20}
      name="person"
    />
  )
}
