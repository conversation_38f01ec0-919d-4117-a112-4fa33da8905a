import React, { FC } from 'react'
import { observer } from 'mobx-react-lite'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { UnifiedApplicationStore } from '../../Store/UnifiedApplicationStore'
import { AddressEditor } from '../../Components/AddressEditor'
import { IApplicationAddress } from '@linqpal/models'
import { runInAction } from 'mobx'
import { IUnifiedApplicationEditor } from '../getUnifiedApplicationEditor'

const OwnerAddressStepEditor: FC = () => {
  const store = useUnifiedApplication()

  const handleChange = (value: IApplicationAddress) => {
    runInAction(() => {
      store.currentUser.address = value
    })
  }

  return (
    <AddressEditor
      address={store.currentUser.address || {}}
      onChange={handleChange}
      testID="UnifiedApplication.BusinessOwner.Address"
    />
  )
}

export const OwnerAddressStep: IUnifiedApplicationEditor = {
  options: {
    title: (store: UnifiedApplicationStore) => {
      return store.isOwner || store.isAuthorized
        ? 'Owner.HomeAddress'
        : 'Owner.AuthorizedHomeAddress'
    },
  },
  component: observer(OwnerAddressStepEditor),
}
