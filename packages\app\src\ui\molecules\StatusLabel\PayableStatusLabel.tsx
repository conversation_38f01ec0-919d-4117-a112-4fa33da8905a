import React, { FC } from 'react'
import { StatusLabel } from './StatusLabel'
import { getSalesDocumentStatusUIOptions } from './options/getSalesDocumentStatusUIOptions'

interface IPayableStatusLabelProps {
  status: any
}

export const PayableStatusLabel: FC<IPayableStatusLabelProps> = ({
  status,
}) => {
  // customer-side label for sales documents (invoices, quotes etc)
  return (
    <StatusLabel
      status={status}
      uiOptionsProvider={getSalesDocumentStatusUIOptions}
    />
  )
}
