import React, { <PERSON> } from 'react'
import { BtRadioGroup, BtSelect } from '@linqpal/components/src/ui'
import { observer } from 'mobx-react'
import { useTranslation } from 'react-i18next'
import { useLenderApplication } from '../../LenderApplicationContext'
import { LegalResidencyStatus } from '@linqpal/models/src/applications/lender/ILenderApplicationDraft'
import { runInAction } from 'mobx'
import { ILenderApplicationEditor } from '../getLenderApplicationEditor'
import { editorStyles } from '../editorStyles'
import { Spacer } from '../../../../../ui/atoms'

const SponsorCitizenshipEditor: FC = () => {
  const { t } = useTranslation('application')
  const store = useLenderApplication()

  const handleChange = (option: boolean) => {
    runInAction(() => {
      if (!store.draft.data.sponsor.citizenship) {
        store.draft.data.sponsor.citizenship = {}
      }

      store.draft.data.sponsor.citizenship.isUsCitizen = option

      if (option === true) {
        store.draft.data.sponsor.citizenship.legalResidencyStatus = undefined
      }
    })
  }

  const handleLegalResidencyChange = (option: { id: LegalResidencyStatus }) => {
    runInAction(() => {
      if (!store.draft.data.sponsor.citizenship) {
        store.draft.data.sponsor.citizenship = {}
      }

      store.draft.data.sponsor.citizenship.legalResidencyStatus = option.id
    })
  }

  return (
    <>
      <BtRadioGroup
        value={store.draft.data.sponsor.citizenship?.isUsCitizen}
        // prettier-ignore
        options={[
          {
            label: t('LenderApplication.Flow.Sponsor.Citizenship.Radio.Yes'),
            value: true,
          },
          {
            label: t('LenderApplication.Flow.Sponsor.Citizenship.Radio.No'),
            value: false,
          },
        ]}
        onChange={handleChange}
        labelStyle={editorStyles.radioLabel}
        groupStyle={editorStyles.radioGroupVertical}
        testID="LenderApplication.Sponsor.Citizenship"
      />

      {store.draft.data.sponsor.citizenship?.isUsCitizen === false && (
        <>
          <Spacer height={16} />
          <BtSelect
            mandatory
            label={t(
              'LenderApplication.Flow.Sponsor.Citizenship.LegalResidencyLabel',
            )}
            value={store.draft.data.sponsor.citizenship?.legalResidencyStatus}
            // prettier-ignore
            options={[
              {
                name: t('LenderApplication.Flow.Sponsor.Citizenship.Options.PermanentResident'),
                id: LegalResidencyStatus.PermanentResident,
              },
              {
                name: t('LenderApplication.Flow.Sponsor.Citizenship.Options.Visa'),
                id: LegalResidencyStatus.Visa,
              },
              {
                name: t('LenderApplication.Flow.Sponsor.Citizenship.Options.ForeignResident'),
                id: LegalResidencyStatus.ForeignResident,
              },
            ]}
            onChange={handleLegalResidencyChange}
            // modern appearance for now has borderWidth: 2, use to avoid changing legacy selects. Later can be moved to default theme and removed
            appearance="thick-border"
            testID="LenderApplication.Sponsor.Citizenship.LegalResidency"
          />
        </>
      )}
    </>
  )
}

export const SponsorCitizenshipStep: ILenderApplicationEditor = {
  options: {
    title: 'LenderApplication.Flow.Sponsor.Citizenship.Title',
    canSkip: false,
  },
  component: observer(SponsorCitizenshipEditor),
}
