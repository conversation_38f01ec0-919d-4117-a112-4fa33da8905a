import {
  attachInvoiceIntegrationConnectorToCompanyConnectorReferences,
  BankAccount,
  cardPaymentService,
  Company,
  CustomerAccount,
  Invoice,
  LoanApplication,
  Notification,
  Operation,
  payWithAch,
  User,
} from '@linqpal/common-backend'
import { authRequired, supplierRequired } from '../../../services/auth.service'
import moment from 'moment-timezone'
import { dictionaries, exceptions } from '@linqpal/models'
import transactional from '../../../services/transactional.service'
import { isFeatureOn } from '../../../services/config.service'
import numbro from 'numbro'
import { invoiceSchemaStatus } from '@linqpal/models/src/dictionaries'

import { ICustomerAccount } from '@linqpal/common-backend/src/models/types'
import { Types } from 'mongoose'
import {
  getCustomersData,
  prepareDataAndUpsertCustomers,
} from '@linqpal/common-backend/src/services/customer/customer-status.service'
import { ControllerItem } from '../../controllerItem'

export default {
  middlewares: {
    pre: [
      isFeatureOn('payments'),
      authRequired(),
      supplierRequired,
      ...transactional.pre,
    ],
    post: transactional.post,
  },
  post: async (req, res, next) => {
    const { id, ...data } = req.body

    let inv = await Invoice.findById(id)
    if (!inv) throw new exceptions.LogicalError('invoice/not-found')

    const customerCompany = await Company.findById(inv.payer_id)
    const customerContact = await User.findById(
      inv.supplierInvitationDetails.userId,
    )
    const existingCustomerAccount = await CustomerAccount.findOne({
      phone: customerContact?.phone || '',
      company_id: req.company!.id,
    })
    let customerAccount: ICustomerAccount
    if (existingCustomerAccount) {
      customerAccount = existingCustomerAccount
    } else {
      const customerAccountCreated = await CustomerAccount.create(
        [
          {
            company_id: req.company!.id,
            first_name: customerContact?.firstName,
            last_name: customerContact?.lastName,
            phone: customerContact?.phone,
            name: customerCompany?.name,
            invited: false,
            settings: {
              acceptAchPayment: true,
              sendFinalPaymentWhenLoanIsPaid:
                !!req.company?.settings.sendFinalPaymentWhenLoanIsPaid,
            },
          },
        ],
        { session: req.session },
      )
      customerAccount = customerAccountCreated[0]

      const customerId = new Types.ObjectId(customerAccount._id.toString())
      const updatedCustomer = await getCustomersData([customerId])

      await prepareDataAndUpsertCustomers(updatedCustomer)
      //await emitCustomerEvent(customerAccount.id, 'Created')
    }

    await Operation.findOneAndUpdate(
      {
        owner_id: id.toString(),
      },
      {
        date: moment().tz('America/Chicago').toDate(),
        type: dictionaries.OPERATION_TYPES.INVOICE.PAYMENT,
        amount: inv.total_amount,
        status: dictionaries.OPERATION_STATUS.PLACED,
        metadata: { payee_id: req.company!.id },
      },
      {
        new: true,
        upsert: true,
        session: req.session,
      },
    )

    const paymentMethodId = inv.supplierInvitationDetails.paymentMethodId

    inv =
      (await Invoice.findOneAndUpdate(
        {
          _id: id,
        },
        {
          company_id: req.company!.id,
          customer_account_id: customerAccount._id.toString(),
          approved: true,
          status: invoiceSchemaStatus.placed,
          supplierInvitationDetails: null,
          payer_id: inv.payer_id,
        },
        {
          new: true,
          session: req.session,
        },
      )) || inv

    // lint fails without this line with RangeError: Maximum call stack size exceeded
    const totalAmount: number = inv.total_amount

    const content = `${req.company!.name} approved your invoice of $${numbro(
      totalAmount,
    ).format({ thousandSeparated: true, mantissa: 2 })}`
    await Notification.findOneAndUpdate(
      { 'metadata.alertType': 'approved', 'metadata.invoice_id': inv._id },
      {
        sender: {
          company_id: inv.company_id,
          user_id: '',
        },
        receiver: {
          company_id: customerCompany?._id.toString(),
          user_id: '',
        },
        content: content,
        type: 'INVOICES',
        metadata: {
          alertType: 'sent',
          invoice_id: inv._id,
          dueDate: inv.invoice_due_date,
          expiryDate: inv.expiration_date,
          companyName: req.company!.name,
          amount: inv.total_amount,
        },
        isRead: false,
        isViewed: false,
      },
      { new: true, upsert: true, session: req.session },
    )

    const companyDetails = await Company.findById(req.company!.id)
    if (data.approved && paymentMethodId && paymentMethodId !== 'credit') {
      //await checkStatusAndGetOperation(inv, req.session)

      const paymentMethodDetails = await BankAccount.findById(paymentMethodId)
      if (paymentMethodDetails?.paymentMethodType === 'bank') {
        await payWithAch(
          [inv],
          paymentMethodId,
          inv.payer_id,
          req.company!.id, // supplier in this specific case
          req.session,
          req.user?.id || '',
        )
      } else {
        if (
          companyDetails?.settings?.cardPricingPackageId === 'optOut' ||
          !companyDetails?.settings?.cardPricingPackageId
        ) {
          throw new exceptions.LogicalError('No card package selected')
        }
        await cardPaymentService.makeCardPayment({
          invoices: [inv],
          accountId: paymentMethodDetails?.cardMetadata?.accountId,
          payerCompanyId: inv.payer_id,
          session: req.session,
          userId: req.user?.id || '',
        })
      }

      await attachInvoiceIntegrationConnectorToCompanyConnectorReferences(
        inv.payer_id,
        [inv.id],
      )
    } else if (paymentMethodId === 'credit') {
      if (
        companyDetails?.settings?.loanPricingPackageId === 'optOut' ||
        !companyDetails?.settings?.loanPricingPackageId
      ) {
        throw new exceptions.LogicalError('No loan package selected')
      }
      const existingApplication = await LoanApplication.findOne({
        company_id: inv.payer_id,
        'invoiceDetails.invoiceId': id,
      })
      if (!existingApplication) {
        throw new exceptions.LogicalError('No existing application found')
      }
    }
    res.locals.result = { id }
    next()
  },
} as ControllerItem
/*
async function emitCustomerEvent(
  customerId: string,
  eventType: 'Created' | 'Updated',
) {
  try {
    await AwsService.sendSQSMessage(
      'customer-syncback',
      JSON.stringify({ customerId, eventType }),
    )
  } catch (e) {
    console.error(e)
  }
}*/
