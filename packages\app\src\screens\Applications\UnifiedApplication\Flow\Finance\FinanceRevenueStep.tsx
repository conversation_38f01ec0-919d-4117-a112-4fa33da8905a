import React, { FC } from 'react'
import { observer } from 'mobx-react-lite'
import { useTranslation } from 'react-i18next'
import { BtCurrencyInput } from '@linqpal/components/src/ui'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { runInAction } from 'mobx'
import { IUnifiedApplicationEditor } from '../getUnifiedApplicationEditor'

const FinanceRevenueEditor: FC = () => {
  const { t } = useTranslation('application')
  const store = useUnifiedApplication()

  const handleChange = (value: string) => {
    runInAction(() => {
      const cleaned = value?.trim().replace(/[^0-9.]/g, '')
      const numeric = parseFloat(cleaned)

      store.draft.data.finance.revenue = !isNaN(numeric) ? numeric : undefined
    })
  }

  const value = store.draft.data.finance.revenue
    ? store.draft.data.finance.revenue.toString()
    : ''

  return (
    <BtCurrencyInput
      value={value}
      label={t('Finance.RevenueLabel')}
      size="large"
      onChangeText={handleChange}
      testID="UnifiedApplication.Finance.Revenue"
    />
  )
}

export const FinanceRevenueStep: IUnifiedApplicationEditor = {
  options: {
    title: 'Finance.AnnualRevenue',
    canSkip: false,
  },
  component: observer(FinanceRevenueEditor),
}
