import React, { FC, useEffect, useRef } from 'react'
import { ScrollView, View } from 'react-native'
import { useResponsive } from '../../../utils/hooks'

interface WizardScrollAreaProps {
  currentStep: string
  wrapperStyle?: any
  children: React.ReactNode
}

export const WizardScrollArea: FC<WizardScrollAreaProps> = ({
  children,
  currentStep,
  wrapperStyle,
}) => {
  const { screenWidth, sm } = useResponsive()

  const scrollRef = useRef<ScrollView>(null)

  useEffect(() => {
    // reset scroll position when navigating between large components
    // like preview -> coowner -> preview
    scrollRef.current?.scrollTo({ y: 0, animated: false })
  }, [currentStep])

  return (
    <ScrollView
      ref={scrollRef}
      contentContainerStyle={{ flexGrow: 1, alignItems: 'center' }}
      showsVerticalScrollIndicator={false}
    >
      <View
        style={[
          {
            paddingHorizontal: sm ? 0 : 20,
            paddingVertical: 20,
            flex: 1,
            minWidth: screenWidth > 700 ? 700 : '100%',
            maxWidth: screenWidth > 700 ? 700 : undefined,
          },
          wrapperStyle,
        ]}
      >
        {children}
      </View>
    </ScrollView>
  )
}
