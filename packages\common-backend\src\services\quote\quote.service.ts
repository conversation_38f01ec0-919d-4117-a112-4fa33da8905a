import {
  Company,
  Invoice,
  LoanApplication,
  LoanPaymentPlan,
  Operation,
  Settings,
} from '../../models'
import { EInvoiceType, InvoicePaymentType, QuoteDetails } from '@linqpal/models'
import { IInvoice, ILoanApplication, IOperation } from '../../models/types'
import { Logger } from '../logger/logger.service'
import {
  ActionPerformer,
  DefaultSettings,
  invoiceSchemaStatus,
  InvoiceSchemaStatusType,
  invoiceStatus,
  LOAN_APPLICATION_STATUS,
  LoanApplicationStatuses,
  OPERATION_STATUS,
  OPERATION_TYPES,
  SettingKeys,
} from '@linqpal/models/src/dictionaries'
import AwsService from '../aws.service'
import mongoose, { ClientSession } from 'mongoose'
import { LoanApplicationService } from '../loanApplication.service'
import moment, { Moment } from 'moment-timezone'
import { QuoteRepository } from '../../repositories/quote.repository'
import { QuoteNotifications } from './quoteNotifications.service'
import { LOAN_APPLICATION_TYPE } from '@linqpal/models/src/dictionaries/loanApplicationTypes'
import { InvoiceDocumentGenerationService } from './invoiceDocumentGeneration.service'
import { getOwningUser } from '../company.service'
import { logPromiseErrors } from '../../helpers/logging'
import { CompanyUtils } from '@linqpal/models/src/helpers/companyUtils'
import {
  DrawApprovalStatus,
  IDrawApproval,
  PayableType,
} from '../onBoarding/types'
import { onBoardingService } from '../onBoarding/onBoarding.service'
import { loanService } from '../loan.dotnet.service'
import { FactoringService } from '../factoring'
import { findBuilderByAccountId } from '../invoices.service/findBuilderAccount'
import { CriticalError } from '@linqpal/models/src/types/exceptions'
import {
  ArAdvanceStatus,
  PaymentDelayCodeParser,
} from '@linqpal/models/src/dictionaries/factoring'
import { DrawApprovalService } from '../drawApproval.service'
import { sendInvoicePlacedNotifications } from '../invoices.service/notifications'
import { getApiURL } from '../../helpers/common'

export class QuoteService {
  private static logger = new Logger({
    module: 'bluetape.services',
    subModule: 'QuoteService',
  })

  private static notifications = QuoteNotifications

  public static async initQuoteAuthorization(app: ILoanApplication) {
    if (app.type === LOAN_APPLICATION_TYPE.QUOTE) {
      const quote = await LoanApplicationService.getSingleInvoice(app)

      if (quote?.type === EInvoiceType.QUOTE) {
        quote.status = invoiceSchemaStatus.authorizationInReview
        await quote.save()
      }
    }
  }

  public static async authorizeQuote(
    drawApprovalId: string,
    session: ClientSession | null,
  ) {
    const drawApproval = await onBoardingService.getDrawApprovalById(
      drawApprovalId,
    )

    this.logger.info({ drawApproval }, 'found draw approval')

    // prettier-ignore
    {
      if (!drawApproval) throw new CriticalError(`unable to find draw approval ${drawApprovalId}`)
      if (!drawApproval.companyId) throw new CriticalError(`no customer company in draw approval`, { drawApproval })
      if (!drawApproval.creditHoldAmount) throw new CriticalError(`no hold amount in draw approval`, { drawApproval })
    }

    const quote = await DrawApprovalService.getSinglePayable(
      drawApproval,
      session,
    )

    // prettier-ignore
    if (!quote) throw new CriticalError(`no quote found`, { drawApprovalId })

    if (quote.type !== EInvoiceType.QUOTE) {
      // prettier-ignore
      this.logger.warn(`attempt to authorize the invoice ${quote.id}`)
      return
    }

    this.logger.info({ quote }, 'authorizing quote')

    quote.status = invoiceSchemaStatus.authorized

    if (!quote.quoteDetails) quote.quoteDetails = QuoteDetails.create()

    const authorization_deadline = moment(drawApproval.expirationDate)

    quote.quoteDetails.authorization_limit = drawApproval.creditHoldAmount
    quote.quoteDetails.authorization_deadline = authorization_deadline.toDate()
    quote.quoteDetails.authorization_date = drawApproval.approvedAt
      ? new Date(drawApproval.approvedAt)
      : moment().startOf('day').toDate()

    await Promise.all([
      quote.save({ session }),
      this.authorizeLegacyLoanApplication(quote, session),
    ])

    await this.notifications.quoteAuthorized(quote, drawApproval, session)
  }

  public static async tryMatchInvoiceToQuote(
    invoice: IInvoice,
    originalDueDate: Date | null | undefined,
    host: string | null,
    session: ClientSession | null,
  ): Promise<boolean> {
    if (
      !invoice.invoice_number ||
      !invoice.quote_number ||
      invoice.type !== EInvoiceType.INVOICE ||
      invoice.status !== invoiceStatus.placed
    ) {
      return false
    }

    const quote = await this.findMatchingQuote(invoice)
    if (!quote) return false

    this.logger.info(`found a quote ${quote.id} for invoice ${invoice.id}`)

    if (!this.isInvoiceAmountValid(quote, invoice.total_amount)) {
      this.logger.info(`detaching quote ${quote.id} from invoice ${invoice.id}`)

      invoice.quote_number = null
      await invoice.save({ session })

      // keep loan application when invoice amount mismatch authorized amount
      // so another invoice can be submitted for the quote
      return false
    }

    const drawApproval = await DrawApprovalService.getAuthorizedApproval(
      quote.id,
    )

    // prettier-ignore
    if (!drawApproval) throw new CriticalError(`no authorized approval found`, { quote })

    return quote.paymentDetails?.paymentType === InvoicePaymentType.FACTORING
      ? this.tryAssignFactoringInvoiceToQuote(
          quote,
          invoice,
          drawApproval,
          originalDueDate,
          host,
          session,
        )
      : this.tryAssignInvoiceToQuote(quote, invoice, drawApproval, session)
  }

  public static async postTransaction(
    quoteId: string,
    amount: number,
    host: string | null,
    session: ClientSession,
  ) {
    // prettier-ignore
    this.logger.info(`posting transaction for quote ${quoteId}, amount ${amount}`)

    // quick fix to minimize risk of double-posting because of slow OBS response - ensure quote status is read after OBS respond
    // would be better to use distributed locks like
    // schema:
    // await Locks.collection.createIndex({ _id: 1 }, { unique: true })
    // await Locks.collection.createIndex({ expiresAt: 1 }, { expireAfterSeconds: 0 }) // TTL
    //
    // logic
    // try {
    //   // atomic mongo creation will fail on duplicate
    //   await Locks.create({
    //     _id: `postTransaction-${quoteId}`,
    //     createdAt: new Date(),
    //     expiresAt: new Date(Date.now() + 30000) // 30 sec timeout
    //   })
    //   // do logic here
    // } catch (error) {
    //   // duplicate key
    //   if (error.code === 11000) throw new Error()
    // } finally {
    //   await Locks.deleteOne({ _id: lockId }) // release the lock
    // }
    const drawApproval = await DrawApprovalService.getAuthorizedApproval(
      quoteId,
    )
    const quote = await Invoice.findById(quoteId)

    // prettier-ignore
    {
      if (!quote) throw new Error(`no quote ${quoteId} found`)
      if (!drawApproval) throw new Error(`no authorized draw approval found for quote ${quoteId}`)

      if (quote.type !== EInvoiceType.QUOTE) throw new Error(`receivable ${quoteId} has wrong type ${quote.type}`)
      if (quote.status !== invoiceSchemaStatus.authorized) throw new Error(`unable to post transaction for a quote ${quoteId} in ${quote.status}`)
      if (!this.isInvoiceAmountValid(quote, amount)) throw new Error(`amount ${amount} of posted transaction exceeds available for quote ${quoteId}`)
    }

    this.logger.info(`generating invoice for quote ${quoteId}`)

    const currentDate = moment.tz('America/Chicago').endOf('day')
    const dueDate = currentDate.clone().add(1, 'day') // has no meaning for posted invoice, but required by schema

    const invoiceData: Partial<IInvoice> = {
      type: EInvoiceType.INVOICE,
      status: invoiceSchemaStatus.placed,
      company_id: quote.company_id,
      customer_account_id: quote.customer_account_id,
      address: quote.address,
      addressType: quote.addressType,

      invoice_date: currentDate.format(),
      invoice_due_date: dueDate.format(),

      invoice_number: `inv ${quote.invoice_number}`,
      quote_number: quote.invoice_number,

      material_subtotal: quote.material_subtotal,
      tax_amount: quote.tax_amount,
      total_amount: quote.total_amount,

      document_name: `invoice-${quote.invoice_number}.pdf`,
    }

    invoiceData.invoice_document = await this.generateInvoiceDocument(
      quote,
      invoiceData,
      drawApproval,
    )

    const invoice: IInvoice = await Invoice.create(invoiceData)

    // same operation is created in save.ts for new invoices
    await Operation.findOneAndUpdate(
      { owner_id: invoice.id },
      {
        date: moment().toDate(),
        type: OPERATION_TYPES.INVOICE.PAYMENT,
        amount: invoice.total_amount,
        status: OPERATION_STATUS.PLACED,
        metadata: { payee_id: invoice.company_id },
      } as IOperation,
      { new: true, upsert: true },
    )

    return quote.paymentDetails?.paymentType === InvoicePaymentType.FACTORING
      ? this.tryAssignFactoringInvoiceToQuote(
          quote,
          invoice,
          drawApproval,
          dueDate.toDate(),
          host,
          session,
        )
      : this.tryAssignInvoiceToQuote(quote, invoice, drawApproval, session)
  }

  public static async extendAuthorization(
    quoteId: string,
    authorizationDeadline: string,
    actionPerformer: ActionPerformer,
  ) {
    const quote = await Invoice.findByIdAndUpdate(
      quoteId,
      {
        $set: {
          'quoteDetails.authorization_deadline': moment
            .tz(authorizationDeadline, 'MM/DD/YYYY', 'America/Chicago')
            .endOf('day')
            .toDate(),
        },
      },
      { new: true, upsert: false },
    )

    if (!quote?.quoteDetails?.authorization_deadline)
      throw new CriticalError(`unable to update quote ${quoteId}`, { quote })

    if (actionPerformer === ActionPerformer.Supplier) {
      // when authorization is extended from new BO, draw approval expiration is already updated
      const drawApproval = await DrawApprovalService.getAuthorizedApproval(
        quote.id,
      )

      if (drawApproval) {
        await onBoardingService.patchDrawApproval(drawApproval.id, {
          expirationDate: moment(
            quote.quoteDetails.authorization_deadline,
          ).toDate(),
          updatedBy: 'AutomaticNodeJsServices',
        })
      } else {
        this.logger.warn(`no draw approval for quote ${quoteId} found`)
      }
    }

    await this.notifications.authorizationExtended(quote, actionPerformer)
  }

  public static async cancelQuote(
    quote: IInvoice,
    session: ClientSession,
  ): Promise<void> {
    // to be called by supplier only
    this.logger.info({ quote }, 'canceling a quote by supplier')

    if (!quote) throw new Error('quote is required')

    if (quote.quoteDetails?.invoice_id) {
      throw new Error('Unable to cancel quote with invoice assigned')
    }

    const [app, drawApproval] = await Promise.all([
      this.findQuoteApplication(quote.id),
      DrawApprovalService.getQuoteApproval(quote.id, quote.company_id),
    ])

    // prettier-ignore
    this.logger.info({ quote, app, drawApproval }, 'found quote application and approval')

    await this.cancelQuoteApplication(app, drawApproval, quote, session)
  }

  public static async cancelQuoteApplication(
    app: ILoanApplication | null,
    drawApproval: IDrawApproval | null,
    quote: IInvoice,
    session: ClientSession | null,
  ) {
    if (
      [
        invoiceSchemaStatus.cancelled,
        invoiceSchemaStatus.rejected,
        invoiceSchemaStatus.expired,
        invoiceSchemaStatus.invoiced,
      ].includes(quote.status as any)
    ) {
      // when supplier cancels a quote, we update draw approval status,
      // OBS sync logic calls quote cancellation for a second time
      this.logger.info(`cannot cancel quote in final status ${quote.status}`)
      return
    }

    this.logger.info({ quote, drawApproval, app }, 'canceling quote')

    // TODO: VK: manage session properly - commit before calling .NET APIs
    await this.discardQuote(
      quote,
      app,
      invoiceSchemaStatus.cancelled,
      LOAN_APPLICATION_STATUS.CANCELED,
      session,
    )
    await this.discardDrawApproval(
      quote,
      drawApproval,
      DrawApprovalStatus.Canceled,
    )

    const customerCompanyId =
      drawApproval?.companyId ??
      (await this.ensureCustomerCompany(quote, session)).id

    await this.notifications.quoteCanceled(quote, customerCompanyId, session)
  }

  public static async rejectQuoteApplication(
    drawApproval: IDrawApproval,
    silentRejection: boolean,
    session: ClientSession | null,
  ) {
    const quoteId = drawApproval.payables?.find(
      (p) => p.type === PayableType.Quote,
    )?.id

    // prettier-ignore
    if (!quoteId) throw new CriticalError('no quoteId in draw approval', { drawApproval })

    const quote = await Invoice.findById(quoteId).session(session)

    // prettier-ignore
    if (!quote)
      throw new CriticalError(`unable to find quote ${quoteId}`, {
        drawApproval,
      })

    // for IHC quotes loan application is absent
    const app = await this.findQuoteApplication(quote.id)

    // TODO: VK: manage session properly - commit before calling .NET APIs
    await this.discardQuote(
      quote,
      app,
      invoiceSchemaStatus.rejected,
      LOAN_APPLICATION_STATUS.REJECTED,
      session,
    )
    await this.discardDrawApproval(
      quote,
      drawApproval,
      DrawApprovalStatus.Rejected,
    )

    if (!silentRejection) {
      // prettier-ignore
      if (!drawApproval.companyId) throw new CriticalError('no customer company', { drawApproval })

      await this.notifications.quoteRejected(
        quote,
        drawApproval.companyId,
        session,
      )
    }
  }

  public static async handleQuoteApplicationSendBack(
    app: ILoanApplication,
    session: ClientSession,
  ) {
    // TODO: VK: How it works with new BO?
    const quote = await LoanApplicationService.getSingleInvoice(app, session)

    if (!quote || quote.type !== EInvoiceType.QUOTE) return

    quote.status = invoiceSchemaStatus.placed

    // prettier-ignore
    await Promise.all([
      quote.save({ session }),
      this.releaseHeldAmount(quote)
    ])
  }

  public static async releaseHeldAmount(
    quote: IInvoice,
    approval: IDrawApproval | null = null,
  ): Promise<void> {
    // TODO: VK: pass draw approval always
    const drawApproval =
      approval ??
      (await DrawApprovalService.getQuoteApproval(quote.id, quote.company_id))

    if (!drawApproval) {
      this.logger.info(`unable to find a draw approval for quote ${quote.id}`)
      return
    }

    if (!drawApproval.authorizationPeriodId) {
      // prettier-ignore
      this.logger.info({ drawApproval }, 'Cannot release credit hold, authorizationPeriodId is missing')
      return
    }

    // prettier-ignore
    await Promise.all([
      drawApproval.authorizationPeriodId
        ? loanService.cancelCreditHold(drawApproval.authorizationPeriodId)
        : Promise.resolve(),
      drawApproval.arAdvanceAuthorizationPeriodId
        ? loanService.cancelCreditHold(drawApproval.arAdvanceAuthorizationPeriodId)
        : Promise.resolve(),
    ])
  }

  public static async batchExpireQuoteAuthorizations() {
    // to be run daily, will cancel quote and app with expired authorization
    const expiredQuotes = await QuoteRepository.getExpiredQuotes()
    if (!expiredQuotes?.length) return

    this.logger.info(`found ${expiredQuotes.length} expired quotes`)

    const batchSize = 5

    for (let i = 0; i < expiredQuotes.length; i += batchSize) {
      const batch = expiredQuotes.slice(i, i + batchSize)

      await Promise.allSettled(
        batch.map(async (quote) => {
          try {
            await this.tryExpireQuoteAuthorization(quote)
          } catch (e: any) {
            this.logger.error(
              { error: e },
              `failed to expire quote ${quote.id}`,
            )
          }
        }),
      )
    }
  }

  public static async tryExpireQuoteAuthorization(
    quote: IInvoice,
  ): Promise<boolean> {
    if (!quote.quoteDetails?.authorization_deadline) {
      this.logger.warn(`no authorization deadline for quote ${quote.id}`)
      return false
    }

    if (moment().isBefore(moment(quote.quoteDetails.authorization_deadline))) {
      return false
    }

    const app = await this.findAuthorizedApplication(quote.id)

    if (app && app.status !== LOAN_APPLICATION_STATUS.AUTHORIZED) {
      // prettier-ignore
      this.logger.warn(`authorized quote ${quote.id} is referenced by app ${app.id} in status ${app.status}`)
      return false
    }

    const drawApproval = await DrawApprovalService.getAuthorizedApproval(
      quote.id,
    )

    if (!drawApproval) {
      this.logger.warn({ quote }, 'no authorized approval for expired quote')
      return false
    }

    // prettier-ignore
    if (!drawApproval.companyId) throw new CriticalError(`no customer company found`, { drawApproval })

    this.logger.info(`quote ${quote.id} is expired`)

    const session = await mongoose.startSession()
    session.startTransaction()

    try {
      await this.discardQuote(
        quote,
        app,
        invoiceSchemaStatus.expired,
        LOAN_APPLICATION_STATUS.CANCELED,
        session,
      )
      await session.commitTransaction()
    } catch (e) {
      await session.abortTransaction()
      throw e
    } finally {
      await session.endSession()
    }

    // ensure node.js mongo updates are committed before calling OBS, so it has latest data
    await this.discardDrawApproval(
      quote,
      drawApproval,
      DrawApprovalStatus.Canceled,
    )

    await this.notifications.authorizationExpired(
      quote,
      drawApproval.companyId,
      session,
    )

    return true
  }

  public static async batchNotifyNearingExpiration() {
    const quotes = await QuoteRepository.getNearingExpirationQuotes()
    if (!quotes.length) return

    this.logger.info(`found ${quotes.length} quotes nearing expiration`)

    const results = await Promise.allSettled(
      quotes.map(async (quote) => {
        await this.notifications.authorizationNearingExpiration(quote)
      }),
    )

    logPromiseErrors(results, this.logger)
  }

  public static async getExpirationSeconds(): Promise<number> {
    const expirationSetting = await Settings.findOne({
      key: SettingKeys.QuoteExpirationDelay,
    })

    return expirationSetting?.value ?? DefaultSettings.QuoteExpirationDelay
  }

  public static async getAuthorizationDeadline(): Promise<Moment> {
    return moment()
      .add(await this.getExpirationSeconds(), 'seconds')
      .endOf('day')
  }

  public static calculateAuthorizationLimit(totalAmount: number) {
    return Math.round(totalAmount * 1.1 * 100) / 100
  }

  private static async tryAssignFactoringInvoiceToQuote(
    quote: IInvoice,
    invoice: IInvoice,
    drawApproval: IDrawApproval,
    originalDueDate: Date | null | undefined,
    host: string | null,
    session: ClientSession | null,
  ): Promise<boolean> {
    // prettier-ignore
    {
      if (!quote.quoteDetails) throw new CriticalError('no quoteDetails in quote', { quote })
      if (!quote.paymentDetails) throw new CriticalError('no paymentDetails in quote', { quote })
    }

    const [supplier, supplierUser] = await Promise.all([
      Company.findById(invoice.company_id),
      getOwningUser(invoice.company_id),
    ])

    if (!supplier) throw new Error(`no supplier for invoice ${invoice.id}`)
    if (!supplierUser) throw new Error(`no user for invoice ${invoice.id}`)

    // use original payment plan from authorized quote, not a currently selected for customer
    const [factoringOptions, paymentPlan] = await Promise.all([
      FactoringService.ensureArAdvanceOptions(supplier, invoice),
      LoanPaymentPlan.findById(quote.paymentDetails.loanPlanId),
    ])

    // prettier-ignore
    if (!paymentPlan) throw new CriticalError('unable to find payment plan for quote', { quote })

    if (!factoringOptions.isValid) {
      this.logger.info(
        { invoice, quote },
        'no factoring options are available for invoice with quote, process it like a regular invoice',
      )
      return false
    }

    quote.quoteDetails.invoice_id = invoice.id // add redundant cross-reference to simplify queries
    quote.status = invoiceSchemaStatus.invoiced

    const paymentDelay = PaymentDelayCodeParser.getPaymentDelay(
      paymentPlan.paymentDelayCode,
    )

    invoice.quoteId = quote.id
    invoice.invoice_due_date = paymentDelay.moment.format()
    invoice.invoiceNotificationType = quote.invoiceNotificationType

    invoice.paymentDetails = {
      paymentType: InvoicePaymentType.FACTORING,
      pricingPackageId: quote.paymentDetails.pricingPackageId,
      loanPlanId: quote.paymentDetails.loanPlanId,
      originalDueDate: originalDueDate ?? paymentDelay.moment.toDate(),
      arAdvanceStatus: ArAdvanceStatus.Approved,
      cardPricingPackageId: null,
      customerFee: null,
      supplierFee: null,
      fees: null,
    }

    await Promise.all([
      invoice.save({ session }),
      quote.save({ session }),
      this.addDrawApprovalInvoice(drawApproval, invoice),
      this.releaseHeldAmount(quote, drawApproval),
      this.reportToNetSuite({
        id: quote.id,
        operationType: 'QuoteProcessed',
        status: 'approved',
      }),
    ])

    // ensure updates are available for issue loan
    const invoicedApproval = await onBoardingService.getDrawApprovalById(
      drawApproval.id,
    )

    await FactoringService.issueLoan(invoicedApproval)

    await sendInvoicePlacedNotifications(
      invoice.id,
      [invoice.customer_account_id],
      moment().format('YYYY-MM-DD'),
      invoice.invoiceNotificationType,
      supplier,
      supplierUser,
      host ?? getApiURL(),
      session,
    )

    return true
  }

  private static isInvoiceAmountValid(
    quote: IInvoice,
    amount: number,
  ): boolean {
    // keep application and quote alive if authorized amount is exceeded, detach invoice from quote (RB, 31/05/2024)
    // so they may submit pay this invoice in nirmal way and submit another invoice for the quote
    if (!quote.quoteDetails?.authorization_limit) {
      // prettier-ignore
      this.logger.warn(`no authorization limit for quote ${quote.id}, fallback to quote amount ${quote.total_amount}`)
    }

    const authorizationLimit =
      quote.quoteDetails?.authorization_limit ?? quote.total_amount
    const isAmountValid = amount <= authorizationLimit

    if (!isAmountValid) {
      // prettier-ignore
      this.logger.info(`Amount ${amount} exceeds approved amount ${authorizationLimit} of quote ${quote.id}`)
    }

    return isAmountValid
  }

  private static async findMatchingQuote(
    invoice: IInvoice,
  ): Promise<IInvoice | null> {
    if (!invoice.quote_number) {
      this.logger.warn(`no quote number on invoice ${invoice.id}`)
      return null
    }

    const quotes = await Invoice.find({
      company_id: invoice.company_id,
      customer_account_id: invoice.customer_account_id,
      invoice_number: mongoose.trusted({
        $regex: invoice.quote_number,
        $options: 'i',
      }),
      type: EInvoiceType.QUOTE,
      status: invoiceSchemaStatus.authorized,
    }).sort({ createdAt: -1 })

    if (!quotes || !quotes.length) {
      // prettier-ignore
      this.logger.info({ invoice }, `unable to find matching quote for invoice ${invoice.id}`)
      return null
    }

    // use the most recent quote for phase 1, in future can be reviewed
    if (quotes.length > 1) {
      this.logger.warn(
        `Found more than one matching quote for a company ${invoice.company_id}, invoice ${invoice.invoice_number}`,
      )
    }

    const quote = quotes[0]

    this.logger.info(
      `Found matching quote ${quote.id} for a company ${invoice.company_id}, invoice ${invoice.invoice_number}`,
    )

    if (quote.quoteDetails?.invoice_id) {
      this.logger.info(
        `quote ${quote.id} already has invoice ${quote.quoteDetails?.invoice_id} attached`,
      )
      return null
    }

    return quote
  }

  private static async generateInvoiceDocument(
    quote: IInvoice,
    invoiceData: Partial<IInvoice>,
    drawApproval: IDrawApproval,
  ): Promise<string> {
    // prettier-ignore
    if (!drawApproval.companyId) throw new CriticalError('no customer company', { drawApproval })

    const [supplier, customerUser] = await Promise.all([
      Company.findById(quote.company_id),
      getOwningUser(drawApproval.companyId),
    ])

    // prettier-ignore
    {
      if (!supplier) throw new CriticalError(`no supplier company found`, { quote, drawApproval })
      if (!customerUser) throw new CriticalError('no customer user found', { quote, drawApproval })
    }

    this.logger.info(`generating pdf for quote ${quote.id} posting`)

    const companyName = CompanyUtils.getCompanyName(supplier)

    try {
      const invoiceDocument =
        await InvoiceDocumentGenerationService.generateInvoiceWithQuoteDocument(
          invoiceData,
          quote,
          customerUser,
          companyName,
        )

      return invoiceDocument
    } catch (e: any) {
      this.logger.error(e, 'invoice document generation failed')
      return ''
    }
  }

  private static async tryAssignInvoiceToQuote(
    quote: IInvoice,
    invoice: IInvoice,
    drawApproval: IDrawApproval,
    session: ClientSession | null,
  ): Promise<boolean> {
    const app = await this.findAuthorizedApplication(quote.id)
    if (!app) return false

    if (!quote.quoteDetails) quote.quoteDetails = QuoteDetails.create()

    quote.quoteDetails.invoice_id = invoice.id // add redundant cross-reference to simplify queries
    app.invoiceDetails.invoiceId = invoice.id // replace quote with invoice in loan app

    invoice.quoteId = quote.id

    quote.status = invoiceSchemaStatus.invoiced
    app.status = LOAN_APPLICATION_STATUS.PROCESSING

    await Promise.all([
      app.save({ session }),
      invoice.save({ session }),
      quote.save({ session }),
      this.reportToNetSuite({
        id: quote.id,
        operationType: 'QuoteProcessed',
        status: 'approved',
      }),
      this.addDrawApprovalInvoice(drawApproval, invoice),
    ])

    this.logger.info(`resuming step function for application ${app.id}`)

    await AwsService.sendSQSMessage(
      'issue-loan',
      JSON.stringify({ applicationId: app.id }),
      undefined,
      app.id,
    )

    await this.notifications.quoteInvoiceAssigned(
      quote,
      invoice,
      app.company_id,
      session,
    )

    return true
  }

  private static async addDrawApprovalInvoice(
    drawApproval: IDrawApproval,
    invoice: IInvoice,
  ) {
    await onBoardingService.addDrawApprovalInvoices(drawApproval.id, [
      {
        id: invoice.id,
        amount: invoice.total_amount,
        type: EInvoiceType.INVOICE,
        invoiceNumber: invoice.invoice_number,
      },
    ])
  }

  private static async discardQuote(
    quote: IInvoice,
    app: ILoanApplication | null,
    quoteStatus: InvoiceSchemaStatusType,
    appStatus: LoanApplicationStatuses,
    session: ClientSession | null,
  ) {
    // prettier-ignore
    this.logger.info({ quote, app, quoteStatus, appStatus }, `discarding quote ${quote.id}`)

    quote.status = quoteStatus

    if (app) {
      app.status = appStatus
      // probably is not needed now for apps before issue-loan?
      app.executionArn = ''
      app.progress = { step: '', action: '', error: '' }
    }

    await Promise.all([
      quote.save({ session }),
      app ? app.save({ session }) : Promise.resolve(null),
    ])
  }

  private static async discardDrawApproval(
    quote: IInvoice,
    drawApproval: IDrawApproval | null,
    drawApprovalStatus: DrawApprovalStatus,
  ) {
    // prettier-ignore
    this.logger.info({ quote, drawApproval, drawApprovalStatus }, `discarding draw approval ${drawApproval?.id}`)

    await Promise.all([
      this.releaseHeldAmount(quote, drawApproval),
      drawApproval
        ? onBoardingService.patchDrawApproval(drawApproval.id, {
            status: drawApprovalStatus,
            updatedBy: 'AutomaticNodeJsServices',
          })
        : Promise.resolve(null),
    ])
  }

  private static async ensureCustomerCompany(
    receivable: IInvoice,
    session: ClientSession | null,
  ) {
    const customerCompany = await findBuilderByAccountId(
      receivable.customer_account_id,
      session,
    )

    // prettier-ignore
    if (!customerCompany) throw new CriticalError(`no customer company for receivable`, { receivable })

    return customerCompany
  }

  private static async reportToNetSuite(payload: {
    id: string
    operationType: string
    status: string
    reason?: string
  }) {
    const payloadJson = JSON.stringify(payload)

    this.logger.info(`sending quote to NetSuite: ${payloadJson}`)

    await AwsService.sendSQSMessage(
      'netsuite-connector',
      payloadJson,
      'NETSUITE',
    )
  }

  // region Legacy stuff

  private static async authorizeLegacyLoanApplication(
    quote: IInvoice,
    session: ClientSession | null,
  ) {
    const app = await this.findQuoteApplication(quote.id)
    if (!app) return // no loan apps for IHC quotes

    this.logger.info({ app }, `authorizing legacy loan app ${app.id}`)

    await this.updateLoanApplicationStatus(
      app,
      LOAN_APPLICATION_STATUS.AUTHORIZED,
      session,
    )

    this.logger.info(`authorized legacy loan app ${app.id}`)
  }

  private static async updateLoanApplicationStatus(
    app: ILoanApplication | null,
    status: LoanApplicationStatuses,
    session: ClientSession | null,
  ) {
    if (!app) {
      this.logger.info(`no loan application to update status to ${status}`)
      return
    }

    app.status = status
    await app.save({ session })
  }

  private static async findQuoteApplication(quoteId: string) {
    const app = await LoanApplication.findOne({
      'invoiceDetails.invoiceId': quoteId,
    })

    if (!app) this.logger.warn(`no loan application found for quote ${quoteId}`)

    return app
  }

  private static async findAuthorizedApplication(quoteId: string) {
    const app = await this.findQuoteApplication(quoteId)

    if (app?.status !== LOAN_APPLICATION_STATUS.AUTHORIZED) {
      this.logger.warn(`no authorized application found for quote ${quoteId}`)
      return null
    }

    return app
  }

  // endregion Legacy stuff
}
