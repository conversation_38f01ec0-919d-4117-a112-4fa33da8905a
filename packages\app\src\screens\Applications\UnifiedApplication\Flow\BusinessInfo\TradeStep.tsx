import React, { FC } from 'react'
import { BtSelect } from '@linqpal/components/src/ui'
import { observer } from 'mobx-react-lite'
import { useTranslation } from 'react-i18next'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { runInAction } from 'mobx'
import { IUnifiedApplicationEditor } from '../getUnifiedApplicationEditor'

const options = [
  'Electrical, Solar, Low Voltage',
  'Plumbing, Pipeline, and Fire Protection',
  'Framing and Rough Carpentry Contractor',
  'Roofing and Waterproofing',
  'Countertop fabricator and Installation',
  'Landscaping, Paving, Earthwork',
  'Finish Carpentry, Cabinet and Millwork',
  'Drywall and Plaster',
  'Concrete and Foundation',
  'Insulation and Acoustical',
  'Appliance Installation',
  'Ceramic and Mosaic Tile',
  'Flooring and Floor Covering',
  'Lathing and Plastering',
  'Masonry',
  'Painting and Decorating',
  'Structural Steel, Reinforcing Steel, and Sheet Meta',
  'Swimming Pool',
  'Glazing, Glass and Mirror',
  'Elevator',
  'Other',
]

const TradeEditor: FC = observer(() => {
  const { t } = useTranslation('application')
  const store = useUnifiedApplication()

  const handleChange = (selected: string[]) => {
    runInAction(() => {
      store.draft.data.businessInfo.trade = selected
    })
  }

  const value = store.draft.data.businessInfo.trade ?? []

  return (
    <BtSelect
      value={value}
      onChange={handleChange}
      label={t('Business.TradeLabel')}
      placeholder={t('Business.TradePlaceholder')}
      options={options}
      multiSelect
      primitiveArray
      size="large"
      name="value"
      testID="UnifiedApplication.BusinessInfo.Trade"
    />
  )
})

export const TradeStep: IUnifiedApplicationEditor = {
  options: {
    title: 'Business.Trade',
    description: 'Business.TradeDescription',
  },
  component: TradeEditor,
}
