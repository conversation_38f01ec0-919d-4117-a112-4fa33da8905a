import React, { FC } from 'react'
import { BtInput } from '@linqpal/components/src/ui'
import { observer } from 'mobx-react-lite'
import { useTranslation } from 'react-i18next'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { WizardStepDescription } from '../../Wizard/WizardStepDescription'
import { runInAction } from 'mobx'
import { Spacer } from '../../../../../ui/atoms'
import { IUnifiedApplicationEditor } from '../getUnifiedApplicationEditor'

const BusinessNameEditor: FC = observer(() => {
  const { t } = useTranslation('application')

  const store = useUnifiedApplication()

  const handleLegalNameChange = (legalName: string) => {
    runInAction(
      () =>
        (store.draft.data.businessInfo.businessName = {
          ...(store.draft.data.businessInfo.businessName ?? {}),
          legalName,
        }),
    )
  }

  const handleDbaChange = (dba: string) => {
    runInAction(
      () =>
        (store.draft.data.businessInfo.businessName = {
          ...(store.draft.data.businessInfo.businessName ?? {}),
          dba,
        }),
    )
  }

  return (
    <>
      <BtInput
        size="large"
        testID="BusinessNameInput"
        value={store.draft.data.businessInfo.businessName?.legalName || ''}
        label={t('Business.NameLabel')}
        onChangeText={handleLegalNameChange}
      />

      {!store.isInReview && (
        <WizardStepDescription
          // eslint-disable-next-line i18next/no-literal-string
          description="Business.DbaTitle"
          descriptionStyle={{ marginTop: 24 }}
        />
      )}

      <Spacer height={22} />

      <BtInput
        size="large"
        testID="DbaInput"
        value={store.draft.data.businessInfo.businessName?.dba || ''}
        label={t('Business.DbaLabel')}
        onChangeText={handleDbaChange}
      />
    </>
  )
})

export const BusinessNameStep: IUnifiedApplicationEditor = {
  options: {
    title: 'Business.Name',
    description: 'Business.NameDescription',
  },
  component: BusinessNameEditor,
}
