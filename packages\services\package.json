{"name": "@linqpal/services", "version": "1.0.0", "private": true, "scripts": {"tsc": "tsc", "sls-offline": "sls offline start --skipCacheInvalidation", "deploy": "serverless deploy --verbose", "test": "cross-env NODE_ENV=test mocha --exit --timeout 30000 --require test/setup.js test", "test:coverage": "cross-env NODE_ENV=test nyc --reporter lcov --reporter text mocha --timeout 120000 --exit --recursive --require test/setup.js test", "debug": "nodemon --watch .env --watch . --exec \"ts-node app.ts --transpile-only \" -e js,ts,json"}, "dependencies": {"@aws-sdk/client-eventbridge": "3.236.0", "@aws-sdk/client-kms": "3.236.0", "@aws-sdk/client-lambda": "3.236.0", "@aws-sdk/client-s3": "3.236.0", "@aws-sdk/client-secrets-manager": "3.237.0", "@aws-sdk/client-sfn": "3.236.0", "@aws-sdk/client-sns": "3.236.0", "@aws-sdk/client-sqs": "3.236.0", "@hubspot/api-client": "3.4.1", "@linqpal/common-backend": "workspace:*", "@linqpal/models": "workspace:*", "@sendgrid/client": "7.7.0", "@sendgrid/helpers": "7.7.0", "@sendgrid/mail": "7.7.0", "@sentry/serverless": "7.112.2", "axios": "0.21.1", "chai": "4.2.0", "core-js": "3.35.1", "crypto-js": "4.0.0", "csvtojson": "^2.0.10", "dotenv": "8.2.0", "express": "5.0.0-alpha.8", "hashids": "2.3.0", "libphonenumber-js": "1.10.61", "lodash": "4.17.21", "mailparser": "^3.4.0", "mathjs": "9.4.4", "moment": "^2.29.4", "moment-business-days": "1.2.0", "moment-timezone": "^0.5.40", "mongoose": "8.16.4", "nodemon": "2.0.6", "numbro": "2.3.5", "query-string": "6.14.1", "sinon": "9.0.3", "string-similarity": "4.0.4", "typescript": "^5.4.5", "ua-parser2": "0.5.1", "uuid": "9.0.0", "xlsx": "0.16.9"}, "devDependencies": {"@babel/cli": "7.12.1", "@babel/core": "^7.17.2", "@babel/node": "7.12.1", "@babel/preset-env": "7.12.1", "@babel/register": "7.12.1", "@types/express": "4.17.13", "@types/lambda-tester": "3.6.2", "@types/mailparser": "3.4.0", "@types/string-similarity": "4.0.0", "aws-sdk-client-mock": "2.0.1", "chai": "4.2.0", "chai-http": "4.3.0", "cross-env": "7.0.2", "debug": "2.6.9", "dotenv": "8.2.0", "lambda-tester": "4.0.1", "mocha": "8.1.3", "mongodb-memory-server": "8.11.5", "nock": "13.0.11", "nyc": "15.1.0", "serverless": "3.38.0", "serverless-bundle": "6.1.0", "serverless-domain-manager": "7.3.8", "serverless-dotenv-plugin": "6.0.0", "serverless-offline": "13.5.0", "serverless-plugin-warmup": "8.3.0", "serverless-prune-plugin": "2.0.2", "serverless-step-functions": "3.21.0", "sinon": "9.0.3"}, "mocha": {"extension": ["ts", "js"]}}