import {
  AwsService,
  connectToDatabase,
  getEnvironmentVariables,
  LoanService,
  Logger,
  Slack,
} from '@linqpal/common-backend'
import { eomXlsxReport } from '@linqpal/models/src/helpers/eom-xlsx-report'
import moment from 'moment-timezone'
import xlsx from 'xlsx'
import { ReportFormatter } from '@linqpal/common-backend/src/services/loan/reportFormatter'
import { customErrorToString } from '@linqpal/common-backend/src/helpers/SnsEventBuilder'

const WAREHOUSE_BUCKET = 'dwh-assets'
const REPORTS_FOLDER = 'reports'

const logger = new Logger({ module: 'reports' })

// noinspection JSUnusedGlobalSymbols
export async function loanPortfolioReport() {
  await getEnvironmentVariables()
  await connectToDatabase()

  const items = await LoanService.loanPortfolioReport()
  const date_time = moment().format('YYYYMMDD')
  const filename = `${REPORTS_FOLDER}/LOAN_PORTFOLIO_REPORT_${date_time}.json`
  await saveReport(items, filename)
}

// noinspection JSUnusedGlobalSymbols
export async function loanActivityReport() {
  await getEnvironmentVariables()
  await connectToDatabase()

  const items = await LoanService.loanActivityReport()

  const date_time = moment().format('YYYYMMDD')
  const filename = `${REPORTS_FOLDER}/loanActivity/LOAN_ACTIVITY_REPORT_${date_time}.xlsx`

  await saveReport(items, filename)
  await saveWarehouseReport(items, 'Loan_Activity_Report.xlsx')
}

// noinspection JSUnusedGlobalSymbols
export async function eomReport() {
  await getEnvironmentVariables()
  await connectToDatabase()

  const bucket = `${process.env.LP_MODE}.uw1.linqpal-user-assets`
  const date = moment().subtract(1, 'month').startOf('month')
  const date_str = date.format('YYYYMMDD')
  const filename = `EOM_REPORT_${date_str}.xlsx`

  const report = await LoanService.eomReport(date)
  const xlsx_book = await eomXlsxReport(date, report)

  await AwsService.putS3File(
    bucket,
    `reports/${filename}`,
    xlsx.write(xlsx_book, { type: 'buffer', bookType: 'xlsx' }),
    'us-west-1',
  )
}

// noinspection JSUnusedGlobalSymbols
export async function loanExperianReport(e: any) {
  await getEnvironmentVariables()
  await connectToDatabase()

  let items: any
  let filename = `BC.C865672.STSAUTO`
  if (e.all) {
    items = await LoanService.loanExperianReport()
    filename = `${filename}.json`
  } else {
    const date_start = moment().subtract(1, 'month').startOf('month')
    const date_end = moment().startOf('month')
    items = await LoanService.loanExperianReport(date_start, date_end)
    filename = `${filename}.${date_start.format('YYYYMMDD')}.json`
  }
  await saveReport(items, `${REPORTS_FOLDER}/experian/${filename}`)
}

// noinspection JSUnusedGlobalSymbols
export async function lineOfCreditAccountsReport() {
  await getEnvironmentVariables()
  await connectToDatabase()

  const startTime = moment()
  logger.info('start')

  const reportLines = await LoanService.lineOfCreditAccountsReport(logger)

  const elapsedTime = moment().diff(startTime, 'seconds')
  logger.info(
    `completed collecting report data, ${reportLines?.length} lines in ${elapsedTime} sec`,
  )

  const currentDate = moment().format('YYYYMMDD')
  const fileName = `${REPORTS_FOLDER}/locAccounts/LOC_ACCOUNTS_REPORT_${currentDate}.xlsx`

  await saveReport(reportLines, fileName)
  await saveWarehouseReport(reportLines, 'LoC_Accounts_Report.xlsx')
}

// noinspection JSUnusedGlobalSymbols
export async function transactionsReport() {
  await getEnvironmentVariables()
  await connectToDatabase()

  const startTime = moment()
  logger.info('start')

  try {
    const reportLines = await LoanService.transactionsReport(logger)

    const elapsedTime = moment().diff(startTime, 'seconds')

    logger.info(
      `completed collecting report data, ${reportLines?.length} lines in ${elapsedTime} sec`,
    )

    const dateTime = moment().format('YYYYMMDD')
    const fileName = `${REPORTS_FOLDER}/transactions/TRANSACTIONS_REPORT_${dateTime}.xlsx`

    await saveReport(reportLines, fileName)
    await saveWarehouseReport(reportLines, 'Transactions_Report.xlsx')
  } catch (e: any) {
    logger.error(`Error generating transactions report: ${e.message}`)
    await Slack.notifyError(
      'Transactions Report',
      'Transactions Report',
      customErrorToString(e),
    )
    throw e
  }
}

// noinspection JSUnusedGlobalSymbols
export async function lateLoansReport() {
  await getEnvironmentVariables()
  await connectToDatabase()

  const startTime = moment()
  logger.info('start')

  const reportLines = await LoanService.lateLoansReport(logger)

  const elapsedTime = moment().diff(startTime, 'seconds')
  logger.info(
    `completed collecting report data, ${reportLines?.length} lines in ${elapsedTime} sec`,
  )

  const date_time = moment().format('YYYYMMDD')
  const filename = `${REPORTS_FOLDER}/lateLoans/LATE_LOANS_REPORT_${date_time}.json`

  await saveReport(reportLines, filename)
}

function saveReport(
  reportData: any,
  filePath: string,
  bucket = `${process.env.LP_MODE}.uw1.linqpal-user-assets`,
) {
  const extension = filePath.split('.').pop()

  logger.info(`generating report document at ${bucket}/${filePath}`)

  let fileContent: any

  switch (extension) {
    case 'json':
      fileContent = JSON.stringify(reportData)
      break
    case 'xlsx':
      fileContent = ReportFormatter.toXlsx(reportData)
      break
    default:
      throw new Error(`unsupported report type ${extension}`)
  }

  return AwsService.putS3File(bucket, filePath, fileContent, 'us-west-1')
}

function saveWarehouseReport(reportLines: any[], fileName: string) {
  if (process.env.LP_MODE !== 'prod') return

  logger.info('saving a sanitized copy')

  const sanitizedLines = sanitizeHeaders(reportLines)

  return saveReport(sanitizedLines, fileName, WAREHOUSE_BUCKET)
}

function sanitizeHeaders(tableItems: any[]) {
  const sanitizedLines: any[] = []

  for (const tableItem of tableItems) {
    const sanitizedLine: any = {}

    for (const [key, value] of Object.entries(tableItem)) {
      const sanitizedKey = key.replace(/[^a-zA-Z0-9_]/g, '')
      sanitizedLine[sanitizedKey] = value
    }

    sanitizedLines.push(sanitizedLine)
  }

  return sanitizedLines
}
