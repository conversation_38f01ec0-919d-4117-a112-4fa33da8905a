import React, { FC } from 'react'
import { TFunction, useTranslation } from 'react-i18next'
import { invoiceStatus } from '@linqpal/models/src/dictionaries'
import { StatusLabel } from './StatusLabel'
import { getSalesDocumentStatusUIOptions } from './options/getSalesDocumentStatusUIOptions'

interface IReceivableStatusLabelProps {
  receivable: any
  onPress?: () => void
}

export const ReceivableStatusLabel: FC<IReceivableStatusLabelProps> = ({
  receivable,
  onPress,
}) => {
  // supplier-side label for sales documents (invoices, quotes etc)

  const { t } = useTranslation('global')

  return (
    <StatusLabel
      status={
        receivable.status === invoiceStatus.placed && receivable.seen
          ? invoiceStatus.seen
          : receivable.status
      }
      displayStatus={
        receivable.status === invoiceStatus.pastDue && receivable.seen
          ? t('invoiceStatusTooltip.past-due-seen-status')
          : undefined
      }
      tooltip={getInvoiceStatusTooltip(receivable, t)}
      onPress={onPress}
      uiOptionsProvider={getSalesDocumentStatusUIOptions}
    />
  )
}

const getInvoiceStatusTooltip = (receivable: any, t: TFunction<'global'>) => {
  switch (receivable.status) {
    case invoiceStatus.draft:
      return t('invoiceStatusTooltip.invoice-not-sent')
    case invoiceStatus.placed:
    case invoiceStatus.seen:
      return t('invoiceStatusTooltip.invoice-sent')
    case invoiceStatus.expired:
      return t('invoiceStatusTooltip.invoice-expired-add-new-one')
    case invoiceStatus.dismissed:
      return t('invoiceStatusTooltip.customer-dismissed-invoice')
    default:
      return ''
  }
}
