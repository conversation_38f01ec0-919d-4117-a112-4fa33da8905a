import {
  AgreementService,
  AwsService,
  invoicesService,
  LoanApplication,
  Logger,
} from '@linqpal/common-backend'
import { ControllerItem } from 'src/routes/controllerItem'
import controllers from '../../../controllers'
import middlewares from './middlewares'
import { AgreementType } from '@linqpal/common-backend/src/services/agreement/types'
import { LogicalError } from '@linqpal/models/src/types/exceptions'
import PersonalGuarantorAgreement from '@linqpal/common-backend/src/services/agreement/personalGuarantorAgreement'
import { Request, Response } from 'express'
import { AgreementsSubmissionType } from '@linqpal/models/src/dictionaries/agreementsSubmissionType'

const logger = new Logger({
  module: 'bluetape.services',
  subModule: 'creditApplication',
})

export const upload = {
  middlewares: { pre: [...middlewares.pre] },
  post: async (req, res) => {
    // TODO fix this hack
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    req.company = { _id: req.body.companyId }
    await controllers.file.uploadFile(req, res, 'credit_applications')
  },
} as ControllerItem

export const callDecisionEngine = {
  async post(req, res) {
    const applicationId = await invoicesService.startDecisionEngine(
      req.body.id,
      req.body.downPaymentDetails,
      req.user,
    )
    res.send({ id: applicationId })
  },
} as ControllerItem

export const saveOwnerData = {
  async post(req: any, res: any) {
    await invoicesService.saveOwnerData(req.company.id, req.user)
    res.send({ result: 'ok' })
  },
}

export const makeAgreement = {
  async post(req, res) {
    if (!req.company) throw new Error('Company is required')

    const { agreement_type, paymentPlan, invoiceIds } = req.body
    const data = await controllers.company.previewAgreement(
      {
        agreement_type,
        paymentPlan,
        invoiceIds,
        companyId: req.company.id,
      },
      req.user,
    )
    res.send({ ...data })
  },
} as ControllerItem

export const makeIntegrationAgreement = {
  async post(req, res) {
    if (!req.company) throw new Error('Company is required')

    const { paymentPlan, totalAmount, supplierCompanyId } = req.body
    const data = await controllers.company.previewIntegrationAgreement(
      {
        paymentPlan,
        totalAmount,
        supplierCompanyId,
        companyId: req.company.id,
      },
      req.user,
    )
    res.send({ ...data })
  },
} as ControllerItem

export const previewPersonalGuarantorAgreement = {
  async get(req, res) {
    const draftId = req.query.draftId?.toString()
    if (!draftId) throw new LogicalError('draftId is required')

    const agreementLink = await PersonalGuarantorAgreement.preview(
      draftId,
      req.user!,
    )
    res.send({ ...agreementLink, result: 'ok' })
  },
} as ControllerItem

export const previewMasterAgreement = {
  async get(req, res) {
    const companyId = req.company!.id
    const fileInfo = await AgreementService.createMasterAgreementFromDraft(
      companyId,
      true,
      '',
      req.user,
    )

    res.send({
      url: fileInfo.url,
      fileName: fileInfo.fileName,
      result: 'ok',
    })
  },
} as ControllerItem

export const submitCreditApplication = {
  async post(req, res) {
    const { loanApplicationId, downPayment, ipAddress } = req.body

    logger.info(
      { loanApplicationId, downPayment, ipAddress },
      'submitting credit application',
    )

    const companyId = req.company!._id.toString()
    const userId = req.user!._id.toString()

    // legacy code creates loan application when application filling is started, long before submit
    // so it's possible that invoices in started application are paid by other method, so filter them out here
    const app = await LoanApplication.findById(loanApplicationId)

    if (app?.invoiceDetails?.invoiceId) {
      const invoiceIds = Array.isArray(app.invoiceDetails.invoiceId)
        ? app.invoiceDetails.invoiceId
        : [app.invoiceDetails.invoiceId]

      const [paymentChecks, cancellationChecks] = await Promise.all([
        Promise.all(
          invoiceIds.map((id) => invoicesService.checkInvoiceIsPaid(id)),
        ),
        Promise.all(
          invoiceIds.map((id) => invoicesService.checkInvoiceIsCanceled(id)),
        ),
      ])

      const payableInvoiceIds = invoiceIds.filter(
        (_, index) =>
          !paymentChecks[index].isPaid && !cancellationChecks[index].isCanceled, // remove also missed invoices, if any
      )

      if (invoiceIds.length !== payableInvoiceIds.length) {
        // prettier-ignore
        logger.info({ invoiceIds, payableInvoiceIds }, 'filtered non-payable invoices')

        if (payableInvoiceIds.length === 0) {
          app.set('invoiceDetails.invoiceId', undefined)
        } else {
          app.invoiceDetails.invoiceId = payableInvoiceIds
        }

        await app.save()
      }
    }

    // ensure business owner data is filled with submitter information (if not filled before)
    await invoicesService.saveOwnerData(companyId, req.user!)

    const event = JSON.stringify({
      companyId,
      userId,
      ipAddress,
      type: AgreementsSubmissionType.Credit,
    })

    logger.info(
      { event },
      `sending event to a credit-application-agreements queue`,
    )

    await AwsService.sendSQSMessage(
      'submit-application-agreements',
      event,
      `${companyId}-${AgreementsSubmissionType.Credit}`,
      `${companyId}-${AgreementsSubmissionType.Credit}`,
    )

    await invoicesService.startDecisionEngine(
      loanApplicationId,
      downPayment,
      req.user!,
    )

    res.send({ result: 'ok' })
  },
} as ControllerItem

export const viewAgreement = {
  async get(req, res) {
    const data = await controllers.company.viewAgreement(req)
    res.send({ ...data })
  },
} as ControllerItem

export const tryGetMasterAgreement = {
  async get(req, res) {
    const data = await controllers.company.tryGetMasterAgreement(
      req.company?.id,
    )
    res.send({ ...data })
  },
} as ControllerItem

export const getAgreementMetadata = {
  async get(req, res) {
    const metadata = await AgreementService.getAgreementMetadata(
      req.company?.id,
      // TODO: VK: move agreementType to models
      req.query.agreement_type === 'master'
        ? AgreementType.MASTER_AGREEMENT
        : AgreementType.BNPL_AGREEMENT,
    )

    res.send({ ...metadata, result: 'ok' })
  },
} as ControllerItem

export const hasPersonalGuarantorAgreement = {
  async get(req: Request, res: Response) {
    const companyId = req.company!._id.toString()
    const hasAgreement = await PersonalGuarantorAgreement.existsForCompany(
      companyId,
    )

    res.send({ hasAgreement, result: 'ok' })
  },
} as ControllerItem

export const downloadPersonalGuarantorAgreements = {
  async get(req: Request, res: Response) {
    const companyId = req.company!._id.toString()
    const zipLink = await PersonalGuarantorAgreement.downloadAll(companyId)

    res.send({ ...zipLink, result: 'ok' })
  },
} as ControllerItem
