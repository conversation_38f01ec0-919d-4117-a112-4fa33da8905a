import { cbw, Operation } from '@linqpal/common-backend'
import {
  IBankAccount,
  ICompany,
} from '@linqpal/common-backend/src/models/types'
import {
  OPERATION_STATUS,
  OPERATION_TYPES,
} from '@linqpal/models/src/dictionaries'
import moment from 'moment'
import { adminRequired } from '../../../../services/auth.service'
import transactional from '../../../../services/transactional.service'
import { NextFunction, Request, Response } from 'express'
import { ControllerItem } from 'src/routes/controllerItem'

interface ReqBody {
  payerCompany: ICompany
  payerBankAccount: IBankAccount
  payeeCompany: ICompany
  payeeBankAccount: IBankAccount
  amount: number
  reason?: string | null
}

export const createAchPayment: ControllerItem = {
  middlewares: {
    pre: [adminRequired, ...transactional.pre],
    post: transactional.post,
  },
  async post(
    req: Request<unknown, unknown, ReqBody>,
    _res: Response,
    next: NextFunction,
  ) {
    const amount = req.body.amount

    const [operation] = await Operation.create(
      [
        {
          amount,
          date: moment().toDate(),
          type: OPERATION_TYPES.ACH.PULL,
          status: OPERATION_STATUS.PLACED,
          metadata: {
            payer_id: req.body.payerCompany._id,
            payee_id: req.body.payeeCompany._id,
          },
        },
      ],
      { session: req.session },
    )

    await cbw.achPayment(
      [operation],
      {
        sender: {
          company_id: req.body.payerCompany.id,
          account_id: req.body.payerBankAccount.id,
        },
        recipient: {
          company_id: req.body.payeeCompany.id,
          account_id: req.body.payeeBankAccount.id,
        },
        reason: req.body.reason || 'ACH Payment',
        currency: 'USD',
      },
      req.session,
    )

    return next()
  },
}
