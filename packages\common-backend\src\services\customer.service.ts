import { Company, CustomerAccount, User, UserRole } from '../models'
import { RoleStatus } from '@linqpal/models'

export const getCustomerCompanyPipeline = [
  {
    $lookup: {
      from: User.collection.name,
      localField: 'email',
      foreignField: 'login',
      as: 'email_user',
    },
  },
  { $unwind: { path: '$email_user', preserveNullAndEmptyArrays: true } },
  {
    $lookup: {
      from: UserRole.collection.name,
      localField: 'email_user.sub',
      foreignField: 'sub',
      as: 'email_role',
    },
  },
  { $unwind: { path: '$email_role', preserveNullAndEmptyArrays: true } },
  {
    $addFields: {
      email_customer_id: { $toObjectId: '$email_role.company_id' },
    },
  },
  { $unset: ['email_user', 'email_role'] },
  {
    $lookup: {
      from: User.collection.name,
      localField: 'phone',
      foreignField: 'login',
      as: 'user',
    },
  },
  { $unwind: { path: '$user', preserveNullAndEmptyArrays: true } },
  {
    $lookup: {
      from: UserRole.collection.name,
      localField: 'user.sub',
      foreignField: 'sub',
      as: 'role',
    },
  },
  { $unwind: { path: '$role', preserveNullAndEmptyArrays: true } },
  {
    $addFields: {
      phone_customer_id: { $toObjectId: '$role.company_id' },
    },
  },
  {
    $addFields: {
      customer_id: {
        $cond: [
          { $ne: ['$email_customer_id', null] },
          '$email_customer_id',
          '$phone_customer_id',
        ],
      },
    },
  },
  {
    $lookup: {
      from: Company.collection.name,
      as: 'customer',
      localField: 'customer_id',
      foreignField: '_id',
    },
  },
  { $unwind: { path: '$customer', preserveNullAndEmptyArrays: true } },
  { $unset: ['user', 'role', 'email_customer_id', 'phone_customer_id'] },
]

export async function findSupplierFromCompanyId(companyId: string) {
  const roles = await UserRole.find({
    company_id: companyId,
    status: RoleStatus.Active,
  }).populate('user')

  const users = roles
    .map((r) => {
      const user = (r as any).user
      if (!user) return []

      return [user.phone, user.email].filter(Boolean).reduce((c: any[], v) => {
        c.push({ email: v }, { phone: v })
        return c
      }, [])
    })
    .flat()
  if (users.length > 0)
    return CustomerAccount.find({ $or: users }).distinct('company_id')
  return []
}
