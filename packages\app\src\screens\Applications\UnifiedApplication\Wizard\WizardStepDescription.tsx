import { BtPlainText } from '@linqpal/components/src/ui'
import React, { FC } from 'react'
import { StyleSheet } from 'react-native'
import { useTranslation } from 'react-i18next'
import { useUnifiedApplication } from '../UnifiedApplicationContext'
import { observer } from 'mobx-react'
import { useResponsive } from '../../../../utils/hooks'

interface IProps {
  description?: string | (() => string)
  descriptionStyle?: any
}

export const WizardStepDescription: FC<IProps> = observer(
  ({ description, descriptionStyle }) => {
    const { sm } = useResponsive()
    const { t } = useTranslation('application')

    const store = useUnifiedApplication()

    const descriptionText = description || store.stepOptions?.description
    const style = descriptionStyle || store.stepOptions?.descriptionStyle

    if (!descriptionText) return null

    const text =
      descriptionText instanceof Function ? descriptionText() : descriptionText

    return (
      <BtPlainText
        style={[
          styles.description,
          sm ? styles.descriptionDesktop : styles.descriptionMobile,
          style,
        ]}
      >
        {t(text as any)}
      </BtPlainText>
    )
  },
)

const styles = StyleSheet.create({
  description: {
    fontSize: 16,
    fontWeight: '600',
    color: '#335C75',
  },
  descriptionDesktop: {
    lineHeight: 30,
  },
  descriptionMobile: {
    marginBottom: 12,
  },
})
