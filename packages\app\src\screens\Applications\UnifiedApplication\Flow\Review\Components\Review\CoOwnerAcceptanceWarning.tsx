import React, { <PERSON> } from 'react'
import { Trans, useTranslation } from 'react-i18next'
import { Text, View } from 'react-native'
import { BtPlainText } from '@linqpal/components/src/ui'
import { IconEmail } from '../../../../../../../assets/icons'

interface IProps {
  style?: any
}

export const CoOwnerAcceptanceWarning: FC<IProps> = ({ style }) => {
  const { t } = useTranslation('application')

  return (
    <View style={[styles.wrapper, style]}>
      <IconEmail width={40} style={styles.icon} />

      <BtPlainText style={styles.message}>
        <Trans
          t={t}
          i18nKey="CoOwners.AgreementWarning"
          components={{
            styled: <Text style={{ fontWeight: '700' }} />,
          }}
        />
      </BtPlainText>
    </View>
  )
}

const styles = {
  wrapper: {
    width: '100%',
    borderRadius: 12,
    backgroundColor: '#F5F7F8',
    padding: 15,
    flexDirection: 'row',
  },
  icon: {
    marginLeft: 10,
    marginRight: 20,
    marginTop: 5,
  },
  message: {
    color: '#003353',
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '500',
  },
}
