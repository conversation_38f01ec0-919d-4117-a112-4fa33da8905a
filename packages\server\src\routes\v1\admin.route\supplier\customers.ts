import {
  Company,
  CustomerAccount,
  Invoice,
  LoanApplication,
  UserRole,
} from '@linqpal/common-backend'
import { PipelineStage } from 'mongoose'
import { Request, Response } from 'express'
import { RoleStatus } from '@linqpal/models'
import { LOAN_APPLICATION_STATUS } from '@linqpal/models/src/dictionaries'

export const getCustomerInvoicesCount = {
  async get(req: Request, res: Response) {
    const customerId = req.query.id
    const invoicesCount = await Invoice.countDocuments({
      customer_account_id: customerId,
    })

    res.send({ count: invoicesCount })
  },
}

export const deleteSupplierCustomers = {
  async delete(req: Request, res: Response) {
    const customerId = req.query.id

    await CustomerAccount.findByIdAndDelete(customerId)

    res.send({})
  },
}

export const getSupplierCustomers = async (req: Request, res: Response) => {
  const { search, tradeCredit, loanPaymentCollection } = req.query
  const companyId = (req.query?.id as string) || ''

  const company = await Company.findById(companyId)
  if (!company) {
    res.status(404).send({ reason: 'Company not found' })
    return
  }

  const page = parseInt((req.query?.page as string) || '0')
  const limit = parseInt((req.query?.limit as string) || '0')

  const pipeline: PipelineStage[] = [
    { $addFields: { id: { $toString: '$_id' } } },
    {
      $match: {
        company_id: companyId,
        isGuest: { $ne: true },
      },
    },
    {
      $lookup: {
        from: 'users',
        as: 'users',
        let: {
          email: '$email',
          phone: '$phone',
        },
        pipeline: [
          { $match: { $expr: { $in: ['$login', ['$$email', '$$phone']] } } },
        ],
      },
    },
    { $addFields: { user: { $arrayElemAt: ['$users', 0] } } },
    {
      $lookup: {
        from: UserRole.collection.name,
        as: 'role',
        let: { sub: '$user.sub' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$sub', '$$sub'] },
                  { $eq: ['$status', RoleStatus.Active] },
                ],
              },
            },
          },
        ],
      },
    },
    { $unwind: '$role' },
    {
      $addFields: {
        'role.company_objectId': { $toObjectId: '$role.company_id' },
      },
    },
    {
      $lookup: {
        from: Company.collection.name,
        localField: 'role.company_objectId',
        foreignField: '_id',
        as: 'customerCompany',
      },
    },
    {
      $addFields: {
        customerCompany: { $arrayElemAt: ['$customerCompany', 0] },
      },
    },
    // Complete search filter - all search fields available here
    ...(search
      ? [
          {
            $match: {
              $or: [
                { phone: { $regex: search, $options: 'i' } },
                { email: { $regex: search, $options: 'i' } },
                { name: { $regex: search, $options: 'i' } },
                { 'customerCompany.name': { $regex: search, $options: 'i' } },
              ],
            },
          },
        ]
      : []),
    {
      $lookup: {
        from: LoanApplication.collection.name,
        let: { company_id: '$role.company_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$company_id', '$$company_id'] },
                  { $eq: ['$status', LOAN_APPLICATION_STATUS.APPROVED] },
                ],
              },
            },
          },
          { $limit: 1 },
        ],
        as: 'approvedApplications',
      },
    },
    {
      $addFields: {
        hasApprovedApplications: {
          $gt: [{ $size: '$approvedApplications' }, 0],
        },
      },
    },
    {
      $project: {
        id: 1,
        name: 1,
        first_name: 1,
        last_name: 1,
        email: 1,
        type: 1,
        phone: 1,
        address: 1,
        isDeleted: 1,
        hasApprovedApplications: 1,
        company_id: '$role.company_id',
        companyName: '$customerCompany.name',
        settings: 1,
      },
    },
    ...(tradeCredit
      ? [
          {
            $match: {
              'settings.autoTradeCreditEnabled': tradeCredit === 'on',
            },
          },
        ]
      : []),
    ...(loanPaymentCollection
      ? [
          {
            $match: {
              'settings.loanPaymentCollection': loanPaymentCollection,
            },
          },
        ]
      : []),
  ]

  // Add facet stage to get both count and data in single query
  pipeline.push({
    $facet: {
      data: [
        ...(page > 0 && limit > 0 ? [{ $skip: (page - 1) * limit }] : []),
        ...(limit > 0 ? [{ $limit: limit }] : []),
      ],
      totalCount: [{ $count: 'count' }],
    },
  })

  const result = await CustomerAccount.aggregate(pipeline)
  const items = result[0]?.data || []
  const total = result[0]?.totalCount[0]?.count || 0

  const processedItems = items.map((customer: any) => {
    const calculatedSettings = { ...customer.settings }

    if (
      calculatedSettings?.inHouseCredit?.isEnabled &&
      !calculatedSettings.inHouseCredit?.debtInvestor
    ) {
      calculatedSettings.inHouseCredit = {
        ...calculatedSettings.inHouseCredit,
        debtInvestor: company.settings.arAdvance.defaultDebtInvestor,
      }
    }

    if (
      !calculatedSettings?.debtInvestorTradeCredit &&
      !calculatedSettings?.inHouseCredit?.isEnabled
    ) {
      calculatedSettings.debtInvestorTradeCredit =
        company.settings.defaultDebtInvestorTradeCredit
    }

    return {
      ...customer,
      settings: calculatedSettings,
    }
  })

  res.send({
    items: processedItems,
    total,
    companyName: company.name,
  })
}
