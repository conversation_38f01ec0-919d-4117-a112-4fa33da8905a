import React, { FC } from 'react'
import { useTranslation } from 'react-i18next'
import { BtDateInput } from '@linqpal/components/src/ui'
import { observer } from 'mobx-react-lite'
import { UnifiedApplicationStore } from '../../Store/UnifiedApplicationStore'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { runInAction } from 'mobx'
import { UnifiedApplicationValidator } from '@linqpal/models'
import { IUnifiedApplicationEditor } from '../getUnifiedApplicationEditor'

const OwnerBirthdateEditor: FC = () => {
  const { t } = useTranslation('application')

  const store = useUnifiedApplication()

  const handleValidation = (value: string) =>
    value.length !== 10 || UnifiedApplicationValidator.validateBirthdate(value)
      ? ''
      : t('ValidationErrors.InvalidDate')

  const handleChange = (value: string) => {
    runInAction(() => {
      store.currentUser.birthdate = value
    })
  }

  return (
    <BtDateInput
      value={store.currentUser.birthdate || ''}
      //eslint-disable-next-line i18next/no-literal-string
      format="MM/DD/YYYY"
      size="large"
      validate={handleValidation}
      onChangeText={handleChange}
      label={t('Owner.BirthdayLabel')}
      testID={'UnifiedApplication.BusinessOwner.Birthdate'}
    />
  )
}

export const OwnerBirthdateStep: IUnifiedApplicationEditor = {
  options: {
    title: (store: UnifiedApplicationStore) => {
      return store.isOwner || store.isAuthorized
        ? 'Owner.Birthdate'
        : 'Owner.AuthorizedBirthdate'
    },
  },
  component: observer(OwnerBirthdateEditor),
}
