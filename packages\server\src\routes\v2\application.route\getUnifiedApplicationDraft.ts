import {
  IGetUnifiedApplicationRequest,
  IGetUnifiedApplicationResponse,
} from '@linqpal/models/src/routes2/application/types'
import { TCompoundRoute } from '@linqpal/models/src/routes2/types'
import { authMiddleware } from '../middlewares'

export const getUnifiedApplicationDraft: TCompoundRoute<
  IGetUnifiedApplicationRequest,
  IGetUnifiedApplicationResponse
> = {
  middlewares: {
    pre: [...authMiddleware.pre],
    post: [],
  },
  get: async (
    req: IGetUnifiedApplicationRequest,
  ): Promise<IGetUnifiedApplicationResponse> => {
    if (!req.companyId) throw new Error('companyId is required')

    console.log(req)

    return { draft: null }
  },
}
