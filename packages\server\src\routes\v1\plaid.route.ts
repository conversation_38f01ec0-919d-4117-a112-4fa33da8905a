import {
  BankAccount,
  Company,
  crypt,
  PlaidService,
} from '@linqpal/common-backend'
import { exceptions } from '@linqpal/models'
import { authRequired } from '../../services/auth.service'
import { ControllerItem } from '../controllerItem'
import { IBankAccount } from '@linqpal/common-backend/src/models/types'

export const middlewares = { pre: [authRequired()] }

export const createLinkToken: ControllerItem = {
  middlewares: {
    pre: [...middlewares.pre],
  },
  get: async (req, res) => {
    if (!req.user) throw new Error('User is required')
    if (!req.company) throw new Error('Company is required')

    const { highlightRountingNumber, bankId } = req.query
    let tokenResponse
    try {
      if (bankId) {
        const bank = await BankAccount.findById(bankId)
        const access_token = await crypt.decrypt(
          bank?.plaid?.access_token.cipher,
        )
        tokenResponse = await PlaidService.getLinkTokenInUpdateMode(
          access_token,
          req.user.id,
          req.company.id,
        )
      } else {
        tokenResponse = await PlaidService.getLinkToken(
          req.company?._id.toString(),
          req.user?._id.toString(),
          highlightRountingNumber ? String(highlightRountingNumber) : '',
        )
      }
      res.send(tokenResponse)
    } catch (err) {
      console.log('Create link token error', err)
      throw new exceptions.LogicalError('Something went wrong')
    }
  },
}

export const exchangePublicToken: ControllerItem = {
  middlewares: {
    pre: [...middlewares.pre],
  },
  post: async (req, res) => {
    if (!req.user) throw new Error('User is required')
    if (!req.company) throw new Error('Company is required')

    const { publicToken } = req.body
    try {
      await PlaidService.exchangePublicToken(
        req.user.id,
        publicToken,
        req.company.id,
      )
      const company = await Company.findById(req.company?._id)
        .populate('bankAccounts')
        .lean()
      const onlineConnectedBanks =
        company?.bankAccounts?.filter(
          (b) => !b.isManualEntry && b.paymentMethodType === 'bank',
        ) || []
      const updatedBanks: IBankAccount[] = await Promise.all(
        onlineConnectedBanks?.map((bank) => {
          if (typeof bank.accountNumber === 'object') {
            return crypt
              .decrypt(bank.accountNumber.cipher)
              .then(
                (value: string) =>
                  ({ ...bank, accountNumber: value } as IBankAccount),
              )
          } else {
            return bank
          }
        }),
      )
      const plaidBanks: IBankAccount[] =
        updatedBanks?.filter((bank) => !!bank.plaid?.access_token) || []
      const finicityBanks =
        updatedBanks?.filter(
          (bank) => !bank.plaid?.access_token && !bank.isDeactivated,
        ) || []
      await Promise.all(
        finicityBanks.map(async (fb) => {
          const found = plaidBanks.find(
            (pb) =>
              pb.routingNumber === fb.routingNumber &&
              pb.accountNumber === fb.accountNumber,
          )
          if (found) {
            await BankAccount.findOneAndUpdate(
              { _id: fb._id },
              { isDeactivated: true },
            )
          }
        }),
      )
      res.send({ result: 'ok' })
    } catch (err) {
      console.log('Exchange public token error', err)
      throw new exceptions.LogicalError('Something went wrong')
    }
  },
}

export const searchInstitution: ControllerItem = {
  middlewares: {
    pre: [...middlewares.pre],
  },
  get: async (req, res) => {
    if (!req.user) throw new Error('User is required')

    const { search } = req.query
    try {
      const resp = await PlaidService.searchInstitution(
        req.user.id,
        String(search),
      )
      res.send({ result: 'ok', items: resp.institutions })
    } catch (err) {
      console.log('Search institution failed', err)
      throw new exceptions.LogicalError('Cannot search institution')
    }
  },
}

export const healthCheck: ControllerItem = {
  middlewares: {
    pre: [...middlewares.pre],
  },
  post: async (req, res) => {
    if (!req.user) throw new Error('User is required')

    const { bankId } = req.body
    const bank = await BankAccount.findById(bankId)
    const access_token = await crypt.decrypt(bank?.plaid?.access_token.cipher)
    try {
      await PlaidService.tokenHealthCheck(
        access_token,
        req.user.id,
        bank?.plaid,
      )
      res.send({ result: 'ok' })
    } catch (err) {
      console.log('Health Check failed', err)
      throw new exceptions.LogicalError('Health check failed')
    }
  },
}
