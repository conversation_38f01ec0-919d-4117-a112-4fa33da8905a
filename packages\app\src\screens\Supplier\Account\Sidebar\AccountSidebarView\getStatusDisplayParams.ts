import { InHouseCreditStatus } from '@linqpal/models/src/dictionaries'
import { TradeCreditStatus } from '@linqpal/models/src/dictionaries/TradeCreditStatus'
import moment from 'moment'
import { TFunction } from 'react-i18next'

export const Colors = {
  Neutral: '#3E4756',
  Normal: '#0EC06B',
  Warning: '#EB9928',
  Danger: '#EC002A',
}

export const getTradeCreditStatusDisplayParams = (
  creditInfo: any,
  t: TFunction<'global'>,
) => {
  switch (creditInfo?.credit_status) {
    case TradeCreditStatus.NotApplied:
      return {
        title: t('Account.trade-credit-status.not-applied'),
        color: Colors.Neutral,
      }
    case TradeCreditStatus.GoodStanding:
      return {
        title: t('Account.trade-credit-status.good-standing'),
        color: Colors.Normal,
      }
    case TradeCreditStatus.Approved:
      return {
        title: t('Account.trade-credit-status.approved', {
          validTill: moment(creditInfo?.credit_status_valid_till).format(
            'MM/DD/YYYY',
          ),
          interpolation: { escapeValue: false },
        }),
        hint: t('Account.trade-credit-status.approved-hint', {
          validTill: moment(creditInfo?.credit_status_valid_till).format(
            'MM/DD/YYYY',
          ),
          interpolation: { escapeValue: false },
        }),
        color: Colors.Normal,
      }
    case TradeCreditStatus.IncompleteApplication:
      return {
        title: t('Account.trade-credit-status.incomplete-application'),
        color: Colors.Neutral,
      }
    case TradeCreditStatus.PendingReview:
      return {
        title: t('Account.trade-credit-status.pending'),
        color: Colors.Neutral,
      }
    case TradeCreditStatus.BankDisconnected:
    case TradeCreditStatus.BankDataMissing:
    case TradeCreditStatus.AccountInReview:
      return {
        title: t('Account.trade-credit-status.account-in-review'),
        hint: t('Account.trade-credit-status.account-in-review-hint'),
        color: Colors.Warning,
      }

    case TradeCreditStatus.Delinquent:
      return {
        title: t('Account.trade-credit-status.delinquent'),
        color: Colors.Danger,
      }

    case TradeCreditStatus.AccountOnHold:
      return {
        title: t('Account.trade-credit-status.account-in-review'),
        hint: t(
          'Account.trade-credit-status.account-in-review-decision-rule-hint',
        ),
        color: Colors.Warning,
      }

    case TradeCreditStatus.PastDue:
    case TradeCreditStatus.PastDue60:
      return {
        title: t('Account.trade-credit-status.past-due-60'),
        hint: t('Account.trade-credit-status.past-due-60-hint'),
        color: Colors.Danger,
      }
    case TradeCreditStatus.PastDue10:
      return {
        title: t('Account.trade-credit-status.past-due-10'),
        hint: t('Account.trade-credit-status.past-due-10-hint'),
        color: Colors.Danger,
      }
    /*case TradeCreditStatus.Denied:
      return {
        title: t('Account.trade-credit-status.denied', {
          validTill: moment(creditInfo?.credit_status_valid_till).format(
            'MM/DD/YYYY',
          ),
          interpolation: { escapeValue: false },
        }),
        color: Colors.Neutral,
      }*/ //currently remove this
    case TradeCreditStatus.Denied:
      return {
        title: t('Account.trade-credit-status.credit-denied'),
        color: Colors.Neutral,
      }
    default:
      return { title: '', color: Colors.Neutral }
  }
}

export const getInHouseCreditStatusDisplayParams = (
  creditInfo: any,
  t: TFunction<'global'>,
) => {
  switch (creditInfo?.credit_status) {
    case InHouseCreditStatus.NotApplied:
      return {
        title: t('Account.in-house-credit-status.not-applied'),
        color: Colors.Neutral,
      }
    case InHouseCreditStatus.GoodStanding:
      return {
        title: t('Account.in-house-credit-status.good-standing'),
        color: Colors.Normal,
      }

    case InHouseCreditStatus.IncompleteApplication:
      return {
        title: t('Account.in-house-credit-status.incomplete-application'),
        color: Colors.Neutral,
      }

    case InHouseCreditStatus.PendingReview:
      return {
        title: t('Account.in-house-credit-status.pending'),
        hint: t('Account.in-house-credit-status.pending-hint'),
        color: Colors.Neutral,
      }

    case InHouseCreditStatus.AccountOnHold:
      return {
        title: t('Account.in-house-credit-status.on-hold'),
        hint: t('Account.in-house-credit-status.on-hold-hint'),
        color: Colors.Warning,
      }

    case InHouseCreditStatus.Inactive:
      return {
        title: t('Account.in-house-credit-status.inactive'),
        hint: t('Account.in-house-credit-status.inactive-hint'),
        color: Colors.Neutral,
      }

    case InHouseCreditStatus.InCollection:
      return {
        title: t('Account.in-house-credit-status.in-collection'),
        hint: t('Account.in-house-credit-status.in-collection-hint'),
        color: Colors.Warning,
      }
    case InHouseCreditStatus.PastDue:
      return {
        title: t('Account.in-house-credit-status.past-due'),
        hint: t('Account.in-house-credit-status.past-due-hint'),
        color: Colors.Danger,
      }
    case InHouseCreditStatus.Closed:
      return {
        title: t('Account.in-house-credit-status.closed'),
        color: Colors.Neutral,
      }
    case InHouseCreditStatus.Cancelled:
      return {
        title: t('Account.in-house-credit-status.cancelled'),
        color: Colors.Neutral,
      }
    case InHouseCreditStatus.Rejected:
      return {
        title: t('Account.in-house-credit-status.rejected'),
        color: Colors.Danger,
      }

    default:
      return { title: '', color: Colors.Neutral }
  }
}
