import {
  Invitation,
  User,
  UserRole,
  Company,
  signupService,
} from '@linqpal/common-backend'
import { IUserRole } from '@linqpal/common-backend/src/models/types'
import { exceptions } from '@linqpal/models'
import admin from 'firebase-admin'
import { ControllerItem } from 'src/routes/controllerItem'

async function getInvitation(id: string) {
  const inv = await Invitation.findById(id)
  if (!inv) {
    throw new exceptions.LogicalError('Invitation has expired or already used')
  }
  return inv
}

async function getFirebaseUser(email: string) {
  try {
    return await admin.auth().getUserByEmail(email)
  } catch (e: any) {
    if (e.code !== 'auth/user-not-found') {
      throw new exceptions.LogicalError(e.message)
    }
  }
  return null
}

async function createFirebaseUser(
  email: string,
  password: string,
  displayName: string,
) {
  return admin.auth().createUser({
    email,
    emailVerified: true,
    password,
    displayName,
    disabled: false,
  })
}

export default {
  middlewares: { pre: [] },
  async get(req, res) {
    const { id } = req.query
    if (!id) {
      throw new exceptions.LogicalError('Invitation code is not defined')
    }

    const invitation = await getInvitation(id as string)

    const fbUser = await getFirebaseUser(invitation.login)

    let userRole: IUserRole | undefined | null
    if (fbUser) {
      const user = await User.findOne({ firebaseId: fbUser.uid })
      if (user?.sub) {
        userRole = await UserRole.findOne({ sub: user.sub })
      }
    }

    res.send({ result: 'ok', invitation, userRole })
  },
  async post(req, res) {
    const { password, invitationId, action } = req.body

    const {
      login,
      fullName,
      firstName,
      lastName,
      role,
      company_id,
      businessName,
    } = await getInvitation(invitationId)

    // if rejected the invitation
    if (action === 'reject') {
      await Invitation.findByIdAndDelete(invitationId)
      res.send({ result: 'ok' })
      return
    }

    const company = await Company.findById(company_id)
    const invitedByContractor = company!.type === 'contractor'
    let companyId = company_id
    if (invitedByContractor) {
      const newCompany = await Company.create({
        name: businessName,
        type: 'supplier',
      })
      companyId = newCompany.id
    }

    let fbUser = await getFirebaseUser(login)
    const settings = { conversion: { isEmailVerified: true } }

    let token: string | undefined, sub: string, firebaseId: string
    if (!fbUser) {
      fbUser = await createFirebaseUser(login, password, fullName)
      firebaseId = fbUser.uid

      sub = await signupService.generateUniqueSub()

      await User.create({
        sub,
        firebaseId,
        type: 'supplier',
        login: login?.toLowerCase(),
        name: fullName,
        firstName,
        lastName,
        settings,
      })
      await UserRole.create({
        sub,
        company_id: companyId,
        role,
        status: 'Active',
      })
      token = await admin.auth().createCustomToken(firebaseId)
    } else {
      firebaseId = fbUser.uid

      const user = await User.findOne({ firebaseId })
      if (!user) {
        sub = await signupService.generateUniqueSub()
        await User.create({
          sub,
          firebaseId,
          type: 'supplier',
          login: login?.toLowerCase(),
          name: fullName,
          firstName,
          lastName,
          settings,
        })
      } else {
        sub = user.sub
      }
      const userRole = await UserRole.findOne({ sub })
      if (userRole) {
        await UserRole.findOneAndUpdate(
          { sub },
          { company_id: companyId, role },
        )
      } else {
        await UserRole.create({
          sub,
          company_id: companyId,
          role,
          status: 'Active',
        })
      }
    }

    await Invitation.findByIdAndDelete(invitationId)
    res.send({ result: 'ok', token })
  },
} as ControllerItem
