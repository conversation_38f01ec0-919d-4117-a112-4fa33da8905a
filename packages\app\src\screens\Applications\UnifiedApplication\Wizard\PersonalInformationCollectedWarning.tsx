import { StyleSheet, View } from 'react-native'
import { BtText } from '@linqpal/components/src/ui'
import { useTranslation } from 'react-i18next'
import React from 'react'
import { observer } from 'mobx-react'
import { useUnifiedApplication } from '../UnifiedApplicationContext'
import { Steps } from '@linqpal/models/src/applications/unified/UnifiedApplicationSteps'

// TODO: VK: Unified: review shared styles

export const PersonalInformationCollectedWarning = observer(() => {
  const { t } = useTranslation('application')
  const store = useUnifiedApplication()

  if (
    ![
      Steps.businessInfo.email,
      Steps.businessInfo.category,
      Steps.businessInfo.trade,
    ].includes(store.currentStep)
  ) {
    return null
  }

  return (
    <View style={styles.container}>
      <BtText style={styles.agreement}>{t('ToComplyWithFederalLaws')}</BtText>
    </View>
  )
})

const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: '#F5F7F8',
    padding: 20,
    alignItems: 'center',
    alignSelf: 'baseline',
  },
  agreement: {
    maxWidth: 615,
    textAlign: 'justify',
    color: '#668598',
    lineHeight: 21,
  },
})
