import {
  Company,
  CustomerAccount,
  getPaymentPlans,
  Invoice,
  LoanPaymentPlan,
} from '@linqpal/common-backend'
import {
  IInvoiceLoanPlan,
  ILoanPaymentPlan,
} from '@linqpal/common-backend/src/models/types'
import {
  IGetAllRequest,
  IGetResponse,
  TCompoundRoute,
} from '@linqpal/models/src/routes2/types'
import mongoose from 'mongoose'
import { LOAN_PLAN_TYPE } from '@linqpal/models/src/dictionaries'
import { authRequired } from '../../../services/auth.service'
import { PricingProduct } from '@linqpal/models/src/dictionaries/pricingProduct'

export const allPlans: TCompoundRoute<any, any> = async () => {
  const plans = await getPaymentPlans()
  return { data: plans }
}

export const getPlansForIntegration: TCompoundRoute<
  IGetAllRequest<{
    customerAccountId?: string
    companyId: string
    totalAmount: number
    supplierInvitationDetails?: any
  }>,
  IGetResponse<any>
> = {
  middlewares: { pre: [authRequired(false)] },
  get: async (req: {
    companyId: string
    totalAmount: number
    customerAccountId?: string
    supplierInvitationDetails?: any
  }) => {
    let plans: ILoanPaymentPlan[]

    const {
      customerAccountId,
      companyId,
      totalAmount,
      supplierInvitationDetails,
    } = req

    const [customer, company] = await Promise.all([
      CustomerAccount.findById(customerAccountId),
      Company.findById(companyId),
    ])

    const loanPlansBySupplier: Array<string> | undefined =
      company?.settings?.loanPlans
    const invoiceBasedLoanPlans = company?.settings?.invoiceLoanPlans
    let matchedPlan: undefined | IInvoiceLoanPlan
    if (invoiceBasedLoanPlans?.length) {
      matchedPlan = invoiceBasedLoanPlans.find(
        (p) =>
          p.minAmount < totalAmount && (p.maxAmount ?? Infinity) >= totalAmount,
      )
    }
    const loanPlansForCustomer: Array<string> | undefined =
      customer?.settings?.loanPlans
    const allowedToPayWithCredit: boolean | undefined =
      customer?.settings?.acceptCreditPayment ?? true
    const loanPlans = loanPlansForCustomer?.length
      ? loanPlansForCustomer
      : matchedPlan
      ? matchedPlan.plans
      : loanPlansBySupplier
    if (!allowedToPayWithCredit) {
      plans = []
    } else if (loanPlans?.length) {
      const planIds = loanPlans.map((p) => new mongoose.Types.ObjectId(p))
      const pipeline = [
        {
          $match: {
            type: { $ne: LOAN_PLAN_TYPE.VIRTUAL_CARD },
            _id: { $in: planIds },
            product: PricingProduct.LineOfCredit,
          },
        },
      ]
      plans = await LoanPaymentPlan.aggregate(pipeline).sort({ days: 1 })
    } else if (supplierInvitationDetails) {
      plans = await LoanPaymentPlan.find({
        type: LOAN_PLAN_TYPE.NO_SUPPLIER,
        product: PricingProduct.LineOfCredit,
      }).sort({
        days: 1,
      })
    } else {
      plans = await LoanPaymentPlan.find({
        type: LOAN_PLAN_TYPE.REGULAR,
        product: PricingProduct.LineOfCredit,
      }).sort({ days: 1 })
    }

    return { data: plans }
  },
}

export const getPlans: TCompoundRoute<
  IGetAllRequest<{ id: string; totalAmount: number }>,
  IGetResponse<any>
> = {
  middlewares: { pre: [authRequired(false)] },
  get: async (req: { id: string; totalAmount: number }) => {
    let plans: ILoanPaymentPlan[]

    const inv = await Invoice.findById(req.id)

    if (inv?.company_id) await inv.populate('company')
    if (inv?.customer_account_id) await inv.populate('customer')

    const payerCompany = inv?.payer_id
      ? await Company.findById(inv.payer_id)
      : null

    const loanPlansBySupplier: Array<string> | undefined =
      inv?.company?.settings?.loanPlans
    const invoiceBasedLoanPlans = inv?.company?.settings?.invoiceLoanPlans

    let matchedPlan: undefined | IInvoiceLoanPlan
    if (invoiceBasedLoanPlans?.length) {
      matchedPlan = invoiceBasedLoanPlans.find(
        (p) =>
          p.minAmount < req.totalAmount &&
          (p.maxAmount ?? Infinity) >= req.totalAmount,
      )
    }

    const loanPlansForCustomer: Array<string> | undefined =
      inv?.supplierInvitationDetails &&
      payerCompany?.settings?.directTerms?.loanPlans
        ? payerCompany?.settings?.directTerms?.loanPlans
        : inv?.customer?.settings?.loanPlans

    const allowedToPayWithCredit: boolean | undefined =
      inv?.customer?.settings?.acceptCreditPayment ?? true

    const loanPlans = loanPlansForCustomer?.length
      ? loanPlansForCustomer
      : matchedPlan
      ? matchedPlan.plans
      : loanPlansBySupplier
    if (!allowedToPayWithCredit) {
      plans = []
    } else if (loanPlans?.length) {
      const planIds = loanPlans.map((p) => new mongoose.Types.ObjectId(p))
      plans = await LoanPaymentPlan.find({
        type: mongoose.trusted({ $ne: LOAN_PLAN_TYPE.VIRTUAL_CARD }),
        _id: mongoose.trusted({ $in: planIds }),
      }).sort({ days: 1 })
    } else if (inv?.supplierInvitationDetails) {
      plans = await LoanPaymentPlan.find({
        type: LOAN_PLAN_TYPE.NO_SUPPLIER,
      }).sort({
        days: 1,
      })
    } else {
      plans = await LoanPaymentPlan.find({ type: LOAN_PLAN_TYPE.REGULAR }).sort(
        { days: 1 },
      )
    }

    return { data: plans }
  },
}
