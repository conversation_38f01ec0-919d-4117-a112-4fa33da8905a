import React, { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import {
  LOAN_REPAYMENT_NAME,
  LOAN_REPAYMENT_SCHEDULE_STATUS,
  LOAN_REPAYMENT_STATUS,
  LOAN_REPAYMENT_TYPE,
} from '@linqpal/models/src/dictionaries/loanStatuses'
import { Text } from 'react-native-paper'
import { StyleSheet, View } from 'react-native'
import { composeStyle } from '@linqpal/common-frontend/src/helpers'
import { currencyMask, dateMask } from '../../utils/helpers/masking'
import { roundVal } from '../../screens/Contractor/CreditTab/LoanSchedule/roundVal'
import { getDisplayAmount } from '../../utils/helpers/getDisplayAmount'
import { VerticalPaymentProgress } from './VerticalPaymentProgress'
import { TooltipIcon } from '../atoms/TooltipIcon'
import { PopoverPlacements } from '@ui-kitten/components'
import { TFunction } from 'i18next'

export enum PaymentTimelineContext {
  TRADE_CREDIT = 'tradeCredit',
  IHC_INVOICE = 'ihcInvoice',
}

export interface ITimelineItem {
  type: string
  scheduleStatus: string
  status: string
  expectedDate: string
  expectedAmount: number
  processingAmount: number
  processingDate: string
  paidAmount: number
  paidDate: string
}

interface TimelineItemProps {
  installment: ITimelineItem
  isLastPayment?: boolean
  context: PaymentTimelineContext
}

const { CURRENT } = LOAN_REPAYMENT_SCHEDULE_STATUS
const {
  LATE_FEE,
  EXTENSION_FEE,
  MANUAL_LATE_FEE,
  PENALTY_INTEREST_FEE,
  LOAN_FEE,
} = LOAN_REPAYMENT_TYPE

const redHighlight = '#DB2424'
const defaultColor = '#19262F'
const disabled = 'rgba(25, 38, 47, 0.3)'

const getLabelTranslations = (
  context: PaymentTimelineContext,
  t: TFunction,
) => {
  if (context === PaymentTimelineContext.TRADE_CREDIT) {
    return {
      [LOAN_REPAYMENT_STATUS.PAID]: t(
        'tradeCredit.drawDetails.details.paymentSchedule.paid-caption',
      ),
      [LOAN_REPAYMENT_STATUS.PROCESSING]: t(
        'tradeCredit.drawDetails.details.paymentSchedule.processing-caption',
      ),
      [LOAN_REPAYMENT_STATUS.PENDING]: t(
        'tradeCredit.drawDetails.details.paymentSchedule.due-caption',
      ),
      [LOAN_REPAYMENT_STATUS.RESCHEDULED]: t(
        'tradeCredit.drawDetails.details.paymentSchedule.rescheduled-caption',
      ),
      [LOAN_REPAYMENT_STATUS.DUENEXT]: t(
        'tradeCredit.drawDetails.details.paymentSchedule.due-caption',
      ),
      [LOAN_REPAYMENT_STATUS.PASTDUE]: t(
        'tradeCredit.drawDetails.details.paymentSchedule.past-due-caption',
      ),
      [LOAN_REPAYMENT_STATUS.LATE]: t(
        'tradeCredit.drawDetails.details.paymentSchedule.past-due-caption',
      ),
      [LOAN_REPAYMENT_STATUS.FAILED]: t(
        'tradeCredit.drawDetails.details.paymentSchedule.past-due-caption',
      ),
    }
  } else {
    return {
      [LOAN_REPAYMENT_STATUS.PAID]: t(
        'PaymentHistoryInvoice.paymentSchedule.paid',
      ),
      [LOAN_REPAYMENT_STATUS.PROCESSING]: t(
        'PaymentHistoryInvoice.paymentSchedule.processing',
      ),
      [LOAN_REPAYMENT_STATUS.PENDING]: t(
        'PaymentHistoryInvoice.paymentSchedule.due',
      ),
      [LOAN_REPAYMENT_STATUS.RESCHEDULED]: t(
        'PaymentHistoryInvoice.paymentSchedule.rescheduled',
      ),
      [LOAN_REPAYMENT_STATUS.DUENEXT]: t(
        'PaymentHistoryInvoice.paymentSchedule.due',
      ),
      [LOAN_REPAYMENT_STATUS.PASTDUE]: t(
        'PaymentHistoryInvoice.paymentSchedule.due',
      ),
      [LOAN_REPAYMENT_STATUS.LATE]: t(
        'PaymentHistoryInvoice.paymentSchedule.due',
      ),
      [LOAN_REPAYMENT_STATUS.FAILED]: t(
        'PaymentHistoryInvoice.paymentSchedule.due',
      ),
    }
  }
}

const Label: React.FC<{
  status: string
  context: PaymentTimelineContext
}> = ({ status, context }) => {
  const { t } = useTranslation(
    context === PaymentTimelineContext.TRADE_CREDIT ? 'tradeCredit' : 'global',
  )

  const PaymentStatus = getLabelTranslations(context, t)

  const labelColor = [
    LOAN_REPAYMENT_STATUS.PASTDUE,
    LOAN_REPAYMENT_STATUS.FAILED,
    LOAN_REPAYMENT_STATUS.LATE,
  ].includes(status)
    ? redHighlight
    : defaultColor

  const label = PaymentStatus[status]

  return (
    <Text
      style={{
        fontWeight:
          label === PaymentStatus[LOAN_REPAYMENT_STATUS.PAID] ? '500' : '400',
        fontSize: 16,
        color: labelColor,
        lineHeight: 24,
        fontFamily: 'Inter',
      }}
    >
      {label}
    </Text>
  )
}

const ItemDate: React.FC<{ date: string; scheduleStatus }> = ({
  date,
  scheduleStatus,
}) => {
  const color = scheduleStatus !== CURRENT ? disabled : defaultColor
  return (
    <Text style={composeStyle(styles.date, { color })}>{dateMask(date)}</Text>
  )
}

const Amount: React.FC<{
  status: string
  expectedAmount: number
  context: PaymentTimelineContext
}> = ({ status, expectedAmount, context }) => {
  const amtColor = useMemo(() => {
    let color = defaultColor
    if (
      [LOAN_REPAYMENT_STATUS.LATE, LOAN_REPAYMENT_STATUS.PASTDUE].includes(
        status,
      )
    )
      color = redHighlight
    else if (status === LOAN_REPAYMENT_STATUS.RESCHEDULED) color = disabled
    return color
  }, [status])

  const formattedAmount =
    context === PaymentTimelineContext.TRADE_CREDIT
      ? `$${getDisplayAmount(roundVal(expectedAmount))}`
      : currencyMask(expectedAmount)

  return (
    <Text style={composeStyle(styles.amount, { color: amtColor })}>
      {formattedAmount}
    </Text>
  )
}

const Info: React.FC<{
  status: string
}> = ({ status }) => {
  const { t } = useTranslation('global')
  return status === LOAN_REPAYMENT_STATUS.PROCESSING ? (
    <View style={{ marginLeft: 5, marginTop: 3 }}>
      <TooltipIcon
        tooltip={t('ViewLoan.processing-info')}
        tooltipPosition={PopoverPlacements.RIGHT}
      />
    </View>
  ) : null
}

const PaymentType: React.FC<{
  type: string
  status: string
  context: PaymentTimelineContext
  isFee: boolean
}> = ({ status, type, context, isFee }) => {
  const { t } = useTranslation(
    context === PaymentTimelineContext.TRADE_CREDIT ? 'tradeCredit' : 'global',
  )

  const color = useMemo(() => {
    if (status === LOAN_REPAYMENT_STATUS.RESCHEDULED) return disabled

    const isPastDueOrFailed = [
      LOAN_REPAYMENT_STATUS.LATE,
      LOAN_REPAYMENT_STATUS.PASTDUE,
      LOAN_REPAYMENT_STATUS.FAILED,
    ].includes(status)

    return isPastDueOrFailed || (isFee && type !== LOAN_FEE)
      ? redHighlight
      : defaultColor
  }, [status, isFee, type])

  const paymentTypeLabel = useMemo(() => {
    if (!isFee) {
      return context === PaymentTimelineContext.TRADE_CREDIT
        ? t('tradeCredit.drawDetails.details.paymentSchedule.principalPayment')
        : t('PaymentHistoryInvoice.paymentSchedule.principalPayment')
    }

    if (context === PaymentTimelineContext.IHC_INVOICE && type === LOAN_FEE) {
      return t('PaymentHistoryInvoice.paymentSchedule.customerFee')
    }

    return LOAN_REPAYMENT_NAME[type]
  }, [isFee, context, type, t])

  return (
    <Text style={composeStyle(styles.paymentType, { color })}>
      {paymentTypeLabel}
    </Text>
  )
}

export const PaymentTimelineItem: React.FC<TimelineItemProps> = ({
  installment,
  isLastPayment,
  context,
}) => {
  const { t } = useTranslation('global')
  const {
    type,
    scheduleStatus,
    status,
    expectedDate,
    expectedAmount,
    processingAmount,
    processingDate,
    paidAmount,
    paidDate,
  } = installment
  const isCompleted = status === LOAN_REPAYMENT_STATUS.PAID
  const isFee = [
    LATE_FEE,
    EXTENSION_FEE,
    MANUAL_LATE_FEE,
    PENALTY_INTEREST_FEE,
    LOAN_FEE,
  ].includes(type)

  const paymentStatus = useMemo(() => {
    const day = 24 * 3600 * 1000
    if (scheduleStatus !== CURRENT) return LOAN_REPAYMENT_STATUS.RESCHEDULED
    if (
      ![
        LOAN_REPAYMENT_STATUS.PASTDUE,
        LOAN_REPAYMENT_STATUS.FAILED,
        LOAN_REPAYMENT_STATUS.LATE,
      ].includes(status) &&
      scheduleStatus === CURRENT &&
      !paidAmount &&
      !processingAmount &&
      new Date(expectedDate).getTime() + 5 * day >= new Date().getTime()
    )
      return LOAN_REPAYMENT_STATUS.DUENEXT
    if (
      [
        LOAN_REPAYMENT_STATUS.PROCESSING,
        LOAN_REPAYMENT_STATUS.PENDING,
        LOAN_REPAYMENT_STATUS.LATE,
      ].includes(status) &&
      (!roundVal(expectedAmount - processingAmount) ||
        !roundVal(expectedAmount - (processingAmount + paidAmount)))
    )
      return LOAN_REPAYMENT_STATUS.PROCESSING

    return status
  }, [
    scheduleStatus,
    status,
    processingAmount,
    paidAmount,
    expectedAmount,
    expectedDate,
  ])

  const showProcessingCaption = useMemo(
    () =>
      [
        LOAN_REPAYMENT_STATUS.LATE,
        LOAN_REPAYMENT_STATUS.PASTDUE,
        LOAN_REPAYMENT_STATUS.PROCESSING,
        LOAN_REPAYMENT_STATUS.PENDING,
      ].includes(paymentStatus) &&
      expectedAmount - processingAmount &&
      processingAmount,

    [processingAmount, expectedAmount, paymentStatus],
  )

  const showPaidOffCaption = useMemo(
    () =>
      [
        LOAN_REPAYMENT_STATUS.LATE,
        LOAN_REPAYMENT_STATUS.PASTDUE,
        LOAN_REPAYMENT_STATUS.PROCESSING,
        LOAN_REPAYMENT_STATUS.PENDING,
      ].includes(paymentStatus) &&
      expectedAmount - paidAmount &&
      paidAmount,

    [paidAmount, expectedAmount, paymentStatus],
  )
  return (
    <View style={{ flexDirection: 'row' }}>
      <VerticalPaymentProgress
        isCompleted={isCompleted}
        shouldHideLine={isLastPayment}
        status={paymentStatus}
      />

      <View
        style={{
          flex: 1,
          paddingLeft: 15,
          paddingBottom: 45,
          flexDirection: 'row',
          marginTop: -5,
          justifyContent: 'space-between',
        }}
      >
        <View style={{ flex: 1, marginRight: 10 }}>
          <ItemDate date={expectedDate} scheduleStatus={scheduleStatus} />{' '}
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Label status={paymentStatus} context={context} />
            <Info status={paymentStatus} />
          </View>
          {!!showProcessingCaption && (
            <Text style={composeStyle(styles.processing, { marginTop: 5 })}>
              {t('LoanSchedule.processing-caption', {
                amount: currencyMask(processingAmount ?? 0),
                partialAmountCoveredOn: dateMask(processingDate ?? new Date()),
              })}
            </Text>
          )}
          {!!showPaidOffCaption && (
            <Text style={styles.processing}>
              {t('LoanSchedule.paid-off-caption', {
                amount: currencyMask(paidAmount),
                partialAmountCoveredOn: dateMask(paidDate),
              })}
            </Text>
          )}
        </View>
        <View style={{ marginTop: -3, minWidth: 100 }}>
          <Amount
            status={paymentStatus}
            expectedAmount={expectedAmount}
            context={context}
          />
          <PaymentType
            type={type}
            status={paymentStatus}
            context={context}
            isFee={isFee}
          />
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  paymentType: {
    fontWeight: '400',
    fontSize: 16,
    lineHeight: 24,
    fontFamily: 'Inter',
    color: redHighlight,
    textAlign: 'right',
    marginTop: -1,
  },
  date: {
    fontWeight: '700',
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 5,
    fontFamily: 'Inter',
  },
  amount: {
    fontWeight: '700',
    fontSize: 16,
    textAlign: 'right',
    lineHeight: 24,
    marginBottom: 8,
    fontFamily: 'Inter',
  },
  processing: {
    fontWeight: '500',
    fontFamily: 'Inter',
    fontSize: 12,
    lineHeight: 16,
    color: 'rgba(25, 38, 47, 0.7)',
  },
})
