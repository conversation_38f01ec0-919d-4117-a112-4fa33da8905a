import React from 'react'
import { observer } from 'mobx-react'

import { useTranslation } from 'react-i18next'
import { View, Text, StyleSheet } from 'react-native'
import {
  LOAN_REPAYMENT_STATUS,
  LOAN_REPAYMENT_TYPE,
} from '@linqpal/models/src/dictionaries/loanStatuses'
import { composeStyle } from '@linqpal/common-frontend/src/helpers'
import {
  PaymentTimelineContext,
  PaymentTimelineItem,
} from '../../../../../../ui/organisms'

export const PaymentSchedule = observer(({ loan, isMobile }) => {
  const { t } = useTranslation('tradeCredit')
  const { loanReceivables } = loan
  const plan = loanReceivables?.length
    ? [...loanReceivables]
        .filter(
          (receivable) =>
            receivable.status !== LOAN_REPAYMENT_STATUS.CANCELED &&
            receivable.type !== LOAN_REPAYMENT_TYPE.PENALTY_INTEREST_FEE,
        )
        .sort(
          (a, b) =>
            new Date(a.expectedDate).getTime() -
            new Date(b.expectedDate).getTime(),
        )
    : []

  return (
    <>
      <View>
        <Text
          style={
            isMobile
              ? composeStyle(styles.title, styles.mobileTitle)
              : styles.title
          }
        >
          {t('tradeCredit.drawDetails.details.paymentSchedule.title')}
        </Text>
        <View style={isMobile && styles.paymentsLayout}>
          {plan.map((p, idx) => (
            <PaymentTimelineItem
              key={idx}
              installment={p}
              isLastPayment={plan.length === idx}
              context={PaymentTimelineContext.TRADE_CREDIT}
            />
          ))}
        </View>
      </View>
    </>
  )
})

const styles = StyleSheet.create({
  title: {
    fontFamily: 'Inter',
    fontWeight: '700',
    fontSize: 20,
    lineHeight: 40,
    color: '#19262F',
    marginBottom: 34,
  },
  mobileTitle: { fontSize: 18, marginBottom: 18 },
  paymentsLayout: {
    marginBottom: 30,
  },
})
