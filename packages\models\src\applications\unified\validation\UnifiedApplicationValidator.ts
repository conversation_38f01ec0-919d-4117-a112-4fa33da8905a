import {
  ApplicationUserRole,
  IApplicationBusinessType,
  IUnifiedApplicationDraft,
} from '../IUnifiedApplicationDraft'
import {
  validateDate,
  validateEmail,
  validateMonthAndYear,
  validateUSPhoneNumber,
} from '../../../helpers/validations'
import { CoOwnerValidator } from './CoOwnerValidator'
import { IUIBankAccount, UIBankAccountModel } from '../../../mst'
import { ApplicationType } from '../../../dictionaries/applicationType'
import { toJS } from 'mobx'
import { isNumber } from 'lodash'
import { IUnifiedApplicationOptions } from '../IUnifiedApplicationOptions'
import { Steps as $ } from '../UnifiedApplicationSteps'

// this validator is designed to be independent of the underlying data structure.
// Main principle - it's validating not fields, but logical application steps,
// so if single composite field in application is filled in three steps we can run three step validations on same draft field
// and otherwise, if single step is responsible for multiple fields, we can validate it in single step validation
//
// This validator is intended to be used on UI and in backend.
// so we can be sure that exactly the same validation is used
// when moving to the next step, when submitting on UI and when submitting on backend
// UI may have its own extra validation logic e.g. to display extra messages
// but final decision should be done by this validation
// For convenience, static methods can be used on UI to validate field value before it's assigned to the draft

export class UnifiedApplicationValidator {
  private draft: IUnifiedApplicationDraft

  constructor(draft: IUnifiedApplicationDraft) {
    this.draft = draft
  }

  validate(path: string, options: IUnifiedApplicationOptions): boolean {
    switch (path) {
      // businessInfo group
      case $.businessInfo.email:
        return validateEmail(this.draft.data?.businessInfo?.email)
      case $.businessInfo.category:
        // done
        return !!this.draft.data?.businessInfo?.category
      case $.businessInfo.businessName:
        // done
        return !!this.draft.data?.businessInfo?.businessName?.legalName
      case $.businessInfo.trade:
        return UnifiedApplicationValidator.validateTrade(
          this.draft.data?.businessInfo.trade,
        )
      case $.businessInfo.businessPhone:
        return UnifiedApplicationValidator.validatePhone(
          this.draft.data?.businessInfo.businessPhone,
        )
      case $.businessInfo.businessAddress:
        return UnifiedApplicationValidator.validateAddress(
          this.draft.data?.businessInfo.businessAddress,
        )
      case $.businessInfo.startDate:
        return UnifiedApplicationValidator.validateBusinessStartDate(
          this.draft.data?.businessInfo.startDate || '',
        )
      case $.businessInfo.type:
        return UnifiedApplicationValidator.validateBusinessType(
          this.draft.data?.businessInfo.type,
        )
      case $.businessInfo.ein:
        return UnifiedApplicationValidator.validateEin(
          this.draft.data?.businessInfo.ein,
        )

      // finance group
      case $.finance.revenue:
        // done
        return !!this.draft.data?.finance.revenue
      case $.finance.debt:
        // done
        return (
          isNumber(this.draft.data?.finance.debt) &&
          this.draft.data.finance.debt >= 0
        )
      case $.finance.creditLimit:
        // done
        return !!this.draft.data?.finance.creditLimit
      case $.finance.arAdvanceRequestedLimit:
        // done
        // optional for  Supplier app
        return (
          options.type === ApplicationType.Supplier ||
          !!this.draft.data?.finance.arAdvanceLimit
        )

      // businessOwner group
      case $.businessOwner.isOwner:
        return !!this.getCurrentUser(options.userId)?.role
      case $.businessOwner.isAuthorized:
        return !!this.getCurrentUser(options.userId)?.role
      case $.businessOwner.ownershipPercentage:
        return this.validateOwnershipPercentage(options.userId)
      case $.businessOwner.authorizedDetails:
        return this.validateInvitedOwner()
      case $.businessOwner.address:
        // TODO: VK: Unified: make optional
        // optional for non-owner non-authorized
        return UnifiedApplicationValidator.validateAddress(
          this.getCurrentUser(options.userId).address,
        )
      case $.businessOwner.birthdate:
        // TODO: VK: Unified make optional
        // optional for non-owner non-authorized
        return UnifiedApplicationValidator.validateBirthdate(
          this.getCurrentUser(options.userId).birthdate || '',
        )
      case $.businessOwner.ssn:
        // TODO: VK: Unified make optional
        // optional for non-owner non-authorized
        return UnifiedApplicationValidator.validateSsn(
          this.getCurrentUser(options.userId).ssn || '',
        )

      // coOwners group
      case $.coOwners.coOwners:
        return this.validateCoOwners()

      // primaryBankAccount group
      case $.bank.primaryAccount:
        // optional for IHC, see appStore.isOptionalStep for nuances
        return UnifiedApplicationValidator.validateBankAccount(
          this.draft.data?.primaryBankAccount,
          options,
        )

      // review group
      case $.review.review:
        return true

      default:
        throw new Error(`Unknown validation path: ${path}`)
    }
  }

  private getCurrentUser(userId: string) {
    return this.draft.users.find((user) => user.id === userId)!
  }

  static validateBirthdate(value: string): boolean {
    return validateDate(value, 1900)
  }

  static validateSsn(value: string): boolean {
    return /^\d{9}$/.test(value)
  }

  private validateOwnershipPercentage(userId: string): boolean {
    const currentUser = this.getCurrentUser(userId)
    const value = currentUser.ownershipPercentage

    if (value === undefined) return false

    const coOwnersPercentage = this.getCoOwnersPercentage()
    const maxOwnerPercentage = 100 - coOwnersPercentage

    return value > 0 && value <= maxOwnerPercentage
  }

  static validateTrade(value: string[] | undefined): boolean {
    return !!value && value.length > 0
  }

  static validatePhone(value: string | undefined): boolean {
    return validateUSPhoneNumber(value)
  }

  static validateAddress(value: any): boolean {
    return !!value?.address && !!value?.city && !!value?.state && !!value?.zip
  }

  static validateBusinessStartDate(value: string): boolean {
    return !!value && validateMonthAndYear(value, 1900)
  }

  static validateBusinessType(
    value: IApplicationBusinessType | undefined,
  ): boolean {
    if (!value) return false

    if (value.selectedType === 'Other' && !value.other?.trim()?.length) {
      return false
    }

    return !!value.selectedType?.trim()?.length
  }

  static validateEin(value: string | undefined): boolean {
    return typeof value === 'string' && /^\d{9}$/.test(value)
  }

  static validateBankAccount(
    value: IUIBankAccount | undefined,
    options: IUnifiedApplicationOptions,
  ): boolean {
    // using legacy validation as-is for now, to be reviewed later
    try {
      if (options.bankAccount) {
        const { connectionType, plaid } = options.bankAccount

        const bank = UIBankAccountModel.create({
          ...toJS(value),
          connectionType: toJS(connectionType),
          plaid: toJS(plaid),
        })

        return (
          bank?.isFilled ||
          (options.type !== ApplicationType.Credit && bank.allFilled)
        )
      } else {
        return !!value && UIBankAccountModel.create(value)?.isFilled
      }
    } catch (error) {
      console.error(error)
      return false
    }
  }

  private validateCoOwners(): boolean {
    const coOwners = this.draft.data?.coOwners

    if (!coOwners || coOwners.length === 0) {
      return true // co-owners are optional
    }

    const principalPercentage = this.getPrincipalOwnershipPercentage()
    const coOwnersPercentage = this.getCoOwnersPercentage()

    const totalPercentage = principalPercentage + coOwnersPercentage

    if (totalPercentage > 100) return false

    // Check if all co-owners are individually valid
    const allCoOwnersValid = coOwners.every((coOwner) => {
      const validator = new CoOwnerValidator(coOwner)
      return validator.validateCoOwner()
    })

    if (!allCoOwnersValid) return false

    // Check if all emails are unique
    const emails = coOwners.map((c) => c.email).filter(Boolean)
    const allEmailsUnique = new Set(emails).size === emails.length

    return allEmailsUnique
  }

  private getPrincipalOwnershipPercentage(): number {
    // Find the user with Owner role and return their ownership percentage
    const ownerUser = this.draft.users.find(
      (user) => user.role === ApplicationUserRole.Owner,
    )
    return ownerUser ? ownerUser.ownershipPercentage || 0 : 0
  }

  private getCoOwnersPercentage(): number {
    const coOwners = this.draft.data?.coOwners || []
    if (!coOwners || coOwners.length === 0) return 0

    return coOwners.reduce(
      (sum, coOwner) => sum + (coOwner.percentOwned || 0),
      0,
    )
  }

  validateInvitedOwner(): boolean {
    const authorizedUser = this.draft.users.find(
      (user) => !user.id && user.role === ApplicationUserRole.Owner,
    )

    if (!authorizedUser) return false

    const { firstName, lastName, email, phone } = authorizedUser

    // Check all required fields are present and non-empty
    if (
      !firstName?.trim() ||
      !lastName?.trim() ||
      !email?.trim() ||
      !phone?.trim()
    ) {
      return false
    }

    // Validate email format
    if (!validateEmail(email)) {
      return false
    }

    // Validate phone format
    return validateUSPhoneNumber(phone)
  }
}
