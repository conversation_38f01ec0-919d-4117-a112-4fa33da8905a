import mongoose from 'mongoose'
import {
  BankAccount,
  CardPricingPackage,
  CardProducts,
  Company,
  LoanPricingPackage,
} from '../models'
import { ILoanPricingPackage } from '../models/types'
import { PricingProduct } from '@linqpal/models/src/dictionaries/pricingProduct'
import { exceptions } from '@linqpal/models'

export const LOAN_PRICING = {
  packageA: {
    merchant: 2.5,
    maxAmountReceived: 97.5,
    advanceRate: 80,
    finalPayment: 20,
    merchantRebate: 0.5,
    merchantFeeAfterRebate: 2,
    maxAmountReceivedAfterRebate: 98,
    CustomerFees30: 0,
    CustomerFees6090: 'Based on customer credit',
  },
  packageB: {
    merchant: 3,
    maxAmountReceived: 97,
    advanceRate: 90,
    finalPayment: 10,
    merchantRebate: 0.5,
    merchantFeeAfterRebate: 2.5,
    maxAmountReceivedAfterRebate: 97.5,
    CustomerFees30: 0,
    CustomerFees6090: 'Based on customer credit',
  },
  packageC: {
    merchant: 3.5,
    maxAmountReceived: 96.5,
    advanceRate: 100,
    finalPayment: 0,
    merchantRebate: 0.5,
    merchantFeeAfterRebate: 3,
    maxAmountReceivedAfterRebate: 97,
    CustomerFees30: 0,
    CustomerFees6090: 'Based on customer credit',
  },
}
const loanPackages = [
  {
    name: 'packageA',
    title: 'Package A',
    description: 'Willing To Wait',
    product: PricingProduct.LineOfCredit,
    metadata: LOAN_PRICING.packageA,
    order: 1,
  },
  {
    name: 'packageB',
    title: 'Package B',
    description: 'More Money Upfront',
    product: PricingProduct.LineOfCredit,
    metadata: LOAN_PRICING.packageB,
    order: 2,
  },
  {
    name: 'packageC',
    title: 'Package C',
    description: 'All Money Upfront',
    product: PricingProduct.LineOfCredit,
    metadata: LOAN_PRICING.packageC,
    order: 3,
  },
]

export const CARD_PRICING = {
  packageA: {
    ach: {
      merchant: { percentage: 0.5, min: 1, max: 10 },
      customer: { percentage: 0, amount: 0 },
    },
    debitCardRegulated: {
      merchant: { percentage: 1, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    debitCardUnregulated: {
      merchant: { percentage: 2.75, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    creditCardVisa: {
      merchant: { percentage: 2.85, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    creditCardMasterCard: {
      merchant: { percentage: 2.85, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    creditCardDiscover: {
      merchant: { percentage: 2.85, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    creditCardVisa2: {
      merchant: { percentage: 2.85, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    creditCardMasterCard2: {
      merchant: { percentage: 2.85, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    creditCardDiscover2: {
      merchant: { percentage: 2.85, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    creditCardTravel: {
      merchant: { percentage: 3.2, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    creditCardBusiness: {
      merchant: { percentage: 3.2, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    amex: {
      merchant: { percentage: 3.5, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
  },
  packageB: {
    ach: {
      merchant: { percentage: 0.5, min: 1, max: 10 },
      customer: { percentage: 0, amount: 0 },
    },
    debitCardRegulated: {
      merchant: { percentage: 1, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    debitCardUnregulated: {
      merchant: { percentage: 2.5, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    creditCardVisa: {
      merchant: { percentage: 2.6, amount: 0.3 },
      customer: { percentage: 1, amount: 0 },
      tabapayBin: '',
    },
    creditCardMasterCard: {
      merchant: { percentage: 2.6, amount: 0.3 },
      customer: { percentage: 1, amount: 0 },
      tabapayBin: '',
    },
    creditCardDiscover: {
      merchant: { percentage: 2.6, amount: 0.3 },
      customer: { percentage: 1, amount: 0 },
      tabapayBin: '',
    },
    creditCardVisa2: {
      merchant: { percentage: 2.6, amount: 0.3 },
      customer: { percentage: 1, amount: 0 },
      tabapayBin: '',
    },
    creditCardMasterCard2: {
      merchant: { percentage: 2.6, amount: 0.3 },
      customer: { percentage: 1, amount: 0 },
      tabapayBin: '',
    },
    creditCardDiscover2: {
      merchant: { percentage: 2.6, amount: 0.3 },
      customer: { percentage: 1, amount: 0 },
      tabapayBin: '',
    },
    creditCardTravel: {
      merchant: { percentage: 2.8, amount: 0.3 },
      customer: { percentage: 1, amount: 0 },
      tabapayBin: '',
    },
    creditCardBusiness: {
      merchant: { percentage: 2.8, amount: 0.3 },
      customer: { percentage: 1, amount: 0 },
      tabapayBin: '',
    },
    amex: {
      merchant: { percentage: 2.8, amount: 0.3 },
      customer: { percentage: 1, amount: 0 },
      tabapayBin: '',
    },
  },
  packageC: {
    ach: {
      merchant: { percentage: 0.5, min: 1, max: 10 },
      customer: { percentage: 0, amount: 0 },
    },
    debitCardRegulated: {
      merchant: { percentage: 1, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    debitCardUnregulated: {
      merchant: { percentage: 1, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    creditCardVisa: {
      merchant: { percentage: 1, amount: 0.3 },
      customer: { percentage: 2.5, amount: 0 },
      tabapayBin: '',
    },
    creditCardMasterCard: {
      merchant: { percentage: 1, amount: 0.3 },
      customer: { percentage: 2.5, amount: 0 },
      tabapayBin: '',
    },
    creditCardDiscover: {
      merchant: { percentage: 1, amount: 0.3 },
      customer: { percentage: 2.5, amount: 0 },
      tabapayBin: '',
    },
    creditCardVisa2: {
      merchant: { percentage: 1, amount: 0.3 },
      customer: { percentage: 2.5, amount: 0 },
      tabapayBin: '',
    },
    creditCardMasterCard2: {
      merchant: { percentage: 1, amount: 0.3 },
      customer: { percentage: 2.5, amount: 0 },
      tabapayBin: '',
    },
    creditCardDiscover2: {
      merchant: { percentage: 1, amount: 0.3 },
      customer: { percentage: 2.5, amount: 0 },
      tabapayBin: '',
    },
    creditCardTravel: {
      merchant: { percentage: 1, amount: 0.3 },
      customer: { percentage: 2.5, amount: 0 },
      tabapayBin: '',
    },
    creditCardBusiness: {
      merchant: { percentage: 1, amount: 0.3 },
      customer: { percentage: 2.5, amount: 0 },
      tabapayBin: '',
    },
    amex: {
      merchant: { percentage: 1, amount: 0.3 },
      customer: { percentage: 2.8, amount: 0 },
      tabapayBin: '',
    },
  },
  packageD: {
    ach: {
      merchant: { percentage: 0, min: 1, max: 10 },
      customer: { percentage: 0, amount: 0 },
    },
    debitCardRegulated: {
      merchant: {
        percentage: 0.6,
        amount: 0.3,
      },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    debitCardUnregulated: {
      merchant: { percentage: 0.6, amount: 0.3 },
      customer: { percentage: 1, amount: 0 },
      tabapayBin: '',
    },
    creditCardVisa: {
      merchant: {
        percentage: 0.6,
        amount: 0.3,
        disclaimer:
          'Higher rates may apply for cards not qualified for Level 2 and 3, e.g. corporate and T&E cards',
      },
      customer: { percentage: 2, amount: 0 },
      tabapayBin: '',
    },
    creditCardMasterCard: {
      merchant: { percentage: 0.6, amount: 0.3 },
      customer: { percentage: 2, amount: 0 },
      tabapayBin: '',
    },
    creditCardDiscover: {
      merchant: { percentage: 0.6, amount: 0.3 },
      customer: { percentage: 2, amount: 0 },
      tabapayBin: '',
    },
    creditCardVisa2: {
      merchant: { percentage: 0.6, amount: 0.3 },
      customer: { percentage: 2, amount: 0 },
      tabapayBin: '',
    },
    creditCardMasterCard2: {
      merchant: { percentage: 0.6, amount: 0.3 },
      customer: { percentage: 2, amount: 0 },
      tabapayBin: '',
    },
    creditCardDiscover2: {
      merchant: { percentage: 0.6, amount: 0.3 },
      customer: { percentage: 2, amount: 0 },
      tabapayBin: '',
    },
    creditCardTravel: {
      merchant: { percentage: 0.6, amount: 0.3 },
      customer: { percentage: 2.5, amount: 0 },
      tabapayBin: '',
    },
    creditCardBusiness: {
      merchant: { percentage: 0.6, amount: 0.3 },
      customer: { percentage: 2.5, amount: 0 },
      tabapayBin: '',
    },
    amex: {
      merchant: { percentage: 0.6, amount: 0.3 },
      customer: { percentage: 2.9, amount: 0 },
      tabapayBin: '',
    },
  },
  packageE: {
    ach: {
      merchant: { percentage: 0, min: 1, max: 10 },
      customer: { percentage: 0, amount: 0 },
    },
    debitCardRegulated: {
      merchant: { percentage: 1, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    debitCardUnregulated: {
      merchant: { percentage: 2.5, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    creditCardVisa: {
      merchant: {
        percentage: 2.6,
        amount: 0.3,
        disclaimer: 'Higher rates may apply for unregulated debit card',
      },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    creditCardMasterCard: {
      merchant: { percentage: 2.6, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    creditCardDiscover: {
      merchant: { percentage: 2.6, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    creditCardVisa2: {
      merchant: { percentage: 2.6, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    creditCardMasterCard2: {
      merchant: { percentage: 2.6, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    creditCardDiscover2: {
      merchant: { percentage: 2.6, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    creditCardTravel: {
      merchant: { percentage: 2.8, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    creditCardBusiness: {
      merchant: { percentage: 2.8, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
    amex: {
      merchant: { percentage: 3.5, amount: 0.3 },
      customer: { percentage: 0, amount: 0 },
      tabapayBin: '',
    },
  },
}

const addFixtures = async () => {
  const loanPricingPackage = await LoanPricingPackage.find({})
  if (!loanPricingPackage || loanPricingPackage.length === 0) {
    await LoanPricingPackage.insertMany(loanPackages)
  }
}

export const getLoanPricingPackage = async (name?: string) => {
  await addFixtures()
  return LoanPricingPackage.findOne<ILoanPricingPackage>({ name })
}

export const getLoanPricingPackages = async ({
  individual,
  status,
}: {
  individual?: boolean
  status?: string
}) => {
  await addFixtures()
  const query: mongoose.QueryOptions = {}
  if (individual !== undefined) {
    query.individual = mongoose.trusted({ $eq: individual })
  }
  if (status) {
    query.status = status
  }
  return LoanPricingPackage.find(query).sort('order').exec()
}

export const saveLoanPricingPackage = async (
  loanPricingPackage: Partial<ILoanPricingPackage>,
) => {
  await addFixtures()
  let _loanPricingPackage = await getLoanPricingPackage(loanPricingPackage.name)
  if (_loanPricingPackage) {
    if (
      loanPricingPackage._id &&
      loanPricingPackage._id === _loanPricingPackage._id
    ) {
      await _loanPricingPackage.updateOne(loanPricingPackage)
    } else {
      throw new exceptions.LogicalError(
        'Loan Pricing Package with the same name already exists',
      )
    }
  } else {
    if (loanPricingPackage._id) {
      _loanPricingPackage =
        await LoanPricingPackage.findOne<ILoanPricingPackage>({
          _id: loanPricingPackage._id,
        })

      if (!_loanPricingPackage) {
        throw new exceptions.LogicalError('Loan Pricing Package was not found')
      }

      await _loanPricingPackage.updateOne(loanPricingPackage)
    } else {
      _loanPricingPackage = await LoanPricingPackage.create(loanPricingPackage)
    }
  }
  return _loanPricingPackage
}

const packagesArr = [
  {
    name: 'packageA',
    description: 'Merchant Covers All The fees',
    title: 'Merchant Pays',
    tabapayMID: '0003',
    metadata: CARD_PRICING.packageA,
    deactivated: true,
  },
  {
    name: 'packageB',
    description: 'Merchant Flat-fee',
    title: "Let's Share",
    tabapayMID: '0002',
    metadata: CARD_PRICING.packageB,
    deactivated: true,
  },
  {
    name: 'packageC',
    description: 'Customers Cover Most Fees',
    title: 'Customer Pays',
    tabapayMID: '0001',
    metadata: CARD_PRICING.packageC,
    deactivated: true,
  },
  {
    name: 'packageD',
    color: '#00B396',
    title: 'Customer Pays',
    description: 'Customers Cover Most Fees',
    tabapayMID: '0001',
    metadata: CARD_PRICING.packageD,
    deactivated: false,
  },
  {
    name: 'packageE',
    color: '#007FCF',
    title: 'Merchant Pays',
    description: 'Merchant covers All The Fees',
    tabapayMID: '0003',
    metadata: CARD_PRICING.packageE,
    deactivated: false,
  },
]

export const getCardPricingPackage = async ({
  cardPricingPackageId,
  onlyActive,
}: {
  cardPricingPackageId?: string
  onlyActive?: boolean
} = {}) => {
  const cardPricingPackage = await CardPricingPackage.find({})

  if (!cardPricingPackage || cardPricingPackage.length === 0) {
    await CardPricingPackage.insertMany(packagesArr)
  } else if (cardPricingPackage.some((pck) => !pck.title || !pck.description)) {
    await CardPricingPackage.deleteMany({})
    await CardPricingPackage.insertMany(packagesArr)
  }

  if (cardPricingPackageId) {
    return CardPricingPackage.findOne({
      name: cardPricingPackageId,
    })
  }

  const queryFilter = onlyActive ? { deactivated: false } : {}

  return CardPricingPackage.find(queryFilter)
}

export const getSelectedCardPricingPackageDetails = async (
  companyId: string | undefined | null,
  accountId: string,
) => {
  await ensureCardPricingPackagesExist()

  const company = await getCompanySettings(companyId)

  if (!company) return null

  const query = mongoose.isValidObjectId(accountId)
    ? { _id: accountId }
    : { 'cardMetadata.accountId': accountId }

  const account = await BankAccount.findOne(query)

  if (!account) return null

  const { settings } = company

  if (account.paymentMethodType === 'bank') {
    // eslint-disable-next-line @typescript-eslint/return-await
    return await getPricingPackageForAch(settings)
  }

  if (settings?.cardPricingPackageId) {
    // eslint-disable-next-line @typescript-eslint/return-await
    return await getPricingPackageForCard(
      account.cardMetadata,
      settings.cardPricingPackageId,
    )
  }

  return null
}

const ensureCardPricingPackagesExist = async () => {
  const cpp = await CardPricingPackage.find({})
  if (!cpp?.length) {
    await CardPricingPackage.insertMany(packagesArr)
  }
}

export const getCompanySettings = async (companyId?: string | null) => {
  if (!companyId) {
    return { settings: { cardPricingPackageId: 'packageC' } }
  }

  return Company.findById(companyId)
}

const getPricingPackageForAch = async (settings?: {
  cardPricingPackageId?: string | null
}) => {
  if (settings?.cardPricingPackageId) {
    const cardPricingPackage = await CardPricingPackage.findOne({
      name: settings.cardPricingPackageId,
    })

    if (cardPricingPackage) {
      return cardPricingPackage.metadata?.ach
    }
  }

  const anyActivePackage = await CardPricingPackage.findOne({
    deactivated: false,
  })

  return anyActivePackage?.metadata?.ach
}

export const getPricingPackageForCard = async (
  cardMetadata: any,
  cardPricingPackageId: string | null | undefined,
) => {
  if (!cardPricingPackageId) return null

  const cardPricingPackage = await CardPricingPackage.findOne({
    name: cardPricingPackageId,
  })

  if (!cardPricingPackage) return null

  const { metadata } = cardPricingPackage

  if (cardMetadata.network === 'Amex') {
    return metadata.amex
  }

  if (cardMetadata.type === 'Debit') {
    return cardMetadata.isRegulated
      ? metadata.debitCardRegulated
      : metadata.debitCardUnregulated
  }

  // Temporary until we figure out how to get product code from Tabapay
  if (!cardMetadata.productCode) {
    return metadata.creditCardVisa
  }

  const cardProductDetail = await CardProducts.findOne({
    productCode: cardMetadata.productCode,
  })

  if (cardProductDetail?.isTravel) {
    return metadata.creditCardTravel
  }

  if (cardProductDetail?.personalOrBusiness === 'business') {
    if (cardProductDetail?.isl2eligible) {
      switch (cardMetadata.network) {
        case 'Visa':
          return metadata.creditCardVisa2
        case 'MasterCard':
          return metadata.creditCardMasterCard2
        case 'Discover':
          return metadata.creditCardDiscover2
        default:
          // For non-matching network using Visa temporarily, need to confirm?
          return metadata.creditCardVisa2
      }
    }
    return metadata.creditCardBusiness
  }

  switch (cardMetadata.network) {
    case 'Visa':
      return metadata.creditCardVisa
    case 'MasterCard':
      return metadata.creditCardMasterCard
    case 'Discover':
      return metadata.creditCardDiscover
    default:
      // For non-matching network using Visa temporarily, need to confirm?
      return metadata.creditCardVisa
  }
}
