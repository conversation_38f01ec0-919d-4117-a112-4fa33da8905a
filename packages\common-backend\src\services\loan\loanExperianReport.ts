import { dictionaries } from '@linqpal/models'
import { readDraft } from '@linqpal/models/src/helpers/draftReader'
import round from 'lodash/round'
import sum from 'lodash/sum'
import moment from 'moment-timezone'
import { LoanApplication } from '../../models'
import { ICompany, ILoanApplication } from '../../models/types'
import crypt from '../crypt.service'
import * as LMS from '../lms.service'
import { ILoan } from '../lms.service'
import { Logger } from '../logger/logger.service'
import { getPlan } from '../loanplan.service'
import { REFINANCED_DRAWS } from './transactionsReport'

const { getAbbreviatureFromState } = dictionaries
const logger = new Logger({
  module: 'LoanPro',
  subModule: 'loanExperianReport',
})

async function mapToExperianReport(
  app: ILoanApplication & { company: ICompany },
  lmsInfo: ILoan | null,
  date_end?: moment.Moment,
) {
  const log = logger.startTransaction()
  const endDate = date_end || moment()
  log.info(
    {
      loanId: lmsInfo?.id,
    },
    'mapToExperianReport: started',
  )

  const loanReceivables = lmsInfo?.loanReceivables || []

  const { company } = app
  const draft = readDraft(app.draft)

  log.info(
    {
      loanId: lmsInfo?.id,
    },
    'mapToExperianReport: got first loan with operation',
  )

  const invoice_amount = round(lmsInfo?.amount || 0, 2)
  const fee_amount = round(lmsInfo?.fee || 0, 2)
  const loan_amount = round(invoice_amount + fee_amount)

  const due_amount = round(
    sum(
      loanReceivables.map((i) =>
        i.scheduleStatus === 'Current' ? i.expectedAmount - i.paidAmount : 0,
      ),
    ),
  )

  const future_amount = round(
    sum(
      loanReceivables.map((i) =>
        i.status === 'Pending' &&
        i.scheduleStatus === 'Current' &&
        moment.tz(i.expectedDate, 'UTC').isSameOrAfter(endDate, 'date')
          ? i.expectedAmount - i.paidAmount
          : 0,
      ),
    ),
  )

  log.info(
    {
      loanId: lmsInfo?.id,
    },
    'mapToExperianReport: finished',
  )

  function sumPastDue(start_days: number, end_days: number) {
    const filtered = loanReceivables.filter((i) => {
      const due_days =
        i.scheduleStatus === 'Current'
          ? endDate.diff(moment(i.expectedDate), 'days')
          : 0
      return start_days <= due_days && due_days <= end_days
    })
    return round(
      filtered.reduce((c, i) => c + i.expectedAmount - i.paidAmount, 0),
    )
  }

  let EIN = draft?.businessInfo_ein
  if (EIN && typeof EIN !== 'string') {
    EIN = await crypt.decrypt(EIN.cipher)
  }
  const plan =
    app.metadata?.paymentPlan ||
    (app.invoiceDetails?.paymentPlan
      ? await getPlan(app.invoiceDetails.paymentPlan).catch((e) =>
          console.log(e),
        )
      : null)

  const report_item = {
    'Account Number': app.company_id.toString(),
    'Loan Number': app.lms_id,
    'Business Name': company?.legalName || company?.name,
    'Secondary Name': company?.legalName ? company?.name : '',
    Address: draft?.businessInfo_businessAddress?.address || '',
    City: draft?.businessInfo_businessAddress?.city || '',
    State: draft?.businessInfo_businessAddress?.state
      ? getAbbreviatureFromState(draft.businessInfo_businessAddress.state)
      : '',
    'Zip Code': draft?.businessInfo_businessAddress?.zip || '',
    'Tax ID': EIN,
    'SIC Code': '2600',
    'Date Open': lmsInfo?.startDate
      ? moment.tz(lmsInfo.startDate, 'UTC').format('MM/DD/YYYY')
      : null,
    'Date Close': lmsInfo?.closeDate
      ? moment.tz(lmsInfo.closeDate, 'UTC').format('MM/DD/YYYY')
      : null,
    'Closed Reason': lmsInfo?.closeDate ? '06' : '',
    'Account Status': '-',
    'Extract Date': endDate.format('MM/DD/YYYY'),
    Terms: `NET ${plan?.days || 0}`,
    'Original Loan Amount': loan_amount,
    'Current Loan Amount': '',
    'Total Balance Due': due_amount,
    'Current / Future': future_amount,
    '1-30 days past due': sumPastDue(1, 30),
    '31-60 days past due': sumPastDue(31, 60),
    '61-90 days past due': sumPastDue(61, 90),
    '91-120 days past due': sumPastDue(91, 120),
    '121-150 days past due': sumPastDue(121, 150),
    '151-180 days past due': sumPastDue(151, 180),
    '180+ days past due': sumPastDue(181, Infinity),
  }

  report_item['Account Status'] =
    report_item['Total Balance Due'] === 0
      ? '0'
      : report_item['180+ days past due'] > 0
      ? '7'
      : report_item['151-180 days past due'] > 0
      ? '6'
      : report_item['121-150 days past due'] > 0
      ? '5'
      : report_item['91-120 days past due'] > 0
      ? '4'
      : report_item['61-90 days past due'] > 0
      ? '3'
      : report_item['31-60 days past due'] > 0
      ? '2'
      : report_item['1-30 days past due'] > 0
      ? '1'
      : 'C'

  return report_item
}

async function fetchLoanApplications(
  date_start?: moment.Moment,
  date_end?: moment.Moment,
) {
  const dateStart = date_start || moment().subtract(1, 'month').startOf('month')
  const dateEnd = date_end || moment()
  const join_company = [
    { $addFields: { company_id: { $toObjectId: '$company_id' } } },
    {
      $lookup: {
        from: 'companies',
        localField: 'company_id',
        foreignField: '_id',
        as: 'company',
      },
    },
    { $unwind: '$company' },
  ]
  const mm: any = {
    $match: {
      issueDate: { $lt: dateEnd.toDate() },
      status: { $in: ['approved', 'closed'] },
      lms_id: { $exists: true },
      $expr: { $ne: ['$lms_id', null] },
    },
  }
  let aggItems = Promise.resolve([] as any[])
  if (date_start) {
    mm.$match.$or = [
      {
        closeDate: null,
      },
      {
        closeDate: { $exists: false },
      },
    ]

    aggItems = LoanApplication.aggregate([
      {
        $match: {
          issueDate: { $ne: null },
          status: { $in: ['approved', 'closed'] },
          lms_id: { $exists: true },
          $expr: {
            $and: [
              { $ne: ['$lms_id', null] },
              {
                $or: [
                  {
                    $and: [
                      { $gte: ['$issueDate', dateStart.toDate()] },
                      { $lt: ['$issueDate', dateEnd.toDate()] },
                    ],
                  },
                  {
                    $and: [
                      { $gte: ['$closeDate', dateStart.toDate()] },
                      { $lt: ['$closeDate', dateEnd.toDate()] },
                    ],
                  },
                ],
              },
            ],
          },
        },
      },
      ...join_company,
      { $sort: { issueDate: 1, closeDate: 1 } },
    ])
      .allowDiskUse(true)
      .exec()
  }

  const [not_closed_items, items] = await Promise.all([
    LoanApplication.aggregate([
      mm,
      ...join_company,
      { $sort: { issueDate: 1 } },
    ]).allowDiskUse(true),
    aggItems,
  ])
  return [...not_closed_items, ...items]
}

export default async function loanExperianReport(
  date_start?: moment.Moment,
  date_end?: moment.Moment,
) {
  const log = logger.startTransaction()
  log.info('loanExperianReport: start')
  const items = await fetchLoanApplications(date_start, date_end)

  log.info('loanExperianReport: before making http request to LMS Admin/Loans')

  const loanIds = items
    .filter((item) => !REFINANCED_DRAWS.includes(item.lms_id)) // exclude refinanced
    .map((e) => ({ id: e.lms_id }))

  const loans = await LMS.getLoansByIds({ ids: loanIds }, false)

  log.info('loanExperianReport: made http request to LMS Admin/Loans')

  return Promise.all(
    items.map(async (item) => {
      const loan = loans.find((e) => e.id === item.lms_id) ?? null
      return mapToExperianReport(item, loan, date_end)
    }),
  )
}
