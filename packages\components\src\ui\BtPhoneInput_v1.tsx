import React, { FC, useState } from 'react'
import { NumberFormatValues, PatternFormat } from 'react-number-format'
import { BtInputBaseProps } from './BtInputBase'
import {
  CountryCode,
  isValidNumberForRegion,
  parsePhoneNumber,
} from 'libphonenumber-js'
import { BtInput_v1 } from './BtInput_v1'

interface IProps extends Omit<BtInputBaseProps, 'onChangeText'> {
  countryCode?: CountryCode
  onChangeText: (value: BtPhoneInputValue) => void
  validationError?: string
  format?: string
}

export interface BtPhoneInputValue {
  raw: string
  formatted: string
  parsed: string
}

export const BtPhoneInput_v1: FC<IProps> = ({
  size = 'large',
  value,
  countryCode = 'US',
  label,
  required,
  disabled,
  placeholder,
  validate,
  validationError,
  onChangeText,
  testID,
  format = '###-###-####',
}) => {
  const [phone, setPhone] = useState<string>(() => {
    try {
      return value
        ? parsePhoneNumber(value, countryCode).nationalNumber?.toString()
        : ''
    } catch {
      return ''
    }
  })

  const handleValidate = (text: string) => {
    if (validationError) {
      return text && isValidNumberForRegion(text, 'US') ? '' : validationError
    } else {
      return validate && validate(text)
    }
  }

  const handleValueChange = (formatValues: NumberFormatValues) => {
    setPhone(formatValues.formattedValue)
    const result = {
      raw: formatValues.value,
      formatted: formatValues.formattedValue,
      parsed: '',
    }
    if (onChangeText) {
      try {
        result.parsed = parsePhoneNumber(
          formatValues.value,
          countryCode,
        ).number.toString()
      } catch {
        result.parsed = formatValues.value
      }
      onChangeText(result)
    }
  }

  return (
    <PatternFormat
      format={format}
      placeholder={placeholder}
      size={size}
      label={label}
      value={phone}
      required={required}
      disabled={disabled}
      keyboardType="number-pad"
      customInput={BtInput_v1}
      validate={handleValidate}
      allowEmptyFormatting={false}
      onValueChange={handleValueChange}
      testID={testID}
    />
  )
}
