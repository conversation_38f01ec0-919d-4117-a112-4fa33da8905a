import { DefaultTheme, NavigationContainer } from '@react-navigation/native'
import { createStackNavigator } from '@react-navigation/stack'
import { observer } from 'mobx-react'
import React, { useMemo } from 'react'
import { headerMode, screenOptions } from '../utils/helpers/commonUtils'
import Loading from './Loading'
import RootStore from '../store/RootStore'
import { links, paths } from './links'
import InvoiceLinkStack from './InvoiceLinkStack'
import Invitation from './Invitation'
import Auth from './Auth'
import { InvoiceDetailsProvider } from './Contractor/TabInvoice/InvoiceDetailsContext'
import { navigationRef } from './Supplier/RootNavigation'
import {
  SupplierInvoicesTabStore,
  TabSupplierProvider,
} from '../store/ScreensStore/Supplier/supplierInvoiceTab'
import {
  SupplierAccountsTabStore,
  TabAccountsProvider,
} from '../store/ScreensStore/Supplier/SupplierAccountsTab'
import IdleTimeOutHandler from '../utils/helpers/IdleTimeOutHandler'
import KittenScreen from './KittenScreen'
import Authorize from './Auth/Authorize'
import { GetPaidApplication } from './Supplier/GetPaidApplication'
import { CreditRequest } from './Contractor/CreditRequest'
import { PaymentCollectedReceipt } from './Contractor/InvoicesTab/Receipt/PaymentCollectedReceipt'
import { QuestionnaireLayout } from './ProjectForm/QuestionnaireLayout'
import LinkBankAccount from './Contractor/MoreTab/Wallet/LinkPaymentScreens/LinkBankAccount'
import { InHouseCreditApplication } from './GeneralApplication/InHouseCreditApplication'
import { CreditAgreementsApproval } from './DocumentApproval/CreditAgreementsApproval'
import { GetPaidAgreementsApproval } from './DocumentApproval/GetPaidAgreementsApproval'
import { IntegrationJwtPayment } from './IntegrationPayment/JwtIntegration/JwtIntegrationPayment'
import { IntegrationPayment } from './IntegrationPayment/RegularIntegration/RegularIntegrationPayment'
import { ZohoBooksIntegrationPayment } from './IntegrationPayment/RegularIntegration/ZohoBooksIntegration/ZohoBooksIntegrationPayment'
import useIsMobile from './Contractor/PayablesTab/hooks/useIsMobile'
import { MobileProjectForm } from './ProjectForm/MobileProjectForm'
import { LenderApplication } from './Applications/LenderApplication'

const Onboarding = React.lazy(() => import('./Onboarding'))
const EmailActions = React.lazy(() => import('./EmailActions'))
const Console = React.lazy(() => import('./Console'))
const AcceptAgreement = React.lazy(() => import('./AcceptAgreement'))

const Stack = createStackNavigator()

export const Navigator = observer(() => {
  const { userStore } = RootStore
  const { isAuthenticated, userInfoReady, hasActualAgreement } = userStore
  const onboarded = !!userStore.settings.get('onboarding')

  const InitialScreen = useMemo(() => {
    if (isAuthenticated && !userInfoReady) {
      return Loading
    }
    if (isAuthenticated && !onboarded) {
      return Onboarding
    }
    if (isAuthenticated && !hasActualAgreement) {
      return AcceptAgreement
    }
    if (isAuthenticated && onboarded) {
      return Console
    }
    return Auth
  }, [isAuthenticated, userInfoReady, onboarded, hasActualAgreement])

  const {
    screensStore: { notificationStore },
  } = RootStore
  const accounts = SupplierAccountsTabStore.create()
  const invoices = SupplierInvoicesTabStore.create({}, { notificationStore })

  return (
    <TabAccountsProvider value={accounts}>
      <TabSupplierProvider value={invoices}>
        <InvoiceDetailsProvider>
          <Stack.Navigator
            screenOptions={{ ...screenOptions, cardStyle: { flex: 1 } }}
            headerMode={headerMode}
          >
            <Stack.Screen name={paths.Home._self} component={InitialScreen} />
            <Stack.Screen
              name={paths.IntegrationPayment}
              component={IntegrationPayment}
            />
            <Stack.Screen
              name={paths.IntegrationJwtPayment}
              component={IntegrationJwtPayment}
            />
            <Stack.Screen
              name={paths.InvoicePayment}
              component={ZohoBooksIntegrationPayment}
            />
            <Stack.Screen name={paths.EmailActions} component={EmailActions} />
            <Stack.Screen name={paths.Invitation} component={Invitation} />
            <Stack.Screen name={paths.Kitten} component={KittenScreen} />
            <Stack.Screen name={paths.Authorize} component={Authorize} />
            <Stack.Screen // fallback to legacy URL, to be removed when all in-flight approval requests processed
              name={paths.DocumentApproval}
              component={CreditAgreementsApproval}
            />
            <Stack.Screen
              name={paths.CreditAgreementsApproval}
              component={CreditAgreementsApproval}
            />
            <Stack.Screen
              name={paths.GetPaidAgreementsApproval}
              component={GetPaidAgreementsApproval}
            />
            {isAuthenticated && (
              <>
                <Stack.Screen
                  name={paths.GetPaidApplication}
                  component={GetPaidApplication}
                />
                <Stack.Screen
                  name={paths.CreditApplication}
                  component={CreditRequest}
                />
                <Stack.Screen
                  name={paths.InHouseCreditApplication}
                  component={InHouseCreditApplication}
                />
                <Stack.Screen
                  name={paths.LenderApplication}
                  component={LenderApplication}
                />
                <Stack.Screen
                  name={paths.NewProject}
                  component={({ route, navigation }) => {
                    const isMobile = useIsMobile()
                    return isMobile ? (
                      <MobileProjectForm
                        route={route}
                        navigation={navigation}
                      />
                    ) : (
                      <QuestionnaireLayout
                        route={route}
                        navigation={navigation}
                      />
                    )
                  }}
                />
              </>
            )}
            <Stack.Screen name={paths.LinkBank} component={LinkBankAccount} />
            <Stack.Screen
              name={paths.InvoiceLink._self}
              component={InvoiceLinkStack}
            />
            <Stack.Screen
              name={paths.PaymentCollectedReceipt}
              component={PaymentCollectedReceipt}
            />
          </Stack.Navigator>
        </InvoiceDetailsProvider>
      </TabSupplierProvider>
    </TabAccountsProvider>
  )
})

const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    background: 'white',
  },
}

export default function Screens() {
  return (
    <InvoiceDetailsProvider>
      <React.Suspense fallback={<Loading />}>
        <NavigationContainer
          ref={navigationRef}
          linking={links}
          documentTitle={{ enabled: false }}
          theme={theme}
        >
          <Navigator />
        </NavigationContainer>
        <IdleTimeOutHandler />
      </React.Suspense>
    </InvoiceDetailsProvider>
  )
}
