import { makeAutoObservable } from 'mobx'
import { LenderApplicationDraft } from './LenderApplicationDraft'
import { LenderApplicationFlow } from '@linqpal/models/src/applications/lender/LenderApplicationFlow'
import { ILenderApplicationDraft } from '@linqpal/models/src/applications/lender/ILenderApplicationDraft'
import { ILenderApplicationOptions } from '@linqpal/models/src/applications/lender/ILenderApplicationOptions'
import { FlowController } from '@linqpal/models/src/applications/FlowController'
import {
  Groups,
  Steps,
} from '@linqpal/models/src/applications/lender/LenderApplicationSteps'
import { ILenderApplicationStepOptions } from '../Flow/getLenderApplicationEditor'
import { routes2 } from '@linqpal/models'
import RootStore from '../../../../store/RootStore'

export class LenderApplicationStore {
  private _flow: LenderApplicationFlow = new LenderApplicationFlow()

  private _flowController: FlowController<
    ILenderApplicationDraft,
    ILenderApplicationOptions
  >

  private _draft: LenderApplicationDraft

  private _applicationOptions: ILenderApplicationOptions

  private _stepOptions: ILenderApplicationStepOptions = {}

  private _showWelcomePage = false

  private _isDraftLoading = true

  private _isSubmitting = false

  constructor() {
    this._flow = new LenderApplicationFlow()
    this._draft = new LenderApplicationDraft()
    this._flowController = new FlowController(this._flow.definition)

    this._applicationOptions = {
      canGoBack: true,
      canSkip: true,
      showNavigationButtons: true,
    }

    makeAutoObservable(this)
  }

  public get showWelcomePage(): boolean {
    return this._showWelcomePage
  }

  public get currentStep(): string {
    return this._draft?.currentStep || ''
  }

  public get currentGroup(): string {
    const currentStep = this._draft?.currentStep || ''
    const group = currentStep.split('.')[0] || ''
    return group
  }

  public get draft() {
    return this._draft
  }

  public get stepOptions() {
    return this._stepOptions
  }

  public get isSubmitting(): boolean {
    return this._isSubmitting
  }

  public get isDraftLoading(): boolean {
    return this._isDraftLoading
  }

  public get canGoBack(): boolean {
    if (!this._stepOptions.canGoBack) {
      return false
    }

    return this.currentStep !== this._draft.initialStep
  }

  public get isCurrentStepValid(): boolean {
    return this.currentStep ? this.validateStep(this.currentStep) : false
  }

  // TODO: VK: Lender: Review
  public get canSubmit(): boolean {
    return !this._isSubmitting
  }

  public hideWelcomePage() {
    this._showWelcomePage = false
  }

  public setStepOptions(
    options: ILenderApplicationStepOptions | null | undefined,
  ) {
    this._stepOptions = {
      ...options,
      canGoBack: options?.canGoBack ?? true,
      canSkip: options?.canSkip ?? true,
      showNavigationButtons: options?.showNavigationButtons ?? true,
    }
  }

  public goToStep(path: string) {
    this.setCurrentStep(path)
  }

  public moveNext() {
    const nextStep = this._flowController.getNextStep(
      this.currentStep,
      this._draft,
      {} as ILenderApplicationOptions,
    )

    if (nextStep) {
      this.setCurrentStep(nextStep)
    }

    this.saveDraft().catch((e) => console.error(e))
  }

  public moveBack() {
    const previousStep = this._flowController.getPreviousStep(
      this.currentStep,
      this._draft,
      {} as ILenderApplicationOptions,
    )
    if (previousStep && previousStep !== this.currentStep) {
      this.setCurrentStep(previousStep)
    }
  }

  public skipStep() {
    const skipStep = this._flowController.findSkipStep(
      this.currentStep,
      this._draft,
      this._applicationOptions,
    )
    if (skipStep) {
      this.setCurrentStep(skipStep)
    }
  }

  public getFlowSteps() {
    return this._flowController.getFlowSteps(
      this._draft,
      this._applicationOptions,
    )
  }

  public getFlowGroups() {
    const groups = this._flowController.getFlowGroups(
      this._draft,
      this._applicationOptions,
    )

    return groups.filter((group) => group !== Groups.review)
  }

  public getGroupSteps(group: string): string[] {
    return this._flowController.getGroupSteps(
      group,
      this._draft,
      this._applicationOptions,
    )
  }

  public validateStep(step: string) {
    return this._draft.validate(step)
  }

  public editGroup(group: string, fromBeginning = true) {
    const groupSteps = this.getGroupSteps(group)
    const firstInvalidIndex = groupSteps.findIndex(
      (step) => !this.validateStep(step),
    )

    const allValid = firstInvalidIndex === -1
    const noneValid = firstInvalidIndex === 0

    if (fromBeginning || noneValid || allValid) {
      const newStep = this._flowController.getFirstGroupStep(
        group,
        this._draft,
        this._applicationOptions,
      )
      this.setCurrentStep(newStep)
    } else {
      const newStep = groupSteps[firstInvalidIndex]
      this.setCurrentStep(newStep)
    }
  }

  // region API calls

  public async loadDraft() {
    const companyId = RootStore.userStore.company?.id
    if (!companyId) return

    this._isDraftLoading = true

    const response = await routes2.application
      .getLenderApplicationDraft()
      .finally(() => {
        this._isDraftLoading = false
      })

    // for new drafts show welcome page always
    // for existing - only if no steps filled
    if (!response.draft) {
      this._draft = new LenderApplicationDraft({
        initialStep: Steps.sponsor.loanOfficer,
      })

      this._showWelcomePage = true
    } else {
      this._draft = new LenderApplicationDraft(response.draft)
      console.log('=>(LenderApplicationStore.ts:225) this._draft', this._draft)

      const flowSteps = this.getFlowSteps()
      this._showWelcomePage = !flowSteps.some((step) => this.validateStep(step))
    }
  }

  public async saveDraft() {
    const companyId = RootStore.userStore.company?.id
    if (!companyId) return

    this._isSubmitting = true

    const response = await routes2.application
      .postLenderApplicationDraft({
        companyId: RootStore.userStore.company?.id || '',
        draft: this._draft.toJson(),
      })
      .finally(() => {
        this._isSubmitting = false
      })

    // TODO: VK: Unified: save version?
    this._draft.id = response.draft.id
  }

  public async submitApplication() {
    this._isSubmitting = true
    try {
      // TODO: Implement actual API call to submit application
      console.log('Submitting application:', this._draft)
      return await Promise.resolve()
    } finally {
      this._isSubmitting = false
    }
  }

  // endregion API calls

  // region private methods

  private setCurrentStep(step: string) {
    this._draft.currentStep = step

    this.updateVisitedSteps(step)
  }

  private updateVisitedSteps(newStep: string) {
    if (!this._draft.visitedSteps.includes(newStep)) {
      this._draft.visitedSteps.push(newStep)
    }
  }

  // endregion private methods
}
