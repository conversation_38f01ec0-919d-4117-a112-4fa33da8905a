import {
  ACH_TRANSACTION_TYPE,
  Company,
  CustomerAccount,
  Invoice,
  LoanApplication,
} from '../../models'
import { ILoanApplication, ITransaction } from '../../models/types'
import { getLoanPricingPackage } from '../package.service'
import { PipelineStage } from 'mongoose'
import moment from 'moment-timezone'
import { LMS } from '../../../index'
import { LoanApplicationService } from '../loanApplication.service'
import { dictionaries, ILoanPriceModel } from '@linqpal/models'
import { OutInvoiceData } from '../cbw/ach.out.controller'
import { Logger } from '../logger/logger.service'

export interface LoanFinalPaymentInfo {
  _id: any
  lms_id: string
  issueDate: Date
  invoiceDetails: any
  metadata: any
  status: string
  lastPaymentDate: Date | null
  lastRepaymentTransaction: ITransaction | null
}

const logger = new Logger({ module: 'loanFinalPayment' })

export async function calculateFinalPayment(app: ILoanApplication): Promise<{
  totalAmount: number
  invoiceAmounts: { [key: string]: OutInvoiceData }
}> {
  const invoices = await LoanApplicationService.getInvoices(app)
  const pricingPackage = await getLoanPackage(app, invoices[0]?.company_id)

  if (!pricingPackage) {
    // throw exception?
    logger.warn({ app }, `no pricing package found for app ${app.id}`)
    return {
      totalAmount: 0,
      invoiceAmounts: invoices.reduce((res, inv) => {
        return { ...res, [inv.id]: { amount: 0, discount: 0 } }
      }, {}),
    }
  }

  let totalAmount = 0
  const invoiceAmounts: { [key: string]: OutInvoiceData } = {}

  for (const invoice of invoices) {
    const payment = LoanApplicationService.calculatePayments(
      app,
      invoice.total_amount,
      pricingPackage,
    )

    logger.info({ payment }, `payment for invoice ${invoice.id}`)

    totalAmount += payment.final.amount
    invoiceAmounts[invoice.id] = payment.final
  }

  logger.info({ invoiceAmounts }, `calculated final payments for app ${app.id}`)

  return { totalAmount, invoiceAmounts }
}

export async function getLoanPackage(
  app: ILoanApplication,
  companyId: string,
): Promise<ILoanPriceModel | undefined> {
  let pricingPackage = app.metadata?.loanPackage

  if (!pricingPackage && companyId) {
    const payeeCompany = await Company.findById(companyId)

    const pack = await getLoanPricingPackage(
      payeeCompany?.settings?.loanPricingPackageId,
    )

    if (pack) {
      pricingPackage = { ...pack.metadata.toObject(), name: pack.title }
    }
  }

  return pricingPackage
}

// Do not change loan final payment report
export async function getScheduledFinalPaymentApps(date: Date) {
  // returns loan apps ready to final payment by its payment plan, no matter whether fully paid or not.

  const apps = await LoanApplication.aggregate<LoanFinalPaymentInfo>([
    ...getLoanAppsWithoutFinalPaymentPipeline(),
    {
      $addFields: {
        lastDate: {
          $dateAdd: {
            startDate: '$issueDate',
            unit: 'day',
            amount: '$metadata.paymentPlan.days',
          },
        },
      },
    },
    { $match: { lastDate: { $lte: date } } },
    {
      $match: {
        $or: [
          { 'supplier.settings.sendFinalPaymentWhenLoanIsPaid': { $ne: true } },
          {
            'customerAccount.settings.sendFinalPaymentWhenLoanIsPaid': {
              $ne: true,
            },
          },
        ],
      },
    },
    { $project: projectFinalPaymentInfo() },
  ])

  return apps
}

export async function getFullyPaidLoanApps(): Promise<LoanFinalPaymentInfo[]> {
  // returns fully paid loan apps ready for final payment

  // For suppliers with 'pay final payment after loan is fully paid' setting enabled and loan is fully paid:
  // - 3 days days after last payment we request money from funding source (arcadia)
  // - 4 days after after last payment we make final payment

  const lastPaymentDateDelta = 4

  const lastPaymentMoment =
    process.env.LP_MODE === 'prod'
      ? moment().subtract(lastPaymentDateDelta, 'days').endOf('day') // end of day just to align with other dates in arcadia file
      : moment()

  const results = await LoanApplication.aggregate<
    LoanFinalPaymentInfo & { lms_id: string }
  >([
    ...getLoanAppsWithoutFinalPaymentPipeline(),
    { $addFields: { lastDate: lastPaymentMoment.toDate() } }, // for compatibility with loanTape
    {
      $match: {
        $and: [
          { lastPaymentDate: { $regex: /^\d{4}-\d{2}-\d{2}$/ } },
          { lastPaymentDate: { $lte: lastPaymentMoment.format('YYYY-MM-DD') } },
          { 'supplier.settings.sendFinalPaymentWhenLoanIsPaid': true },
          { 'customerAccount.settings.sendFinalPaymentWhenLoanIsPaid': true },
        ],
      },
    },
    {
      $lookup: {
        from: 'operations',
        as: 'lastRepaymentOperation',
        let: { ownerId: { $toString: '$_id' } },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$owner_id', '$$ownerId'] },
                  {
                    $eq: ['$type', dictionaries.OPERATION_TYPES.LOAN.REPAYMENT],
                  },
                  { $eq: ['$status', 'SUCCESS'] },
                ],
              },
            },
          },
          { $sort: { createdAt: -1 } },
          { $limit: 1 },
        ],
      },
    },
    {
      $unwind: {
        path: '$lastRepaymentOperation',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: 'transactions',
        as: 'lastRepaymentTransaction',
        let: {
          lastRepaymentOperationId: {
            $toString: '$lastRepaymentOperation._id',
          },
        },
        pipeline: [
          {
            $match: {
              $expr: { $eq: ['$operation_id', '$$lastRepaymentOperationId'] },
            },
          },
          { $limit: 1 },
        ],
      },
    },
    {
      $unwind: {
        path: '$lastRepaymentTransaction',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $project: {
        ...projectFinalPaymentInfo(),
        lastRepaymentTransaction: 1,
      },
    },
  ])

  const fullyPaidApps = await Promise.all(
    results.map(async (result) => {
      logger.info(
        `final payment should be sent when loan ${result.lms_id} is fully paid`,
      )

      const isFullyPaid = await LMS.checkIsFullyPaid(result.lms_id)

      if (!isFullyPaid) {
        // prettier-ignore
        logger.info(`loan ${result.lms_id} is not fully paid, delay final payment.`)
        return null
      }

      const isInternalTransaction =
        result.lastRepaymentTransaction?.metadata?.transactionType ===
        ACH_TRANSACTION_TYPE.INTERNAL

      const lastPaymentDate = moment.tz(result.lastPaymentDate, 'UTC')

      const finalPaymentDate =
        process.env.LP_MODE === 'prod' && !isInternalTransaction
          ? lastPaymentDate.clone().businessAdd(3, 'days').endOf('day') // wait 3 BD to make sure that the ACH PULL was not returned
          : lastPaymentDate

      if (moment.utc().isBefore(finalPaymentDate)) {
        // prettier-ignore
        logger.info(`loan ${result.lms_id}: expecting final payment after ${finalPaymentDate.format()}`)
        return null
      }

      logger.info(`final payment can be disbursed for loan ${result.lms_id}`)
      return result
    }),
  )

  return fullyPaidApps.filter((app) => !!app) as LoanFinalPaymentInfo[]
}

function getLoanAppsWithoutFinalPaymentPipeline(): PipelineStage[] {
  return [
    {
      $match: {
        status: { $in: ['approved', 'closed'] },
        'metadata.paymentPlan': { $exists: true },
        'metadata.paymentPlan.type': { $in: ['custom', 'regular'] },
        'invoiceDetails.invoiceId': { $exists: true },
        'metadata.loanPackage.finalPayment': { $gt: 0 },
        'metadata.skip_final_payment': { $ne: true },
      },
    },
    { $sort: { issueDate: -1 } },

    {
      $lookup: {
        from: 'operations',
        as: 'final',
        let: { id: { $toString: '$_id' } },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$owner_id', '$$id'] },
                  { $eq: ['$type', 'loan_final_issue'] },
                  { $eq: ['$status', 'SUCCESS'] },
                ],
              },
            },
          },
        ],
      },
    },
    { $addFields: { finalCount: { $size: '$final' } } },
    { $match: { finalCount: 0 } },

    {
      $addFields: {
        invoices_ids: {
          $cond: [
            { $eq: [{ $type: '$invoiceDetails.invoiceId' }, 'array'] },
            '$invoiceDetails.invoiceId',
            ['$invoiceDetails.invoiceId'],
          ],
        },
      },
    },

    {
      $lookup: {
        from: 'operations',
        as: 'fp',
        let: { invoiceIds: '$invoices_ids' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $in: ['$owner_id', '$$invoiceIds'] },
                  { $eq: ['$type', 'invoice_final_payment'] },
                  { $in: ['$status', ['SUCCESS', 'PROCESSING']] },
                ],
              },
            },
          },
        ],
      },
    },
    { $addFields: { fpCount: { $size: '$fp' } } },
    { $match: { fpCount: 0 } },

    {
      $lookup: {
        from: Invoice.collection.name,
        as: 'invoices',
        let: { invoiceIds: '$invoices_ids' },
        pipeline: [
          {
            $match: {
              $expr: { $in: [{ $toString: '$_id' }, '$$invoiceIds'] },
            },
          },
          { $sort: { createdAt: 1 } },
        ],
      },
    },
    { $addFields: { invoice: { $last: '$invoices' } } },

    {
      $lookup: {
        from: Company.collection.name,
        as: 'supplier',
        let: {
          supplierId: {
            $convert: {
              input: '$invoice.company_id',
              to: 'objectId',
              onError: null,
            },
          },
        },
        pipeline: [{ $match: { $expr: { $eq: ['$_id', '$$supplierId'] } } }],
      },
    },

    {
      $lookup: {
        from: CustomerAccount.collection.name,
        as: 'customerAccount',
        let: {
          customerAccountId: {
            $convert: {
              input: '$invoice.customer_account_id',
              to: 'objectId',
              onError: null,
            },
          },
        },
        pipeline: [
          { $match: { $expr: { $eq: ['$_id', '$$customerAccountId'] } } },
        ],
      },
    },

    { $unwind: { path: '$supplier', preserveNullAndEmptyArrays: true } },
    { $unwind: { path: '$customerAccount', preserveNullAndEmptyArrays: true } },
  ]
}

function projectFinalPaymentInfo(): any {
  return {
    _id: 1,
    issueDate: 1,
    invoiceDetails: 1,
    final: 1,
    metadata: 1,
    status: 1,
    lastPaymentDate: 1,
    lms_id: 1,
  }
}
