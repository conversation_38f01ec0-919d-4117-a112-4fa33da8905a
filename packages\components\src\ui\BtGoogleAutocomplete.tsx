import React, { useEffect, useRef, useState } from 'react'
import {
  Autocomplete,
  AutocompleteItem,
  AutocompleteProps,
} from '@ui-kitten/components'
import useGoogle from 'react-google-autocomplete/lib/usePlacesAutocompleteService'
import { ActivityIndicator, ImageProps } from 'react-native'
import { RenderProp } from '@ui-kitten/components/devsupport'
import { useResponsive } from '../hooks'
import { composeStyle } from '../helpers'
import { BtInputLabel } from './BtInputLabel'

const GOOGLE_MAP_API_KEY = process.env.REACT_APP_GOOGLE_MAP_API_KEY
const MIN_QUERY_LENGTH = 2

interface Props
  extends Omit<AutocompleteProps, 'onChange' | 'onSelect' | 'label'> {
  onChange?: (value) => void
  mandatory?: boolean
  label?: string
  errorText?: string
  onSelect?: (value: IAddress) => void
  size?: string
}

export const BtGoogleAutocomplete = ({
  onChange,
  value,
  mandatory = false,
  label,
  errorText,
  caption,
  placeholder,
  style,
  onSelect,
  onFocus,
  onBlur,
  testID,
  size,
}: Props) => {
  const {
    placePredictions,
    getPlacePredictions,
    isPlacePredictionsLoading,
    placesService,
  } = useGoogle({
    apiKey: GOOGLE_MAP_API_KEY,
    options: {
      types: ['geocode', 'establishment'],
      componentRestrictions: { country: 'us' },
    },
  })

  const [localValue, setLocalValue] = useState<string | undefined>(value)
  const { sm } = useResponsive()
  const renderOption = (item, index) => (
    <AutocompleteItem key={index} title={item.description} />
  )

  const autocompleteRef = useRef<any>(null)

  useEffect(() => {
    if (testID) {
      const textInput =
        autocompleteRef?.current?.inputRef?.current?.textInputRef?.current

      if (textInput && 'setAttribute' in textInput) {
        textInput.setAttribute('data-testid', testID)
      }
    }
  }, [testID])

  useEffect(() => {
    // Only trigger predictions if there's a value or user has started typing
    if (value && value.trim()) {
      getPlacePredictions({ input: value })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value])

  const setDetails = (details) => {
    setLocalValue(details.formatted_address)
    const addressObject = parseGmapAddress(details)
    onSelect && onSelect(addressObject)
  }

  const onSelected = (index) => {
    placesService?.getDetails(
      { placeId: placePredictions[index].place_id },
      setDetails,
    )
  }

  const loadingIndicator = () => {
    if (
      isPlacePredictionsLoading &&
      localValue &&
      localValue.trim().length >= MIN_QUERY_LENGTH
    ) {
      return <ActivityIndicator size={'small'} />
    }
    return null
  }

  return (
    <Autocomplete
      style={composeStyle({ minWidth: 100 }, style)}
      value={localValue}
      caption={errorText ? errorText : caption}
      placeholder={placeholder}
      onSelect={onSelected}
      onFocus={onFocus}
      onBlur={onBlur}
      size={size ? size : sm ? 'large' : 'medium'}
      label={<BtInputLabel required={mandatory}>{label}</BtInputLabel>}
      onChangeText={(text) => {
        // Only trigger predictions if user has typed at least 2 characters
        if (text && text.trim().length >= MIN_QUERY_LENGTH) {
          getPlacePredictions({ input: text })
        }
        setLocalValue(text)
        onChange && onChange(text)
      }}
      accessoryRight={loadingIndicator as RenderProp<Partial<ImageProps>>}
      testID={testID}
      ref={autocompleteRef}
      placement="bottom end"
    >
      {placePredictions.map(renderOption)}
    </Autocomplete>
  )
}

// export const BtGoogleAutocomplete = createEditable({
//   Component: BtGoogleAutocomplete_,
//   valuePropertyName: 'value',
//   changePropertyName: 'onChange',
// })

type IAddress = {
  address?: string
  city?: string
  state?: string
  zip?: string
  street?: string
}

export function parseGmapAddress(place) {
  const { address_components } = place
  const result: IAddress = {
    address: place.name,
  }

  address_components &&
    address_components.forEach((component) => {
      // TO: VK: Review and simplify
      if (
        [
          'political,locality',
          'locality,political',
          'sublocality_level_1,sublocality,political',
          'political,sublocality_level_1,sublocality',
        ].includes(component.types.toString())
      ) {
        result.city = component.long_name
      } else if (component.types.includes('administrative_area_level_1')) {
        result.state = component.long_name
      } else if (component.types.includes('postal_code')) {
        result.zip = component.short_name
      }
    })

  const streetNumber =
    address_components?.find((c) => c.types.includes('street_number'))
      ?.long_name || ''
  const route =
    address_components?.find((c) => c.types.includes('route'))?.long_name || ''

  result.street = `${streetNumber} ${route}`.trim()

  return result
}
