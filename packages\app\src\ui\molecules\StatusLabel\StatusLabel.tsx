import React from 'react'
import styled from 'styled-components'
import TooltipView from '../TooltipView'

export interface IStatusUIParams {
  label: string
  icon?: JSX.Element
  style?: any
  gap?: number
}

interface IStatusLabelProps {
  status: string
  tooltip?: string
  displayStatus?: string
  onPress?: () => void
  uiOptionsProvider: (status: string) => IStatusUIParams
}

export const StatusLabel = ({
  status,
  displayStatus,
  tooltip,
  onPress,
  uiOptionsProvider,
}: IStatusLabelProps): JSX.Element => {
  const uiParams = uiOptionsProvider(status)

  return (
    <TooltipView helper={tooltip}>
      <Wrapper
        uiParams={uiParams}
        testID={'invoice_status_badge'}
        onClick={onPress}
      >
        <Layout id={'status-label-container'} uiParams={uiParams}>
          <Icon id={'status-icon'}>{uiParams.icon}</Icon>
          {displayStatus || uiParams.label}
        </Layout>
      </Wrapper>
    </TooltipView>
  )
}

const Wrapper = styled.div`
  padding: 1px 0 1px 0;
  border-radius: 5px;
  font-size: 14px;
  line-height: 20px;
  font-weight: 600;
  font-family: 'Inter', sans-serif;
  ${(props: { uiParams: IStatusUIParams }) => {
    return props.uiParams?.style
  }}
`

const Layout = styled.div`
  display: flex;
  gap: 8px;
  height: 100%;
  flex-direction: row;
  justify-content: space-between;
  ${(props: { uiParams: IStatusUIParams }) => {
    return props.uiParams?.gap && { gap: props.uiParams?.gap }
  }}
`

const Icon = styled.div`
  flex-shrink: 0;
`
