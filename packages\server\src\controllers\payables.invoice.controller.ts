import {
  Company,
  createPagination,
  CustomerAccount,
  Invoice,
  LoanApplication,
  Operation,
  Transaction,
  User,
  UserRole,
} from '@linqpal/common-backend'
import {
  invoiceStatus,
  IPayable,
  OPERATION_STATUS,
  OPERATION_TYPES,
  PayablesInvoicesQuotesSort,
  PayablesInvoicesQuotesSortColumn,
  PayablesInvoicesQuotesSortColumnType,
} from '@linqpal/models/src/dictionaries'
import { Request } from 'express'
import moment from 'moment'
import { PipelineStage, Types } from 'mongoose'
import { projectInvoiceStatus } from './invoice.controller'
import {
  CompanyStatus,
  EInvoiceType,
  InvoicePaymentType,
} from '@linqpal/models'
import {
  DBSortingOrder,
  DBSortingOrderType,
  SortingOrder,
} from '@linqpal/models/src/dictionaries/global'
import { CustomerAccountType } from '@linqpal/models/src/dictionaries/customerAccountType'

const format = '%m/%d/%Y'
const timezone = 'America/Chicago'

export async function getPayablesInvoices(req: Request) {
  const builderId = req.company!._id.toString()

  const supplierId = req.query?.supplierId?.toString()
  const supplierIsInvited = req.query?.supplierIsInvited

  const statuses = (req.query.statuses || ['']) as string[]
  const types = req.query.types || [EInvoiceType.INVOICE]
  const search = req.query?.search?.toString() ?? ''
  const dateFrom = req.query?.dateFrom?.toString()
  const dateTo =
    req.query?.dateTo?.toString() || moment().tz(timezone).format('MM/DD/YYYY')
  const { paginationPipeline, pageSize } = createPagination(req.query)
  const paginationArr =
    req.query?.pageSize && req.query?.page ? paginationPipeline : []

  const sortColumn = req.query?.sortColumn
    ? req.query.sortColumn.toString()
    : PayablesInvoicesQuotesSort.CREATED_AT

  const sortDirection =
    req.query.sortDirection ||
    (sortColumn === PayablesInvoicesQuotesSort.CREATED_AT
      ? SortingOrder.DESC
      : SortingOrder.ASC)

  const userRoles = await UserRole.aggregate([
    { $match: { company_id: builderId } },
    {
      $lookup: {
        from: User.collection.name,
        as: 'user',
        localField: 'sub',
        foreignField: 'sub',
      },
    },
    { $unwind: '$user' },
  ])
  const matchConditions = userRoles.map(
    (userRole) => userRole.user.email || userRole.user.login,
  )

  const customerAccounts = await CustomerAccount.aggregate([
    {
      $match: {
        $or: [
          { email: { $in: matchConditions } },
          { phone: { $in: matchConditions } },
        ],
      },
    },
  ])

  const customerAccountIds = customerAccounts.map((ca) => ca._id.toString())

  const startDate = moment.utc(dateFrom, 'MM/DD/YYYY').startOf('day').toDate()
  const endDate = moment.utc(dateTo, 'MM/DD/YYYY').endOf('day').toDate()

  const pipeline: PipelineStage[] = [
    {
      $match: {
        $and: [
          { createdAt: { $gte: startDate } },
          { createdAt: { $lte: endDate } },
          {
            $or: [
              { customer_account_id: { $in: customerAccountIds } },
              { payer_id: builderId },
            ],
          },
          { type: { $in: types } },
          {
            $nor: [
              {
                type: EInvoiceType.QUOTE,
                'paymentDetails.paymentType': InvoicePaymentType.FACTORING,
              },
            ],
          },
          { isDeleted: { $ne: true } },
          {
            $or: [
              { status: { $ne: invoiceStatus.draft } },
              {
                $and: [
                  { status: { $eq: invoiceStatus.draft } },
                  {
                    $or: [
                      { company_id: null },
                      { company_id: { $exists: false } },
                      { company_id: '' },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    },
    {
      $lookup: {
        from: User.collection.name,
        let: {
          email: '$supplierInvitationDetails.email',
          company_id: '$company_id',
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $ne: ['$$email', ''] },
                  { $ne: ['$$email', null] },
                  { $eq: ['$email', '$$email'] },
                  { $eq: [{ $ifNull: ['$$company_id', ''] }, ''] }, // Only perform lookup if company_id is null/empty
                ],
              },
            },
          },
          {
            $lookup: {
              from: UserRole.collection.name,
              localField: 'sub',
              foreignField: 'sub',
              as: 'userRoleDetails',
            },
          },
          {
            $unwind: {
              path: '$userRoleDetails',
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $lookup: {
              from: Company.collection.name,
              let: {
                company_id: {
                  $convert: {
                    input: '$userRoleDetails.company_id',
                    to: 'objectId',
                    onError: null,
                    onNull: null,
                  },
                },
              },
              pipeline: [
                { $match: { $expr: { $eq: ['$_id', '$$company_id'] } } },
                { $match: { status: CompanyStatus.Approved } },
              ],
              as: 'companyDetails',
            },
          },
          {
            $project: {
              company_id: {
                $cond: {
                  if: { $gt: [{ $size: '$companyDetails' }, 0] },
                  then: '$userRoleDetails.company_id',
                  else: null,
                },
              },
            },
          },
        ],
        as: 'userDetails',
      },
    },
    {
      $unwind: {
        path: '$userDetails',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $addFields: {
        supplier_id: {
          $cond: {
            if: {
              $eq: [{ $ifNull: ['$company_id', ''] }, ''],
            }, // Only perform lookup if company_id is null/empty
            then: {
              $cond: {
                if: {
                  $or: [
                    {
                      $eq: [
                        { $ifNull: ['$supplierInvitationDetails.email', ''] },
                        '',
                      ],
                    },
                    { $eq: [{ $ifNull: ['$userDetails', null] }, null] },
                    {
                      $eq: [
                        { $ifNull: ['$userDetails.company_id', null] },
                        null,
                      ],
                    },
                    { $eq: [{ $ifNull: ['$userDetails.company_id', ''] }, ''] },
                  ],
                },
                then: {
                  $concat: [
                    {
                      $ifNull: [
                        { $trim: { input: '$supplierInvitationDetails.name' } },
                        '',
                      ],
                    },
                    '_',
                    {
                      $ifNull: [
                        {
                          $trim: { input: '$supplierInvitationDetails.email' },
                        },
                        '',
                      ],
                    },
                  ],
                },
                else: {
                  $convert: {
                    input: '$userDetails.company_id',
                    to: 'objectId',
                    onError: null,
                  },
                },
              },
            },
            else: {
              $convert: { input: '$company_id', to: 'objectId', onError: null }, // If company_id exists, convert it
            },
          },
        },
      },
    },
    {
      $lookup: {
        from: Company.collection.name,
        localField: 'supplier_id',
        foreignField: '_id',
        as: 'tempCompany',
        pipeline: [
          {
            $project: {
              draft: 0,
              credit: 0,
              finicity: 0,
              address: 0,
              bankAccounts: 0,
            },
          },
        ],
      },
    },
    {
      $addFields: {
        company: {
          $cond: {
            if: { $gt: [{ $size: '$tempCompany' }, 0] },
            then: { $arrayElemAt: ['$tempCompany', 0] },
            else: {
              _id: '$supplier_id',
              email: '$supplierInvitationDetails.email',
              name: '$supplierInvitationDetails.name',
              phone: '$supplierInvitationDetails.phone',
              isInvited: true,
            },
          },
        },
      },
    },
    {
      $addFields: {
        lateFee: {
          $cond: {
            if: {
              $eq: [
                '$company.settings.arAdvance.isLateInterestChargedToMerchant',
                false,
              ],
            },
            then: {
              $ifNull: ['$paymentDetails.fees', 0],
            },
            else: 0,
          },
        },
      },
    },
  ]

  if (supplierId) {
    if (Types.ObjectId.isValid(supplierId) && !supplierIsInvited) {
      pipeline.push({
        $match: {
          supplier_id: new Types.ObjectId(supplierId),
        },
      })
    } else {
      pipeline.push({
        $match: {
          supplier_id: supplierId.trim(),
        },
      })
    }
  }

  const additionalStages: PipelineStage[] = [
    {
      $lookup: {
        from: Operation.collection.name,
        as: 'operation',
        let: { invoice_id: { $toString: '$_id' } },
        pipeline: [
          { $match: { $expr: { $eq: ['$owner_id', '$$invoice_id'] } } },
          {
            $match: {
              $expr: {
                $or: [
                  {
                    $and: [
                      {
                        $in: [
                          '$status',
                          [
                            OPERATION_STATUS.PLACED,
                            OPERATION_STATUS.PROCESSING,
                            OPERATION_STATUS.SUCCESS,
                            OPERATION_STATUS.FAIL,
                          ],
                        ],
                      },
                      { $eq: ['$type', OPERATION_TYPES.INVOICE.PAYMENT] },
                    ],
                  },
                  {
                    $and: [
                      { $eq: ['$status', OPERATION_STATUS.SUCCESS] },
                      { $eq: ['$type', OPERATION_TYPES.INVOICE.REFUND] },
                    ],
                  },
                ],
              },
            },
          },
          { $sort: { createdAt: -1 } },
          { $limit: 1 },
        ],
      },
    },
    { $unwind: { path: '$operation', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: LoanApplication.collection.name,
        as: 'loanApp',
        let: { invoice_id: { $toString: '$_id' } },
        pipeline: [
          {
            $addFields: {
              ids: {
                $cond: {
                  if: {
                    $eq: [{ $type: '$invoiceDetails.invoiceId' }, 'array'],
                  },
                  then: '$invoiceDetails.invoiceId',
                  else: ['$invoiceDetails.invoiceId'],
                },
              },
            },
          },
          {
            $match: {
              $expr: {
                $in: ['$$invoice_id', '$ids'],
              },
            },
          },
          { $sort: { createdAt: -1 } },
          { $limit: 1 },
          {
            $project: {
              status: 1,
              lms_id: 1,
              metadata: 1,
            },
          },
        ],
      },
    },
    { $unwind: { path: '$loanApp', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: Invoice.collection.name,
        as: 'quote',
        let: {
          quoteId: {
            $convert: {
              input: '$quoteId',
              to: 'objectId',
              onError: null,
            },
          },
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $ne: ['$$quoteId', null] },
                  { $eq: ['$_id', '$$quoteId'] },
                ],
              },
            },
          },
          {
            $project: {
              status: 1,
            },
          },
        ],
      },
    },
    { $unwind: { path: '$quote', preserveNullAndEmptyArrays: true } },
    {
      $addFields: {
        operationStatus: { $ifNull: ['$operation.status', ''] },
        loanAppStatus: { $ifNull: ['$loanApp.status', ''] },
        quoteStatus: { $ifNull: ['$quote.status', ''] },
      },
    },
    {
      $lookup: {
        from: Transaction.collection.name,
        as: 'transaction',
        let: { operation_id: { $toString: '$operation._id' } },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [{ $eq: ['$operation_id', '$$operation_id'] }],
              },
            },
          },
          { $sort: { createdAt: 1 } },
          { $limit: 1 },
          {
            $project: {
              date: 1,
              amount: 1,
              metadata: 1,
              status: 1,
            },
          },
        ],
      },
    },
    { $unwind: { path: '$transaction', preserveNullAndEmptyArrays: true } },

    {
      $addFields: {
        invoiceStatus: projectInvoiceStatus(builderId, 'customer'), // Calculate the status field
        payment_date: {
          $cond: {
            if: {
              $in: [
                '$operationStatus',
                [
                  OPERATION_STATUS.SUCCESS,
                  OPERATION_STATUS.FAIL,
                  OPERATION_STATUS.PROCESSING,
                ],
              ],
            },
            then: { $ifNull: ['$transaction.date', '$operation.updatedAt'] },
            else: null,
          },
        },
      },
    },
    ...(statuses?.length && statuses[0]
      ? [
          {
            $match: {
              invoiceStatus: { $in: statuses },
            },
          },
        ]
      : []),
    {
      $addFields: {
        totalPaidAmount: {
          $cond: [
            { $ifNull: ['$operation.paidAmount', false] },
            '$operation.paidAmount',
            {
              $cond: [
                { $eq: ['$operation.status', OPERATION_STATUS.SUCCESS] },
                '$operation.amount',
                0,
              ],
            },
          ],
        },
      },
    },
    {
      $addFields: {
        totalProcessingAmount: {
          $cond: [
            { $ifNull: ['$operation.processingAmount', false] },
            '$operation.processingAmount',
            {
              $cond: [
                {
                  $and: [
                    {
                      $eq: ['$operation.status', OPERATION_STATUS.PROCESSING],
                    },
                    { $eq: ['$totalPaidAmount', 0] },
                  ],
                },
                '$operation.amount',
                0,
              ],
            },
          ],
        },
      },
    },
    {
      $addFields: {
        totalRemainingAmount: {
          $round: [
            {
              $subtract: [
                {
                  $round: [
                    {
                      $add: [
                        { $ifNull: ['$operation.amount', '$total_amount'] },
                        { $ifNull: ['$lateFee', 0] },
                        { $ifNull: ['$paymentDetails.customerFee', 0] },
                      ],
                    },
                    2,
                  ],
                },
                {
                  $round: [
                    {
                      $add: [
                        { $ifNull: ['$totalPaidAmount', 0] },
                        { $ifNull: ['$totalProcessingAmount', 0] },
                      ],
                    },
                    2,
                  ],
                },
              ],
            },
            2,
          ],
        },
        totalAmountDue: {
          $round: [
            {
              $add: [
                { $ifNull: ['$total_amount', 0] },
                { $ifNull: ['$lateFee', 0] },
                { $ifNull: ['$paymentDetails.customerFee', 0] },
              ],
            },
            2,
          ],
        },
      },
    },

    {
      $project: {
        _id: 1,
        invoice_date: {
          $dateToString: { date: '$invoice_date', format, timezone },
        },
        invoice_due_date: {
          $dateToString: { date: '$invoice_due_date', format, timezone },
        },
        expiration_date: {
          $dateToString: { date: '$expiration_date', format, timezone },
        },
        authorization_deadline: {
          $dateToString: {
            date: {
              $ifNull: ['$quoteDetails.authorization_deadline', null],
            },
            format,
            timezone,
          },
        },
        payer_id: 1,
        invoice_number: 1,
        type: 1,
        total_amount: 1,
        createdAt: 1,
        invoiceStatus: 1,
        payment_date: 1,
        customerFee: '$paymentDetails.customerFee',
        paymentType: '$paymentDetails.paymentType',
        lateFee: 1,
        loanAppStatus: 1,
        customer_account_id: 1,
        quoteStatus: 1,
        supplierInvitationDetails: {
          paymentMethodId: 1,
          email: 1,
          name: 1,
        },
        approved: { $ifNull: ['$approved', true] },
        company: {
          _id: 1,
          name: 1,
          phone: 1,
          isInvited: { $ifNull: ['$company.isInvited', false] },
        },
        totalPaidAmount: 1,
        totalProcessingAmount: 1,
        totalRemainingAmount: 1,
        totalAmountDue: 1,
      },
    },
  ]

  pipeline.push(...additionalStages)

  if (search) {
    pipeline.unshift({
      $match: {
        invoice_number: { $regex: search, $options: 'i' },
      },
    })
  }

  pipeline.push({
    $sort: {
      [PayablesInvoicesQuotesSortColumn[
        sortColumn as PayablesInvoicesQuotesSortColumnType
      ]]: DBSortingOrder[sortDirection as DBSortingOrderType],
    },
  })

  pipeline.push({
    $facet: {
      totalCount: [
        {
          $count: 'count',
        },
      ],
      paginatedResults: paginationArr,
    },
  })

  const [invoiceAggregation] = await Invoice.aggregate(pipeline)

  if (!invoiceAggregation?.paginatedResults?.length) {
    return {
      items: [],
      totalCount: 0,
    }
  }

  invoiceAggregation.paginatedResults = invoiceAggregation.paginatedResults.map(
    (item: IPayable) => {
      const customerAccount = customerAccounts.find(
        (account) =>
          account._id.toString() === item.customer_account_id.toString(),
      )

      const customerType = customerAccount
        ? customerAccount.type
        : CustomerAccountType.TradeCredit

      const allowedForGroupPayment = supplierIsInvited ? true : item.approved

      return {
        ...item,
        customerType,
        allowedForGroupPayment,
      }
    },
  )

  const payables = invoiceAggregation.paginatedResults

  const responseData: { items: IPayable[]; totalCount: number } = {
    items: payables,
    totalCount:
      pageSize > 0
        ? invoiceAggregation.totalCount[0].count
        : invoiceAggregation.paginatedResults.length,
  }

  return responseData
}
