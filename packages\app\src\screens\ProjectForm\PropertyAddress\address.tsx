import React, { useState } from 'react'
import { StyleSheet, View } from 'react-native'
import {
  BtGoogleAutocomplete,
  BtInput,
  BtNumberInput,
  BtSearchDropdown,
} from '@linqpal/components/src/ui'
import { useTranslation } from 'react-i18next'
import { FullAddressModel } from '@linqpal/models'
import { observer } from 'mobx-react'
import { statesHashMap } from '@linqpal/models/src/dictionaries'
import { validateZip } from '@linqpal/models/src/helpers/validations'
import { composeStyle } from '@linqpal/common-frontend/src/helpers'

const Address = observer(({ model, value }) => {
  const { t } = useTranslation('global')
  const [expanded, setExpanded] = useState(false)
  const [selectedFromGoogleAddress, setSelectedFromGoogleAddress] =
    useState(false)

  const onSelect = (val) => {
    const { address, city, state, zip } = val
    setSelectedFromGoogleAddress(true)
    model.setModelValue('streetAddress', address)
    model.setModelValue('city', city)
    model.setModelValue('state', state)
    model.setModelValue('zip', zip ?? '')
    if (validateZip(zip) && city) {
      setExpanded(false)
    }
  }
  const onChange = (field, val) => {
    model.setModelValue(field, val)
  }
  const getFullAddress = () => {
    const { streetAddress, city, state, zip } = value
    if (streetAddress && city && state && zip)
      return `${streetAddress}, ${city}, ${state}, ${zip}`
    else return ''
  }

  return (
    <>
      <BtGoogleAutocomplete
        value={expanded ? value.streetAddress : getFullAddress()}
        onSelect={onSelect}
        onChange={(text: string) => model.setModelValue('streetAddress', text)}
        label={t('ProjectForm.street-address')}
        testID="streetAddress"
        onFocus={() => setExpanded(true)}
        mandatory
      />
      {expanded && (
        <>
          <BtSearchDropdown
            options={Object.keys(statesHashMap)}
            mandatory
            onSelect={(val) => onChange('state', val)}
            value={value?.state}
            label={t('ProjectForm.state')}
            containerStyle={{ marginTop: 20 }}
          />
          <View style={styles.row}>
            <BtNumberInput
              size="large"
              value={value?.zip}
              onChangeText={(val) => onChange('zip', val)}
              style={composeStyle(
                { width: '48%' },
                selectedFromGoogleAddress &&
                  !validateZip(value?.zip) && { borderColor: 'red' },
              )}
              label={t('ProjectForm.zip')}
              testID="zip"
              validate={() =>
                value?.zip.length === 5 ? '' : t('ProjectForm.invalid-zip')
              }
              format={'#####'}
              required
            />
            <BtInput
              size="large"
              value={value.city}
              onChangeText={(val) => onChange('city', val)}
              style={composeStyle(
                { width: '48%' },
                selectedFromGoogleAddress &&
                  !value.city && { borderColor: 'red' },
              )}
              label={t('ProjectForm.city')}
              testID="city"
              mandatory
            />
          </View>
        </>
      )}
      <BtNumberInput
        size="large"
        value={value?.unitNum}
        onChangeText={(val) => onChange('unitNum', val)}
        style={{ marginTop: 20 }}
        label={t('ProjectForm.unit-num')}
        testID="unitNumber"
        format={'#####'}
      />
      <View style={styles.row}>
        <BtNumberInput
          size="large"
          value={value?.houseNum}
          onChangeText={(val) => onChange('houseNum', val)}
          style={{ width: '48%' }}
          label={t('ProjectForm.house-num')}
          format="#########"
          testID="houseNumber"
        />
        <BtInput
          value={value.subdivision}
          onChangeText={(val) => onChange('subdivision', val)}
          style={{ width: '48%' }}
          label={t('ProjectForm.subdivision')}
          testID="subdivision"
          size="large"
        />
      </View>
      <View style={styles.row}>
        <BtNumberInput
          size="large"
          value={value?.lotNum}
          onChangeText={(val) => onChange('lotNum', val)}
          style={{ width: '48%' }}
          label={t('ProjectForm.lot-num')}
          format="#########"
          testID="lotNumber"
        />
        <BtNumberInput
          size="large"
          value={value.blockNum}
          onChangeText={(val) => onChange('blockNum', val)}
          style={{ width: '48%' }}
          label={t('ProjectForm.block-num')}
          format="#########"
          testID="blockNum"
        />
      </View>
    </>
  )
})

export default {
  identifier: 'address',
  title: '',
  component: Address,
  Model: FullAddressModel,
  shouldProcessAnswer: true,
}

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginTop: 20,
  },
})
