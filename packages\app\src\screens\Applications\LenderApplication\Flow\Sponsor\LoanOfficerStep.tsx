import React, { FC } from 'react'
import { BtInput } from '@linqpal/components/src/ui'
import { observer } from 'mobx-react-lite'
import { useTranslation } from 'react-i18next'
import { runInAction } from 'mobx'
import { useLenderApplication } from '../../LenderApplicationContext'
import { ILenderApplicationEditor } from '../getLenderApplicationEditor'

const LoanOfficerEditor: FC = observer(() => {
  const { t } = useTranslation('application')
  const store = useLenderApplication()

  const handleChange = (value: string) => {
    runInAction(() => (store.draft.data.sponsor.loanOfficer = value))
  }

  return (
    <>
      <BtInput
        value={store.draft.data.sponsor.loanOfficer || ''}
        onChangeText={handleChange}
        label={t('LenderApplication.Flow.Sponsor.LoanOfficer.LoanOfficerLabel')}
        testID="LenderApplication.BusinessInfo.Email"
      />
    </>
  )
})

export const LoanOfficerStep: ILenderApplicationEditor = {
  options: {
    title: 'LenderApplication.Flow.Sponsor.LoanOfficer.Title',
    description: 'LenderApplication.Flow.Sponsor.LoanOfficer.Description',
  },
  component: LoanOfficerEditor,
}
