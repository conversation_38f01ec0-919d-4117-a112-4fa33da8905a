import { PipelineStage } from 'mongoose'
import { BankAccount } from '../../models'

export const getPrimaryAccountQuery: PipelineStage[] = [
  {
    $lookup: {
      from: BankAccount.collection.name,
      as: 'primaryAccount',
      let: {
        ids: { $ifNull: ['$customer.bankAccounts', []] },
      },
      pipeline: [
        {
          $match: {
            $and: [
              {
                $expr: {
                  $in: ['$_id', '$$ids'],
                },
              },
              {
                $expr: {
                  $in: ['$status', ['verified', 'manualverified']],
                },
              },
              {
                isDeactivated: { $in: [false, null] },
              },
              {
                $or: [
                  { isPrimaryForCredit: true },
                  {
                    $and: [
                      {
                        $or: [
                          { isPrimaryForCredit: null },
                          { isPrimaryForCredit: { $exists: false } },
                        ],
                      },
                      { isPrimary: true },
                    ],
                  },
                ],
              },
            ],
          },
        },
      ],
    },
  },
  { $set: { primaryAccount: { $last: '$primaryAccount' } } },
]
