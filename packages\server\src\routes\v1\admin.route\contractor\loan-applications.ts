import {
  AgreementService,
  AwsService,
  BankAccount,
  Company,
  CompanyService,
  crypt,
  CustomerAccount,
  DocumentVersioningService,
  Draft,
  ExperianRequest,
  FinicityTransactions,
  LexisService,
  LoanApplication,
  LoanPaymentPlan,
  Project,
  User,
  UserRole,
} from '@linqpal/common-backend'
import {
  IInvoice,
  ILoanApplication,
  IUser,
} from '@linqpal/common-backend/src/models/types'
import { notifyUser } from '@linqpal/common-backend/src/services/loan/creditStatusNotification'
import { cancelLoanApplication as cancelLoanApp } from '@linqpal/common-backend/src/services/loan/cancelLoanApplication'
import { exceptions } from '@linqpal/models'
import {
  customerStatus,
  LOAN_APPLICATION_STATUS,
  LOAN_PLAN_TYPE,
} from '@linqpal/models/src/dictionaries'
import md5 from 'crypto-js/md5'
import sha512 from 'crypto-js/sha512'
import { NextFunction, Request, Response } from 'express'
import isArray from 'lodash/isArray'
import isFinite from 'lodash/isFinite'
import isNumber from 'lodash/isNumber'
import isObjectLike from 'lodash/isObjectLike'
import isString from 'lodash/isString'
import moment from 'moment-timezone'
import mongoose, { PipelineStage, Types } from 'mongoose'
import { v4 } from 'uuid'
import xlsx from 'xlsx'
import controllers from '../../../../controllers'
import { S3AccessType } from '@linqpal/common-backend/src/services/aws.service'
import { AgreementType } from '@linqpal/common-backend/src/services/agreement/types'
import { LogicalError } from '@linqpal/models/src/types/exceptions'
import { fetchTransactions } from '@linqpal/common-backend/src/services/plaid/plaid.service'
import PersonalGuarantorAgreement from '@linqpal/common-backend/src/services/agreement/personalGuarantorAgreement'
import { LOAN_APPLICATION_TYPE } from '@linqpal/models/src/dictionaries/loanApplicationTypes'
import { compatibilityService } from '@linqpal/common-backend/src/services/compatibility/compatibility.service'
import {
  findCustomerByCompanyId,
  upsertCustomers,
} from '@linqpal/common-backend/src/services/customer/customer-status.service'
import { QuoteService } from '@linqpal/common-backend/src/services/quote/quote.service'
import { CreditAgreementsService } from '@linqpal/common-backend/src/services/agreement/creditAgreementsService'

const { POLICY } = LexisService.constants

interface AggLoanAppParams {
  status: string
  kind: string
  search: string
  page: number
  pageSize: number
}

async function aggLoanApplications(params: AggLoanAppParams) {
  const { status, kind, search, page, pageSize } = params

  let agg = LoanApplication.aggregate()
  agg = agg.sort({ submitDate: -1, createdAt: -1 })
  agg = agg.addFields({
    company_id: { $toObjectId: '$company_id' },
  })
  agg = agg.lookup({
    from: Company.collection.name,
    localField: 'company_id',
    foreignField: '_id',
    as: 'company',
  })
  if (search) {
    const orArray: mongoose.FilterQuery<any>[] = [
      {
        'draft.businessInfo_businessName': { $regex: search, $options: 'i' },
      },
      {
        'draft.businessInfo_legalName': { $regex: search, $options: 'i' },
      },
      {
        'draft.businessInfo_businessName.legalName': {
          $regex: search,
          $options: 'i',
        },
      },
      {
        'draft.businessInfo_businessName.dba': {
          $regex: search,
          $options: 'i',
        },
      },
      {
        'company.name': { $regex: search, $options: 'i' },
      },
      {
        'company.legalName': { $regex: search, $options: 'i' },
      },
    ]

    // The length of the ObjectId in hex in MongoDB is 24 characters
    if (search.length === 24) {
      orArray.push({
        _id: new mongoose.Types.ObjectId(search),
      })
    }

    agg = agg.match({
      $or: orArray,
    })
  }

  if (status === LOAN_APPLICATION_STATUS.PENDING) {
    agg = agg.match({
      $or: [
        {
          status: {
            $in: [
              LOAN_APPLICATION_STATUS.PENDING,
              LOAN_APPLICATION_STATUS.ERROR,
            ],
          },
        },
        {
          $and: [
            { status: LOAN_APPLICATION_STATUS.PROCESSING },
            {
              $or: [
                {
                  // auto trade credit waiting for approval - no loan yet
                  $and: [
                    { 'metadata.repayment.autoTradeCreditEnabled': true },
                    { lms_id: null },
                    { lms_id: { $exists: false } },
                  ],
                },
                { 'metadata.repayment.autoTradeCreditEnabled': false },
                {
                  'metadata.repayment.autoTradeCreditEnabled': {
                    $exists: false,
                  },
                },
              ],
            },
          ],
        },
      ],
    })
  } else if (status === LOAN_APPLICATION_STATUS.APPROVED) {
    agg = agg.match({
      $or: [
        {
          status: {
            $in: [
              LOAN_APPLICATION_STATUS.APPROVED,
              LOAN_APPLICATION_STATUS.AUTHORIZED,
              LOAN_APPLICATION_STATUS.CLOSED,
            ],
          },
        },
        {
          // TODO: VK: Introduce new loan app's PENDING_DISBURSEMENT status for ATC version #2
          $and: [
            { status: LOAN_APPLICATION_STATUS.PROCESSING },
            { lms_id: { $exists: true, $ne: null } },
            { 'metadata.repayment.autoTradeCreditEnabled': true },
          ],
        },
      ],
    })
  } else if (status === 'other') {
    agg = agg.match({
      status: {
        $nin: [
          LOAN_APPLICATION_STATUS.PENDING,
          LOAN_APPLICATION_STATUS.ERROR,
          LOAN_APPLICATION_STATUS.PROCESSING,
          LOAN_APPLICATION_STATUS.AUTHORIZED,
          LOAN_APPLICATION_STATUS.APPROVED,
          LOAN_APPLICATION_STATUS.REJECTED,
          LOAN_APPLICATION_STATUS.CLOSED,
        ],
      },
    })
  } else if (status) {
    agg = agg.match({ status })
  }

  if (kind === 'loan' || kind === 'vc') {
    agg = agg.match({
      $and: [
        { 'invoice_id.length': { $ne: 0 } },
        { 'metadata.paymentPlan': { $exists: true } },
        {
          'metadata.paymentPlan.type':
            kind === 'loan'
              ? { $ne: LOAN_PLAN_TYPE.VIRTUAL_CARD }
              : { $eq: LOAN_PLAN_TYPE.VIRTUAL_CARD },
        },
        { 'metadata.repayment.autoTradeCreditEnabled': { $ne: true } },
      ],
    })
  } else if (kind === 'prequal') {
    agg = agg.match({
      invoiceDetails: { $exists: false },
    })
  } else if (kind === 'automated') {
    agg = agg.match({
      'metadata.repayment.autoTradeCreditEnabled': true,
    })
  } else if (kind === 'authorizations') {
    agg = agg.match({
      type: LOAN_APPLICATION_TYPE.QUOTE,
    })
  }

  const _total = await LoanApplication.aggregate(agg.pipeline()).count('total')
  const { total = 0 }: { total: number } = _total?.[0] || {}

  if (page !== -1) agg = agg.skip((page - 1) * pageSize)
  if (pageSize !== -1) agg = agg.limit(pageSize)

  agg = agg
    .addFields({
      invoiceIds: {
        $cond: {
          if: { $eq: [{ $type: '$invoiceDetails.invoiceId' }, 'array'] },
          then: '$invoiceDetails.invoiceId',
          else: ['$invoiceDetails.invoiceId'],
        },
      },
    })
    .lookup({
      from: 'invoices',
      as: 'invoices',
      let: {
        invoices_id: '$invoiceIds',
      },
      pipeline: [
        { $addFields: { invoice_id: '$$invoices_id' } },
        { $unwind: '$invoice_id' },
        {
          $addFields: {
            objectId: {
              $convert: {
                input: '$invoice_id',
                to: 'objectId',
                onError: null,
              },
            },
          },
        },
        { $match: { $expr: { $eq: ['$_id', '$objectId'] } } },
        {
          $lookup: {
            from: 'invoices',
            as: 'quote',
            let: {
              quoteId: {
                input: '$quoteId',
                to: 'objectId',
                onError: null,
              },
            },
            pipeline: [
              {
                $match: {
                  $expr: { $eq: ['$_id', '$$quoteId'] },
                },
              },
            ],
          },
        },
        { $unwind: { path: '$quote', preserveNullAndEmptyArrays: true } },
      ],
    })
    .addFields({
      invoice: { $first: '$invoices' },
    })
    .unwind('$company')
    .addFields({
      invitedByComp: {
        $convert: {
          input: '$company.settings.invitedBy',
          to: 'objectId',
          onError: null,
        },
      },
    })
    .lookup({
      from: Company.collection.name,
      localField: 'invitedByComp',
      foreignField: '_id',
      pipeline: [
        {
          $project: {
            name: 1,
            legalName: 1,
          },
        },
      ],
      as: 'invitedBy',
    })
    .unwind({ path: '$invitedBy', preserveNullAndEmptyArrays: true })
    .addFields({
      ppId: {
        $convert: {
          input: '$invoiceDetails.paymentPlan',
          to: 'objectId',
          onError: [],
        },
      },
    })
    .lookup({
      from: LoanPaymentPlan.collection.name,
      localField: 'ppId',
      foreignField: '_id',
      as: 'pp',
    })
    .unwind({ path: '$pp', preserveNullAndEmptyArrays: true })
    .addFields({
      bankId: {
        $convert: {
          input: '$draft.bank_details._id',
          to: 'objectId',
          onError: null,
        },
      },
    })
    .lookup({
      from: BankAccount.collection.name,
      as: 'bankSelectedForLoan',
      localField: 'bankId',
      foreignField: '_id',
    })
    .unwind({ path: '$bankSelectedForLoan', preserveNullAndEmptyArrays: true })
    .lookup({
      from: Project.collection.name,
      localField: 'invoice.project_id',
      foreignField: '_id',
      as: 'project',
    })
    .unwind({ path: '$project', preserveNullAndEmptyArrays: true })

  const company_agg = Company.aggregate()
    .match({ $expr: { $eq: ['$_id', '$$company_id'] } })
    .project({ name: 1 })
    .lookup({
      from: UserRole.collection.name,
      as: 'owners',
      let: { company_id: { $toString: '$_id' } },
      pipeline: UserRole.aggregate()
        .match({
          $expr: {
            $and: [
              { $eq: ['$company_id', '$$company_id'] },
              { $eq: ['$role', 'Owner'] },
              { $eq: ['$status', 'Active'] },
            ],
          },
        })
        .lookup({
          from: User.collection.name,
          as: 'user',
          localField: 'sub',
          foreignField: 'sub',
        })
        .unwind('user')
        .project({
          'user.firstName': 1,
          'user.lastName': 1,
          'user.phone': 1,
          'user.email': 1,
        })
        .replaceRoot('$user')
        .pipeline() as Exclude<
        PipelineStage,
        PipelineStage.Merge | PipelineStage.Out | PipelineStage.Search
      >[],
    })
    .project({ name: 1, phone: 1, email: 1, owners: 1 })

  agg = agg
    .addFields({
      'invoice.customer_account_id': {
        $convert: {
          input: '$invoice.customer_account_id',
          to: 'objectId',
          onError: null,
        },
      },
    })
    .lookup({
      from: Company.collection.name,
      as: 'invoice.company',
      let: {
        company_id: {
          $convert: {
            input: '$invoice.company_id',
            to: 'objectId',
            onError: null,
          },
        },
      },
      pipeline: company_agg.pipeline() as Exclude<
        PipelineStage,
        PipelineStage.Merge | PipelineStage.Out | PipelineStage.Search
      >[],
    })
    .unwind({ path: '$invoice.company', preserveNullAndEmptyArrays: true })
    .lookup({
      from: CustomerAccount.collection.name,
      as: 'invoice.customer',
      localField: 'invoice.customer_account_id',
      foreignField: '_id',
    })
    .unwind({
      path: '$invoice.customer',
      preserveNullAndEmptyArrays: true,
    })

  let items = total === 0 ? [] : await LoanApplication.aggregate(agg.pipeline())
  items.forEach(
    (item) =>
      item.invoice &&
      item.invoices?.forEach((invoice: IInvoice) => {
        invoice.company = item.invoice.company
        invoice.customer = item.invoice.customer
      }),
  )
  if (page === -1) {
    items = await Promise.all(
      items.map(async (item) => {
        if (!['new'].includes(item.status) && !item.draft?.normalized) {
          const draft = await CompanyService.getLoanApplicationDraft(
            item.company_id,
          )
          await LoanApplication.updateOne(
            { _id: item._id },
            { draft: { ...draft, normalized: true } },
          )
          item.draft = draft
        }
        return item
      }),
    )
  }

  const baseUrl = process.env.NEW_BACK_OFFICE_URL

  items = items.map((item) => ({
    ...item,
    creditApplicationNewBackOfficeURL: item.creditApplicationId
      ? new URL(`line-of-credit/${item.creditApplicationId}/detailed`, baseUrl)
      : undefined,
    drawApprovalNewBackOfficeURL: item.drawApprovalId
      ? new URL(`draw-application/${item.drawApprovalId}/detailed`, baseUrl)
      : undefined,
  }))

  return { items, total }
}

async function loanApplications(req: Request, res: Response) {
  const { items, total } = await aggLoanApplications({
    status: (req.query?.status as string) || '',
    kind: (req.query?.kind as string) || '',
    search: ((req.query?.search as string) || '').trim(),
    page: parseInt((req.query.page as string) || '1'),
    pageSize: parseInt((req.query.pageSize as string) || '20'),
  })

  res.send({ items, total })
}

function loanAppToCSV(item: any) {
  return {
    id: item.id || item._id,
    company: item.company?.name,
    status: item.status,
    createdAt: item.createdAt,
    finicityAccountAdded: item.finicityAccountAdded ? 'Yes' : 'No',
    submitDate: item.submitDate,
    decisionDate: item.decisionDate || item.issueDate,
    decisionTime: moment(item.decisionDate || item.issueDate).diff(
      item.submitDate,
      'hours',
      true,
    ),
    approvedAmount: item.approvedAmount,
    ...Object.entries<any>({ ...item.draft }).reduce(
      (obj: any, [key, value]) => {
        if (isString(value) || isNumber(value)) {
          obj[`draft_${key}`] = value
        } else if (isArray(value)) {
          obj[`draft_${key}`] = value
            .filter((v) => isString(v) || isNumber(v))
            .join(', ')
        } else if (isObjectLike(value)) {
          if (
            key.toLowerCase().includes('ein') ||
            key.toLowerCase().includes('ssn')
          ) {
            obj[`draft_${key}`] = value.display
          } else {
            Object.entries(value).forEach(([k, v]) => {
              if (isString(v) || isNumber(v)) {
                obj[`draft_${key}_${k}`] = v
              }
            })
          }
        }
        return obj
      },
      {},
    ),
  }
}

export async function exportLoanApplications(req: Request, res: Response) {
  const { items } = await aggLoanApplications({
    status: (req.query?.status as string) || '',
    kind: (req.query?.kind as string) || '',
    search: ((req.query?.search as string) || '').trim(),
    page: -1,
    pageSize: -1,
  })

  const file = xlsx.utils.sheet_to_csv(
    xlsx.utils.json_to_sheet(items.map(loanAppToCSV)),
  )

  const filename = `loan-applications-${moment().unix()}.csv`
  const bucket = `${process.env.LP_MODE}.uw1.linqpal-temp-assets`

  await AwsService.putS3File(bucket, filename, file, 'us-west-1')

  res.send({
    downloadLink: await AwsService.getPreSignedUrl({
      key: filename,
      method: 'get',
      bucket,
    }),
  })
}

export const processLoanApplication = {
  async post(req: Request, res: Response, next: NextFunction) {
    const item = await LoanApplication.findById(req.body._id)
    if (!item) return next()

    try {
      await compatibilityService.runDotNetDEForLineOfCredit(item.id)
    } catch (e) {
      console.log('Error happened during new Credit Application DE running', e)
    }

    try {
      await compatibilityService.runDotNetDEForDrawApplication(item.id, null)
    } catch (e) {
      console.log('Error happened during new Draw Approval DE running', e)
    }

    res.send({ id: item._id })
  },
}

export const approveLoanApplication = {
  async post(req: Request, res: Response, next: NextFunction) {
    const amount = parseFloat(req.body.amount)

    const app = await LoanApplication.findById(req.body._id)
    if (!app) return next()

    if (
      [
        LOAN_APPLICATION_STATUS.EXPIRED,
        LOAN_APPLICATION_STATUS.REJECTED,
        LOAN_APPLICATION_STATUS.CANCELED,
      ].includes(app.status as any)
    ) {
      throw new LogicalError(`Unable to approve app in status ${app.status}`)
    }

    if (!(isFinite(amount) && amount > 0 && amount < 1e9)) {
      throw new LogicalError('Amount should be in range of 1 and 1B')
    }

    const { step, action } = app.progress || {}
    if (step === 'HumanApproval' && action) {
      await AwsService.resumeStepFunction(action, {
        applicationId: app.id,
        loanType: app.type,
        decision: { approvedAmount: amount, status: 'approved' },
      })
    }

    app.approvedBy = req.user?.id.toString() ?? 'unknown'
    await app.save()

    const customer = (await findCustomerByCompanyId(app.company_id))[0]

    if (customer) {
      if (customer.status !== customerStatus.tradeCredit) {
        const customerId = new Types.ObjectId(customer._id.toString())
        await upsertCustomers(customerStatus.tradeCredit, [customerId])
      }
    }

    await addNoteLoanApplication(app, req.user!, req.body.note)

    res.send({ id: app._id })
  },
}

export const resumeDecisionEngine = {
  async post(req: Request, res: Response, next: NextFunction) {
    const item = await LoanApplication.findById(req.body._id)
    if (!item) return next()
    const { output } = req.body
    if (output.status === 'error') {
      try {
        await AwsService.cancelStepFunction(item.executionArn)
      } catch (e) {
        console.log(e)
      } finally {
        item.executionArn = ''
        item.status = LOAN_APPLICATION_STATUS.ERROR
        item.progress = { step: '', action: '', error: '' }
        item.decisionDate = moment().toDate()
        await item.save()
      }
    } else {
      const { action } = item.progress || {}
      if (action) {
        try {
          await AwsService.resumeStepFunction(action, {
            applicationId: item.id,
            decision: req.body.output,
          })
        } catch (e: any) {
          if (e.name === 'TaskTimedOut') {
            item.executionArn = ''
            item.progress = { step: '', action: '', error: '' }
            item.decisionDate = moment().toDate()
            switch (output.status) {
              case 'rejected':
                item.status = LOAN_APPLICATION_STATUS.REJECTED
                break
              default:
                item.status = LOAN_APPLICATION_STATUS.ERROR
            }
            await item.save()
          } else {
            console.log(e)
          }
        }
      }
    }
    res.send({ id: item._id })
  },
}

export const cancelLoanApplication = {
  async post(req: Request, res: Response, next: NextFunction) {
    const item = await cancelLoanApp(req.body._id, null, true)
    if (!item) return next()
    res.send({ id: item._id })
  },
}

export const sendBackApplication = {
  async post(req: Request, res: Response, next: NextFunction) {
    const app = await LoanApplication.findById(req.body.id)
    if (!app) return next()

    if (app.executionArn) {
      try {
        await AwsService.cancelStepFunction(app.executionArn)
      } catch (e) {
        console.log(e)
      }
    }

    app.executionArn = ''
    app.status = LOAN_APPLICATION_STATUS.NEW
    app.isSentBack = true
    app.progress = { step: '', action: '', error: '' }

    await Promise.all([
      app.save(),
      QuoteService.handleQuoteApplicationSendBack(app, req.session),
    ])

    await app.save()
    await notifyUser(app)

    res.send({ id: app._id })
  },
}

export const uploadFinicityData = {
  async post(req: Request, res: Response) {
    const {
      company_id,
      data,
    }: {
      company_id: string
      data: {
        transactionId: string
        transactionDate: number | string
        amount: number | string
        balance: number | string
      }[]
    } = req.body
    console.log(data)
    await FinicityTransactions.deleteMany({ company_id }).exec()
    const transactions = data.map(
      ({ transactionId, transactionDate, amount, balance }) => {
        return {
          company_id,
          transactionId: transactionId || v4(),
          transactionDate: parseInt(transactionDate as string),
          amount: parseFloat(amount as string),
          balance: parseFloat(balance as string),
        }
      },
    )
    await FinicityTransactions.insertMany(transactions)
    res.send({})
  },
}

export const changeLoanApplicationPaymentPlan = {
  async post(req: Request, res: Response, next: NextFunction) {
    await AwsService.executeStateMachine({
      name: 'loan-app-change-payment-plan',
      data: { app_id: req.body._id, data: req.body },
    })
    res.locals.result = { id: req.body._id }
    return next()
  },
}

export const getFinicityTransactions = {
  async get(req: Request, res: Response) {
    const { company_id, download } = req.query
    const company = await Company.findById(company_id).populate('bankAccounts')
    const customerId = company?.finicity?.customerId
    const match = customerId
      ? { customerId: parseInt(customerId) }
      : { company_id }
    let agg = FinicityTransactions.aggregate().match(match)
    if (download !== 'true')
      agg = agg.project({
        transactionDate: 1,
        amount: 1,
        balance: 1,
        description: 1,
        status: 1,
        accountId: 1,
      })
    let transactions = await agg.sort({ transactionDate: -1 }).exec()
    transactions =
      transactions.length > 0
        ? transactions
        : await FinicityTransactions.find({ company_id })
            .sort({ transactionDate: -1 })
            .exec()

    const accounts_id = new Set(transactions.map((t) => t.accountId))
    const bankAccounts =
      company?.bankAccounts?.filter((a) =>
        accounts_id.has(parseInt(a.finicity?.accountId || '0')),
      ) || []

    res.send({ transactions, bankAccounts })
  },
}

export const getPlaidTransactions = {
  async get(req: Request, res: Response) {
    const { bankId } = req.query
    const bank = await BankAccount.findById(bankId)
    const token = await crypt.decrypt(bank?.plaid?.access_token.cipher)
    const today = moment().format('YYYY-MM-DD')
    const from = moment().subtract(4, 'months').format('YYYY-MM-DD')
    const { transactions, accounts } = await fetchTransactions(
      req.user?.id,
      token,
      {
        startDate: from,
        endDate: today,
        count: 200,
        offset: 0,
      },
    )
    const accs = await Promise.all(
      accounts.map((acc) =>
        BankAccount.findOne({ 'plaid.account_id': acc.account_id }),
      ),
    )
    res.send({
      transactions,
      plaidAccounts: accounts,
      bankAccounts: accs,
    })
  },
}

export const getBankAccounts = {
  async get(req: Request, res: Response) {
    const { companyId } = req.query
    const company = await Company.findById(companyId).populate('bankAccounts')
    const plaidBanks = company?.bankAccounts?.filter(
      (b) => !!b.plaid?.access_token,
    )
    res.send({
      plaidBanks,
    })
  },
}

async function getSSN(company_id: string) {
  const company = await Company.findById(company_id).exec()
  const doc = await Draft.findOne({
    company_id: company?.id,
    type: mongoose.trusted({
      $in: ['general_application'],
    }),
  }).exec()

  const personalInfo =
    doc?.data.get('businessOwner') || doc?.data.get('personalInfo')
  const ssn =
    personalInfo?.items.find((item) => item.identifier === 'ssn')?.content || ''

  return ssn?.cipher ? crypt.decrypt(ssn.cipher) : ssn
}

export const lexisNexisInstantId = {
  async get(req: Request, res: Response) {
    const company = await Company.findById(req.query.company_id).exec()
    const doc = await Draft.findOne({
      company_id: company?.id,
      type: mongoose.trusted({
        $in: ['general_application'],
      }),
    }).exec()

    const ein = doc?.data
      .get('businessInfo')
      ?.items.find((i) => i.identifier === 'ein')?.content
    const businessFein = ein?.cipher ? await crypt.decrypt(ein.cipher) : ein

    const data = await LexisService.db.getItem(
      businessFein,
      POLICY.BUSINESS_INSTANT_ID,
      moment().subtract(1, 'year').startOf('year'),
      true,
    )

    res.send(data || {})
  },
}
export const lexisNexisEmailAge = {
  async get(req: Request, res: Response) {
    const { company_id } = req.query
    const fullData = await LexisService.db.getItem(
      await getSSN(company_id as string),
      POLICY.EMAILAGE,
      moment().subtract(1, 'year').startOf('year'),
      true,
    )
    res.send(fullData || {})
  },
}

export const lexisNexisFraudPoint = {
  async get(req: Request, res: Response) {
    const { company_id } = req.query
    const fullData = await LexisService.db.getItem(
      await getSSN(company_id as string),
      POLICY.FRAUDPOINT,
      moment().subtract(1, 'year').startOf('year'),
      true,
    )
    res.send(fullData || {})
  },
}

export const findLexisNexisInstantIdByHashes = {
  async post(req: Request, res: Response) {
    return getLexisNexisDataByHashes(POLICY.BUSINESS_INSTANT_ID, req, res)
  },
}

export const findLexisNexisEmailAgeByHashes = {
  async post(req: Request, res: Response) {
    return getLexisNexisDataByHashes(POLICY.EMAILAGE, req, res)
  },
}

export const findLexisNexisFraudPointByHashes = {
  async post(req: Request, res: Response) {
    return getLexisNexisDataByHashes(POLICY.FRAUDPOINT, req, res)
  },
}

async function getLexisNexisDataByHashes(
  policy: any,
  req: Request,
  res: Response,
) {
  const hashes = req.body.hashes as string[]
  const data = await Promise.all(
    hashes.map((hash) =>
      LexisService.db.getItemByHash(
        hash,
        policy,
        moment().subtract(1, 'year').startOf('year'),
        true,
      ),
    ),
  )

  res.send(data || [])
}

export const experianData = {
  async get(req: Request, res: Response) {
    const { company_id } = req.query
    const doc = await Draft.findOne({
      company_id,
      type: mongoose.trusted({
        $in: ['general_application'],
      }),
    }).exec()

    let ein = doc
      ? doc.data.get('businessInfo')?.items.find((i) => i.identifier === 'ein')
          ?.content
      : ''
    ein = ein?.cipher ? await crypt.decrypt(ein.cipher) : ein

    const personalInfo =
      doc?.data.get('businessOwner') || doc?.data.get('personalInfo')
    const authorized =
      personalInfo?.items.reduce((data: Record<string, any>, next) => {
        data[next.identifier] = next.content
        return data
      }, {}) || {}
    const ssn = authorized.ssn?.cipher
      ? await crypt.decrypt(authorized.ssn.cipher)
      : authorized.ssn

    const search = await ExperianRequest.findOne({
      requestType: 'search',
      $or: [
        { bin_hash: ein ? md5(ein).toString() : '' },
        { bin_hash: ein ? sha512(ein).toString() : '' },
      ],
    }).exec()
    const [experianCompany] = search?.data || []
    const { bin } = experianCompany || {}

    const data = [
      ...(bin
        ? await ExperianRequest.find({
            $or: [
              { bin_hash: md5(bin).toString() },
              { bin_hash: sha512(bin).toString() },
            ],
          }).exec()
        : []),
      ...(ssn
        ? await ExperianRequest.find({
            $or: [
              { bin_hash: md5(ssn).toString() },
              { bin_hash: sha512(ssn).toString() },
            ],
          }).exec()
        : []),
    ]

    res.send({ search, data })
  },
}

export const findExperianDataByHashes = {
  async post(req: Request, res: Response) {
    const hashes = req.body.hashes as string[]
    const data = await ExperianRequest.find({
      bin_hash: mongoose.trusted({
        $in: hashes,
      }),
    }).exec()

    res.send({ results: data })
  },
}

export const loanApplicationNotes = {
  async get(req: Request, res: Response) {
    const app = await LoanApplication.findById(req.query.appId).exec()
    const notes = app?.notes || []
    const history = app?.prevOutputs
    res.send({ notes, history })
  },
}

async function addNoteLoanApplication(
  app: ILoanApplication,
  user: IUser,
  note: string,
) {
  app.notes = app.notes || []
  if (!note) return
  app.notes.push({
    login: user.login || '',
    name: [user.firstName, user.lastName].filter(Boolean).join(', '),
    message: note,
  })
  app.markModified('notes')
  await app.save()
}

export const loanApplicationAddNote = {
  async post(req: Request, res: Response) {
    const { appId, note = '' } = req.body
    const app = await LoanApplication.findById(appId).exec()
    if (app) {
      await addNoteLoanApplication(app, req.user!, note)
    }
    const notes = app?.notes || []
    res.send({ notes })
  },
}

export const loanApplicationDeleteNote = {
  async delete(req: Request, res: Response) {
    const { appId, noteId } = req.query
    await LoanApplication.updateOne(
      { _id: new mongoose.Types.ObjectId(appId as string) },
      {
        $pull: {
          notes: { _id: new mongoose.Types.ObjectId(noteId as string) },
        },
      },
    )
    const app = await LoanApplication.findById(appId).exec()
    const notes = app?.notes || []
    res.send({ notes })
  },
}

export default {
  get: loanApplications,
}

export const viewAgreement = {
  async get(req: Request, res: Response) {
    const { id } = req.query
    let data
    const application = await LoanApplication.findById(id)
    if (!application)
      throw new exceptions.LogicalError('No loan application found')
    const { lms_id } = application || {}

    if (!lms_id) {
      data = await controllers.company.makeAgreement(req)
    } else {
      data = await controllers.company.viewAgreement(req)
    }

    res.send(data)
  },
}

export const tryGetLatestMasterAgreement = {
  async get(req: Request, res: Response) {
    const { id } = req.query
    const data = await controllers.company.tryGetMasterAgreement(id as string)
    res.send(data)
  },
}

export const hasPersonalGuarantorAgreement = {
  async get(req: Request, res: Response) {
    const companyId = req.query.companyId?.toString()
    if (!companyId) throw new LogicalError('companyId is required')

    const hasAgreement = await PersonalGuarantorAgreement.existsForCompany(
      companyId,
    )
    res.send({ hasAgreement, result: 'ok' })
  },
}

export const downloadPersonalGuarantorAgreements = {
  async get(req: Request, res: Response) {
    const companyId = req.query.companyId?.toString()
    if (!companyId) throw new LogicalError('companyId is required')

    const zipLink = await PersonalGuarantorAgreement.downloadAll(companyId)
    res.send({ ...zipLink, result: 'ok' })
  },
}

export const uploadAgreement = {
  async post(req: Request, res: Response) {
    const { id } = req.body
    const application = await LoanApplication.findById(id)
    if (!application)
      throw new exceptions.LogicalError('No loan application found')

    const data =
      await AgreementService.getLatestAgreementByLoanApplicationIdAndType(
        id,
        S3AccessType.Put,
      )
    res.send({ url: data?.url, result: 'ok' })
  },
}

export const getMasterAgreementApprovals = {
  async get(req: Request, res: Response) {
    const { companyId } = req.query

    if (!companyId) throw new LogicalError('companyId is required')

    const approvals =
      await DocumentVersioningService.getApprovalsForInitialVersion(
        companyId.toString(),
        AgreementType.MASTER_AGREEMENT,
      )

    res.send({ ...approvals })
  },
}

export const submitApprovalRequest = {
  async post(req: Request, res: Response) {
    const { documentId, approver, applicantName, companyId, companyName } =
      req.body

    if (!documentId || !approver || !applicantName || !companyName)
      throw new LogicalError(
        'documentId, approver, applicantName & companyName are required',
      )

    const guarantorAgreement =
      await DocumentVersioningService.getLatestDocument(
        companyId,
        approver.id,
        AgreementType.PERSONAL_GUARANTOR_AGREEMENT,
      )

    await CreditAgreementsService.submitApprovalRequest(
      { documentId: documentId.toString(), url: '', fileName: '' },
      guarantorAgreement
        ? { documentId: guarantorAgreement?.id, url: '', fileName: '' }
        : null,
      approver,
      applicantName,
      companyName,
      req.user?.id,
    )

    res.send({ result: 'ok' })
  },
}
