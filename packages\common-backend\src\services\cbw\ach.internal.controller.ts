import { dictionaries, exceptions } from '@linqpal/models'
import mongoose from 'mongoose'
import {
  ACH_TRANSACTION_TYPE,
  AchInternalPayload,
  AchTransactionTypes,
  Transaction,
} from '../../models'
import { IOperation } from '../../models/types'
import checkSession from './checkSession'
import { toPayload } from './payload-mapper'
import { processTransaction } from './processTransaction'
import { CbwAccount } from './recipient'
import { CbwTransactionStatus } from '@linqpal/models/src/dictionaries'
import { Logger } from '../logger/logger.service'

const logger = new Logger({
  module: 'ACH Internal controller',
})

export interface BaseInternalTransferData {
  amount?: number
  transactionType?: dictionaries.TransactionTypes
  reason?: string
  currency?: string
}

export interface GlInternalTransferData extends BaseInternalTransferData {
  sender: CbwAccount
  recipient: CbwAccount
  achTransactionType: typeof ACH_TRANSACTION_TYPE.GL_TRANSFER
}

export interface InternalTransferData extends BaseInternalTransferData {
  sender?: string
  recipient?: string
  achTransactionType?: Exclude<
    AchTransactionTypes,
    typeof ACH_TRANSACTION_TYPE.GL_TRANSFER
  >
}

/**
 * Makes internal transfer
 * @param operation
 * @param amount
 * @param sender
 * @param recipient
 * @param transactionType
 * @param [reason]
 * @param [currency]
 * @param [achTransactionType]
 * @param session
 * @returns {Promise<void>}
 */
export async function internal(
  operation: IOperation,
  {
    amount = 0,
    sender,
    recipient,
    transactionType,
    reason = 'Transfer',
    currency = 'USD',
    achTransactionType = ACH_TRANSACTION_TYPE.INTERNAL,
  }: InternalTransferData | GlInternalTransferData,
  session: mongoose.ClientSession,
) {
  checkSession(session)
  operation.markModified('metadata')
  if (!amount) {
    amount = operation.amount
  }

  logger.info(`processing amount ${amount} for operation ${operation.id}`)

  if (amount <= 0)
    throw new exceptions.LogicalError(
      'Minimum amount should be bigger than zero',
    )

  const [transaction] = await Transaction.create(
    [
      {
        operation_id: operation.id,
        amount: amount.toFixed(2),
        fee: 0,
        currency,
        type:
          transactionType ||
          dictionaries.TRANSACTION_TYPES.ACH.INTERNAL_TRANSFER,
        payment_method: dictionaries.PAYMENT_METHODS.ACH,
        status: dictionaries.TRANSACTION_STATUS.PENDING,
        reason,
        metadata: {},
      },
    ],
    { session },
  )

  logger.info({ transaction }, `created transaction ${transaction.id}`)

  const payload =
    achTransactionType === ACH_TRANSACTION_TYPE.GL_TRANSFER
      ? toPayload(
          {
            id: transaction.id,
            debtorAccount: sender as CbwAccount,
            creditorAccount: recipient as CbwAccount,
            amount,
            reason,
            currency,
          },
          ACH_TRANSACTION_TYPE.GL_TRANSFER,
        )
      : toPayload(
          {
            id: transaction.id,
            accountNumber: recipient as string,
            amount,
            reason,
            currency,
          },
          achTransactionType,
          sender as string,
        )

  const achPayload = new AchInternalPayload(payload)
  await achPayload.validate()

  // prettier-ignore
  logger.info({ amount, transaction }, `processing transaction ${transaction.id}`)

  const data = await processTransaction(
    [transaction],
    achPayload.toObject() as any,
    session,
  )

  logger.info({ result: data }, `processed transaction ${transaction.id}`)

  if (data.transactionStatus === CbwTransactionStatus.Processed) {
    operation.status = dictionaries.OPERATION_STATUS.SUCCESS
    logger.info({ operation }, `marked operation ${operation.id} as success`)
  } else {
    operation.status = dictionaries.OPERATION_STATUS.PROCESSING
    logger.info({ operation }, `marked operation ${operation.id} as processing`)
  }

  await operation.save()
  return data
}
