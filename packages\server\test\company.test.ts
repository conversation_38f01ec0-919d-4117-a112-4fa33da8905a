import { CompanyStatus, routes } from '@linqpal/models'
import chai from 'chai'
import chaiHttp from 'chai-http'
import { _Authorization } from './_axios'
import { _FirebaseTokenVerifier, FakeUsers } from './_firebase_admin'
import {
  BankAccount,
  CardBins,
  Company,
  crypt,
  CustomerAccount,
  Invoice,
  Operation,
  signupService,
  User,
  UserRole,
} from '@linqpal/common-backend'
import './hooks'
import nock from 'nock'
import { beforeEachMockEncryption, beforeEachMockSQS } from './helper'
import md5 from 'crypto-js/md5'
import mongoose from 'mongoose'
import moment from 'moment'
import sinon from 'sinon'

chai.use(chaiHttp)
chai.should()
let tokenVerifier: sinon.SinonStub
let auth: ReturnType<typeof _Authorization>
const { constructor1, supplier1 } = FakeUsers

describe('Supplier Company', () => {
  beforeEach(() => {
    tokenVerifier = _FirebaseTokenVerifier()
    auth = _Authorization(supplier1.auth)
  })
  afterEach(() => {
    auth.restore()
    tokenVerifier.restore()
  })
  it('should return invoice outstanding amount for only placed and due invoices', async () => {
    const company = await Company.create({ status: CompanyStatus.Approved })
    await UserRole.create({
      sub: supplier1.info.sub,
      company_id: company._id,
      role: 'Owner',
    })
    await User.create({
      firebaseId: supplier1.info.firebaseId,
      sub: supplier1.info.sub,
      login: supplier1.info.email,
      email: supplier1.info.email,
    })
    await User.findOne({ sub: supplier1.info.sub })
    const customerAccount = await CustomerAccount.create({
      phone: '+***********',
      company_id: company.id,
    })

    const i1 = await Invoice.create({
      status: 'PLACED',
      total_amount: 100.0,
      invoice_due_date: moment('2021-08-11T00:00:00.000Z').format(),
      expiration_date: null,
      customer_account_id: customerAccount._id,
      company_id: company._id,
    })
    const i2 = await Invoice.create({
      status: 'PLACED',
      total_amount: 150.0,
      invoice_due_date: moment('2021-08-11T00:00:00.000Z').format(),
      expiration_date: null,
      customer_account_id: customerAccount._id,
      company_id: company._id,
    })
    await Invoice.create({
      status: 'DRAFT',
      total_amount: 150.0,
      invoice_due_date: moment('2021-08-11T00:00:00.000Z').format(),
      expiration_date: null,
      customer_account_id: customerAccount._id,
      company_id: company._id,
    })
    const i4 = await Invoice.create({
      status: 'PLACED',
      total_amount: 150.0,
      invoice_due_date: moment('2021-08-11T00:00:00.000Z').format(),
      expiration_date: moment('2021-08-11T00:00:00.000Z').format(),
      customer_account_id: customerAccount._id,
      company_id: company._id,
    })

    await Operation.create({
      owner_id: i1._id,
      amount: 100,
      metadata: {
        payee_id: company._id.toString(),
      },
      status: 'PLACED',
      type: 'invoice_payment',
    })

    await Operation.create({
      owner_id: i2._id,
      amount: 150,
      metadata: {
        payee_id: company._id.toString(),
      },
      status: 'PLACED',
      type: 'invoice_payment',
    })

    await Operation.create({
      owner_id: i4._id,
      amount: 150,
      metadata: {
        payee_id: company._id.toString(),
      },
      status: 'PLACED',
      type: 'invoice_payment',
    })

    const r = await routes.company.outstandingInvoices({
      currentDate: moment().format('MM-DD-YYYY'),
    })
    // only sum of two invoices i1 and i2 should be there...rest should be ignored
    r.sum.should.equal(250)
  })
})
describe('Company', () => {
  let generateUniqueSubStub: sinon.SinonStub<any[], any>

  beforeEach(async () => {
    generateUniqueSubStub = sinon
      .stub(signupService, 'generateUniqueSub')
      .resolves(constructor1.info.sub)
    tokenVerifier = _FirebaseTokenVerifier()
    auth = _Authorization(constructor1.auth)
  })
  afterEach(async () => {
    generateUniqueSubStub.restore()
    auth.restore()
    tokenVerifier.restore()
  })
  it('should update settings', async () => {
    await routes.company.settings({ approveRead: true })
    await routes.company.settings({ accountAdded: true })
    const role = await UserRole.findOne({ sub: constructor1.info.sub })
    const company = await Company.findOne({ _id: role!.company_id })
    console.log(company!.settings)
    company!.settings!.approveRead!.should.be.equal(true)
    company!.settings!.accountAdded!.should.be.equal(true)
    chai.expect(company!.credit.LoCnumber).to.equal(undefined)
    chai.expect(company!.credit.LoCnumCreatedAt).to.equal(undefined)
  })

  it('should update LoC details', async () => {
    const resp = await routes.company.updateInfo({ generateLoC: true })
    const company_id = resp.id
    const company = await Company.findOne({ _id: company_id })
    company!.credit.LoCnumber!.should.exist
    company!.credit.LoCnumCreatedAt!.should.exist
  })

  it('should update only sent parameters', async () => {
    const resp = await routes.company.updateInfo({ phone: '**********' })
    const company_id = resp.id
    let company = await Company.findOne({ _id: company_id })
    company!.phone.should.equal('+***********')
    await routes.company.updateInfo({ name: 'Some Name' })
    company = await Company.findOne({ _id: company_id })
    company!.name.should.equal('Some Name')
    company!.phone.should.equal('+***********')
  })

  describe('BankAccount', () => {
    beforeEachMockEncryption()
    beforeEachMockSQS()
    it('should return Bank Accounts of the company', async () => {
      const bankAccount1 = await BankAccount.create({
        accountNumber: {
          cipher: await crypt.encrypt('**********'),
          hash: md5('**********').toString(),
          display: '******7890',
        },
        isPrimary: true,
        name: 'GreatBank',
        routingNumber: '********',
        accountType: 'checking',
        paymentMethodType: 'bank',
      })
      const bankAccount2 = await BankAccount.create({
        accountNumber: {
          cipher: await crypt.encrypt('**********'),
          hash: md5('**********').toString(),
          display: '******7890',
        },
        isPrimary: true,
        name: 'GreatBank',
        routingNumber: '********',
        accountType: 'savings',
        paymentMethodType: 'bank',
      })
      const bankAccount3 = await BankAccount.create({
        accountNumber: {
          cipher: await crypt.encrypt('**********'),
          hash: md5('**********').toString(),
          display: '******7890',
        },
        isPrimary: true,
        name: 'GreatBank',
        routingNumber: '********',
        accountType: 'abc',
        paymentMethodType: 'bank',
      })
      const bankAccount4 = await BankAccount.create({
        accountNumber: {
          cipher: await crypt.encrypt('********98'),
          hash: md5('********98').toString(),
          display: '******7890',
        },
        isPrimary: true,
        name: 'GreatBank',
        routingNumber: '********',
        accountType: 'def',
        paymentMethodType: 'bank',
      })
      const creditCard = await BankAccount.create({
        accountNumber: {
          display: '********1111',
        },
        cardMetadata: {
          accountId: 'ASASx6587ghkjj',
          avsAuthorizeID: '234',
          avsCode: 'Y',
          avsNetworkRC: '44',
          avsResultText: 'NOT DECLINED',
          avsSecurityCode: 'M',
          expirationDate: '202303',
          isPullEnabled: true,
          isRegulated: true,
          lastFour: '1111',
          network: 'Visa',
          token: '80E1iEMJ243WsTF0pBM',
          type: 'Credit',
        },
        name: 'Visa - Card',
        paymentMethodType: 'card',
        isManualEntry: true,
        accountholderName: 'Test Card 77',
        billingAddress: {
          addressLine1: 'Traction Street',
          addressLine2: '3094 ',
          city: 'Spartanburg',
          stateCode: 'South Carolina',
          zipCode: '29303',
        },
      })
      const company = await Company.create({
        name: 'Test supplier',
        type: 'supplier',
        bankAccounts: [
          new mongoose.Types.ObjectId(bankAccount1._id.toString()),
          new mongoose.Types.ObjectId(bankAccount2._id.toString()),
          new mongoose.Types.ObjectId(bankAccount3._id.toString()),
          new mongoose.Types.ObjectId(bankAccount4._id.toString()),
          new mongoose.Types.ObjectId(creditCard._id.toString()),
        ],
        email: '<EMAIL>',
        phone: '+***********',
        address: {
          address: '100 Main St',
          city: 'San Francisco',
          zip: '94061',
          state: 'CA',
        },
        ein: {
          cipher: await crypt.encrypt('**********'),
          hash: md5('**********').toString(),
          display: '******7890',
        },
      })
      await UserRole.create({
        sub: constructor1.info.sub,
        company_id: company._id,
        role: 'Owner',
      })
      const response = await routes.company.bankAccounts()
      response.should.have.property('bankAccounts')
      console.log(response)
      response.bankAccounts.should.be.an('array')
      response.bankAccounts.length.should.equal(3)
      response.bankAccounts[0].accountNumber.should.be.a('string')
      response.bankAccounts[2].accountNumber.should.be.a('string')
      response.bankAccounts[2].cardMetadata.should.not.have.property(
        'avsAuthorizeID',
      )
      response.bankAccounts[2].cardMetadata.should.not.have.property('avsCode')
      response.bankAccounts[2].cardMetadata.should.not.have.property(
        'avsNetworkRC',
      )
      response.bankAccounts[2].cardMetadata.should.not.have.property(
        'avsResultText',
      )
      response.bankAccounts[2].cardMetadata.should.not.have.property(
        'avsSecurityCode',
      )
      response.bankAccounts[2].cardMetadata.should.not.have.property(
        'isPullEnabled',
      )
      response.bankAccounts[2].cardMetadata.should.not.have.property('token')
    })
    it('should delete account', async () => {
      const resp = await routes.company.addBankAccount({
        accountNumber: '**********',
      })
      let account = await BankAccount.findById(resp.id)
      account!.accountNumber.should.have.a.property('cipher')
      await routes.company.removeBankAccount(resp.id)
      account = await BankAccount.findById(account!.id)
      account!.isDeactivated!.should.equal(true)
    })
  })

  describe('Payment Card', () => {
    const tabapayBaseUrl = 'https://api.sandbox.tabapay.net:10443'
    process.env.LP_TABAPAY_CLIENT_ID = 'test-client-id'
    process.env.LP_TABAPAY_BEARER_TOKEN = 'test-token'
    process.env.LP_TABAPAY_SETTLEMENT_ACCOUNT_ID = 'test-settlement-account-id'
    process.env.LP_TABAPAY_MID_NO_CONVENIENCE_FEE = 'test-mid-0001'
    process.env.LP_TABAPAY_MID_WITH_CONVENIENCE_FEE = 'test-mid-0002'

    it('should throw error when quering card for attributes in tabapay', async () => {
      const postBody = {
        cardToken: 'test_tabapay_token',
        firstName: 'John',
        lastName: 'Doe',
        addressLine1: '100 Main St',
        addressLine2: 'Suite 100',
        city: 'San Francisco',
        stateCode: 'CA',
        zipCode: '94061',
      }

      // mock query card api call
      const queryCardErrorResponse = {
        SC: 207,
        EC: 'EC123',
        EM: 'query card failure',
      }
      nock(tabapayBaseUrl)
        .post((uri) => uri.includes('cards'))
        .reply(500, queryCardErrorResponse)

      const response = await routes.company.addPaymentCard(postBody)
      response.should.have.property('error')
      response.result.should.be.equal('failed')
      if (response.result === 'failed') {
        response.error.should.be.equal('Invalid card information')
      }
    })

    it('should throw error when creating payment card account in tabapay', async () => {
      const postBody = {
        cardToken: 'test_tabapay_token',
        firstName: 'John',
        lastName: 'Doe',
        addressLine1: '100 Main St',
        addressLine2: 'Suite 100',
        city: 'San Francisco',
        stateCode: 'CA',
        zipCode: '94061',
      }

      // mock query card api call
      const queryCardSuccessResponse = {
        SC: 200,
        EC: 0,
        card: {
          pull: {
            enabled: true,
            network: 'Visa',
            type: 'Credit',
            regulated: true,
          },
          push: {
            network: 'Visa',
            type: 'Credit',
          },
        },
        AVS: {
          networkRC: '00',
          authorizeID: 'dsf9343',
          resultText: 'avs_result_text',
          codeAVS: 'Y',
          codeSecurityCode: 'M',
          EC: '00',
        },
      }
      nock(tabapayBaseUrl)
        .post((uri) => uri.includes('cards'))
        .reply(200, queryCardSuccessResponse)

      // mock create account api call
      const createAccountErrorResponse = {
        SC: 409,
        EC: 'EC123',
        EM: 'create account failure',
      }
      nock(tabapayBaseUrl)
        .post((uri) => uri.includes('accounts'))
        .reply(409, createAccountErrorResponse)

      const resp = await routes.company.updateInfo({ phone: '**********' })
      resp.result.should.be.equal('ok')
      const response = await routes.company.addPaymentCard(postBody)
      response.should.have.property('error')
      response.result.should.be.equal('failed')
      if (response.result === 'failed') {
        response.error.should.be.equal(
          'Unable to add card now. Please try again later.',
        )
      }
    })

    it('should query card in tabapay, create account in tabapay and add payment card successfully in DB', async () => {
      const postBody = {
        cardToken: '80AAxbdmsDIpK73n3NmhnTwz6TeIanp',
        firstName: 'John',
        lastName: 'Doe',
        binNumber: '4111111',
        addressLine1: '100 Main St',
        addressLine2: 'Suite 100',
        city: 'San Francisco',
        stateCode: 'California',
        zipCode: '94061',
      }

      await CardBins.create({
        bin: '4111111',
        name: 'ABC Bank',
        productCode: 'AB',
        metadata: {},
        country: '131',
      })

      // mock query card api call
      const queryCardSuccessResponse = {
        SC: 200,
        EC: 0,
        card: {
          bin: '4111111',
          pull: {
            enabled: true,
            network: 'Visa',
            type: 'Credit',
            regulated: true,
          },
          push: {
            network: 'Visa',
            type: 'Credit',
          },
        },
        AVS: {
          networkRC: '00',
          authorizeID: 'dsf9343',
          resultText: 'avs_result_text',
          codeAVS: 'Y',
          codeSecurityCode: 'M',
          EC: '00',
        },
      }
      nock(tabapayBaseUrl)
        .post((uri) => uri.includes('cards'))
        .reply(200, queryCardSuccessResponse)

      // mock create account api call
      const createAccountSuccessResponse = {
        SC: 200,
        EC: 0,
        accountID: '************',
        card: {
          last4: '1111',
          expirationDate: '202202',
        },
      }
      nock(tabapayBaseUrl)
        .post((uri) => uri.includes('accounts'))
        .reply(200, createAccountSuccessResponse)

      // get existing number of payment cards for contractor
      const resp = await routes.company.updateInfo({ phone: '**********' })
      let contractorCompany = await Company.findById(resp.id).populate(
        'bankAccounts',
      )
      const existingNumOfPaymentCards = contractorCompany!.bankAccounts!.filter(
        (a) => a.paymentMethodType === 'card',
      ).length

      const response = await routes.company.addPaymentCard(postBody)
      response.should.have.property('result')
      response.result.should.be.equal('ok')

      // get number of payment cards after adding a card for contractor and check to see whether card was added
      contractorCompany = await Company.findById(resp.id).populate(
        'bankAccounts',
      )
      const totalNumOfPaymentCards = contractorCompany!.bankAccounts!.filter(
        (a) => a.paymentMethodType === 'card',
      ).length
      totalNumOfPaymentCards.should.be.equal(existingNumOfPaymentCards + 1)
      contractorCompany!.bankAccounts![0].cardMetadata.productCode.should.equal(
        'AB',
      )
    })
  })
})
