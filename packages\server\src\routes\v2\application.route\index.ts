import { TFrontendRoute, TMiddlewares } from '@linqpal/models/src/routes2/types'
import { routes2 } from '@linqpal/models'
import { authMiddleware } from '../middlewares'
import { getUnifiedApplicationDraft } from './getUnifiedApplicationDraft'
import {
  IGetLenderApplicationResponse,
  IGetUnifiedApplicationRequest,
  IGetUnifiedApplicationResponse,
  IPostLenderApplicationRequest,
  IPostLenderApplicationResponse,
} from '@linqpal/models/src/routes2/application/types'
import { getLenderApplicationDraft } from './getLenderApplicationDraft'
import { postLenderApplicationDraft } from './postLenderApplicationDraft'

export default class Application extends routes2.classes.Application {
  middlewares: TMiddlewares = authMiddleware

  getUnifiedApplicationDraft = getUnifiedApplicationDraft as TFrontendRoute<
    IGetUnifiedApplicationRequest,
    IGetUnifiedApplicationResponse
  >

  getLenderApplicationDraft = getLenderApplicationDraft as TFrontendRoute<
    any,
    IGetLenderApplicationResponse
  >

  postLenderApplicationDraft = postLenderApplicationDraft as TFrontendRoute<
    IPostLenderApplicationRequest,
    IPostLenderApplicationResponse
  >
}
