import { makeAutoObservable, toJS } from 'mobx'
import { ICoOwner } from '@linqpal/models'
import { CoOwner } from './CoOwner'
import { v4 } from 'uuid'
import { UnifiedApplicationDraft } from './UniffiedApplicationDraft'

export class CoOwnersStore {
  coOwners: CoOwner[] = []

  private _currentCoOwner: CoOwner | null = null

  constructor() {
    makeAutoObservable(this)
  }

  get currentCoOwner(): CoOwner | null {
    return this._currentCoOwner
  }

  createNew(): void {
    this._currentCoOwner = new CoOwner({ id: v4() })
  }

  edit(coOwner: ICoOwner) {
    // do not update draft directly when editing co-owner, so create a copy here
    this._currentCoOwner = new CoOwner(toJS(coOwner))
  }

  save(draft: UnifiedApplicationDraft) {
    if (!this._currentCoOwner) return

    if (!draft.data?.coOwners) {
      draft.data.coOwners = []
    }

    const coOwnerIndex = draft.data?.coOwners.findIndex(
      (c) => c.id === this._currentCoOwner?.id,
    )

    const coOwnerSnapshot = toJS(this._currentCoOwner)
    const newCoOwner = new CoOwner(coOwnerSnapshot)

    if (coOwnerIndex !== -1) {
      draft.data.coOwners[coOwnerIndex] = newCoOwner
    } else {
      draft.data.coOwners.push(newCoOwner)
    }

    this._currentCoOwner = null
  }

  cancelEdit() {
    this._currentCoOwner = null
  }

  remove(coOwner: ICoOwner) {
    const index = this.coOwners.findIndex((c) => c.id === coOwner.id)
    this.coOwners.splice(index, 1)
  }
}
