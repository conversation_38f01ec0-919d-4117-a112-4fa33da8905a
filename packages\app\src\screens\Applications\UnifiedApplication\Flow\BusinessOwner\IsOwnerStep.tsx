import React from 'react'
import { BtRadioGroup } from '@linqpal/components/src/ui'
import { observer } from 'mobx-react'
import { useTranslation } from 'react-i18next'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { ApplicationUserRole } from '../../../../../../../models/src/applications/unified/IUnifiedApplicationDraft'
import { runInAction } from 'mobx'
import { IUnifiedApplicationEditor } from '../getUnifiedApplicationEditor'

const YES = 'Yes'
const NO = 'No'

const IsOwnerEditor = () => {
  const { t } = useTranslation('application')
  const store = useUnifiedApplication()

  const userRole = store.currentUser.role

  const value = !userRole
    ? undefined
    : userRole === ApplicationUserRole.Owner
    ? YES
    : NO

  const handleChange = (option: string) => {
    const role =
      option === YES ? ApplicationUserRole.Owner : ApplicationUserRole.None

    const userInfo = store.draft.users.find(
      (user) => user.id === store.currentUserId,
    )

    runInAction(() => {
      if (!userInfo) {
        store.draft.users.push({
          id: store.currentUserId,
          role,
        })
      } else {
        userInfo.role = role
      }
    })
  }

  return (
    <BtRadioGroup
      disabled={store.isInReview}
      value={value}
      options={[
        { label: t('Finance.OwnerLabelYes'), value: YES },
        { label: t('Finance.OwnerLabelNo'), value: NO },
      ]}
      onChange={handleChange}
      radioStyle={{ marginRight: 32 }}
      groupStyle={{ flexDirection: 'row' }}
      testID="UnifiedApplication.BusinessOwner.IsOwner"
    />
  )
}

export const IsOwnerStep: IUnifiedApplicationEditor = {
  options: {
    title: 'Finance.Owner',
    canSkip: false,
  },
  component: observer(IsOwnerEditor),
}
