import { Image, StyleSheet, View } from 'react-native'
import React from 'react'
import { observer } from 'mobx-react-lite'
import {
  invoiceStatus,
  IPayable,
  PROCESSING_STATUSES,
} from '@linqpal/models/src/dictionaries'
import { BtText } from '@linqpal/components/src/ui'
import { TouchableOpacity } from 'react-native-gesture-handler'
import { Divider, FAB, List } from 'react-native-paper'
import BuilderBottomModal from '../../../../ui/molecules/BuilderBottomModal'
import { commonColors } from '@linqpal/common-frontend/src/theme'
import { GreenCheckIcon, IconAddInvoice } from '../../../../assets/icons'
import { useTranslation } from 'react-i18next'
import { ListPlaceholder } from '../../../../ui/atoms/ListPlaceholder'
import noInvoice from '../../../../assets/images/noInvoice.svg'
import { useNavigation } from '@react-navigation/core'
import { paths } from '../../../links'
import { payableInvoiceStatuses } from '../Invoice/ScreenSelector'
import CompanyHeader from '../Invoice/CompanyHeader'
import { useResponsive } from '../../../../../../components/src/hooks'

export const FILTERS = {
  All: 'All',
  Paid: invoiceStatus.paid,
  Collected: invoiceStatus.collected,
  Due: invoiceStatus.due,
  'Past Due': invoiceStatus.pastDue,
  'Application Processing': invoiceStatus.applicationProcessing,
  'Authorization in Review': invoiceStatus.authorizationInReview,
  Authorized: invoiceStatus.authorized,
  Invoiced: invoiceStatus.invoiced,
  'Credit Applied': invoiceStatus.creditApplied,
  //Dismissed: invoiceStatus.dismissed,
  Cancelled: invoiceStatus.cancelled,
  Expired: invoiceStatus.expired,
  'Invoice Rejected': invoiceStatus.rejected,
  Placed: invoiceStatus.placed,
  Failed: invoiceStatus.paymentFailed,
}

export enum SwitchType {
  INVOICES = 'invoices',
  VENDORS = 'vendors',
}

export type ISwitchType = SwitchType.INVOICES | SwitchType.VENDORS

type SwitchProps = {
  active: ISwitchType
  onPress: (value: ISwitchType) => void
}

export const EmptyInvoicePageMessage = () => {
  const { t } = useTranslation('global')
  return (
    <>
      <ListPlaceholder
        image={
          <Image
            style={{
              marginHorizontal: 70,
              width: 302,
              height: 230,
              alignSelf: 'center',
            }}
            source={{ uri: noInvoice }}
          />
        }
        style={undefined}
        heading={t('InvoiceListing.no-invoices')}
        subHeading={t('InvoiceListing.no-invoices-message')}
      />
    </>
  )
}

const isActiveInvoice = (invoice: IPayable) => {
  const processing = invoice.loanAppStatus
    ? PROCESSING_STATUSES.some((status) => status === invoice.loanAppStatus)
    : false
  return (
    ['invoice', 'sales_order'].includes(invoice.type) &&
    invoice.company &&
    payableInvoiceStatuses.includes(invoice.invoiceStatus) &&
    !processing &&
    (invoice.company.isInvited || invoice.customer_account_id)
  )
}

export const GroupedInvoices = ({
  invoices,
  t,
}: {
  invoices: IPayable[]
  t: any
}) => {
  if (!invoices?.length) return <EmptyInvoicePageMessage />

  const companies: any[] = []
  invoices
    .filter((invoice) => !!invoice.company?._id)
    .forEach((invoice) => {
      const eligible = isActiveInvoice(invoice)

      const company = invoice.company

      const found = companies.find((c) => c._id === company._id)
      if (found) {
        found.totalAmount += eligible ? invoice.totalRemainingAmount : 0
        found.invoiceCount = found.invoiceCount + 1
        found.invoicesIds.push(invoice._id)
      } else {
        companies.push({
          ...company,
          totalAmount: eligible ? invoice.totalRemainingAmount : 0,
          invoiceCount: 1,
          invoicesIds: [invoice._id],
        })
      }
    })

  if (companies.length === 0) return <EmptyInvoicePageMessage />

  return (
    <>
      {companies
        .sort((a, b) => b.totalAmount - a.totalAmount)
        .map((company, idx) => (
          <CompanyItem company={company} key={idx} />
        ))}
    </>
  )
}

const CompanyItem = ({ company }) => {
  const navigation = useNavigation()
  const { t } = useTranslation('global')
  return (
    <TouchableOpacity
      style={{
        borderColor: '#E6EBEE',
        borderWidth: 1,
        borderRadius: 8,
        marginBottom: 10,
      }}
      onPress={() => {
        if (company.isInvited) {
          navigation.navigate(paths.Console.Payables.Vendor, {
            invoicesIds: company.invoicesIds,
            companyId: undefined,
            isInvited: true,
          })
        } else if (company.totalAmount === 0 && !company.isInvited) {
          navigation.navigate(paths.Console.Payables.Vendor, {
            companyId: company._id,
          })
        } else if (company.invoiceCount === 1) {
          navigation.navigate(paths.Console.Payables.Invoice, {
            id: company.invoicesIds[0],
          })
        } else {
          navigation.navigate(paths.Console.Payables.Vendor, {
            companyId: company._id,
          })
        }
      }}
    >
      <CompanyHeader
        company={company}
        totalAmount={company.totalAmount}
        style={{ flexDirection: 'row', padding: 15 }}
        companyFontSize={16}
        amountLabel={t('TabInvoice.amount_due')}
      />
    </TouchableOpacity>
  )
}

export const Switcher = ({ active, onPress }: SwitchProps) => {
  return (
    <View style={styles.switchContainer}>
      <SwitchItem
        active={active}
        onPress={onPress}
        type={SwitchType.INVOICES}
      />
      <SwitchItem active={active} onPress={onPress} type={SwitchType.VENDORS} />
    </View>
  )
}

const SwitchItem = ({
  active,
  onPress,
  type,
}: SwitchProps & { type: ISwitchType }) => {
  return (
    <BtText
      onPress={() => onPress(type)}
      style={[styles.switchItem, active === type && styles.switchItemActive]}
    >
      {type === SwitchType.INVOICES ? 'Invoices/SOs' : 'Vendors'}
    </BtText>
  )
}

export const Filter = ({ visible, onClose, onPress, filterStatus, t }) => {
  const { sm } = useResponsive()
  return (
    <BuilderBottomModal
      visible={visible}
      height={sm ? '70%' : '80%'}
      title={t('InvoiceListing.filters')}
      onClose={onClose}
    >
      {Object.keys(FILTERS).map((filter) => {
        return (
          <TouchableOpacity
            key={filter}
            onPress={() => {
              onClose()
              onPress(FILTERS[filter])
            }}
          >
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}
            >
              <List.Item key={filter} title={filter} />
              {filterStatus === FILTERS[filter] && (
                <GreenCheckIcon
                  style={{ alignSelf: 'center', width: 25, height: 15 }}
                />
              )}
            </View>
            <Divider />
          </TouchableOpacity>
        )
      })}
    </BuilderBottomModal>
  )
}

interface FabButtonProps {
  onPress: () => void
}

export const FabButton = observer(({ onPress }: FabButtonProps) => {
  const { mobileWidth: width } = useResponsive()
  return (
    <FAB
      style={{
        position: 'absolute',
        width: 48,
        height: 48,
        left: width * 0.83,
        bottom: 74,
        backgroundColor: commonColors.accentText,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 24,
      }}
      icon={() => (
        <IconAddInvoice style={{ alignSelf: 'center', marginTop: 2 }} />
      )}
      onPress={onPress}
    />
  )
})

const styles = StyleSheet.create({
  companyItemContainer: {},
  switchContainer: {
    width: '100%',
    flexDirection: 'row',
    height: 40,
    alignItems: 'center',
    backgroundColor: '#f0f6fa',
    borderRadius: 9,
    padding: 4,
  },
  switchItem: {
    width: '50%',
    height: '100%',
    borderRadius: 9,
    backgroundColor: 'transparent',
    textAlign: 'center',
    paddingTop: 7,
    color: '#335C75',
    fontWeight: '500',
  },
  switchItemActive: {
    backgroundColor: 'white',
    color: '#00A0F3',
    fontWeight: '600',
  },
})
