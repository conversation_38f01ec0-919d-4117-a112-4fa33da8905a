import React, { ComponentType, FC } from 'react'
import { BtSecureInput_v1 } from './BtSecureInput_v1'
import BackgroundGradient from '../assets/images/encrypted-input-gradient.png'
import { BtText } from './BtText'
import { useResponsive } from '../hooks'
import { Trans, useTranslation } from 'react-i18next'
import { BtInputBaseProps } from './BtInputBase'
import { Icon } from '@ui-kitten/components'

//Needs better typing to type props based on InputComponent props type
export interface BtEncryptedInputProps extends BtInputBaseProps {
  validationError?: string
  InputComponent?: ComponentType<BtInputBaseProps>
  showBadge?: boolean
}

export const BtEncryptedInput: FC<BtEncryptedInputProps> = ({
  InputComponent = BtSecureInput_v1,
  showBadge = true,
  ...inputComponentProps
}) => {
  const { t } = useTranslation('global')
  const { sm } = useResponsive()

  let height: number
  let marginTop: number
  let marginBottom: number
  switch (inputComponentProps.size) {
    case 'small':
      height = 32
      marginTop = -8
      marginBottom = -8
      break
    case 'medium':
      height = 40
      marginTop = -11
      marginBottom = -11
      break
    case 'large':
      height = 48
      marginTop = -20
      marginBottom = -20
      break
    default:
      throw new Error('This size is not supported: ' + inputComponentProps.size)
  }

  return (
    <InputComponent
      textStyle={{ width: '100%' }}
      {...inputComponentProps}
      accessoryRight={
        showBadge ? (
          <div
            style={{
              height,
              marginTop,
              marginBottom,
              position: 'relative',
              width: sm ? 280 : 150,
              marginRight: -8,
            }}
          >
            <img
              src={BackgroundGradient}
              style={{
                width: 'inherit',
                height: 'inherit',
                borderTopRightRadius: 4,
                borderBottomRightRadius: 4,
              }}
            />
            <BtText
              size={sm ? 16 : 13}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                color: 'white',
                fontWeight: '600',
              }}
            >
              <Icon
                fill="white"
                name="shield-outline"
                style={{ width: sm ? 22 : 15, marginRight: 5, marginTop: 2 }}
              />
              <Trans
                t={t}
                values={{ lineBreak: sm ? '' : '\n' }}
                i18nKey="BtEncryptedInput.EncryptedAndSecured"
              />
            </BtText>
          </div>
        ) : undefined
      }
    />
  )
}
