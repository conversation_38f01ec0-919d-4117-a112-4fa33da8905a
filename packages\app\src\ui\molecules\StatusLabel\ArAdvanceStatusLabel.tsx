import React, { FC } from 'react'
import { StatusLabel } from './StatusLabel'
import { getArAdvanceStatusUIOptions } from './options/getArAdvanceStatusUIOptions'

interface IArAdvanceStatusLabelProps {
  status: string
  onPress?: () => void
}

export const ArAdvanceStatusLabel: FC<IArAdvanceStatusLabelProps> = ({
  status,
  onPress,
}) => {
  return (
    <StatusLabel
      status={status}
      onPress={onPress}
      uiOptionsProvider={getArAdvanceStatusUIOptions}
    />
  )
}
