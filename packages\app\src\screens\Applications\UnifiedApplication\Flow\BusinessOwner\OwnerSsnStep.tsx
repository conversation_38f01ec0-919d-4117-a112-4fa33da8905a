import { observer } from 'mobx-react-lite'
import { UnifiedApplicationStore } from '../../Store/UnifiedApplicationStore'
import React, { FC } from 'react'
import { useTranslation } from 'react-i18next'
import { BtSocialSecurityNumberInput } from '@linqpal/components/src/ui'
import { UnifiedApplicationValidator } from '@linqpal/models'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { runInAction } from 'mobx'
import { IUnifiedApplicationEditor } from '../getUnifiedApplicationEditor'

export const OwnerSsnEditor: FC = () => {
  const { t } = useTranslation('application')
  const store = useUnifiedApplication()

  const handleValidation = (value: string) => {
    const unformattedValue = value.replace(/\D/g, '')

    // hide validation message when 10th char is pressed
    if (unformattedValue.length > 9) return ''

    return UnifiedApplicationValidator.validateSsn(unformattedValue)
      ? ''
      : t('ValidationErrors.InvalidSSN')
  }

  const handleChange = (value: string) => {
    const unformattedValue = value.replace(/\D/g, '')

    if (unformattedValue.length <= 9) {
      runInAction(() => {
        store.currentUser.ssn = unformattedValue
      })
    }
  }

  return (
    <BtSocialSecurityNumberInput
      size="large"
      label={t('Finance.SSNlabel')}
      value={store.currentUser.ssn || ''}
      validate={handleValidation}
      onChangeText={handleChange}
      testID={'UnifiedApplication.BusinessOwner.Ssn'}
    />
  )
}

export const OwnerSsnStep: IUnifiedApplicationEditor = {
  options: {
    title: (store: UnifiedApplicationStore) => {
      return store.isOwner || store.isAuthorized
        ? 'Owner.SSN'
        : 'Owner.AuthorizedSSN'
    },
  },
  component: observer(OwnerSsnEditor),
}
