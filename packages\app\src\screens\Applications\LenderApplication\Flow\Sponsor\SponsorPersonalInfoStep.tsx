import React, { FC } from 'react'
import { View } from 'react-native'
import { BtInput_v1 } from '@linqpal/components/src/ui'
import { observer } from 'mobx-react-lite'
import { useTranslation } from 'react-i18next'
import { runInAction } from 'mobx'
import { useLenderApplication } from '../../LenderApplicationContext'
import { ILenderApplicationEditor } from '../getLenderApplicationEditor'
import SecurityNotice from '../../../../../ui/molecules/SecurityNotice'
import { ISponsorPersonalInfo } from '@linqpal/models/src/applications/lender/ILenderApplicationDraft'
import { Validators } from '../../../Helpers/Validators'
import { BtDateInput_v1 } from '@linqpal/components/src/ui/BtDateInput_v1'
import { BtSocialSecurityNumberInput_v1 } from '@linqpal/components/src/ui/BtSocialSecurityNumberInput_v1'
import { BtPhoneInput_v1 } from '@linqpal/components/src/ui/BtPhoneInput_v1'
import { editorStyles } from '../editorStyles'

const SponsorPersonalInfoEditor: FC = observer(() => {
  const { t } = useTranslation('application')
  const store = useLenderApplication()

  const setValue = (key: keyof ISponsorPersonalInfo, value: string) => {
    runInAction(() => {
      if (!store.draft.data.sponsor.personalInfo) {
        store.draft.data.sponsor.personalInfo = {}
      }

      store.draft.data.sponsor.personalInfo[key] = value
    })
  }

  return (
    <View style={editorStyles.formContainer}>
      <View style={editorStyles.row}>
        <View style={editorStyles.columnLeft}>
          <BtInput_v1
            required
            autoFocus
            value={store.draft.data.sponsor.personalInfo?.fullName || ''}
            label={t('LenderApplication.Flow.Sponsor.PersonalInfo.FullName')}
            testID="LenderApplication.Sponsor.FullName"
            onChangeText={(val: string) => setValue('fullName', val)}
            validate={Validators.required}
          />
        </View>
        <View style={editorStyles.columnRight}>
          <BtInput_v1
            required
            value={store.draft.data.sponsor.personalInfo?.email || ''}
            label={t('LenderApplication.Flow.Sponsor.PersonalInfo.Email')}
            testID="LenderApplication.Sponsor.Email"
            onChangeText={(val: string) => setValue('email', val)}
            validate={Validators.email}
          />
        </View>
      </View>

      <View style={editorStyles.row}>
        <View style={editorStyles.columnLeft}>
          <BtPhoneInput_v1
            required
            value={store.draft.data.sponsor.personalInfo?.phone || ''}
            label={t('LenderApplication.Flow.Sponsor.PersonalInfo.Phone')}
            onChangeText={(val) => setValue('phone', val.parsed)}
            testID="LenderApplication.Sponsor.Phone"
            validate={Validators.phone}
          />
        </View>
        <View style={editorStyles.columnRight}>
          <BtDateInput_v1
            required
            value={store.draft.data.sponsor.personalInfo?.birthdate || ''}
            label={t('LenderApplication.Flow.Sponsor.PersonalInfo.Birthdate')}
            testID="LenderApplication.Sponsor.Birthdate"
            onChangeText={(val: string) => setValue('birthdate', val)}
            validate={Validators.birthdate}
          />
        </View>
      </View>

      <View style={editorStyles.singleColumnRow}>
        <BtSocialSecurityNumberInput_v1
          required
          value={store.draft.data.sponsor.personalInfo?.ssn || ''}
          label={t('LenderApplication.Flow.Sponsor.PersonalInfo.Ssn')}
          testID="LenderApplication.Sponsor.Ssn"
          onChangeText={(val: string) => setValue('ssn', val)}
          validate={Validators.ssn}
        />
      </View>

      <SecurityNotice
        description={t('LenderApplication.Flow.SecurityNotice')}
      />
    </View>
  )
})

// Styles moved to commonStyles.ts

export const SponsorPersonalInfoStep: ILenderApplicationEditor = {
  options: {
    title: 'LenderApplication.Flow.Sponsor.PersonalInfo.Title',
  },
  component: SponsorPersonalInfoEditor,
}
