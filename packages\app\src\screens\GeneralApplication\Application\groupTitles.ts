const groupTitles = new Map<string, string>([
  ['businessInfo', 'BusinessDetails'],
  ['finance', 'BusinessFinancialDetails'],
  ['businessOwner', 'BusinessOwner'],
  ['coOwners', 'BusinessCoOwners'],
  ['coOwnerInfo', 'BusinessCoOwners'], // TODO: VK: Unified: for legacy version, remove
  ['bank', 'BankDetails'],
  ['review', 'Review.SectionTitle'],
])

export const getGroupTitle = (groupName: string) => {
  return groupTitles.get(groupName) || ''
}
