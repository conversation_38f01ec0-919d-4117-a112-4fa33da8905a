import React, { FC } from 'react'
import { StyleSheet, Text, View } from 'react-native'
import { Spacer } from '../../../../../ui/atoms'
import { PricingProduct } from '@linqpal/models/src/dictionaries/pricingProduct'
import { PaymentPlanCard } from '../../../../../ui/atoms/PaymentPlanCard'
import { ILoanPaymentPlanModel } from '@linqpal/models'
import { IDownPaymentDetails } from '@linqpal/models/src/types/routes'
import { Invoice } from '../../../InvoicesTab/Invoice/InvoiceDetails'
import ApplicationStore from '../../../../GeneralApplication/Application/ApplicationStore'
import { Trans, useTranslation } from 'react-i18next'
import { LoadingIndicator } from '../../../../Supplier/Settlements/LoadingIndicator'
import { BtPlainText } from '@linqpal/components/src/ui'
import RootStore from '../../../../../store/RootStore'
import { observer } from 'mobx-react-lite'
import { EstimatedDueDatesHint } from './EstimatedDueDatesHint'

interface IProps {
  invoices: Invoice[] // legacy type, review
  paymentPlan?: ILoanPaymentPlanModel | null
  onSelect: (
    paymentPlan: ILoanPaymentPlanModel,
    downPayment: IDownPaymentDetails | null,
  ) => void
}

export const PaymentPlansList: FC<IProps> = observer(
  ({ invoices, paymentPlan, onSelect }) => {
    const { t } = useTranslation('global')
    const { paymentPlans, loadingPlans } = ApplicationStore

    const downPaymentSettings =
      RootStore.userStore.company?.settings?.downPaymentDetails

    const totalAmount = invoices.reduce(
      (sum, inv) => sum + Number(inv.total_amount),
      0,
    )

    const handleSelect = (
      paymentPlanDetails: ILoanPaymentPlanModel,
      downPaymentDetails: IDownPaymentDetails | null,
    ) => {
      onSelect(paymentPlanDetails, downPaymentDetails)
    }

    const useDownPayment = invoices?.some(
      (invoice) => !!invoice.supplierInvitationDetails,
    )

    return (
      <>
        {loadingPlans ? (
          <Loading />
        ) : (
          <View style={{ width: '100%' }}>
            {!!paymentPlans.length && <Spacer height={12} />}

            {paymentPlans
              .filter((plan) => plan.product === PricingProduct.LineOfCredit)
              .map((plan, index) => (
                <View key={index}>
                  <PaymentPlanCard
                    onPress={handleSelect}
                    paymentOption={plan}
                    selected={paymentPlan?.id === plan._id}
                    paymentAmount={totalAmount}
                    useDownPayment={useDownPayment}
                  />
                  <Spacer height={16} />
                </View>
              ))}

            {useDownPayment && downPaymentSettings?.isRequired ? (
              <View>
                <BtPlainText style={styles.footerText}>
                  <Trans
                    t={t}
                    i18nKey="PaymentPlanCard.down-payment-required-message"
                    values={{
                      percentage: downPaymentSettings.downPaymentPercentage,
                    }}
                    components={{
                      styled: <Text style={{ fontWeight: '700' }} />,
                    }}
                  />
                </BtPlainText>
              </View>
            ) : (
              <View>
                <EstimatedDueDatesHint />
              </View>
            )}
          </View>
        )}
      </>
    )
  },
)

const Loading: FC = () => {
  const { t } = useTranslation('global')

  return (
    <View style={styles.loadingView}>
      <LoadingIndicator maxWidth={32} />
      <BtPlainText style={styles.loadingText}>
        {t('CreditApplication.LoadingPlans')}
      </BtPlainText>
    </View>
  )
}

const styles = StyleSheet.create({
  loadingText: {
    fontWeight: '500',
  },
  loadingView: {
    flexDirection: 'row',
    gap: 10,
    minHeight: 250,
    alignItems: 'center',
    justifyContent: 'center',
  },
  footerText: {
    maxWidth: 600,
  },
})
