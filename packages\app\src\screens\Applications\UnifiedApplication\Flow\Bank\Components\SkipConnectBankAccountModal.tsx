import React, { FC } from 'react'
import { useTranslation } from 'react-i18next'
import { BtText } from '@linqpal/components/src/ui'
import { useResponsive } from '@linqpal/components/src/hooks'
import { WarningWithCircle } from '../../../../../../assets/icons'
import { Alert } from '../../../../../GeneralApplication/Application/components'
import { Spacer } from '../../../../../../ui/atoms'

interface Props {
  isVisible: boolean
  isLoanApplication: boolean
  onClose: () => void
  onLoginToBank: () => void
  onEnterManually: () => void
}

export const SkipConnectBankAccountModal: FC<Props> = ({
  isVisible,
  isLoanApplication,
  onClose,
  onLoginToBank,
  onEnterManually,
}) => {
  const { t } = useTranslation('application')
  const { sm } = useResponsive()

  return (
    <Alert
      icon={
        <>
          <WarningWithCircle />
          <Spacer height={26} />
        </>
      }
      visible={isVisible}
      onClose={onClose}
      title={t('Bank.SkipConnectTitle')}
      buttons={[
        {
          testID: 'BankConfirmationGoBack',
          label: sm
            ? t('Bank.SkipConnectEnterManually')
            : t('Bank.AddManually'),
          onPress: onEnterManually,
        },
        {
          testID: 'BankConfirmationContinue',
          label: sm ? t('Bank.SkipConnectLoginToBank') : t('Bank.SearchBank'),
          onPress: onLoginToBank,
        },
      ]}
      buttonsContainerStyle={
        sm ? { width: '90%', alignSelf: 'center' } : undefined
      }
    >
      <Spacer height={10} />
      <BtText
        style={{
          fontSize: 16,
          color: '#335C75',
          textAlign: 'center',
          width: sm ? 430 : 300,
          lineHeight: 26,
        }}
        testID={'BankConfirmationSubTitle'}
      >
        {t(
          isLoanApplication
            ? 'Bank.SkipConnectLoanDescription'
            : 'Bank.SkipConnectDescription',
        )}
      </BtText>
      <Spacer height={10} />
    </Alert>
  )
}
