import {
  Draft as DraftModel,
  Group as DraftGroupModel,
  IDraftModel,
  Item as DraftItemModel,
} from '@linqpal/models'
import mongoose, { Schema } from 'mongoose'
import createSchema from '../helpers/createSchema'
import { LenderApplicationSchema } from './lenderApplication.model'

const DraftItemSchema = new Schema(createSchema(DraftItemModel), {
  _id: false,
  timestamps: false,
})

const DraftGroupSchema = createSchema(DraftGroupModel)

DraftGroupSchema.items = { type: [DraftItemSchema] }

const draftGroupSchema = new Schema(DraftGroupSchema, {
  _id: false,
  timestamps: false,
})

const DraftSchema = createSchema(DraftModel)

DraftSchema.data = { type: Map, of: draftGroupSchema }
DraftSchema.filled = { type: [String] }
DraftSchema.company_id.index = true
DraftSchema.lenderApplication = LenderApplicationSchema
DraftSchema.visitedSteps = { type: [String] }

const draftSchema = new Schema<IDraftModel>(DraftSchema)
export const Draft = mongoose.model<IDraftModel>(DraftModel.name, draftSchema)
