import { FC } from 'react'
import { OwnerSsnStep } from './BusinessOwner/OwnerSsnStep'
import { FinanceDebtStep } from './Finance/FinanceDebtStep'
import { FinanceCreditLimitStep } from './Finance/FinanceCreditLimitStep'
import { FinanceArAdvanceLimitStep } from './Finance/FinanceArAdvanceLimitStep'
import { EmailStep } from './BusinessInfo/EmailStep'
import { CategoryStep } from './BusinessInfo/CategoryStep'
import { BusinessNameStep } from './BusinessInfo/BusinessNameStep'
import { UnifiedApplicationStore } from '../Store/UnifiedApplicationStore'
import { ReviewStep } from './Review/ReviewStep'
import { CoOwnersStep } from './CoOwners/CoOwnersStep'
import { OwnerAddressStep } from './BusinessOwner/OwnerAddressStep'
import { OwnerBirthdateStep } from './BusinessOwner/OwnerBirthdateStep'
import { TradeStep } from './BusinessInfo/TradeStep'
import { BusinessPhoneStep } from './BusinessInfo/BusinessPhoneStep'
import { BusinessAddressStep } from './BusinessInfo/BusinessAddressStep'
import { BusinessStartDateStep } from './BusinessInfo/BusinessStartDateStep'
import { BusinessTypeStep } from './BusinessInfo/BusinessTypeStep'
import { BusinessEinStep } from './BusinessInfo/BusinessEinStep'
import { FinanceRevenueStep } from './Finance/FinanceRevenueStep'
import { OwnerInvitationStep } from './BusinessOwner/OwnerInvitationStep'
import { PrimaryBankAccountStep } from './Bank/PrimaryBankAccountStep'
import { IsOwnerStep } from './BusinessOwner/IsOwnerStep'
import { IsAuthorizedStep } from './BusinessOwner/IsAuthorizedStep'
import { BusinessOwnerPercentageStep } from './BusinessOwner/BusinessOwnerPercentageStep'
import { Steps } from '@linqpal/models/src/applications/unified/UnifiedApplicationSteps'

export type ApplicationStepTitle =
  | string
  | ((store: UnifiedApplicationStore) => string)

// TODO: VK: Unified: Review
// TODO: VK: Unified: Remove extra type options from title, description (just for backward compatibility for refactoring time)

export interface IUnifiedApplicationStepOptions {
  title?: ApplicationStepTitle
  titleStyle?: any
  description?: string
  descriptionStyle?: any
  canGoBack?: boolean
  showNavigationButtons?: boolean // TODO: VK: Unified: replace with canMoveNext
  canSkip?: boolean
  showGroupTitle?: boolean
  onMoveBack?: (store: UnifiedApplicationStore) => boolean
  onMoveNext?: (store: UnifiedApplicationStore) => void
}

export interface IUnifiedApplicationEditor {
  options?: IUnifiedApplicationStepOptions
  component: FC<any> | ((props: any) => JSX.Element)
}

export function getUnifiedApplicationEditor(
  path: string,
): IUnifiedApplicationEditor {
  switch (path) {
    // Business Info
    case Steps.businessInfo.email:
      return EmailStep
    case Steps.businessInfo.category:
      return CategoryStep
    case Steps.businessInfo.businessName:
      return BusinessNameStep
    case Steps.businessInfo.trade:
      return TradeStep
    case Steps.businessInfo.businessPhone:
      return BusinessPhoneStep
    case Steps.businessInfo.businessAddress:
      return BusinessAddressStep
    case Steps.businessInfo.startDate:
      return BusinessStartDateStep
    case Steps.businessInfo.type:
      return BusinessTypeStep
    case Steps.businessInfo.ein:
      return BusinessEinStep

    // Finance
    case Steps.finance.revenue:
      return FinanceRevenueStep
    case Steps.finance.debt:
      return FinanceDebtStep
    case Steps.finance.creditLimit:
      return FinanceCreditLimitStep
    case Steps.finance.arAdvanceRequestedLimit:
      return FinanceArAdvanceLimitStep

    // Business Owner
    case Steps.businessOwner.isOwner:
      return IsOwnerStep
    case Steps.businessOwner.ownershipPercentage:
      return BusinessOwnerPercentageStep
    case Steps.businessOwner.isAuthorized:
      return IsAuthorizedStep
    case Steps.businessOwner.authorizedDetails:
      return OwnerInvitationStep
    case Steps.businessOwner.address:
      return OwnerAddressStep
    case Steps.businessOwner.birthdate:
      return OwnerBirthdateStep
    case Steps.businessOwner.ssn:
      return OwnerSsnStep

    // Co Owners
    case Steps.coOwners.coOwners:
      return CoOwnersStep

    // Primary Bank Account
    case Steps.bank.primaryAccount:
      return PrimaryBankAccountStep

    // Review
    case Steps.review.review:
      return ReviewStep

    default:
      throw new Error(`no editor for path: ${path}`)
  }
}
