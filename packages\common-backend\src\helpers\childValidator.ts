import { encryptValidate } from './cryptValidator'
import { validatePhone } from './phoneValidator'
import { Document, Query, Schema } from 'mongoose'

type ValidatorTypes = 'crypt' | 'phone'

export function wireChildValidator<T extends Document>(
  schema: Schema<any>,
  child: string,
  column: string,
  type: ValidatorTypes,
) {
  schema.pre('validate', preValidate<T>(child, column, type))
  schema.pre('updateOne', preUpdateOne<T>(child, column, type))
}

function preValidate<T extends Document>(
  child: string,
  column: string,
  type: ValidatorTypes,
) {
  return async function (this: T, next: () => void) {
    const childRecord = (this as any)[child] as Record<string, any>
    if (childRecord && type === 'crypt') {
      childRecord[column] = await encryptValidate(childRecord, column)
    }
    if (childRecord && type === 'phone') {
      childRecord[column] = validatePhone(childRecord[column])
    }
    next()
  }
}

function preUpdateOne<TDoc>(
  child: string,
  column: string,
  type: ValidatorTypes,
) {
  return async function (this: Query<TDoc, TDoc>, next: () => void) {
    const data = this.getUpdate() as any
    if (data) {
      const val = (this as any)[child]
      const childRecord = data[child] as Record<string, any>
      if (val && type === 'crypt') {
        childRecord[column] = await encryptValidate(childRecord, column)
      }
      if (val && type === 'phone') {
        childRecord[column] = validatePhone(childRecord[column])
      }
      this.setUpdate(data)
    }

    next()
  }
}
