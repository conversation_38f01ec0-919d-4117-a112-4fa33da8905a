import React from 'react'
import { Linking, View, StyleSheet } from 'react-native'
import { BtText } from '@linqpal/components/src/ui'
import { PhoneIconBlue } from '../../../../assets/icons'
import { currencyMask } from '../../../../utils/helpers/masking'
import { parsePhoneNumber } from 'libphonenumber-js'
import { PayableStatusLabel } from '../../../../ui/molecules/StatusLabel/PayableStatusLabel'

export default function Header({
  company,
  totalAmount,
  style,
  companyFontSize = 18,
  amountLabel,
  amountLabelStyles = {
    marginTop: 4,
    color: '#758590',
  },
  status = '',
  showInvoiceStatus = false,
}) {
  const handlePhonePress = () => {
    if (company?.phone) {
      Linking.openURL(`tel:${company.phone}`)
    }
  }

  const formattedPhone = company?.phone
    ? parsePhoneNumber(company.phone, 'US').formatInternational().slice(3)
    : null
  const formattedPhoneWithParentheses = formattedPhone
    ? `(${formattedPhone.slice(0, 3)})${formattedPhone.slice(3)}`
    : null
  return (
    <View style={style}>
      <View style={styles.leftContainer}>
        <BtText
          size={companyFontSize}
          style={styles.companyName}
          weight={'600'}
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {company?.name}
        </BtText>
        {showInvoiceStatus ? (
          <View style={styles.statusContainer}>
            <PayableStatusLabel status={status} />
          </View>
        ) : (
          formattedPhoneWithParentheses && (
            <View style={styles.phoneContainer}>
              <PhoneIconBlue />
              <BtText
                style={styles.phoneText}
                weight={'500'}
                onPress={handlePhonePress}
              >
                {formattedPhoneWithParentheses}
              </BtText>
            </View>
          )
        )}
      </View>
      <View style={styles.rightContainer}>
        <BtText weight="700" size={18} style={styles.totalAmount}>
          {currencyMask(totalAmount)}
        </BtText>
        <BtText style={[amountLabelStyles, styles.amountLabel]} weight={'500'}>
          {amountLabel}
        </BtText>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  leftContainer: {
    flex: 1,
  },
  companyName: {
    lineHeight: 28,
    letterSpacing: 0.2,
    color: '#001929',
    fontFamily: 'Inter',
  },
  statusContainer: {
    marginTop: 4,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-start',
  },
  phoneContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  phoneIcon: {
    alignSelf: 'center',
    width: 13,
    height: 13,
    marginTop: 2,
  },
  phoneText: {
    color: '#00A0F3',
    marginLeft: 5,
  },
  rightContainer: {
    alignItems: 'flex-end',
  },
  totalAmount: {
    color: '#001929',
    lineHeight: 28,
    letterSpacing: 0.2,
    fontFamily: 'Inter',
  },
  amountLabel: {
    lineHeight: 21,
    fontFamily: 'Inter',
  },
  normalText: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 21,
    color: '#758590',
    fontFamily: 'Inter',
  },
  statusValue: {
    maxWidth: '50%',
  },
})
