import { formatMultilineAddress } from '@linqpal/models/src/helpers/addressFormatter'
import moment from 'moment-timezone'
import { OwnerTypes } from '@linqpal/models/src/dictionaries/UnifiedApplication'
import { formatPersonName } from '@linqpal/models/src/helpers/personNameFormatter'
import { LogicalError } from '@linqpal/models/src/types/exceptions'
import { exceptions } from '@linqpal/models'
import { Logger } from '../logger/logger.service'
import { ICompany, IUser } from '../../models/types'
import {
  AgreementService,
  CompanyService,
  DocumentVersioningService,
  emailService,
  getApiURL,
} from '../../../index'
import DraftService from '../draft.service'
import { migrateCompany } from '../company/api'
import { AgreementType, IAgreementFileDetails } from './types'
import PersonalGuarantorAgreement from './personalGuarantorAgreement'
import {
  createApproval,
  getDownloadLink,
  getEmailAttachment,
} from '../documentVersioning/document.versioning.service'
import { createMasterAgreementFromDraft } from './agreement.service'
import { IApprover } from '../documentVersioning/IApprover'
import awsService from '../aws.service'

export class CreditAgreementsService {
  private static logger = new Logger({
    module: 'CreditAgreementsService',
  })

  // submits all agreements for supplier - seller, invoice purchase, guaranty & security etc.
  public static async submitAgreements(
    company: ICompany,
    submitter: IUser,
    ipAddress: string,
  ) {
    // do not send approval emails to co-owners when submitter approves updated version of an agreement
    const agreementCheck = await this.tryUpdateMasterAgreement(company)

    if (agreementCheck.isActual) {
      return agreementCheck.agreement
    }

    this.logger.info('start generating agreement file')

    const locNumber = await CompanyService.generateLoCNumber(company.id)
    this.logger.info(
      `generated LoC number for company ${company.id}: ${locNumber}`,
    )

    const [normalizedDraft] = await Promise.all([
      DraftService.getNormalizedDraft(company.id),
      // force migrate company from mongo to Postgres here to avoid concurrent migrations of same company
      migrateCompany(company.id),
    ])

    const agreementJobs: Promise<IAgreementFileDetails>[] = [
      createMasterAgreementFromDraft(company.id),
    ]

    const guarantorAgreementData = {
      date: moment().format('MM/DD/YYYY'),
      locAccount: locNumber.toString(),
      borrowerCompany: normalizedDraft.businessInfo_businessName.legalName,
    }

    const submitterAddress = formatMultilineAddress(
      normalizedDraft.businessOwner_address,
    )

    // submitter PGA
    agreementJobs.push(
      PersonalGuarantorAgreement.submit(
        company.id,
        normalizedDraft.businessOwner_id,
        OwnerTypes.INDIVIDUAL,
        {
          ...guarantorAgreementData,
          ownerName: formatPersonName(
            normalizedDraft.businessOwner_firstName || submitter.firstName,
            normalizedDraft.businessOwner_lastName || submitter.lastName,
          ),
          ownerEmail: normalizedDraft.businessOwner_email,
          ownerAddress1: submitterAddress.line1,
          ownerAddress2: submitterAddress.line2,
        },
      ),
    )

    // co-owners' PGAs
    const coOwners = DraftService.getCoOwners(normalizedDraft)
    agreementJobs.push(
      ...coOwners.map((coOwner) => {
        const coOwnerAddress = formatMultilineAddress(coOwner)
        return PersonalGuarantorAgreement.submit(
          company.id,
          coOwner.id,
          coOwner.type,
          {
            ...guarantorAgreementData,
            ownerName: formatPersonName(coOwner.firstName, coOwner.lastName),
            ownerEntity: coOwner.entityName,
            ownerEmail: coOwner.email,
            ownerAddress1: coOwnerAddress.line1,
            ownerAddress2: coOwnerAddress.line2,
          },
        )
      }),
    )

    const [masterAgreement, , ...coOwnersGuarantorAgreements] =
      await Promise.all(agreementJobs)

    this.logger.info('start submitting approval requests')

    return this.submitCoOwnersApprovalRequests(
      masterAgreement,
      coOwnersGuarantorAgreements,
      normalizedDraft,
      company,
      submitter,
      ipAddress,
    )
  }

  static async submitApprovalRequest(
    masterAgreement: IAgreementFileDetails,
    guarantorAgreement: IAgreementFileDetails | null | undefined,
    approver: IApprover,
    applicantName: string,
    companyName: string,
    userId: string,
  ) {
    if (!masterAgreement.documentId)
      throw new LogicalError('Unable to submit approval for unknown documentId')

    const approvalJwt = await createApproval(masterAgreement.documentId, {
      ownerIdentifier: approver.id,
      firstName: approver.firstName,
      lastName: approver.lastName,
      emailAddress: approver.email,
      ipAddress: '',
      userId: userId,
    })

    if (approvalJwt) {
      this.logger.info(
        `collecting approval email attachments for ${approver.firstName} ${approver.lastName}, approverId ${approver.id}`,
      )

      const [masterAgreementAttachment, guarantorAgreementAttachment] =
        await Promise.all([
          getEmailAttachment(masterAgreement),
          guarantorAgreement
            ? getEmailAttachment(guarantorAgreement)
            : Promise.resolve(null),
        ])

      await emailService.send({
        to: approver.email,
        templateId: emailService.TEMPLATES.LOC_AGREEMENT_APPROVAL_REQUEST,
        dynamicTemplateData: {
          inviteeName: `${approver.firstName} ${approver.lastName}`,
          applicantName: applicantName,
          companyName: companyName,
          link: `${getApiURL()}/credit-agreements?token=${approvalJwt}`,
        },
        attachments: guarantorAgreementAttachment
          ? [masterAgreementAttachment, guarantorAgreementAttachment]
          : [masterAgreementAttachment],
      })

      this.logger.info(
        `approval email sent to ${approver.firstName} ${approver.lastName}`,
      )
    }
  }

  private static async tryUpdateMasterAgreement(
    company: ICompany,
  ): Promise<{ agreement?: IAgreementFileDetails; isActual: boolean }> {
    // LoC number is generated on first submission
    if (company.credit?.LoCnumber) {
      this.logger.info('LoC is accepted already, checking agreement version')

      const agreementMetadata = await AgreementService.getAgreementMetadata(
        company.id,
        AgreementType.MASTER_AGREEMENT,
      )

      if (!agreementMetadata.hasActualVersion) {
        this.logger.info(`updating master agreement for company ${company.id}`)

        const agreement = await createMasterAgreementFromDraft(company.id)
        return { agreement, isActual: true }
      }

      this.logger.info(`master agreement for company ${company.id} is actual`)

      return { isActual: true }
    }

    return { isActual: false }
  }

  private static async submitCoOwnersApprovalRequests(
    masterAgreement: IAgreementFileDetails,
    guarantorAgreements: IAgreementFileDetails[],
    normalizedDraft: any,
    company: ICompany,
    user: IUser,
    ipAddress: string,
  ): Promise<any> {
    if (!normalizedDraft)
      throw new LogicalError(
        'Unable to find application draft to submit approvals',
      )

    if (!masterAgreement?.documentId)
      throw new LogicalError(
        'Unable to submit approvals for unknown documentId',
      )

    const companyName = company.legalName ?? company.name
    const submitterName = `${user.firstName} ${user.lastName}`

    // application submitter approves LoC by default - no email needed
    const saveSubmitterApproval = createApproval(
      masterAgreement.documentId,
      {
        ownerIdentifier: normalizedDraft.businessOwner_id,
        firstName: normalizedDraft.businessOwner_firstName,
        lastName: normalizedDraft.businessOwner_lastName,
        emailAddress: normalizedDraft.businessOwner_email,
        ipAddress: ipAddress,
        userId: user._id.toString(),
      },
      true,
    ).then(() => {}) // to Promise<void>

    const coOwners = DraftService.getCoOwners(normalizedDraft)

    return Promise.all([
      saveSubmitterApproval,
      ...coOwners.map((coOwner) =>
        this.submitApprovalRequest(
          masterAgreement,
          guarantorAgreements.find((a) => a.referenceId === coOwner.id),
          coOwner,
          submitterName,
          companyName,
          user._id.toString(),
        ),
      ),
    ]).catch((e) => {
      console.log(e)
      throw new exceptions.LogicalError('Unable to send approval requests')
    })
  }

  static async approveAgreements(token: string, ipAddress: string) {
    const approval = await DocumentVersioningService.approveDocument(
      token,
      ipAddress,
    )

    const fileLink = await getDownloadLink(approval.documentId)
    const fileContent = await awsService.getS3FileByUrl(fileLink.url)

    return emailService.send({
      to: approval.emailAddress,
      templateId: emailService.TEMPLATES.LOC_AGREEMENT_APPROVAL_CONFIRMATION,
      dynamicTemplateData: {
        inviteeName: `${approval.firstName} ${approval.lastName}`,
      },
      attachments: [
        {
          filename: fileLink.fileName,
          type: 'application/pdf',
          disposition: 'attachment',
          content: fileContent,
        },
      ],
    })
  }
}
