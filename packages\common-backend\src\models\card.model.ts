import mongoose, { ObjectId, Schema } from 'mongoose'
import { VirtualCardStatus } from '@linqpal/models/src/dictionaries'

export interface VirtualCardModel extends mongoose.Document<ObjectId> {
  company_id: string
  invoice_id: string
  name: string
  amount: number
  cardId?: string
  useDate?: Date
  usedAmount?: number
  status: VirtualCardStatus
}

const virtualCardSchema = new Schema<VirtualCardModel>(
  {
    company_id: { type: String, required: true, index: true },
    invoice_id: { type: String, required: true },
    name: { type: String, required: true },
    amount: { type: Number, required: true },
    cardId: { type: String, required: false, index: true },
    useDate: { type: Date, required: false },
    usedAmount: { type: Number, required: false },
    status: { type: String, required: true, default: VirtualCardStatus.ACTIVE },
  },
  { toJSON: { virtuals: true } },
)

export const VirtualCard = mongoose.model<VirtualCardModel>(
  'VirtualCard',
  virtualCardSchema,
)
