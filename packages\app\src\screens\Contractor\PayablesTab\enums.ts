import { invoiceStatus } from '@linqpal/models/src/dictionaries'

export enum TabNames {
  InvoicesTab = 'invoices',
  QuotesTab = 'quotes',
  VendorsTab = 'vendors',
}

export enum RouteTabsNames {
  Pay = 'Pay',
  Payables = 'Payables',
  Vendors = 'Vendors',
}

export enum InvoiceStatusOptions {
  All = 'All Status',
  Paid = 'Paid',
  PaymentProcessing = 'Payment Processing',
  Due = 'Due',
  PastDue = 'Past Due',
  Authorized = 'Authorized',
  Invoiced = 'Invoiced',
  CreditApplied = 'Credit Applied',
  Cancelled = 'Cancelled',
  Expired = 'Expired',
  InvoiceRejected = 'Invoice Rejected',
  Placed = 'Placed',
  Seen = 'Seen',

  AuthorizationInReview = 'Authorization in Review',
  ApplicationProcessing = 'Application Processing',
  ApplicationCanceled = 'Application Cancelled',
  PendingDisbursement = 'Pending Disbursement',
  Failed = 'Failed',
  PaymentError = 'Payment Error',
  Collected = 'Collected',
  Dismissed = 'Dismissed',
}

export const UnviewableInvoiceStatuses = [
  invoiceStatus.cancelled,
  invoiceStatus.paymentProcessing,
  invoiceStatus.expired,
]

export const PaidInvoiceStatuses = [
  invoiceStatus.creditApplied,
  invoiceStatus.paid,
  invoiceStatus.collected,
]

export const PaymentProcessingInvoiceStatuses = [
  invoiceStatus.pendingDisbursement,
]

export const InvoiceStatusTypeItems: { label: string; value: string }[] = [
  {
    label: InvoiceStatusOptions.All,
    value: '',
  },
  {
    label: InvoiceStatusOptions.Due,
    value: invoiceStatus.due,
  },
  {
    label: InvoiceStatusOptions.PastDue,
    value: invoiceStatus.pastDue,
  },
  {
    label: InvoiceStatusOptions.Placed,
    value: invoiceStatus.placed,
  },
  {
    label: InvoiceStatusOptions.Paid,
    value: invoiceStatus.paid,
  },

  {
    label: InvoiceStatusOptions.ApplicationProcessing,
    value: invoiceStatus.applicationProcessing,
  },

  {
    label: InvoiceStatusOptions.Authorized,
    value: invoiceStatus.authorized,
  },
  {
    label: InvoiceStatusOptions.Invoiced,
    value: invoiceStatus.invoiced,
  },
  {
    label: InvoiceStatusOptions.CreditApplied,
    value: invoiceStatus.creditApplied,
  },
  {
    label: InvoiceStatusOptions.Cancelled,
    value: invoiceStatus.cancelled,
  },
  {
    label: InvoiceStatusOptions.Expired,
    value: invoiceStatus.expired,
  },
  {
    label: InvoiceStatusOptions.InvoiceRejected,
    value: invoiceStatus.rejected,
  },
  {
    label: InvoiceStatusOptions.AuthorizationInReview,
    value: invoiceStatus.authorizationInReview,
  },
  {
    label: InvoiceStatusOptions.Failed,
    value: invoiceStatus.paymentFailed,
  },
  {
    label: InvoiceStatusOptions.Collected,
    value: invoiceStatus.collected,
  },
  {
    label: InvoiceStatusOptions.ApplicationCanceled,
    value: invoiceStatus.applicationCancelled,
  },
  /*{
    label: InvoiceStatusOptions.Dismissed,
    value: invoiceStatus.dismissed,
  },*/
]
