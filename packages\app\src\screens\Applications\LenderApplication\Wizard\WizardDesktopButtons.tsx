import React, { FC } from 'react'
import { BtButton } from '@linqpal/components/src/ui'
import { StyleSheet, View } from 'react-native'
import RootStore from '../../../../store/RootStore'
import { useLenderApplication } from '../LenderApplicationContext'
import { useTranslation } from 'react-i18next'
import { observer } from 'mobx-react-lite'

export const WizardDesktopButtons: FC = observer(() => {
  const { t } = useTranslation('application')
  const store = useLenderApplication()

  if (!store.stepOptions.showNavigationButtons) return null

  return (
    <View style={styles.buttonsWrapper}>
      <BtButton
        onPress={() => store.skipStep()}
        status={'basic'}
        disabled={RootStore.isBusy}
        testID="LenderAppication.Wizard.SkipButton"
      >
        {t('SkipForNow')}
      </BtButton>
      <BtButton
        onPress={() => store.moveNext()}
        disabled={RootStore.isBusy || !store.isCurrentStepValid}
        style={{ width: 150 }}
        testID="LenderApplication.Wizard.NextButton"
      >
        {t('Next')}
      </BtButton>
    </View>
  )
})

const styles = StyleSheet.create({
  buttonsWrapper: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'flex-end',
    marginTop: 36,
    gap: 20,
  },
  buttonLabel: {
    fontWeight: '700',
    fontSize: 16,
    color: '#668598',
  },
})
