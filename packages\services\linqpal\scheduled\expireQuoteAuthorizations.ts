import {
  connectToDatabase,
  getEnvironmentVariables,
  Logger,
} from '@linqpal/common-backend'
import { QuoteService } from '@linqpal/common-backend/src/services/quote/quote.service'
import moment from 'moment-timezone'
import { logPromiseErrors } from '@linqpal/common-backend/src/helpers/logging'

const logger = new Logger({
  module: 'quotes',
  subModule: 'expireQuoteAuthorizations',
})

export async function expireQuoteAuthorizations(): Promise<void> {
  await getEnvironmentVariables()
  await connectToDatabase()

  logger.info(`started batch expiration job at ${moment().toDate()}`)

  const results = await Promise.allSettled([
    QuoteService.batchNotifyNearingExpiration(),
    QuoteService.batchExpireQuoteAuthorizations(),
  ])

  logPromiseErrors(results, logger)
}
