import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { observer } from 'mobx-react'
import { useResponsive } from '../../../utils/hooks'

import { Spacer } from '../../../ui/atoms'
import ApplicationStore from './ApplicationStore'
import RootStore from '../../../store/RootStore'
import {
  BackButton,
  CloseApplicationAlert,
  Description,
  DesktopButtons,
  Header,
  MobileButtons,
  ProgressBar,
  Question,
  Title,
} from './components'
import { FlowController, ModelUpdateInfo } from './FlowController'
import { ScrollView, View } from 'react-native'
import { getGroupTitle } from './groupTitles'
import { CompanyUtils } from '@linqpal/models/src/helpers/companyUtils'

export interface CompProps {
  doc: any
  group: string
  item: string
  review: boolean
  sm: boolean
  onValueUpdate: (updateInfo: ModelUpdateInfo) => void
}

export default observer(function Editor({ document }) {
  const { t } = useTranslation('application')
  const [displayCloseAlert, setDisplayCloseAlert] = useState(false)
  const [showNavigationControls, setShowNavigationControls] = useState(true)
  const { isBusy, userStore } = RootStore
  const { isOptionalStep } = ApplicationStore
  const { screenWidth, sm } = useResponsive()

  const flowController = useMemo(() => new FlowController(document), [document])

  const currentPath = flowController.getCurrentStep(document)

  const [groupName, itemName] = currentPath.split('.')

  const {
    component: Component,
    title,
    description,
    descriptionStyle,
    canSkip = true,
    canMoveNext = true,
    showFooterMessage,
    handleBack,
  } = flowController.findComponentByPath(currentPath) || {}

  useEffect(() => {
    const flowSteps = flowController.getFlowSteps(true)

    if (!flowSteps.includes(currentPath)) {
      document.setCurrent('review.review')
    }
  }, [currentPath, document, flowController])

  useEffect(() => {
    if (document?.current === 'review.agreement') {
      document.setCurrent('review.review')
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const [currentValue, setCurrentValue] = useState<ModelUpdateInfo>({
    value: '',
    filled: false,
    group: groupName,
    identifier: itemName,
  })

  const onValueUpdate = useCallback((updateInfo: ModelUpdateInfo) => {
    setCurrentValue(updateInfo)
  }, [])

  const onSetNavigationVisible = useCallback((visible: boolean) => {
    setShowNavigationControls(visible)
  }, [])

  const onNext = () =>
    canMoveNext &&
    flowController.moveToNextStep(
      currentValue,
      getGroupTitle(currentValue.group),
    )

  const onSkip = () =>
    canSkip &&
    flowController.skipToNextStep(
      currentValue,
      getGroupTitle(currentValue.group),
    )

  const onBack = () => {
    // let the current component do its own back handling
    // and do backward navigation only when needed
    if (!handleBack || !handleBack()) {
      setShowNavigationControls(true)
      flowController.moveToPreviousStep(
        currentValue,
        getGroupTitle(currentValue.group),
      )
    }
  }

  const onCloseApplication = () => {
    setDisplayCloseAlert(false)

    flowController.updateDocument(
      currentValue,
      getGroupTitle(currentValue.group),
    )

    ApplicationStore.setCurrentCoOwnerIndex(-1)
  }

  const flowSteps = flowController.getFlowSteps()
  const canSubmit = ApplicationStore.canSubmit(flowSteps)

  const flowStep = RootStore.userStore?.document?.current
  const isPreview = flowStep === 'review.preview'
  const disableNext = !currentValue?.filled && !isOptionalStep(flowStep)
  const isReview = flowStep === 'review.review'

  const scrollRef = useRef<any>()

  useEffect(() => {
    // reset scroll position when navigating between large components
    // like preview -> coowner -> preview
    scrollRef.current?.scrollTo({ y: 0, animated: false })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [document?.current])

  const screenTitle = ApplicationStore.isGetPaidApplication
    ? t('GetPaidApplication')
    : ApplicationStore.isCreditApplication
    ? t('CreditRequest')
    : ApplicationStore.isInHouseCreditApplication
    ? t('InHouseCreditApplication', {
        supplierName: CompanyUtils.getCompanyName(
          userStore.suppliers?.find(
            (s) => s.id === ApplicationStore.supplierId,
          ),
        ),
      })
    : ''

  return (
    <View style={{ flex: 1 }}>
      <Header
        title={screenTitle}
        sm={sm}
        t={t}
        skip={canSkip && showNavigationControls}
        onClose={() => setDisplayCloseAlert(true)}
        onSkip={onSkip}
      />
      {currentPath !== 'review.agreement' && (
        <>
          <Title text={t(getGroupTitle(groupName) as any)} />
          <ProgressBar progress={flowController.getProgress()} />
        </>
      )}
      <ScrollView
        ref={scrollRef}
        contentContainerStyle={{ flexGrow: 1, alignItems: 'center' }}
        showsVerticalScrollIndicator={false}
      >
        <View
          style={{
            paddingHorizontal: sm ? 0 : 20,
            paddingVertical: isPreview ? 0 : 20,
            flex: 1,
            minWidth: screenWidth > 700 ? 700 : '100%',
            maxWidth: screenWidth > 700 ? 700 : undefined,
          }}
        >
          {sm && flowController.canMoveToPreviousStep() && (
            <div
              style={{
                position: 'sticky',
                top: 0,
                padding: isPreview ? '20px 0' : 0,
                zIndex: 1000,
                backgroundColor: 'white',
              }}
            >
              <BackButton t={t} sm={sm} onPress={onBack} />
            </div>
          )}

          <Spacer height={sm ? 20 : 10} />
          <View>
            {currentPath !== 'review.agreement' ? (
              <Question t={t} sm={sm} title={title} />
            ) : (
              <div style={{ textAlign: 'center' }}>
                <Question t={t} sm={sm} title={title} />
              </div>
            )}
          </View>
          <View>
            <Description
              description={description}
              sm={sm}
              t={t}
              style={descriptionStyle}
            />
          </View>

          {Component ? (
            <Component
              doc={document}
              group={groupName}
              item={itemName}
              sm={sm}
              onValueUpdate={onValueUpdate}
              flowController={flowController}
              setNavigationVisible={onSetNavigationVisible}
            />
          ) : null}

          {showNavigationControls && (
            <DesktopButtons
              t={t}
              sm={sm}
              next={canMoveNext}
              onNext={onNext}
              skip={canSkip && onSkip}
              document={document}
              currentItem={itemName}
              disableNext={disableNext}
              isBusy={isBusy}
            />
          )}
        </View>
        {showNavigationControls && (
          <MobileButtons
            t={t}
            sm={sm}
            next={canMoveNext}
            onNext={onNext}
            onBack={onBack}
            document={document}
            currentItem={itemName}
            disableNext={disableNext}
            isBusy={isBusy}
            canSubmit={canSubmit}
            showFooterMessage={showFooterMessage}
            isReview={isReview}
          />
        )}
      </ScrollView>
      {displayCloseAlert && (
        <CloseApplicationAlert
          onCloseAlert={() => setDisplayCloseAlert(false)}
          onCloseApplication={onCloseApplication}
        />
      )}
    </View>
  )
})
