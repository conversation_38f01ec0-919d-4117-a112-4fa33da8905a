import { IBankAccountModel } from '@linqpal/models'
import { BankAccountType } from './BankAccountType'

export const defaultCreateBankAccountModel: CreateBankAccountModel = {
  name: '',
  accountType: BankAccountType.Checking,
  accountNumber: '',
  accountholderName: '',
  routingNumber: '',
  isManualEntry: true,
}

export type CreateBankAccountModel = Pick<
  IBankAccountModel,
  | 'name'
  | 'accountNumber'
  | 'accountholderName'
  | 'routingNumber'
  | 'accountType'
  | 'isManualEntry'
>
