import React, { FC } from 'react'
import { BtButton } from '@linqpal/components/src/ui'
import { useTranslation } from 'react-i18next'
import { observer } from 'mobx-react-lite'
import { useLenderApplication } from './LenderApplicationContext'
import { View } from 'react-native'

export const LenderApplicationWelcomePage: FC = observer(() => {
  const { t } = useTranslation('application')

  const [saving, setSaving] = React.useState(false)

  const store = useLenderApplication()

  const handlePress = () => {
    if (!store.draft.id) {
      setSaving(true)
      store
        .saveDraft()
        .then(() => store.hideWelcomePage())
        .catch((e) => console.error(e))
        .finally(() => setSaving(false))
    } else {
      store.hideWelcomePage()
    }
  }

  return (
    <View>
      <BtButton onPress={handlePress} loading={saving} style={{ width: '200' }}>
        {t('LenderApplication.WelcomePage.StartApplicationButton')}
      </BtButton>
    </View>
  )
})
