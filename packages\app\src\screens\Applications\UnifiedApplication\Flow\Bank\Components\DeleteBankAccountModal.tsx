import React, { FC } from 'react'
import { useTranslation } from 'react-i18next'
import { BtText } from '@linqpal/components/src/ui'
import { useResponsive } from '@linqpal/components/src/hooks'
import { WarningWithCircle } from '../../../../../../assets/icons'
import { Spacer } from '../../../../../../ui/atoms'
import { Alert } from '../../../../../GeneralApplication/Application/components'

interface Props {
  isVisible: boolean
  isLoading: boolean
  onClose: () => void
  onConfirm: () => void
}

export const DeleteBankAccountModal: FC<Props> = ({
  isVisible,
  isLoading,
  onClose,
  onConfirm,
}) => {
  const { t } = useTranslation('application')
  const { sm } = useResponsive()

  return (
    <Alert
      icon={
        <>
          <WarningWithCircle />
          <Spacer height={26} />
        </>
      }
      visible={isVisible}
      onClose={onClose}
      title={t('Bank.DeleteModalTitle')}
      buttons={[
        {
          testID: 'BankDeleteKeep',
          label: sm
            ? t('Bank.DeleteModalCancel')
            : t('Bank.DeleteModalCancelShort'),
          onPress: onClose,
        },
        {
          testID: 'BankDeleteConfirm',
          label: sm
            ? t('Bank.DeleteModalConfirm')
            : t('Bank.DeleteModalConfirmShort'),
          loading: isLoading,
          disabled: isLoading,
          onPress: onConfirm,
        },
      ]}
      buttonsContainerStyle={
        sm ? { width: '90%', alignSelf: 'center' } : undefined
      }
    >
      <Spacer height={10} />
      <BtText
        style={{
          fontSize: 16,
          color: '#335C75',
          textAlign: 'center',
          width: sm ? 430 : 300,
          lineHeight: 26,
        }}
        testID="BannkDeletionSubTitle"
      >
        {t('Bank.DeleteModalDescription')}
      </BtText>
    </Alert>
  )
}
