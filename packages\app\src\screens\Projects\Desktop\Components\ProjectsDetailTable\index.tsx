import moment from 'moment'
import startCase from 'lodash/startCase'
import { observer } from 'mobx-react-lite'
import { useTranslation } from 'react-i18next'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useRoute } from '@react-navigation/native'

import RowAction from './RowAction'
import { routes2 } from '@linqpal/models'
import { invoiceStatus } from '@linqpal/models/src/dictionaries'
import { BtStatus } from '@linqpal/components/src/ui'
import { customStyles } from './customStyles'
import { BtApiTable, BtSelect, BtButton } from '@linqpal/components/src/ui'
import { currencyMask } from '../../../../../utils/helpers/masking'
import { ProjectsEmptyContent } from '../../Components/ProjectsEmptyContent'
import { styles } from './styles'
import projectsStore from '../../../Store'

type ProjectsDetailTableProps = {
  projectsData?: any
  openSidebar?: any
  setChosen?: any
  onChange?: any
  duration?: any
  params?: any
  status?: any
}

const ProjectDetailFilter = ({
  openSidebar,
  updateType,
  refresher,
  onChange,
  params,
  value,
}) => {
  const { t } = useTranslation('global')

  const setStatus = useCallback(
    (item) => onChange({ ...item, status: item?.value }),
    [onChange],
  )

  const { duration = '0' } = useMemo(() => ({ ...value }), [value])
  const setDuration = useCallback(
    (item) => onChange({ ...item, duration: item?.value }),
    [onChange],
  )

  const filters = {
    All: '',
    Paid: invoiceStatus.paid,
    Due: invoiceStatus.due,
    'Past Due': invoiceStatus.pastDue,
    Dismissed: invoiceStatus.dismissed,
    Cancelled: invoiceStatus.cancelled,
    Expired: invoiceStatus.expired,
    'Invoice Rejected': invoiceStatus.rejected,
    Placed: invoiceStatus.placed,
    'Application Processing': invoiceStatus.applicationProcessing,
    'Application Cancelled': invoiceStatus.applicationCancelled,
    'Application Rejected': invoiceStatus.applicationRejected,
  }
  const payablesFilters = [
    ...Object.entries(filters).map(([key, val]) => ({
      value: val,
      label: startCase(key),
    })),
  ]
  const receivablesFilters = [
    { value: '', label: t('Projects.AllStatuses') },
    ...Object.entries(invoiceStatus).map(([key, val]) => ({
      value: val,
      label: startCase(key),
    })),
  ]
  useEffect(() => {
    setStatus('')
  }, [refresher, setStatus])

  return (
    <>
      {(params?.category !== 'receivable' && params?.status !== 'PAID') ||
      (params?.category !== 'payable' && params?.status !== 'PAID') ? (
        <BtSelect
          onChange={setStatus}
          testID={'projects-detail-status-filter'}
          value={value.status || ''}
          keyName={'value'}
          clearable={false}
          labelName={'label'}
          options={
            params.category === 'payable' ? payablesFilters : receivablesFilters
          }
          style={{ marginLeft: 10, minWidth: 180, marginTop: 0, minHeight: 54 }}
        />
      ) : null}

      <BtSelect
        onChange={setDuration}
        testID={'projects-detail-duration-filter'}
        style={{ marginTop: 0, marginLeft: 10, minHeight: 54 }}
        value={duration}
        labelName="label"
        clearable={false}
        keyName="value"
        options={[
          { value: '0', label: t('Projects.AllDates') },
          { value: '30', label: t('Projects.Last30days') },
          { value: '60', label: t('Projects.Last60days') },
          { value: '90', label: t('Projects.Last90days') },
        ]}
      />
      {(params?.category !== 'receivable' && params?.status !== 'PAID') ||
      (params?.category !== 'payable' && params?.status !== 'PAID') ? (
        <BtButton
          testID={'projects-detail-add-invoice-so'}
          style={styles.addInvoiceButton}
          onPress={() => {
            updateType(params?.category)
            openSidebar()
          }}
        >
          {t('Projects.AddNewInvSo')}
        </BtButton>
      ) : null}
    </>
  )
}

export const ProjectsDetailTable = observer(
  ({ openSidebar, params }: ProjectsDetailTableProps) => {
    const route = useRoute<any>()

    const { t } = useTranslation('global')

    const [isFilterTouched, setIsFilterTouched] = useState(false)

    const { setViewInvoice, setActiveInvoice, updateType, refresher } =
      projectsStore

    const columns = [
      {
        name: t('Projects.InvOrSONo'),
        selector: 'invoice_number',
      },
      {
        name:
          params.category === 'payable'
            ? t('Projects.supplier')
            : t('Projects.Customer'),
        sortable: true,
        selector: 'customer',
        cell: (item: { business_name: 'string' }) => item?.business_name,
      },
      {
        name: t('Projects.status'),
        selector: 'status',
        sortable: true,
        cell: (item: { status: string; seen: any }) => (
          <BtStatus type={item?.status} />
        ),
      },
      {
        name: t('Projects.date'),
        selector: 'invoice_date',
        cell: (item: { invoice_date: moment.MomentInput }) =>
          moment(item.invoice_date).format('MM/DD/YYYY'),
        sortable: true,
      },
      {
        name: t('TransactionsView.amount'),
        selector: 'total_amount',
        cell: (item: { total_amount: number | undefined }) =>
          currencyMask(item.total_amount),
      },
      {
        name: '',
        right: true,
        selector: (item: any) => (
          <RowAction projectId={route?.params?.projectId} item={item} />
        ),
      },
    ]

    const handleEmptyTableText = () => {
      let textTitle: string

      if (isFilterTouched) {
        return t('noItems')
      }

      switch (params?.category) {
        case 'receivable':
          if (params?.status !== 'PAID') {
            textTitle = 'Projects.NoReceivablesYet'
          } else {
            textTitle = 'Projects.NoIncomeYet'
          }
          break
        case 'payable':
          if (params?.status !== 'PAID') {
            textTitle = 'Projects.NoPayablesYet'
          } else {
            textTitle = 'Projects.NoExpensesYet'
          }
          break
        default:
          textTitle = 'Projects.NoReceivablesYet'
      }
      return t(textTitle as any)
    }

    return (
      <BtApiTable
        filterHeight={40}
        filterWidth={500}
        style={customStyles}
        columns={columns}
        apiCaller={(args: any) => {
          if (args?.status === undefined && !args.hasOwnProperty('duration')) {
            args.status = args.value
          }

          return routes2.project.getInvoices(args)
        }}
        onItemPress={(item) => {
          setActiveInvoice(item)
          setViewInvoice(true, params?.category)
        }}
        CustomFilterComponent={(props) => (
          <ProjectDetailFilter
            {...props}
            params={params}
            refresher={refresher}
            updateType={updateType}
            openSidebar={openSidebar}
          />
        )}
        filterPlaceholder={t('Projects.SearchInvoiceSOInputText' as any)}
        defaultSortColumn={t('Projects.createdAt')}
        extraApiParams={{
          category: params?.category,
          status: params?.status?.value || params?.status,
          id: route?.params?.projectId,
        }}
        refresher={refresher}
        noDataComponent={
          <ProjectsEmptyContent
            params={params}
            title={handleEmptyTableText()}
            btnText={t('Projects.AddInvoiceSO')}
            subtitle={
              isFilterTouched
                ? t('Projects.TryAgainInvSo')
                : t('Projects.ProjectTabNoFound')
            }
            buttonOnPress={() => {
              updateType(params?.category)
              openSidebar()
            }}
          />
        }
        hasFilterTouched={(value: boolean) => setIsFilterTouched(value)}
      />
    )
  },
)
