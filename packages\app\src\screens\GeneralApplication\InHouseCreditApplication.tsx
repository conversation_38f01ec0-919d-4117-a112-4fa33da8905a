import { observer } from 'mobx-react-lite'
import { ScreenWithCobrandingHeader } from '../../ui/white-label-components/ScreenWithCobrandingHeader'
import Application from './Application'
import React from 'react'
import { UnifiedApplicationProvider } from '../Applications/UnifiedApplication/UnifiedApplicationContext'

export const InHouseCreditApplication = observer(() => {
  return (
    <UnifiedApplicationProvider>
      <ScreenWithCobrandingHeader>
        <Application />
      </ScreenWithCobrandingHeader>
    </UnifiedApplicationProvider>
  )
})
