import { observer } from 'mobx-react-lite'
import { ScreenWithCobrandingHeader } from '../../../ui/white-label-components/ScreenWithCobrandingHeader'
import React from 'react'
import { LenderApplicationProvider } from './LenderApplicationContext'
import { LenderApplicationPage } from './LenderApplicationPage'

export const LenderApplication = observer(() => {
  return (
    <LenderApplicationProvider>
      <ScreenWithCobrandingHeader>
        <LenderApplicationPage />
      </ScreenWithCobrandingHeader>
    </LenderApplicationProvider>
  )
})
