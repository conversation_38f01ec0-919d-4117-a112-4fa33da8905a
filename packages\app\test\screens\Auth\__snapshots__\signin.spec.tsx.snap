// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Signin for NAB domain should render 1`] = `
<RNCSafeAreaProvider
  onInsetsChange={[Function]}
  style={
    Array [
      Object {
        "flex": 1,
      },
      undefined,
    ]
  }
>
  <View
    collapsable={false}
    pointerEvents="box-none"
    style={
      Object {
        "flex": 1,
      }
    }
  >
    <View
      style={
        Object {
          "flex": 1,
        }
      }
    >
      <View
        style={
          Object {
            "flex": 1,
          }
        }
      >
        <View
          pointerEvents="box-none"
          style={
            Object {
              "zIndex": 1,
            }
          }
        >
          <View
            accessibilityElementsHidden={false}
            importantForAccessibility="auto"
            onLayout={[Function]}
            pointerEvents="box-none"
            style={null}
          >
            <View
              pointerEvents="box-none"
              style={
                Object {
                  "bottom": 0,
                  "left": 0,
                  "opacity": 1,
                  "position": "absolute",
                  "right": 0,
                  "top": 0,
                  "zIndex": 0,
                }
              }
            >
              <View
                style={
                  Object {
                    "backgroundColor": "rgb(255, 255, 255)",
                    "borderBottomColor": "rgb(216, 216, 216)",
                    "flex": 1,
                    "shadowColor": "rgb(216, 216, 216)",
                    "shadowOffset": Object {
                      "height": 0.5,
                      "width": 0,
                    },
                    "shadowOpacity": 0.85,
                    "shadowRadius": 0,
                  }
                }
              />
            </View>
            <View
              pointerEvents="box-none"
              style={
                Object {
                  "height": 44,
                  "maxHeight": undefined,
                  "minHeight": undefined,
                  "opacity": undefined,
                  "transform": undefined,
                }
              }
            >
              <View
                pointerEvents="none"
                style={
                  Object {
                    "height": 0,
                  }
                }
              />
              <View
                pointerEvents="box-none"
                style={
                  Object {
                    "alignItems": "center",
                    "flex": 1,
                    "flexDirection": "row",
                    "justifyContent": "center",
                  }
                }
              >
                <View
                  pointerEvents="box-none"
                  style={
                    Object {
                      "marginHorizontal": 16,
                      "opacity": 1,
                    }
                  }
                >
                  <Text
                    accessibilityRole="header"
                    aria-level="1"
                    collapsable={false}
                    nativeID="animatedComponent"
                    numberOfLines={1}
                    onLayout={[Function]}
                    style={
                      Object {
                        "color": "#2F4858",
                        "fontSize": 17,
                        "fontWeight": "600",
                      }
                    }
                  >
                    login
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </View>
        <RNSScreenContainer
          onLayout={[Function]}
          style={
            Object {
              "flex": 1,
            }
          }
        >
          <RNSScreen
            activityState={2}
            forwardedRef={[Function]}
            gestureResponseDistance={
              Object {
                "bottom": -1,
                "end": -1,
                "start": -1,
                "top": -1,
              }
            }
            onGestureCancel={[Function]}
            pointerEvents="box-none"
            sheetAllowedDetents="large"
            sheetCornerRadius={-1}
            sheetExpandsWhenScrolledToEdge={true}
            sheetGrabberVisible={false}
            sheetLargestUndimmedDetent="all"
            style={
              Object {
                "bottom": 0,
                "left": 0,
                "position": "absolute",
                "right": 0,
                "top": 0,
              }
            }
          >
            <View
              collapsable={false}
              style={
                Object {
                  "opacity": 1,
                }
              }
            />
            <View
              accessibilityElementsHidden={false}
              closing={false}
              gestureVelocityImpact={0.3}
              importantForAccessibility="auto"
              onClose={[Function]}
              onGestureBegin={[Function]}
              onGestureCanceled={[Function]}
              onGestureEnd={[Function]}
              onOpen={[Function]}
              onTransition={[Function]}
              pointerEvents="box-none"
              style={
                Array [
                  Object {
                    "overflow": undefined,
                  },
                  Object {
                    "bottom": 0,
                    "left": 0,
                    "position": "absolute",
                    "right": 0,
                    "top": 0,
                  },
                ]
              }
              transitionSpec={
                Object {
                  "close": Object {
                    "animation": "spring",
                    "config": Object {
                      "damping": 500,
                      "mass": 3,
                      "overshootClamping": true,
                      "restDisplacementThreshold": 10,
                      "restSpeedThreshold": 10,
                      "stiffness": 1000,
                    },
                  },
                  "open": Object {
                    "animation": "spring",
                    "config": Object {
                      "damping": 500,
                      "mass": 3,
                      "overshootClamping": true,
                      "restDisplacementThreshold": 10,
                      "restSpeedThreshold": 10,
                      "stiffness": 1000,
                    },
                  },
                }
              }
            >
              <View
                collapsable={false}
                nativeID="animatedComponent"
                needsOffscreenAlphaCompositing={false}
                pointerEvents="box-none"
                style={
                  Object {
                    "flex": 1,
                  }
                }
              >
                <View
                  collapsable={false}
                  forwardedRef={[Function]}
                  onGestureHandlerEvent={[Function]}
                  onGestureHandlerStateChange={[Function]}
                  style={
                    Object {
                      "flex": 1,
                      "transform": Array [
                        Object {
                          "translateX": 0,
                        },
                        Object {
                          "translateX": 0,
                        },
                      ],
                    }
                  }
                >
                  <View
                    pointerEvents="none"
                    style={
                      Object {
                        "backgroundColor": "#F2F2F2",
                        "bottom": 0,
                        "left": 0,
                        "position": "absolute",
                        "shadowColor": "#000",
                        "shadowOffset": Object {
                          "height": 1,
                          "width": -1,
                        },
                        "shadowOpacity": 0.3,
                        "shadowRadius": 5,
                        "top": 0,
                        "width": 3,
                      }
                    }
                  />
                  <View
                    style={
                      Array [
                        Object {
                          "flex": 1,
                          "overflow": "hidden",
                        },
                        Array [
                          Object {
                            "backgroundColor": "#F2F2F2",
                          },
                          undefined,
                        ],
                      ]
                    }
                  >
                    <View
                      style={
                        Object {
                          "flex": 1,
                          "flexDirection": "column-reverse",
                        }
                      }
                    >
                      <View
                        style={
                          Object {
                            "flex": 1,
                          }
                        }
                      >
                        <View
                          style={
                            Object {
                              "backgroundColor": "white",
                              "flexDirection": "column",
                              "flexGrow": 1,
                              "height": "100%",
                              "justifyContent": "center",
                              "overflowY": "auto",
                            }
                          }
                        >
                          <View
                            accessible={true}
                            collapsable={false}
                            focusable={true}
                            nativeID="animatedComponent"
                            onClick={[Function]}
                            onResponderGrant={[Function]}
                            onResponderMove={[Function]}
                            onResponderRelease={[Function]}
                            onResponderTerminate={[Function]}
                            onResponderTerminationRequest={[Function]}
                            onStartShouldSetResponder={[Function]}
                            style={
                              Object {
                                "alignItems": "center",
                                "opacity": 1,
                                "paddingVertical": 23,
                              }
                            }
                          >
                            <Image
                              resizeMode="contain"
                              source={
                                Object {
                                  "uri": "NABlogo.svg",
                                }
                              }
                              style={
                                Object {
                                  "height": 164,
                                  "width": 468,
                                }
                              }
                            />
                          </View>
                          <View
                            style={
                              Object {
                                "flexGrow": 1,
                                "marginTop": 40,
                              }
                            }
                          >
                            <View
                              style={
                                Object {
                                  "alignSelf": "center",
                                  "flexGrow": 1,
                                  "width": 500,
                                }
                              }
                            >
                              <Text
                                style={
                                  Array [
                                    Object {
                                      "textAlign": "left",
                                    },
                                    Object {
                                      "color": "#2F4858",
                                      "fontWeight": "400",
                                    },
                                    Object {
                                      "writingDirection": "ltr",
                                    },
                                    Object {
                                      "alignSelf": "center",
                                      "color": "#003353",
                                      "fontFamily": "Inter, sans-serif",
                                      "fontSize": 32,
                                      "fontWeight": "600",
                                      "lineHeight": 39,
                                      "textAlign": undefined,
                                    },
                                  ]
                                }
                                testID="title"
                              >
                                Welcome back!
                              </Text>
                              <Text
                                style={
                                  Array [
                                    Object {
                                      "textAlign": "left",
                                    },
                                    Object {
                                      "color": "#2F4858",
                                      "fontWeight": "400",
                                    },
                                    Object {
                                      "writingDirection": "ltr",
                                    },
                                    Object {
                                      "alignSelf": "center",
                                      "color": "#668598",
                                      "fontFamily": "Inter, sans-serif",
                                      "fontSize": 18,
                                      "fontWeight": "400",
                                      "lineHeight": 30,
                                      "marginTop": 20,
                                      "textAlign": undefined,
                                    },
                                  ]
                                }
                                testID="subtitle"
                              >
                                Enter the cell phone or email you registered with. 
                              </Text>
                              <View
                                style={
                                  Object {
                                    "height": 40,
                                    "width": 0,
                                  }
                                }
                              />
                              <View
                                style={
                                  Object {
                                    "backgroundColor": "white",
                                    "borderColor": "#E5E5E5",
                                    "borderRadius": 8,
                                    "borderWidth": 1,
                                    "marginTop": 24,
                                    "overflow": "hidden",
                                    "paddingBottom": 1,
                                  }
                                }
                              >
                                <View
                                  style={
                                    Array [
                                      Object {
                                        "backgroundColor": "rgb(228, 228, 228)",
                                        "borderTopLeftRadius": 0,
                                        "borderTopRightRadius": 0,
                                      },
                                      Object {
                                        "backgroundColor": "white",
                                        "borderRadius": 8,
                                        "flex": 1,
                                        "fontFamily": undefined,
                                        "marginTop": 0,
                                        "minWidth": 200,
                                        "paddingBottom": undefined,
                                        "width": "100%",
                                      },
                                    ]
                                  }
                                >
                                  <View
                                    collapsable={false}
                                    nativeID="animatedComponent"
                                    style={
                                      Object {
                                        "backgroundColor": "transparent",
                                        "bottom": 0,
                                        "height": 2,
                                        "left": 0,
                                        "position": "absolute",
                                        "right": 0,
                                        "transform": Array [
                                          Object {
                                            "scaleY": 0.5,
                                          },
                                        ],
                                        "zIndex": 1,
                                      }
                                    }
                                    testID="text-input-underline"
                                  />
                                  <View
                                    onLayout={[Function]}
                                    style={
                                      Array [
                                        Object {
                                          "paddingBottom": 0,
                                          "paddingTop": 0,
                                        },
                                        Object {
                                          "minHeight": 52,
                                        },
                                      ]
                                    }
                                  >
                                    <View
                                      collapsable={false}
                                      nativeID="animatedComponent"
                                      pointerEvents="none"
                                      style={
                                        Object {
                                          "bottom": 0,
                                          "left": 0,
                                          "opacity": 0,
                                          "position": "absolute",
                                          "right": 0,
                                          "top": 0,
                                          "transform": Array [
                                            Object {
                                              "translateX": 4,
                                            },
                                          ],
                                          "width": 750,
                                          "zIndex": 3,
                                        }
                                      }
                                    >
                                      <Text
                                        collapsable={false}
                                        maxFontSizeMultiplier={1.5}
                                        nativeID="animatedComponent"
                                        numberOfLines={1}
                                        onLayout={[Function]}
                                        onTextLayout={[Function]}
                                        style={
                                          Object {
                                            "color": "#00A0F3",
                                            "fontSize": 18,
                                            "fontWeight": undefined,
                                            "left": 0,
                                            "lineHeight": undefined,
                                            "maxWidth": 72,
                                            "opacity": 0,
                                            "paddingLeft": 12,
                                            "paddingRight": 12,
                                            "position": "absolute",
                                            "textAlign": "left",
                                            "top": 28,
                                            "transform": Array [
                                              Object {
                                                "translateX": 0,
                                              },
                                              Object {
                                                "translateY": -10,
                                              },
                                              Object {
                                                "scale": 0.6666666666666666,
                                              },
                                            ],
                                            "writingDirection": "ltr",
                                          }
                                        }
                                        testID="login-label-active"
                                      >
                                        <Text
                                          style={
                                            Object {
                                              "fontSize": 16,
                                            }
                                          }
                                        >
                                          Cell Phone Number Or Email
                                          <Text
                                            style={
                                              Object {
                                                "paddingLeft": 2,
                                              }
                                            }
                                          >
                                            *
                                          </Text>
                                        </Text>
                                      </Text>
                                      <Text
                                        collapsable={false}
                                        maxFontSizeMultiplier={1.5}
                                        nativeID="animatedComponent"
                                        numberOfLines={1}
                                        style={
                                          Object {
                                            "color": "#94A3AB",
                                            "fontSize": 18,
                                            "fontWeight": undefined,
                                            "left": 0,
                                            "lineHeight": undefined,
                                            "maxWidth": 72,
                                            "opacity": 0,
                                            "paddingLeft": 12,
                                            "paddingRight": 12,
                                            "position": "absolute",
                                            "textAlign": "left",
                                            "top": 28,
                                            "transform": Array [
                                              Object {
                                                "translateX": 0,
                                              },
                                              Object {
                                                "translateY": -10,
                                              },
                                              Object {
                                                "scale": 0.6666666666666666,
                                              },
                                            ],
                                            "writingDirection": "ltr",
                                          }
                                        }
                                        testID="login-label-inactive"
                                      >
                                        <Text
                                          style={
                                            Object {
                                              "fontSize": 16,
                                            }
                                          }
                                        >
                                          Cell Phone Number Or Email
                                          <Text
                                            style={
                                              Object {
                                                "paddingLeft": 2,
                                              }
                                            }
                                          >
                                            *
                                          </Text>
                                        </Text>
                                      </Text>
                                    </View>
                                    <View
                                      style={
                                        Object {
                                          "alignItems": "center",
                                          "flexDirection": "row",
                                          "height": "100%",
                                        }
                                      }
                                    >
                                      <TextInput
                                        autoComplete="off"
                                        autoCorrect={false}
                                        cursorColor="#00A0F3"
                                        editable={true}
                                        keyboardType="default"
                                        labelActiveColor="#00A0F3"
                                        mandatory={true}
                                        maxFontSizeMultiplier={1.5}
                                        multiline={false}
                                        onBlur={[Function]}
                                        onChangeText={[Function]}
                                        onFocus={[Function]}
                                        placeholder=" "
                                        placeholderTextColor="#99ADBA"
                                        returnKeyType="go"
                                        secureTextEntry={false}
                                        selectionColor="#00A0F3"
                                        style={
                                          Object {
                                            "backgroundColor": "transparent",
                                            "color": "#003353",
                                            "flex": 1,
                                            "fontSize": 18,
                                            "fontWeight": "400",
                                            "paddingLeft": 10,
                                            "paddingRight": 5,
                                            "paddingTop": 26,
                                            "width": "100%",
                                          }
                                        }
                                        testID="login"
                                        textContentType="none"
                                        underlineColorAndroid="transparent"
                                        value="<EMAIL>"
                                      />
                                    </View>
                                  </View>
                                </View>
                              </View>
                              <View
                                style={
                                  Object {
                                    "height": 32,
                                    "width": 0,
                                  }
                                }
                              />
                              <View
                                style={
                                  Object {
                                    "backgroundColor": "#00A0F3",
                                    "borderColor": "transparent",
                                    "borderRadius": 8,
                                    "borderStyle": "solid",
                                    "borderWidth": 0,
                                    "elevation": 2,
                                    "minWidth": 64,
                                    "shadowColor": "#000000",
                                    "shadowOffset": Object {
                                      "height": 0.75,
                                      "width": 0,
                                    },
                                    "shadowOpacity": 0.24,
                                    "shadowRadius": 1.5,
                                  }
                                }
                              >
                                <View
                                  accessibilityRole="button"
                                  accessibilityState={
                                    Object {
                                      "disabled": false,
                                    }
                                  }
                                  accessible={true}
                                  collapsable={false}
                                  focusable={true}
                                  onBlur={[Function]}
                                  onClick={[Function]}
                                  onFocus={[Function]}
                                  onResponderGrant={[Function]}
                                  onResponderMove={[Function]}
                                  onResponderRelease={[Function]}
                                  onResponderTerminate={[Function]}
                                  onResponderTerminationRequest={[Function]}
                                  onStartShouldSetResponder={[Function]}
                                  style={
                                    Array [
                                      Object {
                                        "overflow": "hidden",
                                      },
                                      Object {
                                        "borderRadius": 8,
                                      },
                                    ]
                                  }
                                  testID="submit-button"
                                >
                                  <View
                                    style={
                                      Array [
                                        Object {
                                          "alignItems": "center",
                                          "flexDirection": "row",
                                          "justifyContent": "center",
                                        },
                                        Object {
                                          "alignItems": "center",
                                          "borderRadius": 0,
                                          "height": 54,
                                          "justifyContent": "center",
                                          "padding": 16,
                                        },
                                      ]
                                    }
                                  >
                                    <Text
                                      numberOfLines={1}
                                      selectable={false}
                                      style={
                                        Array [
                                          Object {
                                            "textAlign": "left",
                                          },
                                          Object {
                                            "color": "#2F4858",
                                            "fontWeight": "400",
                                          },
                                          Object {
                                            "writingDirection": "ltr",
                                          },
                                          Array [
                                            Object {
                                              "marginHorizontal": 16,
                                              "marginVertical": 9,
                                              "textAlign": "center",
                                            },
                                            Object {
                                              "letterSpacing": 1,
                                            },
                                            false,
                                            undefined,
                                            false,
                                            Object {
                                              "color": "#ffffff",
                                              "fontWeight": "500",
                                            },
                                            Array [
                                              Object {
                                                "fontSize": 18,
                                                "lineHeight": 22,
                                                "marginVertical": 0,
                                              },
                                              Object {
                                                "color": "rgba(255, 255, 255, 1)",
                                              },
                                            ],
                                          ],
                                        ]
                                      }
                                      testID="submit-button-text"
                                    >
                                      Continue
                                    </Text>
                                  </View>
                                </View>
                              </View>
                              <View
                                style={
                                  Object {
                                    "height": 40,
                                    "width": 0,
                                  }
                                }
                              />
                              <Text
                                style={
                                  Array [
                                    Object {
                                      "textAlign": "left",
                                    },
                                    Object {
                                      "color": "#2F4858",
                                      "fontWeight": "400",
                                    },
                                    Object {
                                      "writingDirection": "ltr",
                                    },
                                    Object {
                                      "alignSelf": "center",
                                      "color": "#668598",
                                      "fontFamily": "Inter, sans-serif",
                                      "fontSize": 16,
                                      "fontWeight": "500",
                                    },
                                  ]
                                }
                                testID="link-to-signup"
                              >
                                Don’t have an account?  
                                <View
                                  accessibilityState={
                                    Object {
                                      "disabled": false,
                                    }
                                  }
                                  accessible={true}
                                  collapsable={false}
                                  focusable={true}
                                  nativeID="animatedComponent"
                                  onClick={[Function]}
                                  onResponderGrant={[Function]}
                                  onResponderMove={[Function]}
                                  onResponderRelease={[Function]}
                                  onResponderTerminate={[Function]}
                                  onResponderTerminationRequest={[Function]}
                                  onStartShouldSetResponder={[Function]}
                                  style={
                                    Object {
                                      "alignItems": "center",
                                      "flexDirection": "row",
                                      "justifyContent": "center",
                                      "opacity": 1,
                                    }
                                  }
                                  testID="signup_link"
                                >
                                  <Text
                                    style={
                                      Object {
                                        "color": "#00A0F3",
                                        "fontSize": 16,
                                        "fontWeight": "500",
                                      }
                                    }
                                  >
                                    Sign Up
                                  </Text>
                                </View>
                              </Text>
                            </View>
                            <View
                              style={
                                Object {
                                  "height": 40,
                                  "width": 0,
                                }
                              }
                            />
                            <View
                              style={
                                Object {
                                  "alignItems": "center",
                                  "justifyContent": "flex-start",
                                }
                              }
                              testID="poweredByBluetape"
                            >
                              <Text
                                style={
                                  Object {
                                    "color": "#003353",
                                    "fontFamily": "Inter, sans-serif",
                                    "fontSize": 16,
                                    "fontWeight": "400",
                                    "lineHeight": 19,
                                  }
                                }
                              >
                                Powered By
                              </Text>
                              <View
                                style={
                                  Object {
                                    "height": 10,
                                    "width": 0,
                                  }
                                }
                              />
                              <View
                                accessible={true}
                                collapsable={false}
                                focusable={true}
                                nativeID="animatedComponent"
                                onClick={[Function]}
                                onResponderGrant={[Function]}
                                onResponderMove={[Function]}
                                onResponderRelease={[Function]}
                                onResponderTerminate={[Function]}
                                onResponderTerminationRequest={[Function]}
                                onStartShouldSetResponder={[Function]}
                                style={
                                  Object {
                                    "opacity": 1,
                                  }
                                }
                              >
                                <View
                                  style={
                                    Object {
                                      "height": 24,
                                      "style": undefined,
                                      "width": 167,
                                    }
                                  }
                                />
                              </View>
                              <View
                                style={
                                  Object {
                                    "height": 60,
                                    "width": 0,
                                  }
                                }
                              />
                            </View>
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            </View>
          </RNSScreen>
        </RNSScreenContainer>
      </View>
    </View>
  </View>
</RNCSafeAreaProvider>
`;
