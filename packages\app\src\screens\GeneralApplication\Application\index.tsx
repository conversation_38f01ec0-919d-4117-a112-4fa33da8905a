import React, { useEffect } from 'react'
import { observer } from 'mobx-react'
import { useRoute } from '@react-navigation/core'
import { paths } from '../../links'
import ApplicationStore from './ApplicationStore'
import Loading from '../../Loading'
import { ApplicationType } from '@linqpal/models/src/dictionaries/applicationType'
import RootStore from '../../../store/RootStore'
import Editor from './Editor'
import { useUnifiedApplication } from '../../Applications/UnifiedApplication/UnifiedApplicationContext'
import { WizardStepEditor } from '../../Applications/UnifiedApplication/Wizard/WizardStepEditor'

export default observer(function Application() {
  const route: any = useRoute()

  const applicationStore = useUnifiedApplication()

  useEffect(() => {
    let type: ApplicationType

    const supplierId = route.params?.supplierId // currently for IHC only

    switch (route.name) {
      case paths.CreditApplication:
        type = ApplicationType.Credit
        break
      case paths.GetPaidApplication:
        type = ApplicationType.Supplier
        break
      case paths.InHouseCreditApplication:
        type = ApplicationType.InHouseCredit
        break
      default:
        throw new Error('Unsupported application type')
    }

    ApplicationStore.loadApplication(type, supplierId)
    applicationStore.loadDraft().catch((e) => console.error(e))
  }, [applicationStore, route.name, route.params?.supplierId])

  return ApplicationStore.loading ? (
    <Loading />
  ) : (
    <>
      <WizardStepEditor />
      <div style={{ width: '100%', height: 5, backgroundColor: 'red' }} />
      <Editor document={RootStore.userStore.document} />
    </>
  )
})
