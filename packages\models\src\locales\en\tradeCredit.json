{"tradeCredit": {"tabCreditTransactions": {"table": {"Supplier": "Supplier", "TransactionID": "BlueTape Trans. ID", "BankTransactionID": "Bank Trans. ID", "Date": "Date of Trans.", "Type": "Type", "Amount": "Amount", "Status": "Status", "Invoice#": "Invoice #", "dueDate": "Due Date", "amountDue": "Amount Due", "payNow": "Pay Now", "lateFee": "Late Fee", "penaltyInterest": "Penalty Interest", "invoices": "Invoices", "allTransactions": {"types": {"payment": "Payment", "draw": "Draw"}, "statuses": {"inProgress": "In Progress", "disbursed": "Invoice Processed", "paid": "Paid", "failed": "Failed", "declined": "Declined", "placed": "Placed"}}}, "tabs": {"allTransactions": "All Activity", "upcomingPayments": "Upcoming Payments", "pastDuePayments": "Past Due Payments", "emptyFilterResult": {"allTransactions": {"header": "No matches found", "text": "Please select other filter options"}, "upcomingPayments": {"header": "No matches found", "text": "Please select other filter options"}, "pastDuePayments": {"header": "No matches found", "text": "Please select other filter options"}}, "emptyResult": {"allTransactions": {"header": "You don’t have any transactions yet", "text": "All trade credit transactions will be displayed here"}, "upcomingPayments": {"header": "You don’t have any upcoming payments yet", "text": "All future payments will be displayed here"}, "pastDuePayments": {"header": "You don’t have any past due payments", "text": "All future payments will be displayed here"}}}, "buttons": {"viewDrawDetails": "View Draw", "exportData": "Export Data"}, "transactions": "Transactions", "paymentsHistory": "Payments & History", "searchPlaceholder": "Search by Invoice Number, Supplier", "overview": {"paymentProgress": "Payment in progress", "dueWeek": "Due this week", "dueMonth": "Due this month", "pastDue": "Past due amount"}}, "tabDraws": {"buttons": {"payNow": "Pay Now", "exportData": "Export Data"}, "searchPlaceholder": "Search by Invoice Number, Supplier", "tooltips": {"outstandingBalance": {"title": "What is the Current Balance?", "text": "The total amount owed, including any fees and or interest."}, "creditAvailable": {"title": "Credit Available", "text": "This is an estimated amount that can be used towards invoices."}, "paymentProgress": {"title": "Payment in Progress", "text": "Includes all payments with in progress status."}, "numberDraws": {"title": "Number of Draws", "text": "This includes all your paid off and in progress draws"}}, "draws": "All Draws", "pastDueDrawsWarningMin": "You have one or more Past Due draws. ", "pastDueDrawsWarningPayMin": "Please pay now in order to avoid any late fees.", "pastDueDrawsWarningMid": "You have one or more Past Due draws. ", "pastDueDrawsWarningPayMid": "Please pay now in order to avoid any penalty interest.", "pastDueDrawsWarningMax": "You have one or more draws that are Past Due. ", "pastDueDrawsWarningPayMax": "Please pay now to avoid additional penalty interest.", "overview": {"numberOfActiveDraws": "Number of active draws", "numberOfInactiveDraws": "Number of inactive draws", "numberOfPastDueDraws": "Number of past due draws"}, "tabs": {"activeDraws": "Active", "inactiveDraws": "Inactive", "emptyFilterResult": {"activeDraws": {"header": "No matches found", "text": "Please select other filter options"}, "inactiveDraws": {"header": "No matches found", "text": "Please select other filter options"}}, "emptyResult": {"activeDraws": {"header": "You don’t have any active draws", "text": "New draws will be displayed here"}, "inactiveDraws": {"header": "You don’t have any transactions yet", "text": "All trade credit transactions will be displayed here"}}}, "table": {"Supplier": "Supplier", "Type": "Type", "Amount": "Total Draw Amount", "Status": "Status", "Invoice#": "Invoice #", "NextPaymentDate": "Next Payment Date", "NextPaymentAmount": "Next Payment Amount", "AmountDue": "Amount Due", "invoices": "Multiple", "inactiveDraws": {"statuses": {"paidOff": "Paid <PERSON>", "cancelled": "Cancelled"}}, "activeDraws": {"statuses": {"pastDue": "Past Due", "dueNext": "Due Next"}}}}, "drawDetails": {"tabs": {"activitySummary": "Activity Summary", "details": "Details"}, "buttons": {"goBack": "Back to "}, "activitySummary": {"dueDate": "Due Date", "dueNext": "Due Next", "dueAmount": "Next Due Amount", "penaltyInterestTitle": "Total daily penalty interest", "penaltyInterestInfo": "Your account is past due. Please pay the past due amount now to avoid accruing penalty interest.", "totalRepaid": "Total Repaid", "currentBalance": "Current Balance", "pastDueAmount": "Past Due Amount", "scheduledPayment": "Scheduled Payment", "extensionFee": "Extension Fee", "penaltyInterest": "Penalty Interest", "lateFee": "Late Fee", "processingAmount": "Processing Amount", "transactionsHistory": {"title": "Transactions", "processing-caption": "Processing", "paid-caption": "Paid", "failed-caption": "Failed", "returned-caption": "Returned"}}, "details": {"installments": "Installments", "drawApprovalDate": "Draw Approval Date", "invoiceAmount": "Original Invoice Amount", "fee": "Origination Fee ({{percentage}})", "drawBalance": "Original Draw Balance", "drawId": "Draw ID", "drawStatus": "Draw Status", "downPayment": "Down Payment ({{percentage}})", "fundingAccount": "Funding Account", "drawDisclosure": "Draw Disclosure", "drawDisclosureFilename": "drawdisclosure", "virtualCardDetails": "Virtual Card Details", "viewCardNumber": "View Card Number", "invoiceNumber": "Invoice #", "paymentSchedule": {"title": "Payment Schedule Summary", "processing-caption": "Payment Processing", "paid-caption": "Payment Received", "due-caption": "Payment Due", "past-due-caption": "Payment Due", "rescheduled-caption": "Payment Rescheduled", "principalPayment": "Principal"}}}}}