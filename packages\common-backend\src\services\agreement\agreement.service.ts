﻿import { Company, Invoice, LoanApplication, VirtualCard } from '../../models'
import { dictionaries, exceptions } from '@linqpal/models'
import { getPlan } from '../loanplan.service'
import { Logger } from '../logger/logger.service'
import moment from 'moment'
import 'moment-timezone'
import 'moment-business-days'
import { IDocumentResponse } from '../documentVersioning/IDocumentResponse'
import { ICompany, IInvoice, IUser } from '../../models/types'
import {
  AgreementType,
  IAgreementFileDetails,
  ILoanAgreementCreateInfo,
  ILoanAgreementIntegrationCreateInfo,
  IRemoteFile,
} from './types'
import {
  convertApi,
  DocumentVersioningService,
  FileService,
  LMS,
} from '../../../index'
import AwsService, { S3AccessType } from '../aws.service'
import axios from 'axios'
import * as https from 'https'
import PizZip from 'pizzip'
import Docxtemplater from 'docxtemplater'
import { IDocumentTemplateResponse } from '../documentVersioning/IDocumentTemplateResponse'
import { formatAddress } from '@linqpal/models/src/helpers/addressFormatter'
import numbro from 'numbro'
import { IS3FileDetailsResponse } from '../documentVersioning/IS3FileDetailsResponse'
import { LogicalError } from '@linqpal/models/src/types/exceptions'
import { IDocumentMetadataResponse } from '../documentVersioning/IDocumentMetadataResponse'
import { IDownloadable } from '@linqpal/models/src/types/approvals'
import DraftService from '../draft.service'
import { formatPersonName } from '@linqpal/models/src/helpers/personNameFormatter'
import { OwnerTypes } from '@linqpal/models/src/dictionaries/UnifiedApplication'

const logger = new Logger({
  module: 'bluetape.services',
  subModule: 'agreement.service',
})

export async function collectAgreementInfo(
  loanApplication: ILoanAgreementCreateInfo,
  loanAmount: number,
  user?: any,
  submitter?: any,
) {
  let supplier: ICompany | undefined | null
  let options: any
  let repayment_frequency = ''

  if (loanApplication.invoiceId && loanApplication.paymentPlan) {
    const invoice = await Invoice.findById(loanApplication.invoiceId)
    if (!invoice) throw new exceptions.LogicalError('invoice/not-found')

    options = await getPlan(loanApplication.paymentPlan)
    if (!options) {
      options = {
        term: 1,
        fee: 0,
        frequency: 'weekly',
        firstPaymentDelayDays: 0,
        days: 0,
      }
    }
    repayment_frequency = options.frequency
    if (invoice.company_id) {
      supplier = await Company.findById(invoice.company_id)
    }
  }

  const company = await Company.findById(loanApplication.company_id)
  const fee = options?.fee
  const fee_amount = (loanAmount * fee) / 100
  const total_amount = loanAmount + fee_amount

  const draftInfo = await getDraftInfo(loanApplication.company_id, user)

  const businessAddress = draftInfo.businessAddress
    ? formatAddress(draftInfo.businessAddress)
    : company?.address?.address || 'N/A'

  const supplierAddress =
    typeof supplier?.address === 'object'
      ? formatAddress(supplier?.address)
      : supplier?.address || 'N/A'

  const firstName = draftInfo?.firstName || user?.firstName
  const lastName = draftInfo?.lastName || user?.lastName

  const individual_representative = (firstName || '') + ' ' + (lastName || '')
  let purchaser_representative = individual_representative

  if (submitter) {
    const principal_coowner =
      (submitter.firstName || '') + ' ' + (submitter.lastName || '')

    if (individual_representative !== principal_coowner) {
      purchaser_representative = `${principal_coowner} on behalf of ${individual_representative}`
    } else {
      purchaser_representative = principal_coowner
    }
  }

  const startDate = moment(loanApplication.issueDate || moment().add(7, 'days'))

  const virtualCard =
    typeof loanApplication.invoiceDetails?.cardId === 'string'
      ? await VirtualCard.findOne({
          cardId: loanApplication.invoiceDetails.cardId,
        })
      : null

  const disbursementDate = moment(
    loanApplication.issueDate
      ? typeof loanApplication.invoiceDetails?.cardId === 'string'
        ? virtualCard?.useDate ||
          moment(loanApplication.issueDate).businessAdd(3, 'days')
        : loanApplication.issueDate
      : startDate.businessAdd(3, 'days'),
  )
  const payment_amount =
    options?.term === 1 ? total_amount : total_amount / options?.term
  let repayment_start_date = 'TBD'
  let repayment_number = 'TBD'
  let final_repayment_date_v2 = 'TBD'
  let instalment_amount = 'TBD'

  if (loanApplication.lms_id) {
    let installmentsNumber = 0
    const lms = await LMS.getLoanInfo(loanApplication.lms_id)
    repayment_start_date = moment(lms?.loanReceivables[0]?.expectedDate).format(
      'MM/DD/YYYY',
    )

    if (lms?.loanParameters) {
      const loanParam = lms?.loanParameters.find((lp) => lp.isActive)

      if (loanParam) {
        repayment_number = loanParam.installmentsNumber.toString()
        installmentsNumber = loanParam.installmentsNumber
      }
    }

    if (lms?.loanReceivables) {
      const nonNullReceivables = lms.loanReceivables.filter(
        (receive) =>
          receive.expectedDate !== null && receive.expectedDate !== undefined,
      )

      const latestReceivables = nonNullReceivables.reduce(
        (previous, current) => {
          const previousDate = new Date(previous.expectedDate)
          const currentDate = new Date(current.expectedDate)
          return previousDate > currentDate ? previous : current
        },
      )

      final_repayment_date_v2 = moment(latestReceivables.expectedDate).format(
        'MM/DD/YYYY',
      )

      instalment_amount =
        '$' + `${(total_amount / installmentsNumber).toFixed(2).toString()}`
    }
  }
  const cif_number = loanApplication.company_id
  let masterApprovalDate = 'TBD'

  if (!loanApplication.masterAgreementExist) {
    if (company?.credit?.LoCnumCreatedAt) {
      masterApprovalDate = moment(company?.credit?.LoCnumCreatedAt).format(
        'MM/DD/YYYY',
      )
    }
  } else {
    if (
      !loanApplication.locAppprovalDate &&
      company?.credit?.LoCnumCreatedAt &&
      loanApplication.isMasterAgreementApproved
    ) {
      masterApprovalDate = moment(company?.credit?.LoCnumCreatedAt).format(
        'MM/DD/YYYY',
      )
    }

    if (loanApplication.locAppprovalDate && !company?.credit?.LoCnumCreatedAt) {
      masterApprovalDate = moment(loanApplication.locAppprovalDate).format(
        'MM/DD/YYYY',
      )
    }

    if (company?.credit?.LoCnumCreatedAt && loanApplication.locAppprovalDate) {
      masterApprovalDate = moment(
        company?.credit?.LoCnumCreatedAt > loanApplication.locAppprovalDate
          ? company?.credit?.LoCnumCreatedAt
          : loanApplication.locAppprovalDate,
      ).format('MM/DD/YYYY')
    }
  }

  return {
    loan_amount: loanAmount.toFixed(2),
    total_amount: total_amount.toFixed(2),
    fee_amount: fee_amount.toFixed(2),
    fee: `${fee} %`,
    legal_entity_name: draftInfo?.legalName || '',
    address: businessAddress,
    guarantors: draftInfo?.guarantors || '',
    submitterTitle: draftInfo?.submitterTitle || '',
    individual_representative,
    supplier_legal_entity_name: supplier?.name || 'N/A',
    supplier_address: supplierAddress,
    approval_date: startDate.format('MM/DD/YYYY'),
    payment_amount: payment_amount.toFixed(2),
    disbursement_date: disbursementDate.format('MM/DD/YYYY'),
    net_loan_proceeds: loanAmount.toFixed(2),
    prior_financing_balance: 'N/A',
    final_repayment_date: options?.days,
    repayment_start_date,
    loan_number: loanApplication.loanpro_id || loanApplication.lms_id || 'TBD',
    principal_amount: loanAmount.toFixed(2),
    bluetape_account: ' N/A',
    cif_number,
    max_credit_amount: company?.credit?.limit
      ? `$${numbro(company?.credit?.limit).format({
          thousandSeparated: true,
        })}`
      : 'TBD',
    loc_number: company?.credit?.LoCnumber || 'TBD',
    repayment_number,
    repayment_frequency,
    final_repayment_date_v2,
    instalment_amount,
    master_approval_date: masterApprovalDate,
    purchaser_representative,
  }
}

export async function collectIntegrationAgreementInfo(
  loanApplication: ILoanAgreementIntegrationCreateInfo,
  user: any,
  submitter: any,
) {
  let supplier: ICompany | undefined | null
  let options: any
  let repayment_frequency = ''

  const { companyId, loanAmount, paymentPlan, supplierCompanyId } =
    loanApplication

  if (supplierCompanyId && paymentPlan) {
    options = await getPlan(paymentPlan)
    if (!options) {
      options = {
        term: 1,
        fee: 0,
        frequency: 'weekly',
        firstPaymentDelayDays: 0,
        days: 0,
      }
    }
    repayment_frequency = options.frequency
    supplier = await Company.findById(supplierCompanyId)
  }

  const company = await Company.findById(companyId)
  const fee = options?.fee
  const fee_amount = (loanAmount * fee) / 100
  const total_amount = loanAmount + fee_amount

  const draftInfo = await getDraftInfo(companyId, user)

  const businessAddress = draftInfo.businessAddress
    ? formatAddress(draftInfo.businessAddress)
    : company?.address?.address || 'N/A'

  const supplierAddress =
    typeof supplier?.address === 'object'
      ? formatAddress(supplier?.address)
      : supplier?.address || 'N/A'

  const firstName = draftInfo?.firstName || user?.firstName
  const lastName = draftInfo?.lastName || user?.lastName

  const individual_representative = (firstName || '') + ' ' + (lastName || '')
  let purchaser_representative = individual_representative

  if (submitter) {
    const principal_coowner =
      (submitter.firstName || '') + ' ' + (submitter.lastName || '')

    if (individual_representative !== principal_coowner) {
      purchaser_representative = `${principal_coowner} on behalf of ${individual_representative}`
    } else {
      purchaser_representative = principal_coowner
    }
  }

  const startDate = moment(moment().add(7, 'days'))
  const disbursementDate = moment(startDate.businessAdd(3, 'days'))

  const payment_amount =
    options?.term === 1 ? total_amount : total_amount / options?.term

  const cif_number = companyId
  let masterApprovalDate = 'TBD'

  if (company?.credit?.LoCnumCreatedAt) {
    masterApprovalDate = moment(company?.credit?.LoCnumCreatedAt).format(
      'MM/DD/YYYY',
    )
  }

  return {
    loan_amount: loanAmount.toFixed(2),
    total_amount: total_amount.toFixed(2),
    fee_amount: fee_amount.toFixed(2),
    fee: `${fee} %`,
    legal_entity_name: draftInfo?.legalName || '',
    address: businessAddress,
    guarantors: draftInfo?.guarantors || '',
    submitterTitle: draftInfo?.submitterTitle || '',
    individual_representative,
    supplier_legal_entity_name: supplier?.name || 'N/A',
    supplier_address: supplierAddress,
    approval_date: startDate.format('MM/DD/YYYY'),
    payment_amount: payment_amount.toFixed(2),
    disbursement_date: disbursementDate.format('MM/DD/YYYY'),
    net_loan_proceeds: loanAmount.toFixed(2),
    prior_financing_balance: 'N/A',
    final_repayment_date: options?.days,
    repayment_start_date: 'TBD',
    loan_number: 'TBD',
    principal_amount: loanAmount.toFixed(2),
    bluetape_account: ' N/A',
    cif_number,
    max_credit_amount: company?.credit?.limit
      ? `$${numbro(company?.credit?.limit).format({
          thousandSeparated: true,
        })}`
      : 'TBD',
    loc_number: company?.credit?.LoCnumber || 'TBD',
    repayment_number: 'TBD',
    repayment_frequency,
    final_repayment_date_v2: 'TBD',
    instalment_amount: 'TBD',
    master_approval_date: masterApprovalDate,
    purchaser_representative,
  }
}

async function getDraftInfo(companyId: string, user: any) {
  const draft: any = await DraftService.getNormalizedDraft(companyId)

  if (!draft)
    return {
      businessAddress: '',
      legalName: '',
      firstName: '',
      lastName: '',
      submitterTitle: '',
      guarantors: '',
    }

  const coOwners = DraftService.getCoOwners(draft)

  const submitterTitle =
    draft.businessOwner_isOwner === 'Yes'
      ? 'Business Owner'
      : draft.businessOwner_isAuthorised === 'Yes'
      ? 'Authorized Representative'
      : ''

  const submitterFirstName = draft.businessOwner_firstName || user?.firstName
  const submitterLastName = draft.businessOwner_lastName || user?.lastName

  let guarantors =
    draft.businessOwner_isOwner === 'Yes'
      ? formatPersonName(submitterFirstName, submitterLastName)
      : ''

  if (coOwners.length) {
    const coOwnerNames = coOwners
      // place individual co-owners before entities
      .sort((a, b) => {
        const rank = (type: string) => (type === OwnerTypes.INDIVIDUAL ? 0 : 1)
        return rank(a.type) - rank(b.type)
      })
      .map((coOwner) =>
        coOwner.type === OwnerTypes.INDIVIDUAL
          ? formatPersonName(coOwner.firstName, coOwner.lastName)
          : coOwner.entityName,
      )
      .filter(Boolean)
      ?.join(', ')

    guarantors = guarantors ? `${guarantors}, ${coOwnerNames}` : coOwnerNames
  }

  return {
    legalName:
      draft.businessInfo_businessName?.legalName ||
      draft.businessInfo_businessName?.dba,
    businessAddress: draft.businessInfo_businessAddress,
    firstName: submitterFirstName,
    lastName: submitterLastName,
    submitterTitle,
    guarantors,
  }
}

export async function getLatestAgreementByLoanApplicationIdAndType(
  loanApplicationId: string,
  accessType: S3AccessType = S3AccessType.Get,
): Promise<IAgreementFileDetails | null> {
  const application = await LoanApplication.findById(loanApplicationId)
  if (!application)
    throw new exceptions.LogicalError('No loan application found')
  let agreementType: AgreementType = AgreementType.MASTER_AGREEMENT
  if (application?.invoiceDetails?.invoiceId) {
    agreementType = AgreementType.BNPL_AGREEMENT
  }

  let latestAgreement: IDocumentResponse | null

  switch (agreementType) {
    case AgreementType.MASTER_AGREEMENT:
      latestAgreement = await getLatestMasterAgreement(application.company_id)
      break
    case AgreementType.BNPL_AGREEMENT:
      latestAgreement =
        await DocumentVersioningService.getLatestDocumentByReferenceIdAndType(
          loanApplicationId,
          agreementType,
        )
      break
  }

  if (!latestAgreement) return null

  return BuildResponseFromDocument(latestAgreement, accessType)
}

export async function getLatestMasterAgreementByCompanyId(
  companyId: any,
  accessType: S3AccessType = S3AccessType.Get,
): Promise<IAgreementFileDetails | null> {
  const latestAgreement = await getLatestMasterAgreement(companyId)

  if (latestAgreement == null) return null

  return BuildResponseFromDocument(latestAgreement, accessType)
}

export async function getAgreementMetadata(
  companyId: string,
  type: AgreementType,
): Promise<IDocumentMetadataResponse & { hasActualVersion: boolean }> {
  const metadata = await DocumentVersioningService.getDocumentMetadata(
    companyId,
    type,
  )

  return {
    ...metadata,
    hasActualVersion:
      !!metadata?.currentVersion &&
      metadata.currentVersion === metadata.latestVersion,
  }
}

async function getLatestMasterAgreement(companyId: string) {
  const documents =
    await DocumentVersioningService.getDocumentsByCompanyIdAndType(companyId, [
      AgreementType.MASTER_AGREEMENT,
    ])

  if (documents && documents.length > 0) {
    return getObjectWithHighestDate(documents)
  }

  return null
}

async function BuildResponseFromDocument(
  latestAgreement: IDocumentResponse,
  accessType: S3AccessType = S3AccessType.Get,
) {
  const file = await getAgreementFile(latestAgreement.s3Url, accessType)

  return {
    ...file,
    semanticVersion: latestAgreement.template.semanticVersion,
    createdAt: latestAgreement.createdAt,
  }
}

export async function getAgreementFile(
  s3Url: string,
  accessType: S3AccessType = S3AccessType.Get,
): Promise<IDownloadable> {
  const { fileName, filePath, bucketName } = getS3UrlDetails(s3Url)
  const key = `${filePath}/${fileName}`

  const url = await AwsService.getPreSignedUrl({
    key,
    method: accessType,
    bucket: bucketName,
  })

  return { url, fileName }
}

function getObjectWithHighestDate(
  objects: IDocumentResponse[],
): IDocumentResponse | null {
  if (objects.length === 0) {
    return null
  }

  return objects.reduce((maxObject, currentObject) => {
    if (!maxObject || currentObject.createdAt > maxObject.createdAt) {
      return currentObject
    }
    return maxObject
  })
}

export async function createAgreementForLoanApplication(
  loanApplicationId: string,
  isDraft: boolean,
  user: IUser | null | undefined,
): Promise<IAgreementFileDetails> {
  const application = await LoanApplication.findById(loanApplicationId)
  if (!application)
    throw new exceptions.LogicalError('No loan application found')

  const existingAgreement = await getLatestAgreementByLoanApplicationIdAndType(
    application.id,
  )

  let agreementType: AgreementType = AgreementType.MASTER_AGREEMENT

  if (application?.invoiceDetails?.invoiceId) {
    agreementType = AgreementType.BNPL_AGREEMENT
    if (existingAgreement) return existingAgreement
  }

  if (agreementType === AgreementType.MASTER_AGREEMENT) {
    return createMasterAgreementFromDraft(
      application.company_id,
      isDraft,
      application.agreementTemplateId,
    )
  }

  const { invoiceDetails } = application

  console.info(
    `Invoice details: ${invoiceDetails} and agreement type is: ${agreementType}`,
  )

  const newLoanDocumentFile =
    await DocumentVersioningService.getFileS3InfoByIdAndTemplateType(
      loanApplicationId,
      agreementType,
    )

  const docFileName = `${newLoanDocumentFile.fileName}.docx`
  const pdfFileName = `${newLoanDocumentFile.fileName}.pdf`
  const pdfFilePath = `${newLoanDocumentFile.path}/${pdfFileName}`

  let invoice: IInvoice | undefined | null
  let totalAmount = 0
  let loan_amount = 0
  const invoiceIds: string[] = Array.isArray(invoiceDetails.invoiceId)
    ? (invoiceDetails.invoiceId as string[])
    : [invoiceDetails.invoiceId as string]

  for (const invoiceId of invoiceIds) {
    invoice = await Invoice.findById(invoiceId)
    if (!invoice) throw new exceptions.LogicalError('invoice/not-found')
    totalAmount += invoice.total_amount
  }

  loan_amount = totalAmount

  if (application.status === 'approved' && application.approvedAmount) {
    loan_amount = application.approvedAmount
  }

  if (invoiceDetails.cardId) {
    console.info(`Initiate virtual card payment`)

    const virtualCard = await VirtualCard.findOne({
      cardId: invoiceDetails.cardId,
    })

    if (!virtualCard)
      throw new exceptions.LogicalError(
        `Virtual card ${invoiceDetails.cardId} was not found`,
      )

    loan_amount = virtualCard.usedAmount || virtualCard.amount
  }

  console.info(`Loan_amount is ${loan_amount}`)

  const data = await collectAgreementInfo(
    {
      invoiceDetails: application.invoiceDetails,
      lms_id: application.lms_id,
      loanpro_id: application.loanpro_id,
      company_id: application.company_id,
      issueDate: application.issueDate,
      paymentPlan: application.invoiceDetails?.paymentPlan,
      invoiceId: invoiceIds[0],
    },
    loan_amount,
    user,
    application.submitter,
  )

  let documentTemplate: IDocumentTemplateResponse

  if (application.agreementTemplateId !== undefined) {
    documentTemplate = await DocumentVersioningService.getTemplateById(
      application.agreementTemplateId,
    )
  } else {
    documentTemplate = await DocumentVersioningService.getLatestTemplateByType(
      agreementType,
    )
  }

  const { fileName, filePath, bucketName } = getS3UrlDetails(
    documentTemplate.s3Url,
  )

  const key = `${filePath}/${fileName}`
  const templateUrl = await AwsService.getPreSignedUrl({
    key,
    method: 'get',
    bucket: bucketName,
  })

  logger.info({ data }, `filling template ${templateUrl}`)

  const base64Content = await updateFile(templateUrl, data)

  logger.info('template is filled')

  const wordResult = await FileService.uploadFileBase64(
    base64Content,
    newLoanDocumentFile.path,
    docFileName,
    newLoanDocumentFile.bucket,
  )

  if (!wordResult?.url)
    throw new exceptions.LogicalError('Error file generating document')

  logger.info('uploaded docs document, converting to pdf')

  try {
    const convert = await convertApi.convert(
      'pdf',
      { File: wordResult.url },
      'docx',
    )

    logger.info({ convert }, 'converted docs to pdf, removing docs')

    const { url, fileName: file } = convert.file

    await FileService.deleteFile(
      `${newLoanDocumentFile.path}/${docFileName}`,
      newLoanDocumentFile.bucket,
    )

    if (isDraft) {
      logger.info('returning draft document')
      return {
        url: convert.file.url,
        fileName: convert.file.fileName,
      }
    }

    const axiosResponse = await axios.get(url, { responseType: 'arraybuffer' })
    const content = Buffer.from(axiosResponse.data, 'base64')

    logger.info('uploading pdf document to s3')

    const result = await FileService.uploadFileBase64(
      content,
      newLoanDocumentFile.path,
      file,
      newLoanDocumentFile.bucket,
    )

    if (result.url !== undefined) {
      await DocumentVersioningService.createDocument(
        loanApplicationId,
        newLoanDocumentFile.documentTemplateId,
        pdfFilePath,
      )
    } else {
      throw new exceptions.LogicalError(
        'Unable to create document in DocumentVersioningService for document with undefined url',
      )
    }

    logger.info('document is generated')

    return {
      url: result.url,
      fileName: result.fileName,
    }
  } catch (err: any) {
    throw new exceptions.LogicalError(
      'Error file generating document. ' + err.message,
    )
  }
}

export async function createPreviewAgreementWithoutLoanApplication(
  loanApplication: ILoanAgreementCreateInfo,
  agreementType: AgreementType,
  isDraft = false,
  user: IUser | null | undefined = null,
): Promise<IAgreementFileDetails> {
  switch (agreementType) {
    case AgreementType.CUSTOMER_AGREEMENT_FOR_SELLER:
      //TODO: add implementation here
      return {
        url: '',
        fileName: '',
      }
    case AgreementType.BNPL_AGREEMENT:
      return createPreviewBnplAgreementWithoutLoanApplication(
        loanApplication,
        user,
      )
    case AgreementType.MASTER_AGREEMENT:
      return createMasterAgreementFromDraft(
        loanApplication.company_id,
        isDraft,
        '',
        user,
      )
    default:
      throw new LogicalError(`Unsupported agreement type ${agreementType}`)
  }
}

export async function createIntegrationPreviewBnplAgreement(
  loanApplication: ILoanAgreementIntegrationCreateInfo,
  user: IUser | null | undefined = null,
) {
  const { companyId } = loanApplication
  const agreementType = AgreementType.BNPL_AGREEMENT

  const documentTemplate =
    await DocumentVersioningService.getLatestTemplateByType(agreementType)

  const { fileName, filePath, bucketName } = getS3UrlDetails(
    documentTemplate.s3Url,
  )

  const key = `${filePath}/${fileName}`
  const templateUrl = await AwsService.getPreSignedUrl({
    key,
    method: 'get',
    bucket: bucketName,
  })

  const data = await collectIntegrationAgreementInfo(
    loanApplication,
    user,
    user,
  )

  const base64Content = await updateFile(templateUrl, data)
  const folderPath = `credit_applications/${companyId}`
  const docFileName = `${documentTemplate.futureFileName}.docx`

  const wordResult = await FileService.uploadFileBase64(
    base64Content,
    folderPath,
    docFileName,
    bucketName,
  )

  if (!wordResult?.url)
    throw new exceptions.LogicalError('Error file generating document')

  try {
    const convert = await convertApi.convert(
      'pdf',
      { File: wordResult.url },
      'docx',
    )

    const { url, fileName: file } = convert.file
    await FileService.deleteFile(
      `credit_applications/${companyId}/${docFileName}`,
      bucketName,
    )

    return {
      url: url,
      fileName: file,
    }
  } catch (err: any) {
    throw new exceptions.LogicalError(
      'Error file generating document. ' + err.message,
    )
  }
}

export async function createPreviewBnplAgreementWithoutLoanApplication(
  loanApplication: ILoanAgreementCreateInfo,
  user: IUser | null | undefined = null,
): Promise<IAgreementFileDetails> {
  const { company_id: companyId } = loanApplication
  const agreementType = AgreementType.BNPL_AGREEMENT

  const documentTemplate =
    await DocumentVersioningService.getLatestTemplateByType(agreementType)

  const { fileName, filePath, bucketName } = getS3UrlDetails(
    documentTemplate.s3Url,
  )

  const key = `${filePath}/${fileName}`
  const templateUrl = await AwsService.getPreSignedUrl({
    key,
    method: 'get',
    bucket: bucketName,
  })

  let invoice: IInvoice | undefined | null
  let totalAmount = 0
  let loan_amount = 0
  const invoiceIds = Array.isArray(loanApplication.invoiceId)
    ? (loanApplication.invoiceId as string[])
    : [loanApplication.invoiceId as string]

  for (const invoiceId of invoiceIds) {
    invoice = await Invoice.findById(invoiceId)
    if (!invoice) throw new exceptions.LogicalError('invoice/not-found')
    totalAmount += invoice.total_amount
  }

  loan_amount = totalAmount

  const data = await collectAgreementInfo(
    loanApplication,
    loan_amount,
    user,
    user,
  )

  const base64Content = await updateFile(templateUrl, data)
  const folderPath = `credit_applications/${companyId}`
  const docFileName = `${documentTemplate.futureFileName}.docx`

  const wordResult = await FileService.uploadFileBase64(
    base64Content,
    folderPath,
    docFileName,
    bucketName,
  )

  if (!wordResult?.url)
    throw new exceptions.LogicalError('Error file generating document')

  try {
    const convert = await convertApi.convert(
      'pdf',
      { File: wordResult.url },
      'docx',
    )

    const { url, fileName: file } = convert.file
    await FileService.deleteFile(
      `credit_applications/${companyId}/${docFileName}`,
      bucketName,
    )

    return {
      url: url,
      fileName: file,
    }
  } catch (err: any) {
    throw new exceptions.LogicalError(
      'Error file generating document. ' + err.message,
    )
  }
}

export async function createMasterAgreementFromDraft(
  companyId: string,
  isDraft = false,
  templateId = '',
  user: IUser | null | undefined = null,
): Promise<IAgreementFileDetails> {
  const agreementType = AgreementType.MASTER_AGREEMENT

  let newLoanDocumentFile: IS3FileDetailsResponse

  logger.info(`getting S3 file info for companyId ${companyId}`)

  if (templateId) {
    newLoanDocumentFile =
      await DocumentVersioningService.getDraftFileS3InfoByReferenceIdAndTemplateId(
        companyId,
        templateId,
      )
  } else {
    newLoanDocumentFile =
      await DocumentVersioningService.getFileS3InfoByIdAndTemplateType(
        companyId,
        agreementType,
      )
  }

  const applications = await LoanApplication.find({
    lms_id: null,
    company_id: companyId,
  })

  const application =
    applications.find(
      (x) => x.agreementTemplateId === newLoanDocumentFile.documentTemplateId,
    ) || applications.find((x) => !x.agreementTemplateId && !x?.invoiceDetails)

  const docFileName = `${newLoanDocumentFile.fileName}.docx`
  const pdfFileName = `${newLoanDocumentFile.fileName}.pdf`
  const pdfFilePath = `${newLoanDocumentFile.path}/${pdfFileName}`
  const folderPath = newLoanDocumentFile.path

  const { fileName, filePath, bucketName } = getS3UrlDetails(
    newLoanDocumentFile.templateS3Url,
  )

  const key = `${filePath}/${fileName}`

  logger.info(`getting pre-signed URL for ${bucketName} ${key}`)
  const templateUrl = await AwsService.getPreSignedUrl({
    key,
    method: 'get',
    bucket: bucketName,
  })

  logger.info(`collecting agreement info for company ${companyId}`)
  const data = await collectAgreementInfo(
    {
      company_id: companyId,
      locAppprovalDate: application?.decisionDate,
      masterAgreementExist: !!application,
      isMasterAgreementApproved:
        application?.status === dictionaries.LOAN_APPLICATION_STATUS.APPROVED,
    },
    0,
    user,
  )

  logger.info(`updating file at ${templateUrl}`)
  const base64Content = await updateFile(templateUrl, data)

  logger.info(
    `uploading file ${docFileName} to ${folderPath}, bucket ${newLoanDocumentFile.bucket}`,
  )
  const wordResult = await FileService.uploadFileBase64(
    base64Content,
    folderPath,
    docFileName,
    newLoanDocumentFile.bucket,
  )

  if (!wordResult?.url)
    throw new exceptions.LogicalError('Error file generating document')

  try {
    logger.info(`converting pdf file to docx, url ${wordResult.url}`)
    const convert = await convertApi.convert(
      'pdf',
      { File: wordResult.url },
      'docx',
    )

    logger.info(
      `removing old file version at 'credit_applications/${companyId}/${docFileName}'`,
    )
    const { url, fileName: file } = convert.file
    await FileService.deleteFile(
      `credit_applications/${companyId}/${docFileName}`,
      bucketName,
    )

    if (isDraft) {
      return {
        url: url,
        fileName: file,
      }
    }

    logger.info(`reading file content at ${url}`)
    const axiosResponse = await axios.get(url, { responseType: 'arraybuffer' })
    const content = Buffer.from(axiosResponse.data, 'base64')

    logger.info(
      `uploading file ${file} to ${newLoanDocumentFile.path}, bucket ${newLoanDocumentFile.bucket}`,
    )
    const result = await FileService.uploadFileBase64(
      content,
      newLoanDocumentFile.path,
      file,
      newLoanDocumentFile.bucket,
    )

    if (result.url !== undefined) {
      logger.info(
        `creating document in document versioning service for company ${companyId}, path ${pdfFilePath}`,
      )
      const documentId = await DocumentVersioningService.createDocument(
        companyId,
        newLoanDocumentFile.documentTemplateId,
        pdfFilePath,
      )

      logger.info(`created document ${documentId}`)

      return {
        documentId,
        url: result.url,
        fileName: result.fileName,
        content: content.toString('base64'),
      }
    } else {
      throw new exceptions.LogicalError(
        'Unable to create document in DocumentVersioningService for document with undefined url',
      )
    }
  } catch (err: any) {
    throw new exceptions.LogicalError(
      'Error file generating document. ' + err.message,
    )
  }
}

export async function regenerateMasterAgreement(company_id: string) {
  const agreement = await getLatestMasterAgreement(company_id)
  if (agreement) {
    return createMasterAgreementFromDraft(
      company_id,
      false,
      agreement.template.id,
    )
  }

  return {
    url: '',
    fileName: '',
  }
}

export async function regenerateBnplAgreement(loanApplicationId: string) {
  const application = await LoanApplication.findById(loanApplicationId)
  if (!application)
    throw new exceptions.LogicalError('No loan application found')

  console.info(`lsmId: ${application.lms_id ?? 'is not exist'}`)

  const agreementType = AgreementType.BNPL_AGREEMENT

  const latestAgreement =
    await DocumentVersioningService.getLatestDocumentByReferenceIdAndType(
      application.id,
      agreementType,
    )

  if (!latestAgreement) return null

  const newLoanDocumentFile =
    await DocumentVersioningService.getDraftFileS3InfoByReferenceIdAndTemplateId(
      application.id,
      latestAgreement.template.id,
    )

  const s3Details = getS3UrlDetails(latestAgreement.s3Url)
  const lastDotIndex = s3Details.fileName.lastIndexOf('.')
  const agreementFileNameWithoutExtension =
    lastDotIndex !== -1
      ? s3Details.fileName.substring(0, lastDotIndex)
      : s3Details.fileName

  const { invoiceDetails } = application

  console.info(
    `Invoice details: ${invoiceDetails} and agreement type is: ${agreementType}}`,
  )

  const docFileName = `${agreementFileNameWithoutExtension}.docx`

  let invoice: IInvoice | undefined | null
  let totalAmount = 0
  let loan_amount = 0
  const invoiceIds: string[] = Array.isArray(invoiceDetails.invoiceId)
    ? (invoiceDetails.invoiceId as string[])
    : [invoiceDetails.invoiceId as string]

  for (const invoiceId of invoiceIds) {
    invoice = await Invoice.findById(invoiceId)
    if (!invoice) throw new exceptions.LogicalError('invoice/not-found')
    totalAmount += invoice.total_amount
  }

  loan_amount = totalAmount

  if (application.status === 'approved' && application.approvedAmount) {
    loan_amount = application.approvedAmount
  }

  if (invoiceDetails.cardId) {
    console.info(`Initiate virtual card payment`)

    const virtualCard = await VirtualCard.findOne({
      cardId: invoiceDetails.cardId,
    })

    if (!virtualCard)
      throw new exceptions.LogicalError(
        `Virtual card ${invoiceDetails.cardId} was not found`,
      )

    loan_amount = virtualCard.usedAmount || virtualCard.amount
  }

  console.info(`Loan_amount is ${loan_amount}`)

  const data = await collectAgreementInfo(
    {
      invoiceDetails: application.invoiceDetails,
      lms_id: application.lms_id,
      loanpro_id: application.loanpro_id,
      company_id: application.company_id,
      issueDate: application.issueDate,
      paymentPlan: application.invoiceDetails?.paymentPlan,
      invoiceId: invoiceIds[0],
    },
    loan_amount,
    null,
    application.submitter,
  )

  const { fileName, filePath, bucketName } = getS3UrlDetails(
    latestAgreement.template.s3Url,
  )

  const key = `${filePath}/${fileName}`
  const templateUrl = await AwsService.getPreSignedUrl({
    key,
    method: 'get',
    bucket: bucketName,
  })

  const base64Content = await updateFile(templateUrl, data)
  const wordResult = await FileService.uploadFileBase64(
    base64Content,
    newLoanDocumentFile.path,
    docFileName,
    newLoanDocumentFile.bucket,
  )

  if (!wordResult?.url)
    throw new exceptions.LogicalError('Error file generating document')

  try {
    const convert = await convertApi.convert(
      'pdf',
      { File: wordResult.url },
      'docx',
    )

    const { url, fileName: file } = convert.file
    await FileService.deleteFile(
      `${newLoanDocumentFile.path}/${docFileName}`,
      newLoanDocumentFile.bucket,
    )

    const axiosResponse = await axios.get(url, { responseType: 'arraybuffer' })
    const content = Buffer.from(axiosResponse.data, 'base64')
    const result = await FileService.uploadFileBase64(
      content,
      newLoanDocumentFile.path,
      file,
      newLoanDocumentFile.bucket,
    )

    return {
      url: result.url,
      fileName: result.fileName,
    }
  } catch (err: any) {
    throw new exceptions.LogicalError(
      'Error file generating document. ' + err.message,
    )
  }
}

export async function formatTemplate(
  newDocumentOptions: IS3FileDetailsResponse,
  data: any,
): Promise<IDownloadable> {
  logger.info(
    `getting pre-signed url for a template ${newDocumentOptions.documentTemplateId}`,
  )

  const templateLink = getS3UrlDetails(newDocumentOptions.templateS3Url)

  const templateUrl = await AwsService.getPreSignedUrl({
    key: `${templateLink.filePath}/${templateLink.fileName}`,
    method: 'get',
    bucket: templateLink.bucketName,
  })

  logger.info(`formatting file for ${newDocumentOptions.documentTemplateId}`)

  const base64Content = await updateFile(templateUrl, data)

  const wordFileName = `${newDocumentOptions.fileName}.docx`
  const wordFilePath = `${newDocumentOptions.path}/${wordFileName}`

  logger.info(`uploading ${wordFileName}`)

  const wordUploadResponse = await FileService.uploadFileBase64(
    base64Content,
    newDocumentOptions.path,
    wordFileName,
    newDocumentOptions.bucket,
  )

  if (!wordUploadResponse?.url)
    throw new LogicalError('Error when uploading Agreement .docx')

  logger.info(`converting ${wordFileName} to pdf`)

  const convertedDocument = await convertApi.convert(
    'pdf',
    { File: wordUploadResponse.url },
    'docx',
  )

  logger.info(`removing ${wordFileName}`)

  await FileService.deleteFile(wordFilePath, newDocumentOptions.bucket)

  return {
    url: convertedDocument.file.url,
    fileName: convertedDocument.file.fileName,
  }
}

export async function saveToS3(
  sourceUrl: string,
  newDocumentOptions: IS3FileDetailsResponse,
  extension = 'pdf',
): Promise<IRemoteFile> {
  logger.info(`reading file from ${sourceUrl}`)

  const fileBuffer = await axios.get(sourceUrl, { responseType: 'arraybuffer' })
  const content = Buffer.from(fileBuffer.data, 'base64')

  logger.info(`uploading file, options: ${JSON.stringify(newDocumentOptions)}`)

  const fileName = `${newDocumentOptions.fileName}.${extension}`
  const relativePath = `${newDocumentOptions.path}/${fileName}`

  const result = await FileService.uploadFileBase64(
    content,
    newDocumentOptions.path,
    `${newDocumentOptions.fileName}.${extension}`,
    newDocumentOptions.bucket,
  )

  return {
    url: result.url,
    fileName: result.fileName,
    relativePath,
    content,
  }
}

export function getS3UrlDetails(s3url: string) {
  const urlWithoutS3Prefix = s3url.replace('s3://', '')

  const pathSegments = urlWithoutS3Prefix.split('/')
  const fileName = pathSegments[pathSegments.length - 1]
  const filePath = pathSegments.slice(1, pathSegments.length - 1).join('/')
  const bucketName = pathSegments[0]

  return { filePath, fileName, bucketName }
}

function updateFile(url: string, contentData: any) {
  return new Promise<string>((resolve, reject) => {
    https.get(url, (r) => {
      if (r.statusCode !== 200) {
        console.debug('r.statusCode !== 200', r)
        reject(r)
      } else {
        const data: any[] = []
        let dataLen = 0

        r.on('data', function (chunk) {
          data.push(chunk)
          dataLen += chunk.length
        })
        r.on('end', function () {
          try {
            const buf = Buffer.alloc(dataLen)
            for (let i = 0, len = data.length, pos = 0; i < len; i++) {
              data[i].copy(buf, pos)
              pos += data[i].length
            }
            const zip = new PizZip(buf)

            const doc = new Docxtemplater(zip, {
              paragraphLoop: true,
              linebreaks: true,
            })
            doc.setData(contentData)

            doc.render()
            const base64: string = doc.getZip().generate({ type: 'base64' })
            // creating file for checking purpose
            // fs.writeFileSync(path.resolve(__dirname, 'output.docx'), base64, 'base64')
            resolve(base64)
          } catch (e) {
            console.debug('new PizZip(binary)', e)
            reject(e)
          }
        })
        r.on('error', function (err) {
          console.error(err.message)
          reject(err)
        })
      }
    })
  })
}
