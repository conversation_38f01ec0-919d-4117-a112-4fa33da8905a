import { Authentication, crypt, User } from '@linqpal/common-backend'
import { exceptions } from '@linqpal/models'
import admin from 'firebase-admin'
import controllers from '../controllers'
import moment from 'moment'
import { NextFunction, Request, Response } from 'express'
import { ICompany, IUser } from '@linqpal/common-backend/src/models/types'
import { RequestContext } from '@linqpal/common-backend/src/helpers/RequestContext'

/**
 * The Authorization header contains an IdToken followed by a signature hash.
 * The two are separated by a space. This brings the fb token.
 */
const getIdToken = (req: Request) => {
  const auth = req.header('authorization')
  if (!auth || !auth.startsWith('Bearer ')) {
    return ''
  }

  return auth.split(' ')[1]
}
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    export interface Request {
      user?: IUser | null
      company?: ICompany | null
    }
  }
}
// MIDDLEWARES

/**
 * Validates api key
 */
const apiKeyRequired =
  (throwError = true) =>
  async (req: Request, res: Response, next: NextFunction) => {
    const headerApiKey = req.header('X-API-KEY')
    const apiKey = process.env.BLUETAPE_API_KEY
    if (throwError && (!apiKey || headerApiKey !== apiKey)) {
      throw new exceptions.AuthenticationError()
    }
    return next()
  }

/**
 * Validates token and adds user to "req" adding user to DB if they don't exist.
 */
const authRequired =
  (throwError = true) =>
  async (req: Request, res: Response, next: NextFunction) => {
    const sessionCookie = req.header('session')
    const idToken = getIdToken(req)
    let decodedIdToken: admin.auth.DecodedIdToken | undefined
    if (sessionCookie) {
      try {
        decodedIdToken = await admin
          .auth()
          .verifySessionCookie(sessionCookie, true)
        const { exp, uid } = decodedIdToken
        const expiryDate = moment(exp * 1000)
        if (expiryDate.diff(moment()) / 1000 < 30) {
          const refreshToken = await admin.auth().createCustomToken(uid)
          res.status(460).send({ refreshToken })
          return
        }
      } catch (e: any) {
        console.log(e)
      }
    } else if (idToken) {
      try {
        decodedIdToken = await admin.auth().verifyIdToken(idToken, true)
      } catch (e) {
        console.log(e)
      }
    }
    if (decodedIdToken) {
      const { email, phone_number: phone, firebase, ...rest } = decodedIdToken
      const fUser = await admin.auth().getUser(decodedIdToken.uid)
      if ((email || phone) && rest.uid) {
        const { user, company } = await controllers.user.create({
          login:
            fUser?.providerData?.[0]?.providerId === 'phone' ? phone : email,
          email,
          phone,
          firebaseId: rest.uid,
          ...rest,
        })
        req.user = user
        req.company = company

        RequestContext.setIdentity(user, company ?? undefined)
      }
    }
    if (throwError && !req.user) {
      throw new exceptions.AuthenticationError()
    }

    return next()
  }

export const bffUserIdRequired =
  (throwError = true) =>
  async (req: Request, res: Response, next: NextFunction) => {
    if (req.user) {
      return next()
    }

    const userIdToken = req.header('x-user-id') || req.header('userId')
    if (userIdToken) {
      try {
        req.user = await User.findOne({ firebaseId: userIdToken })

        if (!req.user) {
          if (throwError) {
            throw new exceptions.AuthorizationError()
          }
          return next()
        }

        try {
          await admin.auth().getUser(req.user.firebaseId)
        } catch (e) {
          console.log(e)
          if (throwError) {
            throw new exceptions.AuthorizationError()
          }
          return next()
        }

        return next()
      } catch (e) {
        console.log(e)
      }
    }

    if (throwError) {
      throw new exceptions.AuthorizationError()
    }

    next()
  }

const backOfficeRoleRequired = async (
  req: Request,
  res: Response,
  next: NextFunction,
  roleKey: string,
  throwError = true,
) => {
  const idToken = getIdToken(req)
  if (idToken) {
    try {
      const decodedIdToken = await admin.auth().verifyIdToken(idToken, true)
      req.user = await User.findOne({ firebaseId: decodedIdToken.sub }) // We still need to populate the user
    } catch (e) {
      console.log(e)
    }
  }

  if (!req.user) {
    if (throwError) {
      throw new exceptions.AuthorizationError()
    }
    return next()
  }

  Authentication.init()
  const response = await Authentication.verifyRole(roleKey, 'bluetape', idToken)
  if (!response && throwError) throw new exceptions.RoleRequiredError(roleKey)

  next()
}

const viewerRequired = async (
  req: Request,
  res: Response,
  next: NextFunction,
  throwError = true,
) => {
  return backOfficeRoleRequired(req, res, next, 'viewer', throwError)
}

const adminRequired = async (
  req: Request,
  res: Response,
  next: NextFunction,
  throwError = true,
) => {
  return backOfficeRoleRequired(req, res, next, 'admin', throwError)
}

const supplierRequired = (req: Request, _res: Response, next: NextFunction) => {
  if (req.company?.status !== 'approved') {
    throw new exceptions.AuthorizationError()
  }
  return next()
}

const acceptChallenge = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  const authChallenge = req.header('challenge') || ''
  const sessionCookie = req.header('session') || ''
  if (!sessionCookie && authChallenge) {
    try {
      const [uid, signature] = authChallenge.split(':')
      const challengeVerified = await crypt.verify(signature, uid)
      const fUser = await admin.auth().getUser(uid)
      if (challengeVerified && fUser) {
        const refreshToken = await admin.auth().createCustomToken(uid)
        res.status(460).send({ refreshToken })
        return
      }
    } catch (e) {
      console.log(e)
    }
  }
  next()
}

export {
  authRequired,
  adminRequired,
  viewerRequired,
  supplierRequired,
  acceptChallenge,
  apiKeyRequired,
}
