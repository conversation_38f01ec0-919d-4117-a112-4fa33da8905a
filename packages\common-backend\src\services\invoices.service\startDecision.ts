import { Company, Invoice, LoanApplication } from '../../models'
import { getPaymentPlans, getPlan } from '../loanplan.service'
import { invoicesService } from '.'
import {
  getLoanApplicationDraftFullInfo,
  isLoanRejectedCompany,
} from '../company.service'
import { dictionaries, exceptions } from '@linqpal/models'
import { Logger } from '../logger/logger.service'
import { AgreementType } from '../agreement/types'
import { AwsService, DocumentVersioningService } from '../../../index'
import { compatibilityService } from '../compatibility/compatibility.service'
import { QuoteService } from '../quote/quote.service'
import moment from 'moment-timezone'
import { ILoanApplication } from '../../models/types'
import { PURCHASE_TYPE } from '@linqpal/models/src/dictionaries'
import mongoose, { Types } from 'mongoose'
import { IDownPaymentDetails } from '../onBoarding/types'

const logger = new Logger({
  module: 'bluetape.services',
  subModule: 'startDecisionEngine',
})

export async function startDecisionEngine(
  loanApplicationId: string,
  downPaymentDetails: IDownPaymentDetails | null,
  user?: any,
) {
  logger.info(`staring DE for app ${loanApplicationId}`)

  const item = await LoanApplication.findById(loanApplicationId)
  if (!item) {
    throw new exceptions.LogicalError('application not found')
  }

  const plans = await getPaymentPlans()
  const isRejected = await isLoanRejectedCompany(item.company_id)
  const planIds = plans.map((p) => p._id.toString())
  const planNames = plans.filter((p) => p.name)?.map((t) => t.name)

  if (isRejected) {
    throw new exceptions.LogicalError('last-app-was-rejected')
  } else if (item.invoiceDetails) {
    if (item.invoiceDetails.invoiceId && !item.invoiceDetails.paymentPlan)
      throw new exceptions.LogicalError('error/no-payment-plan-selected')
    else if (
      item.invoiceDetails.paymentPlan &&
      !planIds.includes(item.invoiceDetails.paymentPlan) &&
      !planNames.includes(item.invoiceDetails.paymentPlan)
    )
      throw new exceptions.LogicalError('error/wrong-payment-plan')
  }

  if (item.invoiceDetails && item.invoiceDetails.invoiceId) {
    const invoiceIds = Array.isArray(item.invoiceDetails.invoiceId)
      ? item.invoiceDetails.invoiceId
      : [item.invoiceDetails.invoiceId]

    logger.info(
      `check compatibility of invoices and customer's purchase type ${item.invoiceDetails.invoiceId}`,
    )

    await checkInvoicesPurchaseTypeCompatibility(invoiceIds, item.company_id)

    logger.info(
      `reporting invoices ${item.invoiceDetails.invoiceId} to NetSuite`,
    )
    for (const invoiceId of invoiceIds) {
      await AwsService.sendSQSMessage(
        'netsuite-connector',
        JSON.stringify({
          id: invoiceId,
          operationType: 'InvoiceStatusUpdate',
          status: 'InReview',
        }),
        'NETSUITE',
      )
    }
  }

  logger.info(`updating draft for app ${item.id}`)

  if (user) await invoicesService.saveOwnerData(item.company_id, user)

  const agreementType: AgreementType =
    item.invoiceDetails === undefined
      ? AgreementType.MASTER_AGREEMENT
      : AgreementType.BNPL_AGREEMENT

  const agreementTemplate =
    await DocumentVersioningService.getLatestTemplateByType(agreementType)
  item.agreementTemplateId = agreementTemplate.id

  const detailedDraft = await getLoanApplicationDraftFullInfo(item.company_id)
  const draft = detailedDraft?.data

  if (draft) item.draft = { ...draft, normalized: true }
  if (item.isSentBack) item.isSentBack = false

  if (user && !item.submitter) {
    item.submitter = {
      id: user._id.toString(),
      firstName: user.firstName,
      lastName: user.lastName,
    }
  }

  item.status = dictionaries.LOAN_APPLICATION_STATUS.PROCESSING

  await initApplicationMetadata(item)
  await item.save()

  await QuoteService.initQuoteAuthorization(item)

  try {
    await compatibilityService.runDotNetDEForLineOfCredit(item.id)
  } catch (e) {
    console.log('Error happened during new Credit Application DE running', e)
  }

  try {
    await compatibilityService.runDotNetDEForDrawApplication(
      item.id,
      downPaymentDetails,
    )
  } catch (e) {
    console.log('Error happened during new Draw Approval DE running', e)
  }

  return item.id
}

async function initApplicationMetadata(app: ILoanApplication) {
  // moved from old DE validate step for backward compatibility
  if (!app.metadata) app.metadata = {}

  app.metadata.creditPolicyVersion = 'v2.2'
  app.metadata.fundingSources = [{ name: 'arcadia' }]

  if (app.invoiceDetails?.paymentPlan) {
    app.metadata.paymentPlan = await getPlan(app.invoiceDetails.paymentPlan)
  }

  if (!app.submitDate) app.submitDate = moment().toDate()
}

async function checkInvoicesPurchaseTypeCompatibility(
  invoiceIds: string[],
  companyId: string,
): Promise<void> {
  const company = await Company.findById(companyId)
  if (!company) {
    throw new exceptions.LogicalError('Company not found')
  }

  const objectIds = invoiceIds.map((id) => new Types.ObjectId(id))
  const invoices = await Invoice.find({
    _id: mongoose.trusted({
      $in: objectIds,
    }),
  })

  const invoicesWithoutProject = invoices.filter(
    (invoice) => !invoice.project_id,
  )

  if (
    invoicesWithoutProject.length &&
    company.credit?.purchaseType === PURCHASE_TYPE.PROJECT
  ) {
    throw new exceptions.LogicalError(
      'All invoices must be assigned to a project for this purchase type',
    )
  }

  const firstProjectId =
    invoices.find((invoice) => invoice.project_id)?.project_id || null

  if (firstProjectId) {
    const hasDifferentProjects = invoices.some(
      (invoice) => invoice.project_id?.toString() !== firstProjectId.toString(),
    )

    if (hasDifferentProjects) {
      throw new exceptions.LogicalError(
        'All invoices must be assigned to the same project',
      )
    }
  }
}
