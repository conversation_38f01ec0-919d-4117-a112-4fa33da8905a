import React, { FC, useRef, useState } from 'react'
import { BtGoogleAutocomplete, BtNumberInput } from '@linqpal/components/src/ui'
import { BtInput } from '@linqpal/components/src/ui/'
import { useTranslation } from 'react-i18next'
import { StyleSheet } from 'react-native'
import { observer } from 'mobx-react'
import { useResponsive } from '@linqpal/components/src/hooks'
import { IApplicationAddress } from '@linqpal/models'
import { Col, Row } from '../../../../ui/atoms/Grid'

interface IProps {
  address: IApplicationAddress
  collapsible?: boolean
  label?: string
  testID?: string
  onChange?: (value: IApplicationAddress) => void
}

export const AddressEditor: FC<IProps> = observer(
  ({ address, collapsible = false, label = '', testID, onChange }) => {
    const { t } = useTranslation('application')
    const { sm } = useResponsive()

    const shouldExpand = () =>
      !collapsible ||
      !!address.address ||
      !!address.state ||
      !!address.city ||
      !!address.zip

    const [expanded, setExpanded] = useState(shouldExpand)

    const autoCompleteRef = useRef<HTMLDivElement>(null)

    const onSelect = (value) => {
      address.address = value.address
      address.state = value.state
      address.city = value.city
      address.zip = value.zip

      setExpanded(true)
      !!onChange && onChange(address)
    }

    const onAddressFocused = () => {
      if (!sm) {
        autoCompleteRef?.current?.scrollIntoView()
      }
    }

    const onAddressBlurred = () => {
      if (!shouldExpand()) {
        setExpanded(false)
      }
    }

    const handleChange = (field: keyof IApplicationAddress, value: string) => {
      if (field in address) address[field] = value

      !!onChange && onChange(address)
    }

    const resetAddress = () => {
      if ('address' in address) address.address = ''
      if ('state' in address) address.state = ''
      if ('city' in address) address.city = ''
      if ('zip' in address) address.zip = ''

      !!onChange && onChange(address)
    }

    const formatAddress = () => {
      return [address.address, address.city, address.state, address.zip]
        .filter((c) => !!c)
        .join(', ')
    }

    return (
      <>
        <Row>
          <Col xs={12}>
            <div ref={autoCompleteRef}>
              <BtGoogleAutocomplete
                value={expanded ? address.address : formatAddress()}
                onChange={(text) => {
                  if (!text) {
                    resetAddress()
                  } else if (expanded) {
                    handleChange('address', text)
                  }
                }}
                onSelect={onSelect}
                onFocus={onAddressFocused}
                onBlur={onAddressBlurred}
                label={
                  expanded
                    ? t('Business.StreetAddressLabel')
                    : label || t('Business.HomeAddressLabel')
                }
                testID={`${testID}.address`}
              />
            </div>
          </Col>
        </Row>
        {expanded && (
          <>
            <Row>
              <Col xs={12}>
                <BtNumberInput
                  value={address.zip}
                  onChangeText={(text) => handleChange('zip', text)}
                  style={styles.textBox}
                  label={t('Business.ZipLabel')}
                  testID={`${testID}.zip`}
                  format={'#####'}
                  disabled
                />
              </Col>
            </Row>
            <Row>
              <Col xs={12}>
                <BtInput
                  value={address.city}
                  onChangeText={(text) => handleChange('city', text)}
                  style={styles.textBox}
                  label={t('Business.CityLabel')}
                  testID={`${testID}.city`}
                  disabled
                />
              </Col>
            </Row>
            <Row>
              <Col xs={12}>
                <BtInput
                  value={address.state}
                  onChangeText={(text) => handleChange('state', text)}
                  style={styles.textBox}
                  label={t('Business.StateLabel')}
                  testID={`${testID}.state`}
                  disabled
                />
              </Col>
            </Row>
          </>
        )}
      </>
    )
  },
)

const styles = StyleSheet.create({
  textBox: { marginTop: 24 },
})
