import {
  Company,
  FinicityTransactions,
  initializeFinicity,
} from '@linqpal/common-backend'
import moment from 'moment'
import { Wrapper } from './wrapper'
import stringSimilarity from 'string-similarity'
import * as math from 'mathjs'
import {
  IBankAccount,
  ICompany,
  ILoanApplication,
} from '@linqpal/common-backend/src/models/types'
import { ILinqpalEvent } from '../types'
import { importCompanyTransactions } from '../../finicity/importTransactions'
import mongoose from 'mongoose'

export async function VerifyFinicityAccount(event: ILinqpalEvent) {
  return Wrapper('VerifyFinicityAccount', event, async (app) => {
    const finicityReq = await initializeFinicity()
    const company = await Company.findById(app.company_id).populate(
      'bankAccounts',
    )
    const has = hasConnection(company)
    if (!has) {
      return { status: 'manual' }
    }
    if (company?.bankAccounts && company.bankAccounts.length === 0) {
      return { status: 'manual' }
    }
    const checking = company?.bankAccounts?.find(
      (a) => a.accountType === 'checking' && !a.isManualEntry,
    )
    if (!checking) {
      return { status: 'manual' }
    }
    if (!company?.finicity?.customerId) {
      return { status: 'manual' }
    }
    const accountOwner = await finicityReq.accounts.getAccountOwner(
      company?.finicity.customerId,
      checking.finicity?.accountId || '',
    )
    const businessName = app.draft?.businessInfo_businessName?.legalName || ''
    const businessAddress = app.draft?.businessInfo_businessAddress || {
      address: '',
      city: '',
      state: '',
      zip: '',
    }
    const name = app.draft?.businessOwner_firstName || ''
    const surname = app.draft?.businessOwner_lastName || ''
    const address = app.draft?.businessOwner_address || {
      address: '',
      city: '',
      state: '',
      zip: '',
    }

    const companyNameScore = stringSimilarity.compareTwoStrings(
      accountOwner?.name || '',
      businessName,
    )
    const personalNameScore = stringSimilarity.compareTwoStrings(
      accountOwner?.name || '',
      `${name} ${surname}`,
    )
    const companyAddressScore = stringSimilarity.compareTwoStrings(
      accountOwner?.address || '',
      `${businessAddress.address} ${businessAddress.city} ${businessAddress.state} ${businessAddress.zip}`,
    )
    const personalAddressScore = stringSimilarity.compareTwoStrings(
      accountOwner?.address || '',
      `${address.address} ${address.city} ${address.state} ${address.zip}`,
    )
    const nameScore = math.max(companyNameScore, personalNameScore)
    const addressScore = math.max(companyAddressScore, personalAddressScore)
    return {
      status:
        math.mean(nameScore, addressScore) > 0.7
          ? 'verified'
          : math.max(nameScore, addressScore) > 0.6
          ? 'manual'
          : 'failed',
      companyNameScore,
      personalNameScore,
      nameScore,
      companyAddressScore,
      personalAddressScore,
      addressScore,
    }
  })
}

export async function CheckFinicityData(event: ILinqpalEvent) {
  return Wrapper(
    'CheckFinicityData',
    event,
    async (app) => {
      const { company_id } = app
      const company = await Company.findById(company_id).populate(
        'bankAccounts',
      )
      const has = await hasConnection(company)
      if (!has) {
        return { status: 'no-data' }
      }
      if (!company?.bankAccounts) return { status: 'no-data' }
      const accounts = company.bankAccounts.filter((a) => a.finicity?.accountId)
      const toBeForced: IBankAccount[] = []
      for (const a of accounts) {
        const ts = await FinicityTransactions.find({
          company_id: company_id,
          customerId: parseInt(company?.finicity?.customerId),
          transactionDate: mongoose.trusted({
            $gt: moment().subtract(10, 'days').unix(),
          }),
          accountId: a.finicity?.accountId,
        }).countDocuments()
        if (ts === 0) {
          toBeForced.push(a)
        }
      }
      if (toBeForced.length === 0) {
        return { status: 'ready' }
      }
      const finicity = await initializeFinicity()
      for (const a of toBeForced) {
        if (!(a.finicity?.accountId && company.finicity?.customerId)) continue
        try {
          const info = await finicity.accounts.getAccounts(
            company.finicity.customerId,
            a.finicity.accountId,
          )
          console.log(info)
          a.finicity.syncState = info.aggregationStatusCode
          await a.save()
        } catch (e: any) {
          console.log(e)
          if (e.data?.code === 38003) {
            a.finicity.accountId = undefined
            await a.save()
          }
          if (e.data?.code === 14001) {
            company.finicity.customerId = undefined
            await company.save()
          }
        }
      }
      const allSynced = accounts.filter(
        (a) => a.finicity?.accountId && a.finicity?.syncState === 0,
      )
      await importCompanyTransactions(company_id)
      const ts = await FinicityTransactions.find({
        company_id: company_id,
        customerId: parseInt(company?.finicity?.customerId),
        transactionDate: mongoose.trusted({
          $gt: moment().subtract(2, 'years').unix(),
        }),
      }).countDocuments()
      if (ts > 0) {
        return { status: 'ready' }
      }
      if (allSynced.length === 0) {
        const withError = accounts.filter(
          (a) =>
            typeof a.finicity?.syncState === 'number' &&
            a.finicity?.syncState > 0,
        )
        if (withError.length === accounts.length) return { status: 'no-data' }
        return { status: 'not-ready' }
      }
      return { status: 'no-data' }
    },
    { noTransaction: true },
  )
}

export async function ProcessFinicityData(event: ILinqpalEvent) {
  return Wrapper(
    'ProcessFinicityData',
    event,
    async (app: ILoanApplication) => {
      const company = await Company.findById(app.company_id).populate(
        'bankAccounts',
      )
      if (!company?.finicity?.customerId) return
      const finicityAPI = await initializeFinicity()
      let balance = 0
      try {
        const accounts = await finicityAPI.accounts.getAccounts(
          company?.finicity.customerId,
        )
        for (const a of accounts.filter((_) =>
          ['checking', 'savings'].includes(_.type.toLowerCase()),
        )) {
          balance += a.balance
        }
      } catch (e) {
        console.log(e)
      }
      const cashFlow = await getCashFlow(company?.finicity.customerId, balance)

      let index = app.outputs.findIndex((a) => a.step === 'ProcessPlaidData')
      index !== -1 && app.outputs.splice(index, 1)
      index = app.outputs.findIndex((a) => a.step === 'ProcessManualData')
      index !== -1 && app.outputs.splice(index, 1)

      return { cashFlow }
    },
  )
}

export async function ProcessManualData(event: ILinqpalEvent) {
  return Wrapper('ProcessManualData', event, async (app: ILoanApplication) => {
    const company_id = app.company_id
    const cashFlow = await computeCashFlow(company_id)

    let index = app.outputs.findIndex((a) => a.step === 'ProcessPlaidData')
    index !== -1 && app.outputs.splice(index, 1)
    index = app.outputs.findIndex((a) => a.step === 'ProcessFinicityData')
    index !== -1 && app.outputs.splice(index, 1)

    return { cashFlow }
  })
}

async function hasConnection(company?: ICompany | null) {
  return (
    company?.finicity?.customerId &&
    company?.bankAccounts?.some((a) => a.finicity?.accountId)
  )
}

function getCashFlow(customerId: string, currentBalance = 0) {
  return FinicityTransactions.aggregate([
    {
      $match: {
        customerId: parseInt(customerId),
      },
    },
    {
      $addFields: {
        date: { $toDate: { $multiply: ['$transactionDate', 1000] } },
      },
    },
    {
      $group: {
        _id: {
          customerId: '$customerId',
          date: { $dateToString: { date: '$date', format: '%Y-%m' } },
        },
        debit: {
          $sum: {
            $cond: {
              if: { $lt: ['$amount', 0] },
              then: { $abs: '$amount' },
              else: 0,
            },
          },
        },
        credit: {
          $sum: {
            $cond: {
              if: { $gt: ['$amount', 0] },
              then: { $abs: '$amount' },
              else: 0,
            },
          },
        },
      },
    },
    {
      $project: {
        customerId: '$_id.customerId',
        date: '$_id.date',
        debit: { $round: ['$debit', 2] },
        credit: { $round: ['$credit', 2] },
        cashFlow: { $round: [{ $subtract: ['$credit', '$debit'] }, 2] },
      },
    },
    {
      $setWindowFields: {
        partitionBy: '$customerId',
        sortBy: { date: 1 },
        output: {
          lastBalance: {
            $sum: '$cashFlow',
            window: {
              documents: ['unbounded', 'unbounded'],
            },
          },
          outBalance: {
            $sum: '$cashFlow',
            window: {
              documents: ['unbounded', 'current'],
            },
          },
        },
      },
    },
    { $addFields: { diff: { $subtract: [currentBalance, '$lastBalance'] } } },
    {
      $project: {
        customerId: 1,
        date: 1,
        cashFlow: 1,
        debit: 1,
        credit: 1,
        balance: { $round: [{ $add: ['$diff', '$outBalance'] }, 2] },
      },
    },
    { $sort: { date: -1 } },
  ])
}

async function computeCashFlow(company_id: string) {
  const prev_month = moment().startOf('month').subtract(1, 'month')

  const transactions = await FinicityTransactions.aggregate([
    { $match: { company_id } },
    {
      $addFields: {
        date: { $toDate: { $multiply: ['$transactionDate', 1000] } },
      },
    },
    { $sort: { transactionDate: 1 } },
    {
      $group: {
        _id: { $dateToString: { date: '$date', format: '%Y-%m' } },
        cashFlow: { $sum: '$amount' },
        balance: { $last: '$balance' },
        debit: {
          $sum: {
            $cond: {
              if: { $lt: ['$amount', 0] },
              then: { $abs: '$amount' },
              else: 0,
            },
          },
        },
        credit: {
          $sum: {
            $cond: {
              if: { $gt: ['$amount', 0] },
              then: { $abs: '$amount' },
              else: 0,
            },
          },
        },
      },
    },
    {
      $project: {
        date: '$_id',
        debit: { $round: ['$debit', 2] },
        credit: { $round: ['$credit', 2] },
        cashFlow: { $round: ['$cashFlow', 2] },
        balance: 1,
      },
    },
    { $sort: { date: -1 } },
  ])

  if (transactions.length === 0) {
    return Array(6)
      .fill(0)
      .map((_, i) => ({
        date: prev_month.clone().subtract(i, 'month').format('YYY-MM'),
        cashFlow: 0,
        balance: 0,
      }))
  }

  const cash_flow: any[] = []
  let index = 0,
    date = prev_month.clone()
  while (index < transactions.length) {
    const transaction_date = moment(transactions[index]?.date, 'YYYY-MM')
    if (transaction_date.isAfter(date)) {
      index++
    } else if (transaction_date.isBefore(date)) {
      cash_flow.push({
        date: date.format('YYYY-MM'),
        cashFlow: 0,
        balance: 0,
        debit: 0,
        credit: 0,
      })
      date = date.subtract(1, 'month')
    } else {
      cash_flow.push({
        date: transactions[index].date,
        cashFlow: transactions[index].cashFlow,
        balance: transactions[index].balance,
        debit: transactions[index].debit,
        credit: transactions[index].credit,
      })
      index++
      date = date.subtract(1, 'month')
    }
  }
  index = cash_flow.length - 1
  while (index >= 0) {
    const { cashFlow, balance } = cash_flow[index]
    if (
      cashFlow === 0 &&
      balance === 0 &&
      balance !== cash_flow[index + 1].balance
    ) {
      cash_flow[index].balance = cash_flow[index + 1].balance
    }
    index--
  }
  return cash_flow
}
