import { FlowController } from '../FlowController'

// prettier-ignore
const StepDefinition = {
  sponsor: [
    'loanOfficer',
    'personalInformation',
    'homeAddress',
    'maritalStatus',
    'statements',
    'citizenship',
  ],
  businessEntity: [
    'entityInformation',
    'address',
    'isCreatedForProject',
    'dateIncorporated',
    'representative',
    'generalContractorName',
    'bankAccountId',
  ],
  currentProject: [
    'hasMultipleProducts',
    'loanType',
    'mainAddress',
    'products',
    'loanPurpose',
    'loanTerm',
    'willCompletePermits',
    'originalPurchasePrice',
    'originalPurchaseDate',
    'payOffAmount',
    'subordinateDebtType',
    'subordinateDebtBalance',
    'financialDetails',
  ],
  previousProjects: [
    'projects',
  ],
  review: [
    'review',
  ]
} as const

export const Steps = FlowController.createStepConstants(StepDefinition)
export const Groups = FlowController.createGroupConstants(StepDefinition)
