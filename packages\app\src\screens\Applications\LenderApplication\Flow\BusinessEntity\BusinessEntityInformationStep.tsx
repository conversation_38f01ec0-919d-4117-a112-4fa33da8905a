import React, { FC } from 'react'
import { View } from 'react-native'
import { BtInput_v1, BtSelect } from '@linqpal/components/src/ui'
import { BtTaxIdInput_v1 } from '@linqpal/components/src/ui/BtTaxIdInput_v1'
import { observer } from 'mobx-react-lite'
import { useLenderApplication } from '../../LenderApplicationContext'
import { ILenderApplicationEditor } from '../getLenderApplicationEditor'
import { BusinessEntityType } from '@linqpal/models/src/applications/lender/ILenderApplicationDraft'
import { Validators } from '../../../Helpers/Validators'
import { editorStyles } from '../editorStyles'
import { useTranslation } from 'react-i18next'
import SecurityNotice from '../../../../../ui/molecules/SecurityNotice'

const BusinessEntityInformationEditor: FC = observer(() => {
  const { t } = useTranslation('application')
  const store = useLenderApplication()

  return (
    <View style={editorStyles.formContainer}>
      <View style={editorStyles.singleColumnRow}>
        <BtInput_v1
          required
          autoFocus
          value={store.draft.data.businessEntity.name || ''}
          label={t('LenderApplication.Flow.BusinessEntity.Information.Name')}
          testID="LenderApplication.BusinessEntity.Name"
          onChangeText={(val: string) =>
            (store.draft.data.businessEntity.name = val)
          }
          validate={Validators.required}
        />
      </View>

      <View style={editorStyles.singleColumnRow}>
        <BtSelect
          mandatory
          label={t('LenderApplication.Flow.BusinessEntity.Information.Type')}
          value={store.draft.data.businessEntity?.type}
          // prettier-ignore
          options={[
            {
              name: t('LenderApplication.Flow.BusinessEntity.Information.BusinessTypes.LLC'),
              id: BusinessEntityType.LLC,
            },
            {
              name: t('LenderApplication.Flow.BusinessEntity.Information.BusinessTypes.CCorp'),
              id: BusinessEntityType.CCorp,
            },
            {
              name: t('LenderApplication.Flow.BusinessEntity.Information.BusinessTypes.SCorp'),
              id: BusinessEntityType.SCorp,
            },
          ]}
          onChange={(val: { id: BusinessEntityType }) =>
            (store.draft.data.businessEntity.type = val.id)
          }
          appearance="thick-border"
          testID="LenderApplication.BusinessEntity.Type"
        />
      </View>

      <View style={editorStyles.singleColumnRow}>
        <BtTaxIdInput_v1
          required
          value={store.draft.data.businessEntity?.ein || ''}
          label={t('LenderApplication.Flow.BusinessEntity.Information.Ein')}
          testID="LenderApplication.BusinessEntity.EIN"
          onChangeText={(val: string) =>
            (store.draft.data.businessEntity.ein = val)
          }
          validate={Validators.ein}
        />
      </View>

      <SecurityNotice
        description={t('LenderApplication.Flow.SecurityNotice')}
      />
    </View>
  )
})

export const BusinessEntityInformationStep: ILenderApplicationEditor = {
  options: {
    title: 'LenderApplication.Flow.BusinessEntity.Information.Title',
  },
  component: BusinessEntityInformationEditor,
}
