import { IUnifiedApplicationDraft } from '../../applications/unified/IUnifiedApplicationDraft'
import { ILenderApplicationDraft } from '../../applications/lender/ILenderApplicationDraft'

// region Unified app

export interface IGetUnifiedApplicationRequest {
  companyId: string
}

export interface IGetUnifiedApplicationResponse {
  draft: IUnifiedApplicationDraft | null
}

// endregion Unified app

// region Lender app

export interface IGetLenderApplicationRequest {
  companyId: string
}

export interface IGetLenderApplicationResponse {
  draft: ILenderApplicationDraft | null
}

export interface IPostLenderApplicationRequest {
  companyId: string
  draft: ILenderApplicationDraft
}

export interface IPostLenderApplicationResponse {
  draft: ILenderApplicationDraft
}

// endregion Lender app
