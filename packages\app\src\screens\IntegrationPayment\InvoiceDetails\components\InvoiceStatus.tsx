import React from 'react'
import { View, StyleSheet, Text } from 'react-native'
import { composeStyle } from '@linqpal/common-frontend/src/helpers'
import { useTranslation } from 'react-i18next'
import { Divider } from 'react-native-paper'
import { StatusLabel } from '../../../../ui/molecules/StatusLabel/StatusLabel'
import { getSalesDocumentStatusUIOptions } from '../../../../ui/molecules/StatusLabel/options/getSalesDocumentStatusUIOptions'

export const InvoiceStatusInfo = ({ status }) => {
  const { t } = useTranslation('global')

  return (
    <>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          paddingTop: 4,
        }}
        testID={'invoice-status-info-container'}
      >
        <Text
          style={composeStyle(styles.normalText, { maxWidth: '50%' })}
          testID={'invoice-status-title'}
        >
          {t('integrations.invoice.status')}
        </Text>
        <StatusLabel
          status={status}
          uiOptionsProvider={getSalesDocumentStatusUIOptions}
        />
      </View>
      <Divider style={{ marginTop: 12, marginBottom: 14 }} testID={'divider'} />
    </>
  )
}

const styles = StyleSheet.create({
  normalText: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 21,
    color: '#99ADBA',
  },
})
