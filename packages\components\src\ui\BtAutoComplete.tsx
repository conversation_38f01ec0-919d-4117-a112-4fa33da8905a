import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'

import {
  Autocomplete,
  AutocompleteItem,
  AutocompleteProps,
} from '@ui-kitten/components'
import { ActivityIndicator, ScrollView } from 'react-native'
import { debounce } from 'lodash'
import { BtInputLabel } from './BtInputLabel'
import { useResponsive } from '../hooks'
import { useTranslation } from 'react-i18next'

export interface BtAutoCompleteProps<T>
  extends Omit<AutocompleteProps, 'onSelect'> {
  required?: boolean
  searchParams?: any
  labelSelector: (option: T) => string
  apiSearchFn: (...params: any) => Promise<T[]>
  onSelect?: (value: T) => void
  defaultSearchText?: string
  autoFocus?: boolean
}

export const BtAutoComplete = <T,>({
  label,
  required,
  searchParams,
  labelSelector,
  apiSearchFn,
  onSelect,
  testID,
  defaultSearchText = 'A',
  autoFocus = false,
  ...autoCompleteProps
}: BtAutoCompleteProps<T>) => {
  const [isLoading, setIsLoading] = useState(false)
  const [isInitialLoad, setIsInitialLoad] = useState(false)
  const [options, setOptions] = useState<any[]>([])
  const autocompleteRef = useRef<any>(null)
  const [focused, setFocused] = useState(false)
  const { sm } = useResponsive()
  const { t } = useTranslation('global')

  const search = useCallback(async (searchValue: string) => {
    setIsLoading(true)

    const params = {
      search: searchValue,
      ...(searchParams || {}),
    }

    const response = await apiSearchFn(params)

    setIsLoading(false)
    setIsInitialLoad(false)
    if (response.length) {
      setOptions(response) // Set only when at least one result is returned, if not, keep showing the previous results, because we dont want dropdown to re-render without any options, which will result in overlapping with the input in the next renders (on ios)
    } else if (searchValue.length >= defaultSearchText?.length) {
      setOptions([{ name: t('AutoCompleteInput.no-results-found') }])
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const debouncedSearch = useMemo(() => debounce(search, 300), [search])

  useEffect(() => {
    if (testID) {
      const textInput =
        autocompleteRef?.current?.inputRef?.current?.textInputRef?.current

      if (textInput && 'setAttribute' in textInput) {
        textInput.setAttribute('data-testid', testID)
      }
    }
  }, [testID])

  useEffect(() => {
    return () => {
      debouncedSearch.cancel()
    }
  }, [debouncedSearch])

  const renderAutocompleteItem = (option: T, index: number) => {
    const title = labelSelector(option)
    return (
      <AutocompleteItem
        key={index}
        title={title}
        onPress={() => handleSelect(index)}
        disabled={title === t('AutoCompleteInput.no-results-found')}
      />
    )
  }

  const handleSelect = (index: number) => {
    const selectedOption = options[index]

    // to make LP-6275 and LP-7035 work together
    autocompleteRef?.current?.popoverRef?.current?.backdropConfig?.onBackdropPress?.()

    setTimeout(() => setFocused(false), 200) // Because after an option is selected, keyboard collapses and then dropdown collapses which causes a white line to show over the input (on ios)
    if (onSelect) {
      onSelect(selectedOption)
    }
  }

  useEffect(() => {
    setIsInitialLoad(true)
    debouncedSearch(defaultSearchText)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // Handle autofocus functionality
  useEffect(() => {
    if (autoFocus && autocompleteRef.current) {
      const textInput =
        autocompleteRef?.current?.inputRef?.current?.textInputRef?.current

      if (textInput && textInput.focus) {
        // Small delay to ensure component is fully mounted
        setTimeout(() => {
          textInput.focus()
        }, 100)
      }
    }
  }, [autoFocus])

  const handleChangeText = (text: string) => {
    if (autoCompleteProps.onChangeText) {
      autoCompleteProps.onChangeText(text)
    }
    debouncedSearch(text || defaultSearchText)
  }

  return (
    <Autocomplete
      {...autoCompleteProps}
      label={label && <BtInputLabel required={required}>{label}</BtInputLabel>}
      ref={autocompleteRef}
      accessoryRight={
        <>
          {isLoading ? <ActivityIndicator size="small" /> : null}
          {autoCompleteProps.accessoryRight}
        </>
      }
      disabled={isInitialLoad}
      onSelect={handleSelect}
      onChangeText={handleChangeText}
      onFocus={() => setFocused(true)}
    >
      {focused ? (
        <ScrollView style={sm ? { maxHeight: 160 } : { height: 80 }}>
          {options.map(renderAutocompleteItem)}
        </ScrollView>
      ) : undefined}
    </Autocomplete>
  )
}
