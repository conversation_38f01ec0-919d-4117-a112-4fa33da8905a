import { stringifyUrl } from 'query-string'
import { ICompany, ICustomerAccount } from '../../models/types'
import { getBillingContacts } from '../company.service'
import { getInvoiceLink } from './notifications'

export interface Receiver {
  email: string
  phone: string
  name?: string | null
  fullName: string
  firstName: string | null
  customerAccountId: string
  link: string
}

interface ReceiversForNotifications {
  emailReceivers: Map<string, Receiver>
  smsReceivers: Map<string, Receiver>
}

const createReceiver = (
  email: string | undefined,
  phone: string | undefined,
  firstName: string | null,
  lastName: string | null,
  customerAccountId: string,
  link: string,
): Receiver => ({
  email: email ? email.toLowerCase() : '',
  phone: phone || '',
  name: firstName,
  fullName: [firstName, lastName].filter(Boolean).join(' ').trim(),
  firstName,
  customerAccountId,
  link: stringifyUrl({
    url: link,
    query: { account: customerAccountId },
  }),
})

export async function getReceivers(
  customerAccounts: ICustomerAccount[] = [],
  invoiceId: string,
  company: ICompany | null,
  host: string,
): Promise<ReceiversForNotifications> {
  const emailReceivers = new Map<string, Receiver>()
  const smsReceivers = new Map<string, Receiver>()

  if ((customerAccounts.length || 0) <= 0)
    return { emailReceivers, smsReceivers }

  const link = await getInvoiceLink(invoiceId, company, host)

  const billingContacts = (
    await Promise.all(
      customerAccounts.map((customerAccount) =>
        getBillingContacts(customerAccount.id),
      ),
    )
  ).flat()

  ;[...customerAccounts, ...billingContacts].forEach((account: any) => {
    const receiver = createReceiver(
      account.email,
      account.phone,
      account.first_name,
      account.last_name,
      account._id ? account._id : account.id,
      link,
    )

    if (receiver.email) {
      emailReceivers.set(receiver.email, receiver)
    }

    if (receiver.phone) {
      smsReceivers.set(receiver.phone, receiver)
    }
  })

  return { emailReceivers, smsReceivers }
}
