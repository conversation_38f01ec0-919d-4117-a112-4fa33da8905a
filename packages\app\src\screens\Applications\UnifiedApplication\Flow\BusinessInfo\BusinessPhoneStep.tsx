import React, { FC } from 'react'
import { observer } from 'mobx-react-lite'
import { useTranslation } from 'react-i18next'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { runInAction } from 'mobx'
import { BtPhoneInput } from '@linqpal/components/src/ui/BtPhoneInput'
import { UnifiedApplicationValidator } from '@linqpal/models'
import { IUnifiedApplicationEditor } from '../getUnifiedApplicationEditor'

const BusinessPhoneEditor: FC = observer(() => {
  const { t } = useTranslation('application')
  const store = useUnifiedApplication()

  const handleChange = (val: { parsed: string }) => {
    runInAction(() => {
      store.draft.data.businessInfo.businessPhone = val.parsed
    })
  }

  const handleValidation = (value: string) => {
    return UnifiedApplicationValidator.validatePhone(value)
      ? ''
      : t('ValidationErrors.InvalidPhone')
  }

  return (
    <BtPhoneInput
      value={store.draft.data.businessInfo.businessPhone ?? ''}
      required
      size="large"
      validate={handleValidation}
      onChangeText={handleChange}
      label={t('Business.PhoneLabel')}
      testID="UnifiedApplication.BusinessInfo.Phone"
    />
  )
})

export const BusinessPhoneStep: IUnifiedApplicationEditor = {
  options: {
    title: 'Business.Phone',
    description: 'Business.PhoneDescription',
  },
  component: BusinessPhoneEditor,
}
