import React from 'react'
import { CompanyStatus } from '@linqpal/models'
import { useStore } from '../../../store'
import Application from '../../GeneralApplication/Application'
import { observer } from 'mobx-react-lite'
import { ScreenWithCobrandingHeader } from '../../../ui/white-label-components/ScreenWithCobrandingHeader'
import { UnifiedApplicationProvider } from '../../Applications/UnifiedApplication/UnifiedApplicationContext'

export const GetPaidApplication = observer(() => {
  const { userStore } = useStore()
  const status = userStore.company?.status || CompanyStatus.New
  const approveRead = userStore.settings.get('approveRead')

  if (
    [CompanyStatus.New, CompanyStatus.NotStarted].includes(status) &&
    approveRead
  ) {
    return (
      <UnifiedApplicationProvider>
        <ScreenWithCobrandingHeader>
          <Application />
        </ScreenWithCobrandingHeader>
      </UnifiedApplicationProvider>
    )
  }

  return null
})
