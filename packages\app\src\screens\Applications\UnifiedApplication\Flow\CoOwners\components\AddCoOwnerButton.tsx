import React, { FC } from 'react'
import { BtButton } from '@linqpal/components/src/ui'
import { useTranslation } from 'react-i18next'
import { CoOwnerValidator } from '@linqpal/models'
import { useResponsive } from '../../../../../../utils/hooks'
import { useUnifiedApplication } from '../../../UnifiedApplicationContext'

interface AddCoOwnerButtonProps {
  onPress: () => void
}

export const AddCoOwnerButton: FC<AddCoOwnerButtonProps> = ({ onPress }) => {
  const { t } = useTranslation('application')
  const { sm } = useResponsive()

  const store = useUnifiedApplication()

  const canHaveCoOwners =
    100 - store.draft.totalOwnershipPercentage >=
    CoOwnerValidator.MinCoOwnerPercentage

  return (
    <>
      {canHaveCoOwners ? (
        <BtButton
          size="small"
          appearance="ghost"
          status="basic"
          onPress={onPress}
          style={
            sm
              ? { marginLeft: 'auto' }
              : { marginTop: 20, width: 180, alignSelf: 'center' }
          }
          // eslint-disable-next-line i18next/no-literal-string
          iconLeft="plus-outline"
        >
          {t('CoOwners.AddCoOwner')}
        </BtButton>
      ) : null}
    </>
  )
}
