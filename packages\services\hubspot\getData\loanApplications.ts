import { PipelineStage } from 'mongoose'

export const lookup: PipelineStage = {
  $lookup: {
    from: 'loanapplications',
    as: 'loanApplication',
    let: {
      companyId: '$companyId',
    },
    pipeline: [
      {
        $match: {
          $expr: {
            $eq: ['$company_id', '$$companyId'],
          },
        },
      },
      {
        $lookup: {
          from: 'virtualcards',
          as: 'virtualCard',
          let: {
            virtual_card_id: '$invoiceDetails.cardId',
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$cardId', '$$virtual_card_id'] },
                    {
                      $ne: [{ $type: '$$virtual_card_id' }, 'missing'],
                    },
                  ],
                },
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: '$virtualCard',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $group: {
          _id: '$_id',
          companyId: {
            $first: '$company_id',
          },
          updatedAt: {
            $first: '$updatedAt',
          },
          virtualCard: {
            $first: '$virtualCard',
          },
          createdAt: {
            $first: '$createdAt',
          },
          invoiceDetails: {
            $first: '$invoiceDetails',
          },
          status: {
            $first: '$status',
          },
          approvedAmount: {
            $first: '$approvedAmount',
          },
        },
      },
      {
        $addFields: {
          invoices_ids: {
            $cond: [
              {
                $eq: [
                  {
                    $type: '$invoiceDetails.invoiceId',
                  },
                  'array',
                ],
              },
              '$invoiceDetails.invoiceId',
              ['$invoiceDetails.invoiceId'],
            ],
          },
        },
      },
      {
        $lookup: {
          from: 'invoices',
          as: 'invoices',
          let: {
            invoices_ids: '$invoices_ids',
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $in: [
                    {
                      $toString: '$_id',
                    },
                    '$$invoices_ids',
                  ],
                },
              },
            },
            {
              $sort: { createdAt: 1 },
            },
          ],
        },
      },
      {
        $addFields: {
          lastInvoice: {
            $last: '$invoices',
          },
        },
      },
      {
        $addFields: {
          notNullAndEmptyInvoicesIdCount: {
            $size: {
              $filter: {
                input: '$invoices_ids',
                as: 'invoice_id',
                cond: {
                  $and: [
                    { $ne: ['$invoice_id', null] },
                    { $ne: ['$invoice_id', ''] },
                  ],
                },
              },
            },
          },
        },
      },
      {
        $addFields: {
          credit_application_type: {
            $cond: {
              if: {
                $and: [
                  { $ne: ['$lastInvoice.company_id', ''] },
                  { $ne: ['$lastInvoice.company_id', null] },
                  { $ifNull: ['$lastInvoice.company_id', false] },
                ],
              },
              then: 'loan',
              else: {
                $cond: {
                  if: {
                    $and: [
                      { $gte: ['$notNullAndEmptyInvoicesIdCount', 1] },
                      {
                        $or: [
                          {
                            $eq: [
                              { $ifNull: ['$lastInvoice.company_id', ''] },
                              '',
                            ],
                          },
                        ],
                      },
                    ],
                  },
                  then: 'vc',
                  else: {
                    $cond: {
                      if: {
                        $eq: ['$notNullAndEmptyInvoicesIdCount', 0],
                      },
                      then: 'prequal',
                      else: '',
                    },
                  },
                },
              },
            },
          },
        },
      },
      { $sort: { createdAt: 1 } },
    ],
  },
} as PipelineStage

export const projectStats = {
  company: {
    first_credit_request: {
      $cond: [
        {
          $gte: [{ $size: '$loanApplication' }, 1],
        },
        'yes',
        'no',
      ],
    },
    number_of_credit_requests_submitted: {
      $size: '$loanApplication',
    },
    last_credit_application_status: {
      $last: '$loanApplication.status',
    },
    last_credit_application_type: {
      $last: '$loanApplication.credit_application_type',
    },
    date_of_last_credit_request: {
      $last: '$loanApplication.createdAt',
    },
    number_of_preapproval_request: {
      $sum: {
        $cond: [
          {
            $and: [
              {
                $ne: [{ $size: '$loanApplication' }, 0],
              },
              {
                $ifNull: ['$loanApplication.invoiceDetails', false],
              },
              {
                $ifNull: ['$loanApplication.invoiceDetails.invoiceId', true],
              },
            ],
          },
          1,
          0,
        ],
      },
    },
    number_of_virtual_cards_issued: {
      $sum: {
        $cond: [
          {
            $ne: [{ $size: '$loanApplication.virtualCard' }, 0],
          },
          1,
          0,
        ],
      },
    },
    number_of_virtual_cards_used: {
      $sum: {
        $cond: [
          {
            $and: [
              {
                $ne: [{ $size: '$loanApplication.virtualCard' }, 0],
              },
              {
                $ifNull: ['$loanApplication.virtualCard.usedAmount', false],
              },
            ],
          },
          1,
          0,
        ],
      },
    },
    total_value_of_virtual_cards_transaction: {
      $sum: {
        $cond: [
          {
            $and: [
              {
                $ne: [{ $size: '$loanApplication.virtualCard' }, 0],
              },
              {
                $ifNull: ['$loanApplication.virtualCard.usedAmount', false],
              },
            ],
          },
          '$loanApplication.virtualCard.usedAmount',
          0,
        ],
      },
    },
    first_virtual_card_request: {
      $cond: [
        {
          $and: [
            {
              $ne: [{ $size: '$loanApplication' }, 0],
            },
            {
              $ne: [{ $size: '$loanApplication.virtualCard' }, 0],
            },
            {
              $ifNull: ['$loanApplication.virtualCard', false],
            },
          ],
        },
        true,
        false,
      ],
    },
    latest_loan_amount: {
      $last: '$loanApplication.approvedAmount',
    },
  },
}

export const updatedAtLookup: PipelineStage = {
  $lookup: {
    from: 'loanapplications',
    as: 'loanApplication',
    let: {
      companyId: '$companyId',
    },
    pipeline: [
      {
        $match: {
          $expr: {
            $eq: ['$company_id', '$$companyId'],
          },
        },
      },
      {
        $project: {
          updatedAt: 1,
        },
      },
    ],
  },
}

export const lastUpdatedTimestamps = ['loanApplication.updatedAt']
