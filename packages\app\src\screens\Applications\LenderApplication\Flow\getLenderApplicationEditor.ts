import React, { FC } from 'react'
import { Steps } from '@linqpal/models/src/applications/lender/LenderApplicationSteps'
import { LoanOfficerStep } from './Sponsor/LoanOfficerStep'
import { SponsorPersonalInfoStep } from './Sponsor/SponsorPersonalInfoStep'
import { SponsorHomeAddressStep } from './Sponsor/SponsorHomeAddressStep'
import { SponsorMaritalStatusStep } from './Sponsor/SponsorMaritalStatusStep'
import { SponsorCitizenshipStep } from './Sponsor/SponsorCitizenshipStep'
import { SponsorStatementStep } from './Sponsor/SponsorStatementStep'
import { ReviewStep } from './Review/ReviewStep'
import { BusinessEntityInformationStep } from './BusinessEntity/BusinessEntityInformationStep'

export interface ILenderApplicationStepOptions {
  title?: string
  description?: string
  canGoBack?: boolean
  showNavigationButtons?: boolean
  canSkip?: boolean
}

export interface ILenderApplicationEditor {
  options?: ILenderApplicationStepOptions
  component: FC<any> | ((props: any) => JSX.Element)
}

const PlaceholderComponent: FC = () => React.createElement(React.Fragment)

export function getLenderApplicationEditor(
  path: string,
): ILenderApplicationEditor {
  switch (path) {
    // Sponsor
    case Steps.sponsor.loanOfficer:
      return LoanOfficerStep
    case Steps.sponsor.personalInformation:
      return SponsorPersonalInfoStep
    case Steps.sponsor.homeAddress:
      return SponsorHomeAddressStep
    case Steps.sponsor.maritalStatus:
      return SponsorMaritalStatusStep
    case Steps.sponsor.statements:
      return SponsorStatementStep
    case Steps.sponsor.citizenship:
      return SponsorCitizenshipStep

    // Business Entity
    case Steps.businessEntity.entityInformation:
      return BusinessEntityInformationStep
    case Steps.businessEntity.address:
      return { component: PlaceholderComponent }
    case Steps.businessEntity.isCreatedForProject:
      return { component: PlaceholderComponent }
    case Steps.businessEntity.dateIncorporated:
      return { component: PlaceholderComponent }
    case Steps.businessEntity.representative:
      return { component: PlaceholderComponent }
    case Steps.businessEntity.generalContractorName:
      return { component: PlaceholderComponent }
    case Steps.businessEntity.bankAccountId:
      return { component: PlaceholderComponent }

    // Current Project
    case Steps.currentProject.hasMultipleProducts:
      return { component: PlaceholderComponent }
    case Steps.currentProject.loanType:
      return { component: PlaceholderComponent }
    case Steps.currentProject.mainAddress:
      return { component: PlaceholderComponent }
    case Steps.currentProject.products:
      return { component: PlaceholderComponent }
    case Steps.currentProject.loanPurpose:
      return { component: PlaceholderComponent }
    case Steps.currentProject.loanTerm:
      return { component: PlaceholderComponent }
    case Steps.currentProject.willCompletePermits:
      return { component: PlaceholderComponent }
    case Steps.currentProject.originalPurchasePrice:
      return { component: PlaceholderComponent }
    case Steps.currentProject.originalPurchaseDate:
      return { component: PlaceholderComponent }
    case Steps.currentProject.payOffAmount:
      return { component: PlaceholderComponent }
    case Steps.currentProject.subordinateDebtType:
      return { component: PlaceholderComponent }
    case Steps.currentProject.subordinateDebtBalance:
      return { component: PlaceholderComponent }
    case Steps.currentProject.financialDetails:
      return { component: PlaceholderComponent }

    // Previous Projects
    case Steps.previousProjects.projects:
      return { component: PlaceholderComponent }

    // Review
    case Steps.review.review:
      return ReviewStep

    default:
      throw new Error(`no editor for path: ${path}`)
  }
}
