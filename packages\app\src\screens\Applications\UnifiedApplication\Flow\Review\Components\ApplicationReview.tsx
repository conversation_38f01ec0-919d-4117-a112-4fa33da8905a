import React from 'react'
import { StyleSheet, View } from 'react-native'
import { BtLink } from '@linqpal/components/src/ui'
import { useTranslation } from 'react-i18next'
import { useResponsive } from '@linqpal/components/src/hooks'
import { useUnifiedApplication } from '../../../UnifiedApplicationContext'
import { FilledGroupLine } from './Review/FilledGroupLine'
import { IncompleteGroupLine } from './Review/IncompleteGroupLine'
import { commonStyles } from './Review/commonStyles'
import { PersonalInformationRequiredPrompt } from './Review/PersonalInformationRequiredPrompt'
import { CoOwnerAcceptanceWarning } from './Review/CoOwnerAcceptanceWarning'
import { GoToAgreementButton } from './Review/GoToAgreementButton'
import { ConsentStatement } from './Review/ConsentStatement'
import { UnifiedApplicationReviewStep } from '../../../Store/UnifiedApplicationReviewStore'
import { SubmitApplicationButton } from './Review/SubmitApplicationButton'
import ApplicationStore from '../../../../../GeneralApplication/Application/ApplicationStore'
import { Spacer } from '../../../../../../ui/atoms'
import {
  InvitationSent,
  LineOfCreditAgreement,
  VerifyEmailAlert,
} from '../../../../../GeneralApplication/Application/components'
import RootStore from '../../../../../../store/RootStore'

export const ApplicationReview = (props) => {
  const { t } = useTranslation('application')
  const { doc } = props
  const { sm } = useResponsive()
  const { showVerifyEmail, setShowVerifyEmail } = ApplicationStore

  const store = useUnifiedApplication()

  const hasCoOwners = !!store.draft.data?.coOwners?.length

  const flowGroups = store.getFlowGroups()

  const groupLines = flowGroups.map((group) => {
    const steps = store.getGroupSteps(group)
    const isGroupFilled = steps.every((step) => store.validateStep(step))

    return isGroupFilled ? (
      <FilledGroupLine group={group} key={group} />
    ) : (
      <IncompleteGroupLine group={group} key={group} />
    )
  })

  return (
    <>
      <Spacer height={22} />
      <PersonalInformationRequiredPrompt />

      {groupLines}
      <Spacer height={12} />

      {!store.hasSubmissionRights && store.draft.invitedOwner && (
        <>
          <Spacer height={17} />
          <InvitationSent document={doc} />
        </>
      )}

      {store.isCreditApp && hasCoOwners ? (
        <CoOwnerAcceptanceWarning style={{ marginTop: 20 }} />
      ) : null}

      {sm && (
        <View style={{ alignItems: 'center' }}>
          {store.hasSubmissionRights && (
            <>
              <ConsentStatement />

              {store.isSubmitting || store.isCreditApp ? (
                <GoToAgreementButton />
              ) : (
                <SubmitApplicationButton />
              )}
            </>
          )}

          <BtLink
            title={t('Review.ReviewButton')}
            onPress={() =>
              (store.reviewStore.currentStep =
                UnifiedApplicationReviewStep.PREVIEW)
            }
            disabled={RootStore.isBusy || store.isSubmitting}
            testID="UnifiedApplication.Review.PreviewLink"
            textStyle={
              RootStore.isBusy
                ? styles.disabledLinkText
                : commonStyles.editGroupLink
            }
          />
        </View>
      )}

      <VerifyEmailAlert close={showVerifyEmail} setClose={setShowVerifyEmail} />
      {store.isCreditApp && <LineOfCreditAgreement />}
    </>
  )
}

const styles = StyleSheet.create({
  disabledLinkText: {
    color: '#99ADBA',
    fontWeight: '700',
    fontSize: 16,
    lineHeight: 24,
  },
})
