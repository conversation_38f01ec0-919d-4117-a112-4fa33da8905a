import React, { FC } from 'react'
import { observer } from 'mobx-react-lite'
import { useTranslation } from 'react-i18next'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { runInAction } from 'mobx'
import { BtInput } from '@linqpal/components/src/ui'
import { BtPhoneInput } from '@linqpal/components/src/ui/BtPhoneInput'
import {
  validateEmail,
  validateUSPhoneNumber,
} from '@linqpal/models/src/helpers/validations'
import { AutoCapitalize, KeyboardType } from '@linqpal/models/src/dictionaries'
import { ApplicationUserRole } from '../../../../../../../models/src/applications/unified/IUnifiedApplicationDraft'
import { useResponsive } from '@linqpal/components/src/hooks'
import {
  InviteFormInfo,
  QuestionLabel,
} from '../../../../GeneralApplication/Application/components'
import { Col, Row } from '../../../../../ui/atoms/Grid'
import { Spacer } from '../../../../../ui/atoms'
import { IUnifiedApplicationEditor } from '../getUnifiedApplicationEditor'

const OwnerInvitationEditor: FC = observer(() => {
  const { t } = useTranslation('application')
  const { sm } = useResponsive()
  const store = useUnifiedApplication()

  const authorizedUser = store.draft.invitedOwner || {}

  const handleFieldChange = (field: string, value: string) => {
    runInAction(() => {
      let user = store.draft.invitedOwner

      if (!user) {
        user = {
          role: ApplicationUserRole.Owner,
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
        }

        store.draft.users.push(user)
      }

      user[field] = value
    })
  }

  const validateFirstName = (value: string) =>
    value ? '' : (t('Finance.FirstNameError') as string)

  const validateLastName = (value: string) =>
    value ? '' : (t('Finance.LastNameError') as string)

  const validatePhone = (value: string) =>
    validateUSPhoneNumber(value)
      ? ''
      : (t('ValidationErrors.InvalidPhone') as string)

  const validateEmailField = (value: string) =>
    validateEmail(value) ? '' : (t('ValidationErrors.InvalidEmail') as string)

  return (
    <>
      {store.isInReview ? (
        <QuestionLabel>{t('Finance.AuthDetailsReviewTitle')}</QuestionLabel>
      ) : sm ? (
        <InviteFormInfo />
      ) : null}

      <Row>
        <Col xs={12} sm={12} md={6} xl={6}>
          <BtInput
            value={authorizedUser.firstName || ''}
            onChangeText={(value) => handleFieldChange('firstName', value)}
            label={t('Finance.FirstnameLabel') as string}
            validateInput={validateFirstName}
            style={{ marginTop: store.isInReview ? 24 : 30 }}
            maxLength={70}
            autoCapitalize={AutoCapitalize.Words}
            testID="UnifiedApplication.BusinessOwner.AuthorizedDetails.FirstName"
          />
        </Col>
        <Col xs={12} sm={12} md={6} xl={6}>
          <BtInput
            value={authorizedUser.lastName || ''}
            onChangeText={(value) => handleFieldChange('lastName', value)}
            label={t('Finance.LastnameLabel') as string}
            validateInput={validateLastName}
            style={{ marginTop: store.isInReview ? 24 : 30 }}
            maxLength={70}
            autoCapitalize={AutoCapitalize.Words}
            testID="UnifiedApplication.BusinessOwner.AuthorizedDetails.LastName"
          />
        </Col>
      </Row>
      <Spacer height={store.isInReview ? 24 : 30} />
      <BtPhoneInput
        value={authorizedUser.phone || ''}
        label={t('Finance.PhoneLabel') as string}
        validate={validatePhone}
        onChangeText={(value) => handleFieldChange('phone', value.parsed)}
        testID="UnifiedApplication.BusinessOwner.AuthorizedDetails.Phone"
      />
      <Spacer height={store.isInReview ? 24 : 30} />
      <BtInput
        value={authorizedUser.email || ''}
        onChangeText={(value) => handleFieldChange('email', value)}
        label={t('Finance.EmailLabel') as string}
        validateInput={validateEmailField}
        maxLength={255}
        keyboardType={KeyboardType.EmailAddress}
        testID="UnifiedApplication.BusinessOwner.AuthorizedDetails.Email"
      />

      {!sm && !store.isInReview && (
        <>
          <Spacer height={30} />
          <InviteFormInfo />
        </>
      )}
    </>
  )
})

export const OwnerInvitationStep: IUnifiedApplicationEditor = {
  options: {
    title: 'Finance.AuthorisedDetails',
  },
  component: OwnerInvitationEditor,
}
