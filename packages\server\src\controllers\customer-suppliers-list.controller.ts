import { Company, Invoice, User, UserRole } from '@linqpal/common-backend'
import { CompanyStatus } from '@linqpal/models'
import {
  ICustomerSuppliersList,
  invoiceStatus,
  PayablesVendorsSort,
  PayablesVendorsSortColumn,
  PayablesVendorsSortColumnType,
} from '@linqpal/models/src/dictionaries'
import {
  DBSortingOrder,
  DBSortingOrderType,
  SortingOrder,
} from '@linqpal/models/src/dictionaries/global'
import { Request } from 'express'
import { PipelineStage } from 'mongoose'

export async function getCustomerSuppliersList(
  req: Request,
): Promise<Array<ICustomerSuppliersList>> {
  const builderId = req.company!._id.toString()

  const search = req.query?.search?.toString() ?? ''
  const limit = req.query?.limit ? parseInt(req.query.limit.toString(), 10) : 10

  const sortColumn = req.query?.sortColumn
    ? req.query.sortColumn.toString()
    : PayablesVendorsSort.CREATED_AT

  const sortDirection =
    req.query.sortDirection ||
    (sortColumn === PayablesVendorsSort.CREATED_AT
      ? SortingOrder.DESC
      : SortingOrder.ASC)

  const pipeline: PipelineStage[] = [
    {
      $match: {
        $and: [
          { payer_id: builderId },
          {
            isDeleted: {
              $ne: true,
            },
          },
          {
            $or: [
              { status: { $ne: invoiceStatus.draft } },
              {
                $and: [
                  { status: { $eq: invoiceStatus.draft } },
                  {
                    $or: [
                      { company_id: null },
                      { company_id: { $exists: false } },
                      { company_id: '' },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    },
    {
      $lookup: {
        from: User.collection.name,
        let: {
          email: '$supplierInvitationDetails.email',
          company_id: '$company_id',
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $ne: ['$$email', ''] },
                  { $ne: ['$$email', null] },
                  { $eq: ['$email', '$$email'] },
                  { $eq: [{ $ifNull: ['$$company_id', ''] }, ''] }, // Only perform lookup if company_id is empty or null or missing
                ],
              },
            },
          },
          {
            $lookup: {
              from: UserRole.collection.name,
              localField: 'sub',
              foreignField: 'sub',
              as: 'userRoleDetails',
            },
          },
          {
            $unwind: {
              path: '$userRoleDetails',
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $lookup: {
              from: Company.collection.name,
              let: {
                company_id: {
                  $convert: {
                    input: '$userRoleDetails.company_id',
                    to: 'objectId',
                    onError: null,
                    onNull: null,
                  },
                },
              },
              pipeline: [
                { $match: { $expr: { $eq: ['$_id', '$$company_id'] } } },
                { $match: { status: CompanyStatus.Approved } },
              ],
              as: 'companyDetails',
            },
          },
          {
            $project: {
              company_id: {
                $cond: {
                  if: { $gt: [{ $size: '$companyDetails' }, 0] },
                  then: '$userRoleDetails.company_id',
                  else: null,
                },
              },
            },
          },
        ],
        as: 'userDetails',
      },
    },
    {
      $unwind: {
        path: '$userDetails',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $match: {
        $or: [
          { 'userDetails.company_id': null },
          { 'userDetails.company_id': { $exists: false } },
          { 'userDetails.company_id': '' },
        ],
      },
    },
    {
      $addFields: {
        supplier_id: {
          $cond: {
            if: { $eq: [{ $ifNull: ['$company_id', ''] }, ''] },
            then: {
              $concat: [
                {
                  $ifNull: [
                    { $trim: { input: '$supplierInvitationDetails.name' } },
                    '',
                  ],
                },
                '_',
                {
                  $ifNull: [
                    { $trim: { input: '$supplierInvitationDetails.email' } },
                    '',
                  ],
                },
              ],
            },
            else: null,
          },
        },
      },
    },
    {
      $addFields: {
        company: {
          name: '$supplierInvitationDetails.name',
          phone: '$supplierInvitationDetails.phone',
          email: '$supplierInvitationDetails.email',
          contactName: {
            $concat: [
              {
                $ifNull: [
                  {
                    $trim: {
                      input: '$supplierInvitationDetails.firstName',
                    },
                  },
                  '',
                ],
              },
              ' ',
              {
                $ifNull: [
                  {
                    $trim: { input: '$supplierInvitationDetails.lastName' },
                  },
                  '',
                ],
              },
            ],
          },
          isInvited: true,
        },
      },
    },
  ]

  if (search) {
    pipeline.push({
      $match: {
        'company.name': { $regex: search, $options: 'i' },
      },
    })
  }

  pipeline.push({
    $group: {
      _id: '$supplier_id',
      name: { $first: '$company.name' },
      phone: { $first: '$company.phone' },
      email: { $first: '$company.email' },
      contactName: {
        $first: {
          $trim: { input: '$company.contactName' },
        },
      },
      isInvited: {
        $first: {
          $ifNull: ['$company.isInvited', false],
        },
      },
    },
  })

  pipeline.push({
    $project: {
      _id: 0,
      id: '$_id',
      name: 1,
      phone: 1,
      email: 1,
      contactName: 1,
      isInvited: 1,
    },
  })

  pipeline.push({
    $sort: {
      [PayablesVendorsSortColumn[sortColumn as PayablesVendorsSortColumnType]]:
        DBSortingOrder[sortDirection as DBSortingOrderType],
    },
  })

  if (limit) {
    pipeline.push({ $limit: limit })
  }

  const results = await Invoice.aggregate(pipeline)

  return results
}
