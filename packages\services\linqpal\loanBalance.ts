import {
  Company,
  connectToDatabase,
  getEnvironmentVariables,
} from '@linqpal/common-backend'
import _ from 'lodash'
import 'moment-business-days'
import {
  invoiceSchemaStatus,
  LOAN_APPLICATION_STATUS,
} from '@linqpal/models/src/dictionaries'
import { EInvoiceType } from '@linqpal/models'

async function getPipeline() {
  return Company.aggregate([
    { $addFields: { id: { $toString: '$_id' } } },
    {
      $lookup: {
        from: 'loanapplications',
        as: 'prequal',
        localField: 'id',
        foreignField: 'company_id',
        pipeline: [
          {
            $match: {
              status: 'approved',
              $or: [{ lms_id: null }, { lms_id: { $exists: false } }],
            },
          },
          { $sort: { issueDate: -1 } },
          { $limit: 1 },
          { $project: { approvedAmount: 1 } },
        ],
      },
    },
    { $unwind: { path: '$prequal', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'loanapplications',
        as: 'loan',
        localField: 'id',
        foreignField: 'company_id',
        pipeline: [
          {
            $match: {
              lms_id: { $ne: null },
              $expr: {
                $or: [
                  { $eq: ['$status', LOAN_APPLICATION_STATUS.APPROVED] },
                  // TODO: VK: Introduce new loan app's PENDING_DISBURSEMENT status for ATC version #2
                  {
                    $and: [
                      {
                        $eq: ['$status', LOAN_APPLICATION_STATUS.PROCESSING],
                      },
                      {
                        $eq: [
                          '$metadata.repayment.autoTradeCreditEnabled',
                          true,
                        ],
                      },
                    ],
                  },
                ],
              },
            },
          },
          {
            $addFields: {
              invoiceId: {
                $convert: {
                  input: '$invoiceDetails.invoiceId',
                  to: 'objectId',
                  onError: null,
                },
              },
            },
          },
          {
            $lookup: {
              from: 'invoices',
              as: 'invoice',
              localField: 'invoiceId',
              foreignField: '_id',
            },
          },
          { $unwind: { path: '$invoice', preserveNullAndEmptyArrays: true } },
          {
            $match: {
              $expr: {
                $or: [
                  { $ne: ['invoice.type', EInvoiceType.QUOTE] },
                  { $ne: ['invoice.status', invoiceSchemaStatus.paid] },
                ],
              },
            },
          },
          { $sort: { issueDate: -1 } },
          {
            $group: {
              _id: null,
              total_issued: { $sum: '$approvedAmount' },
              total_remaining: {
                $sum: { $ifNull: ['$remainingAmount', '$approvedAmount'] },
              },
            },
          },
        ],
      },
    },
    { $unwind: { path: '$loan', preserveNullAndEmptyArrays: true } },
    {
      $addFields: {
        approved: { $ifNull: ['$prequal.approvedAmount', 0] },
        balance: { $ifNull: ['$loan.total_remaining', 0] },
      },
    },
    {
      $match: {
        $expr: {
          $or: [
            { $ne: ['$balance', '$credit.balance'] },
            {
              $and: [
                { $ne: ['$approved', '$credit.limit'] },
                { $gt: ['$approved', 0] },
              ],
            },
          ],
        },
      },
    },
    {
      $project: {
        approved: 1,
        balance: 1,
      },
    },
  ])
}

export async function loanBalance() {
  await getEnvironmentVariables()
  await connectToDatabase()

  const data = await getPipeline()
  for (const d of data) {
    const company = await Company.findById(d._id)
    if (company) {
      if (company.credit.balance !== d.balance) {
        company.credit.balance = d.balance
      }
      if (_.isNil(company.credit.limit) && d.approved > 0) {
        company.credit.limit = d.approved
      }
      if (company.isModified()) {
        console.log(company.name, company.modifiedPaths())
        await company.save()
      }
    }
  }
}
