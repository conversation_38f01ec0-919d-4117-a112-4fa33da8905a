import React, { FC } from 'react'
import { observer } from 'mobx-react-lite'
import { useTranslation } from 'react-i18next'
import { BtTaxIdInput } from '@linqpal/components/src/ui'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { runInAction } from 'mobx'
import { UnifiedApplicationValidator } from '@linqpal/models'
import { IUnifiedApplicationEditor } from '../getUnifiedApplicationEditor'

const BusinessEinEditor: FC = () => {
  const { t } = useTranslation('application')
  const store = useUnifiedApplication()

  const handleValidation = (value: string) => {
    const unformattedValue = value.replace(/\D/g, '')

    // hide validation message when 10th char is pressed
    if (unformattedValue.length > 9) return ''

    return UnifiedApplicationValidator.validateEin(unformattedValue)
      ? ''
      : (t('ValidationErrors.InvalidTaxID') as string)
  }

  const handleChange = (value: string) => {
    const unformattedValue = value.replace(/\D/g, '')

    if (unformattedValue.length <= 9) {
      runInAction(() => {
        store.draft.data.businessInfo.ein = unformattedValue
      })
    }
  }

  return (
    <BtTaxIdInput
      size="large"
      label={t('Business.TaxIdLabel') as string}
      value={store.draft.data.businessInfo.ein || ''}
      validate={handleValidation}
      onChangeText={handleChange}
      testID="UnifiedApplication.BusinessInfo.Ein"
    />
  )
}

export const BusinessEinStep: IUnifiedApplicationEditor = {
  options: {
    title: 'Business.TaxId',
  },
  component: observer(BusinessEinEditor),
}
