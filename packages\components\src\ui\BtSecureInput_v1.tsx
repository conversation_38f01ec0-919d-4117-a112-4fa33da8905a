import React, { FC, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { NativeSyntheticEvent, TextInputFocusEventData } from 'react-native'
import { BtInputBaseProps } from './BtInputBase'
import { BtText } from './BtText'
import { BtInput_v1 } from './BtInput_v1'

interface IProps extends BtInputBaseProps {}

export const BtSecureInput_v1: FC<IProps> = ({ ...btInputBaseProps }) => {
  const { t } = useTranslation('global')
  const [isFocused, setIsFocused] = useState(false)
  const [isValueDisplayed, setIsValueDisplayed] = useState(false)

  const handleBlur = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
    setIsFocused(false)
    setIsValueDisplayed(false)

    if (btInputBaseProps.onBlur) {
      btInputBaseProps.onBlur(e)
    }
  }

  const handleFocus = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
    setIsFocused(true)
    setIsValueDisplayed(true)

    if (btInputBaseProps.onFocus) {
      btInputBaseProps.onFocus(e)
    }
  }

  const handleToggleDisplay = () => {
    setIsValueDisplayed((prev) => !prev)
  }

  return (
    <BtInput_v1
      {...btInputBaseProps}
      secureTextEntry={!isValueDisplayed}
      accessoryRight={
        <>
          {!btInputBaseProps.disabled && btInputBaseProps.value && !isFocused && (
            <BtText
              style={{
                marginRight: 8,
                color: '#00A0F3',
              }}
              onPress={handleToggleDisplay}
            >
              {isValueDisplayed
                ? t('BtSecureInput.Hide')
                : t('BtSecureInput.Show')}
            </BtText>
          )}
          {btInputBaseProps.accessoryRight}
        </>
      }
      onBlur={handleBlur}
      onFocus={handleFocus}
    />
  )
}
