import React from 'react'
import { useTranslation } from 'react-i18next'
import { StyleSheet, Text, View } from 'react-native'
import { composeStyle } from '@linqpal/common-frontend/src/helpers'
import { EInvoiceType } from '@linqpal/models'
import capitalize from 'lodash/capitalize'
import moment from 'moment'
import { Divider } from 'react-native-paper'
import { currencyMask } from '../../../../../utils/helpers/masking'

export const OtherInvoiceInfo: React.FC<{
  invoice: any
  isMobile: boolean
}> = ({ invoice, isMobile }) => {
  const { t } = useTranslation('global')

  const width = isMobile ? '100%' : 360

  const dueDate = moment(invoice.invoice_due_date).format('MM/DD/YYYY')
  const dueLabel =
    invoice.type === EInvoiceType.INVOICE
      ? t('Receivables.due-on', { date: dueDate })
      : ''

  return (
    <View style={{ width, paddingHorizontal: 5 }}>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Text style={composeStyle(styles.normalText, { maxWidth: '50%' })}>
          {`${capitalize(invoice?.type).replace('_', ' ')} #${
            invoice.invoice_number
          }`}
        </Text>
        <Text style={styles.valueText}>{dueLabel}</Text>
      </View>
      <Divider style={styles.divider} />
      <AmountDisplay
        amount={invoice.total_amount}
        label={t('TabInvoice.InvoiceAmount')}
      />
      <Divider style={styles.divider} />
    </View>
  )
}

const AmountDisplay = ({ amount, label, valueStyle = {} }) => (
  <View style={styles.amountTypeContainer}>
    <Text style={composeStyle(styles.normalText, { maxWidth: '50%' })}>
      {label}
    </Text>
    <Text style={[styles.valueText, valueStyle]}>{currencyMask(amount)}</Text>
  </View>
)

const styles = StyleSheet.create({
  normalText: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 21,
    color: '#758590',
    fontFamily: 'Inter',
  },
  valueText: {
    fontSize: 14,
    lineHeight: 21,
    fontFamily: 'Inter',
    fontWeight: '500',
    maxWidth: '50%',
    textAlign: 'right',
    color: '#001929',
  },
  statusValue: {
    maxWidth: '50%',
  },
  amountTypeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 26,
  },
  divider: {
    marginTop: 14,
    marginBottom: 14,
  },
  labelStyle: {
    color: '#99ADBA',
    marginTop: 7,
  },
})
