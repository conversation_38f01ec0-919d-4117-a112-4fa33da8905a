import { IGetLenderApplicationResponse } from '@linqpal/models/src/routes2/application/types'
import { TCompoundRoute } from '@linqpal/models/src/routes2/types'
import { authMiddleware } from '../middlewares'
import { Draft } from '@linqpal/common-backend'
import { ApplicationDraftType } from '@linqpal/models/src/helpers/draft/models/ApplicationDraftType'
import { ILenderApplicationDraft } from '@linqpal/models/src/applications/lender/ILenderApplicationDraft'
import { ICompany } from '@linqpal/common-backend/src/models/types'

export const getLenderApplicationDraft: TCompoundRoute<
  any,
  IGetLenderApplicationResponse
> = {
  middlewares: {
    pre: [...authMiddleware.pre],
    post: [],
  },
  get: async (
    _: any,
    req: { company: ICompany },
  ): Promise<IGetLenderApplicationResponse> => {
    const companyId = req.company?.id
    if (!companyId) throw new Error('companyId is required')

    const draft = await Draft.findOne({
      company_id: companyId,
      type: ApplicationDraftType.LenderApplication,
    })

    if (!draft) return { draft: null }

    const response: ILenderApplicationDraft = {
      id: draft.id,
      initialStep: draft.firstQuestion,
      currentStep: draft.current,
      data: draft.lenderApplication ?? {
        sponsor: {},
        businessEntity: {},
        currentProject: {},
        previousProjects: [],
      },
      visitedSteps: draft.visitedSteps,
    }

    return { draft: response }
  },
}
