import React from 'react'
import { BtRadioGroup } from '@linqpal/components/src/ui'
import { observer } from 'mobx-react'
import { useTranslation } from 'react-i18next'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { ApplicationUserRole } from '../../../../../../../models/src/applications/unified/IUnifiedApplicationDraft'
import { runInAction } from 'mobx'
import { IUnifiedApplicationEditor } from '../getUnifiedApplicationEditor'

const YES = 'Yes'
const NO = 'No'

const IsAuthorizedEditor = () => {
  const { t } = useTranslation('application')
  const store = useUnifiedApplication()

  const userRole = store.draft.users.find(
    (user) => user.id === store.currentUserId,
  )?.role

  const value = !userRole
    ? undefined
    : userRole === ApplicationUserRole.Authorized
    ? YES
    : NO

  const handleChange = (option: string) => {
    const role =
      option === YES ? ApplicationUserRole.Authorized : ApplicationUserRole.None

    const userInfo = store.draft.users.find(
      (user) => user.id === store.currentUserId,
    )

    runInAction(() => {
      if (!userInfo) {
        store.draft.users.push({
          id: store.currentUserId,
          role,
        })
      } else {
        userInfo.role = role
      }
    })
  }

  return (
    <BtRadioGroup
      disabled={store.isInReview}
      value={value}
      options={[
        { label: t('Owner.AuthorizedLabelYes'), value: YES },
        { label: t('Owner.AuthorizedLabelNo'), value: NO },
      ]}
      onChange={handleChange}
      radioStyle={{ marginRight: 32 }}
      groupStyle={{ flexDirection: 'row' }}
      testID="UnifiedApplication.BusinessOwner.IsAuthorized"
    />
  )
}

export const IsAuthorizedStep: IUnifiedApplicationEditor = {
  options: {
    title: 'Finance.Authorized',
    canSkip: false,
  },
  component: observer(IsAuthorizedEditor),
}
