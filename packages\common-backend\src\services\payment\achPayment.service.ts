import moment from 'moment'
import {
  AwsService,
  AzureService,
  Company,
  CustomerAccount,
  getEnvironmentVariables,
  Invoice,
  Operation,
  Slack,
} from '../../..'
import {
  CREATE_DRAW_DISBURSEMENT_TEMPLATE_CODE,
  CREATE_DRAW_FINALPAYMENT_TEMPLATE_CODE,
  CREATE_DRAW_FINALPAYMENT_V2_TEMPLATE_CODE,
  CREATE_DRAW_REPAYMENT_TEMPLATE_CODE,
  CREATE_PAY_NOW_FACTORING_INVOICE_TEMPLATE_CODE,
  CREATE_PAY_NOW_INVOICE_TEMPLATE_CODE,
  DiscountDetail,
  EDiscountType,
  EFeeType,
  EPaymentMethod,
  EPaymentType,
  FeeDetail,
  FLOW_TEMPLATE_CODE,
  IPayablesDetails,
  IPayload,
  IPaymentInfo,
  PayablesDetail,
} from './types'
import { dictionaries, exceptions, InvoicePaymentType } from '@linqpal/models'
import { round } from 'lodash'
import {
  IBankAccount,
  ICompany,
  ICustomerAccount,
  IInvoice,
  ILoanApplication,
  IOperation,
} from '../../models/types'
import mongoose, { ClientSession } from 'mongoose'
import * as math from 'mathjs'
import { Logger } from '../logger/logger.service'
import { OutInvoiceData } from '../cbw/ach.out.controller'
import { customErrorToString } from '../../helpers/SnsEventBuilder'
import { PayNowService } from './paynow.service'
import { ArAdvanceStatus } from '@linqpal/models/src/dictionaries/factoring'
import { FactoringService } from '../factoring'
import { CriticalError } from '@linqpal/models/src/types/exceptions'
import { OperationTypes } from '@linqpal/models/src/dictionaries'
import { PaymentNotifications } from './paymentNotifications.service'
import { LoanApplicationService } from '../loanApplication.service'

const logger = new Logger({
  module: 'achPayment.service',
})

export const TEST_ID = 'd40b6919-9c26-4988-8e0c-38ab9924f156' // Test ID suggested by backend team

export const drawFinalPayment = async (
  loanApplication: ILoanApplication,
  operations: IOperation[],
  invoices: IInvoice[],
  invoiceData: {
    [key: string]: OutInvoiceData
  },
  type: OperationTypes,
  session: ClientSession | null,
  reason: string,
  createdBy?: string,
) => {
  if (invoices.length === 0) {
    logger.warn(
      'No invoices provided to perform draw final payment. Abort process',
    )
    return
  }
  const invoice = invoices[0]

  const supplier = await Company.findById(invoice.company_id)
  if (!supplier) throw new exceptions.LogicalError('invoice/supplier-not-found')

  let customerAccount: ICustomerAccount | null = null
  if (invoice.customer_account_id?.trim()) {
    customerAccount = await CustomerAccount.findById(
      invoice.customer_account_id,
    )
  }

  if (!customerAccount)
    logger.info({ invoiceId: invoice.id }, 'No customer account ID provided')

  let paymentRequestAmount = 0
  const payablesDetails: IPayablesDetails[] = invoices
    .filter((payable) => invoiceData[payable.id])
    .map((payable) => {
      const finalPaymentData = invoiceData[payable.id]
      const finalPaymentAmount = round(finalPaymentData.amount, 2)

      paymentRequestAmount += finalPaymentAmount

      return {
        id: payable.id,
        payableType: EPaymentType.invoice,
        payableAmount: payable.total_amount,
        requestedAmount: finalPaymentAmount,
        discountAmount: round(finalPaymentData.discount, 2),
      }
    })

  let flowTemplateCode: FLOW_TEMPLATE_CODE

  logger.info({ payablesDetails }, `Aion payment flow started`)

  switch (type) {
    case dictionaries.OPERATION_TYPES.INVOICE.FINAL_PAYMENT:
      if (
        supplier.settings?.sendFinalPaymentWhenLoanIsPaid &&
        (customerAccount?.settings?.sendFinalPaymentWhenLoanIsPaid ?? false)
      ) {
        flowTemplateCode = CREATE_DRAW_FINALPAYMENT_V2_TEMPLATE_CODE
      } else {
        flowTemplateCode = CREATE_DRAW_FINALPAYMENT_TEMPLATE_CODE
      }
      break
    case dictionaries.OPERATION_TYPES.INVOICE.PAYMENT:
      flowTemplateCode = CREATE_DRAW_DISBURSEMENT_TEMPLATE_CODE
      break
    default:
      throw new Error(`Invalid flow template condition: ${type}`)
  }

  logger.info(`Preparing payment request`)
  const payloadDetails: IPayload = {
    flowTemplateCode: flowTemplateCode,
    blueTapeCorrelationId: supplier.id,
    createdBy: createdBy ?? 'BlueTape',
    details: {
      date: moment().format(),
      currency: 'USD',
      requestedAmount: paymentRequestAmount,
      paymentMethod: EPaymentMethod.ach,
      drawDetails: {
        id: loanApplication.lms_id,
        amount: loanApplication.approvedAmount,
      },
      customerDetails: {
        id: loanApplication.company_id,
        name: '',
        accountId: '',
      },
      sellerDetails: {
        companyId: invoice.company_id,
        name: supplier?.name ?? '',
        paymentSettings: {
          merchantAchDelayDays: 0,
        },
      },
      payablesDetails: payablesDetails,
    },
  }
  logger.info(`Aion payment start sending request`)
  try {
    await AzureService.sendServiceBusMessage(
      process.env.AZ_PAYMENT_SERVICE_QUEUE_CONNECTION_STRING ?? '',
      process.env.AZ_PAYMENT_SERVICE_QUEUE_NAME ?? '',
      payloadDetails,
    )
  } catch (err) {
    console.log('Payment service error', err)
    throw new exceptions.LogicalError('Something went wrong')
  }
}

export const drawRepayment = async (
  loanApplication: ILoanApplication,
  bankAccount: IBankAccount | Partial<IBankAccount> | undefined,
  lms_paymentId: string | undefined,
  amount: number,
  createdBy?: string,
) => {
  const customerCompany = await Company.findById(loanApplication.company_id)
  if (!customerCompany)
    throw new exceptions.LogicalError('invoice/customer-company-not-found')
  if (!lms_paymentId)
    throw new exceptions.LogicalError('LMS payment was not created')
  if (!bankAccount) throw new exceptions.LogicalError('Bank account not found')

  const payloadDetails: IPayload = {
    flowTemplateCode: CREATE_DRAW_REPAYMENT_TEMPLATE_CODE,
    blueTapeCorrelationId: lms_paymentId,
    createdBy: createdBy ?? 'BlueTape',
    details: {
      date: moment().format(),
      currency: 'USD',
      requestedAmount: amount, // CHECK
      paymentMethod: EPaymentMethod.ach,
      drawDetails: {
        id: loanApplication.lms_id,
        amount: amount,
      },
      customerDetails: {
        id: loanApplication.company_id,
        accountId: bankAccount._id!.toString(),
        name: customerCompany.name,
      },
      compatibility: {
        lmsPaymentId: lms_paymentId,
      },
    },
  }

  try {
    await AzureService.sendServiceBusMessage(
      process.env.AZ_PAYMENT_SERVICE_QUEUE_CONNECTION_STRING ?? '',
      process.env.AZ_PAYMENT_SERVICE_QUEUE_NAME ?? '',
      payloadDetails,
    )
  } catch (err) {
    console.log('Payment service error', err)
    throw new exceptions.LogicalError('Something went wrong')
  }
}

export const payWithAch = async (
  invoices: IInvoice[],
  accountId: string,
  payerCompanyId: string,
  payeeCompanyId: string,
  session: mongoose.ClientSession,
  userId: string,
  userRequestedAmount?: number,
): Promise<IPaymentInfo> => {
  logger.info(`processing payment with 'AION'`)
  const paymentInfo = await pay(
    invoices,
    accountId,
    payerCompanyId,
    session,
    userId,
    userRequestedAmount,
  )
  return paymentInfo
}

const pay = async (
  invoices: IInvoice[],
  accountId: string,
  payerCompanyId: string,
  session: mongoose.ClientSession,
  userId: string,
  userRequestedAmount?: number,
): Promise<IPaymentInfo> => {
  try {
    try {
      await FactoringService.cancelFactoringForInvoicesIfAny(invoices, session)
    } catch (err) {
      throw new CriticalError("Can't cancel factoring for invoices", {
        invoices: invoices.map((el) => el.toJSON()),
        accountId,
        payerCompanyId,
        cause: err,
      })
    }

    const factoringInvoice = invoices.find(
      (invoice) =>
        invoice.paymentDetails?.paymentType === InvoicePaymentType.FACTORING &&
        invoice.paymentDetails.arAdvanceStatus === ArAdvanceStatus.Approved,
    )

    if (factoringInvoice && invoices.length > 1) {
      throw new exceptions.LogicalError(
        `Factoring invoice with id ${factoringInvoice.id} could only be paid separately from the rest`,
      )
    }

    const flowTemplateCode = factoringInvoice
      ? CREATE_PAY_NOW_FACTORING_INVOICE_TEMPLATE_CODE
      : CREATE_PAY_NOW_INVOICE_TEMPLATE_CODE

    const payloadDetails: IPayload = {
      flowTemplateCode,
      blueTapeCorrelationId: payerCompanyId,
      createdBy: 'BlueTape',
      details: {
        date: moment().format(),
        currency: 'USD',
        paymentMethod: EPaymentMethod.ach,
      },
    }
    let requestedAmount = 0,
      totalAchDiscount = 0,
      invoiceFees = 0,
      projectId = ''
    let supplier: ICompany | undefined | null

    const customerCompany = await Company.findById(payerCompanyId).session(
      session,
    )
    if (!customerCompany)
      throw new exceptions.LogicalError('invoice/customer-company-not-found')

    const payablesDetails: IPayablesDetails[] = await Promise.all(
      invoices.map(async (invoice) => {
        if (!invoice) throw new exceptions.LogicalError('invoice/not-found')
        supplier = await Company.findById(invoice.company_id).session(session)
        if (!supplier)
          throw new exceptions.LogicalError('invoice/supplier-not-found')

        if (factoringInvoice) {
          invoiceFees = await FactoringService.calculateCustomerFees(
            factoringInvoice,
            supplier,
          )
        }

        const discount = PayNowService.getAchDiscount(invoice, supplier)

        const operation = await Operation.findOne({
          owner_id: invoice.id,
          status: mongoose.trusted({
            $ne: dictionaries.OPERATION_STATUS.DECLINED,
          }),
        }).session(session)

        const baseAmount = factoringInvoice
          ? await FactoringService.calculateInvoiceUnpaidAmount(invoice)
          : invoice.total_amount

        requestedAmount += baseAmount + invoiceFees

        if (
          operation &&
          (operation.status === dictionaries.OPERATION_STATUS.SUCCESS ||
            (operation.status === dictionaries.OPERATION_STATUS.PROCESSING &&
              !(factoringInvoice && requestedAmount)))
        ) {
          throw new exceptions.LogicalError('Invoice already paid')
        }

        if (operation) {
          // update status here to ensure it's reflected on UI instantly
          await operation
            .updateOne({
              status: dictionaries.OPERATION_STATUS.PROCESSING,
            })
            .session(session)
        }

        totalAchDiscount += discount
        projectId = invoice.project_id ?? ''

        return new PayablesDetail(
          invoice.id,
          EPaymentType[invoice.type],
          invoice.total_amount,
          invoice.total_amount,
          discount,
        )
      }),
    )

    if (factoringInvoice && userRequestedAmount) {
      const roundedUserRequestedAmount =
        Math.round(userRequestedAmount * 100) / 100

      const roundedRemainingAmount = Math.round(requestedAmount * 100) / 100

      if (roundedUserRequestedAmount > roundedRemainingAmount) {
        throw new exceptions.LogicalError(
          'Amount requested for payment cannot be larger than remaining amount',
        )
      } else {
        requestedAmount = roundedUserRequestedAmount
      }
    }

    payloadDetails.details.payablesDetails = payablesDetails
    payloadDetails.details.requestedAmount = requestedAmount

    const payeeId = String(supplier?._id)
    const pullAmountTotal = math.round(requestedAmount - totalAchDiscount, 2)

    const supplierFeeTotal = await PayNowService.getSupplierFee(
      pullAmountTotal,
      payeeId,
      accountId,
    )

    payloadDetails.details.customerDetails = {
      id: payerCompanyId,
      name: customerCompany.name || 'default',
      accountId,
    }
    payloadDetails.details.sellerDetails = {
      companyId: payeeId,
      name: supplier?.name ?? '',
      paymentSettings: {
        merchantAchDelayDays: supplier?.settings?.achDelayDisabled ? 0 : 2,
      },
    }
    payloadDetails.details.feeDetails = [
      new FeeDetail(
        TEST_ID,
        supplierFeeTotal,
        'Merchant fee',
        EFeeType.merchantFee,
      ),
      ...(invoiceFees > 0
        ? [
            new FeeDetail(
              TEST_ID,
              invoiceFees,
              'Customer invoice fee',
              EFeeType.purchaserFee,
            ),
          ]
        : []),
    ]
    if (totalAchDiscount) {
      payloadDetails.details.discountDetails = [
        new DiscountDetail(
          TEST_ID,
          totalAchDiscount,
          EDiscountType.achEarlyPaymentDiscount,
        ),
      ]
    }
    if (projectId) {
      payloadDetails.details.projectDetails = {
        id: projectId,
      }
    }

    await Promise.all(
      invoices.map((invoice) =>
        LoanApplicationService.detachInvoice(invoice.id, null, session),
      ),
    )

    // TODO: VK: quick dangerous fix to ensure updates from approveInvoice.ts are committed before processing payment
    // for now payWithAch is the last call everywhere, new logic added after this call will be non-transactional
    // - review approve flow to make it non-transactional or
    // - add some commitCallback to a session object, put sendMessage into callback and call it in middleware after commit or
    // - add some delivery delay to the message to let transaction be committed
    await session.commitTransaction()

    logger.info(`ACH payment payload: ${JSON.stringify(payloadDetails)}`)

    try {
      await AzureService.sendServiceBusMessage(
        process.env.AZ_PAYMENT_SERVICE_QUEUE_CONNECTION_STRING ?? '',
        process.env.AZ_PAYMENT_SERVICE_QUEUE_NAME ?? '',
        payloadDetails,
      )
    } catch (err) {
      console.log('Payment service error', err)
      throw new exceptions.LogicalError('Something went wrong')
    }

    for (const invoice of invoices) {
      await AwsService.sendSQSMessage(
        'netsuite-connector',
        JSON.stringify({
          id: invoice.id,
          operationType: 'InvoiceStatusUpdate',
          status: 'PaymentProcessing',
        }),
        'NETSUITE',
      )
    }

    if (invoices.length === 1) {
      // for now send notification for single invoice only

      // review invoice updates in the preceding logic to update in-memory invoice after each update to avoid extra calls
      const invoice = await Invoice.findById(invoices[0].id)
      if (!invoice) throw new Error(`unable to find invoice ${invoices[0].id}`)

      await PaymentNotifications.invoiceProcessingToCustomer(
        invoice,
        accountId,
        'ach',
        0,
        requestedAmount,
        requestedAmount,
        null,
      )
    }

    return { totalProcessingAmount: requestedAmount }
  } catch (err) {
    console.log(err)

    await Slack.notifyError(
      'payWithAch',
      `Function: payWithAch UserId: ${userId} CompanyId: ${payerCompanyId}`,
      customErrorToString(err),
    )

    throw err
  }
}

export async function isEligibleForIhcCardPayments(...companyIds: string[]) {
  await getEnvironmentVariables()

  logger.info(
    `Received companyIds for IHC card payments: ${companyIds.join(', ')}`,
  )

  const companies = process.env.IHC_CARD_PAYMENT_COMPANIES
  logger.info(`IHC_CARD_PAYMENT_COMPANIES: ${companies}`)

  if (companies) {
    if (companies.toLowerCase().includes('all')) {
      logger.info(`'all' found in IHC_CARD_PAYMENT_COMPANIES`)
      return true
    }

    const ids = companies.split(',')
    const eligibleCompanyId = companyIds.find((id) => ids.includes(id))

    if (eligibleCompanyId) {
      logger.info(`found ${eligibleCompanyId} in IHC_CARD_PAYMENT_COMPANIES`)
      return true
    }
  }

  return false
}
