import React, { forwardRef, useCallback, useState } from 'react'
import { StyleSheet, View } from 'react-native'
import { Input, InputProps } from '@ui-kitten/components'
import { BtInputLabel } from './BtInputLabel'
import ErrorTextComponent from './ErrorTextComponent'
import { AutoCapitalize } from '@linqpal/models/src/dictionaries'

interface IProps extends InputProps {
  validate?: (value: string) => string | null | undefined
  required?: boolean
  containerStyle?: any
  labelStyle?: any
  errorStyle?: any
  displayError?: 'always' | 'onBlur'
}

export const BtInput_v1 = forwardRef<any, IProps>(
  (
    {
      label,
      validate,
      required = false,
      value = '',
      onChangeText,
      onFocus,
      onBlur,
      containerStyle,
      labelStyle,
      errorStyle,
      status,
      displayError = 'onBlur',
      ...rest
    },
    ref,
  ) => {
    const [isFocused, setIsFocused] = useState(false)
    const [isVisited, setIsVisited] = useState(false)
    const [errorMessage, setErrorMessage] = useState('')

    const validateInput = useCallback(
      (text: string) => {
        if (!validate) return

        const validationResult = validate(text)
        setErrorMessage(validationResult || '')
      },
      [validate],
    )

    const handleChangeText = useCallback(
      (text: string) => {
        validateInput(text)
        if (onChangeText) onChangeText(text)
      },
      [validateInput, onChangeText],
    )

    const handleFocus = useCallback(
      (event: any) => {
        setIsFocused(true)
        if (onFocus) onFocus(event)
      },
      [onFocus],
    )

    const handleBlur = useCallback(
      (event: any) => {
        validateInput(value)

        setIsFocused(false)
        setIsVisited(true)

        if (onBlur) onBlur(event)
      },
      [onBlur, validateInput, value],
    )

    const shouldDisplayError =
      displayError === 'always'
        ? !!errorMessage
        : !!errorMessage && !isFocused && isVisited

    // Determine input status based on error state
    // const inputStatus = shouldDisplayError ? 'danger' : status || 'basic'

    return (
      <View style={[styles.container, containerStyle]}>
        <Input
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          autoComplete={'new-password'}
          autoCorrect={false}
          autoCapitalize={AutoCapitalize.NONE}
          ref={ref}
          value={value}
          onChangeText={handleChangeText}
          onFocus={handleFocus}
          onBlur={handleBlur}
          status={'basic'}
          size={'large'}
          style={[
            styles.input,
            // could be moved to eva theme, but will affect legacy inputs
            isFocused
              ? styles.inputFocused
              : shouldDisplayError
              ? styles.inputError
              : {},
          ]}
          label={
            label && (
              <BtInputLabel labelStyle={labelStyle} required={required}>
                {label}
              </BtInputLabel>
            )
          }
          caption={
            shouldDisplayError ? (
              <ErrorTextComponent
                text={errorMessage}
                containerStyle={errorStyle}
              />
            ) : undefined
          }
          {...rest}
        />
      </View>
    )
  },
)

const styles = StyleSheet.create({
  container: {
    marginBottom: 4,
  },
  input: {
    borderWidth: 2,
    borderRadius: 4,
    borderColor: '#99ADBA',
  },
  inputFocused: {
    borderColor: '#00A0F3',
  },
  inputError: {
    borderColor: '#EC002A',
  },
})
