import { dictionaries, routes, routes2 } from '@linqpal/models'
import chai from 'chai'
import chaiHttp from 'chai-http'
import { _FirebaseTokenVerifier, FakeUsers } from './_firebase_admin'
import { _Authorization } from './_axios'
import * as sinon from 'sinon'
import {
  AzureService,
  BankAccount,
  Branding,
  CardPricingPackage,
  CardProducts,
  Company,
  CustomerAccount,
  Draft,
  emailService,
  Invitation,
  Operation,
  Sms,
  Transaction,
  User,
  UserRole,
} from '@linqpal/common-backend'
import admin from 'firebase-admin'
import MD5 from 'crypto-js/md5'
import './hooks'
import {
  beforeEachFirebase,
  beforeEachMockEncryption,
  beforeEachMockEventBridgeClient,
  beforeEachMockSFN,
  beforeEachMockSNS,
  beforeEachMockSQS,
  beforeEachReferralRock,
  beforeEachSms,
} from './helper'
import { initCbwApiRequester } from '@linqpal/common-backend/src/services/cbw/axios-instance'
import nock from 'nock'
import errors from '@linqpal/models/src/locales/en/errors.json'
import { beforeEachMockSecretsManager } from '@linqpal/services/test/helper'
import { onBoardingService } from '@linqpal/common-backend/src/services/onBoarding/onBoarding.service'
import { SellerAgreement } from '@linqpal/common-backend/src/services/agreement/sellerAgreement'

chai.use(chaiHttp)
chai.should()
let tokenVerifier: sinon.SinonStub, auth: ReturnType<typeof _Authorization>
const { supplier1, supplier2, constructor1 } = FakeUsers

let sellerAgreementMock: sinon.SinonStub

describe('Supplier', () => {
  process.env.LP_CBW_ACH_IDENTIFICATION = '123'
  let mailSend: sinon.SinonStub, obsMock: sinon.SinonStub
  beforeEachSms()
  beforeEachReferralRock()

  beforeEach(async () => {
    tokenVerifier = _FirebaseTokenVerifier()
    auth = _Authorization(supplier1.auth)
    mailSend = sinon
      .stub(emailService, 'send')
      .callsFake(() => Promise.resolve([{} as any, {}]))
    obsMock = sinon
      .stub(onBoardingService, 'getCreditApplications')
      .resolves([])
    sellerAgreementMock = sinon
      .stub(SellerAgreement, 'submit')
      .callsFake(() => Promise.resolve({ url: '', fileName: '' }))
  })
  afterEach(() => {
    mailSend.restore()
    auth.restore()
    tokenVerifier.restore()
    obsMock.restore()
    sellerAgreementMock.restore()
  })
  describe('Application', () => {
    let getUserMock: sinon.SinonStub
    beforeEachMockEncryption()
    beforeEachMockSQS()
    beforeEach(() => {
      getUserMock = sinon
        .stub(admin.auth(), 'getUser')
        .callsFake(() => Promise.resolve({} as admin.auth.UserRecord))
    })
    afterEach(() => {
      getUserMock.restore()
    })

    it('should apply', async () => {
      const doc = {
        type: 'general_application',
        data: {
          businessInfo: {
            group: 'businessInfo',
            title: 'Business Details',
            items: [
              {
                identifier: 'businessName',
                title: '',
                filled: true,
                content: {
                  legalName: 'beta four',
                  dba: 'beta four dba',
                },
              },
              {
                identifier: 'businessPhone',
                title: '',
                filled: true,
                content: '************',
              },
              {
                identifier: 'businessAddress',
                title: '',
                filled: true,
                content: {
                  address: '89 South St, New York, NY 10038, USA',
                  city: 'New York',
                  state: 'New York',
                  zip: '10038',
                },
              },
              {
                identifier: 'startDate',
                title: '',
                filled: true,
                content: '12/2000',
              },
              {
                identifier: 'type',
                title: '',
                filled: true,
                content: {
                  selectedType: 'Partnership',
                },
              },
              {
                identifier: 'ein',
                title: '',
                filled: true,
                content: '*********',
              },
            ],
          },
          businessOwner: {
            group: 'businessOwner',
            title: 'Business Owner',
            items: [
              {
                identifier: 'isOwner',
                title: '',
                filled: true,
                content: 'Yes',
              },
              {
                identifier: 'address',
                title: '',
                filled: true,
                content: {
                  address: '787 7th Ave, New York, NY 10019, USA',
                  city: 'New York',
                  state: 'New York',
                  zip: '10019',
                },
              },
              {
                identifier: 'birthdate',
                title: '',
                filled: true,
                content: '10/10/1999',
              },
              {
                identifier: 'ssn',
                title: '',
                filled: true,
                content: {
                  cipher:
                    'AQICAHhVihIUh3OCvy9OWSZWh/HZRHaQUmi7B16WXsD1I8xijwHj2C48/+mWXKITJd3qeFyxAAAAZzBlBgkqhkiG9w0BBwagWDBWAgEAMFEGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMgrwX3HIU9lbIU015AgEQgCQ5/uSi6qr6EgKab9x80YtI29kkvD8bLKsV1WGm/0DUoUaWBOY=',
                  hash: 'a32e5741c6f29a0553bec34e4247c401',
                  display: '*********',
                },
              },
              {
                identifier: 'firstName',
                title: '',
                filled: true,
                content: 'Test',
              },
              {
                identifier: 'lastName',
                title: '',
                filled: true,
                content: 'User',
              },
              {
                identifier: 'email',
                title: '',
                filled: true,
                content: '<EMAIL>',
              },
              {
                identifier: 'phone',
                title: '',
                filled: true,
                content: '+***********',
              },
            ],
          },
          finance: {
            group: 'finance',
            title: 'Business Financial Details',
            items: [
              {
                identifier: 'revenue',
                title: '',
                filled: true,
                content: '$900,000.08',
              },
              {
                identifier: 'debt',
                title: '',
                filled: true,
                content: '$800.00',
              },
              {
                identifier: 'howMuchCredit',
                title: '',
                filled: true,
                content: '$17,000.00',
              },
            ],
          },
          bank: {
            group: 'bank',
            title: 'Business Bank Details',
            items: [
              {
                identifier: 'details',
                title: '',
                filled: true,
                content: {
                  id: '',
                  name: 'FinBank',
                  voidedCheck: '',
                  accountholderName: 'Vijaya',
                  routingNumber: '*********',
                  accountNumber: '**************',
                  accountName: '',
                  thirdPartyId: '',
                  paymentMethodType: 'bank',
                  accountType: 'savings',
                  isPrimary: false,
                  status: 'notverified',
                  isManualEntry: true,
                  isDeactivated: false,
                },
              },
              {
                identifier: 'erp',
                title: '',
                filled: true,
                content: 'ECI spruce',
              },
            ],
          },
        },
      }
      let resp = await routes.user.saveDraft(doc)
      resp.result!.should.be.equal('ok')
      const draft = await Draft.findById(resp._id.toString()).exec()
      const { data } = draft!.toJSON()
      const ein = (data as any).businessInfo.items.find(
        (i: any) => i.identifier === 'ein',
      ).content
      ein.hash.should.equal(MD5('*********').toString())
      ein.cipher.should.equal(Buffer.from('*********').toString('base64'))
      ein.display.should.equal('*********')
      getUserMock.callsFake((uid) =>
        Promise.resolve({
          uid,
          email: '<EMAIL>',
          emailVerified: false,
          providerData: [
            {
              providerId: 'password',
            },
          ],
        }),
      )
      // Step 0: Email should be verified
      resp = await routes2.user.apply('127.0.0.1')
      resp.result.should.equal('LogicalError')
      resp.message.should.equal(
        'You cannot apply until you verified your e-mail',
      )
      getUserMock.callsFake((uid) =>
        Promise.resolve({
          uid,
          emailVerified: true,
          providerData: [
            {
              providerId: 'password',
            },
          ],
        }),
      )

      // Step 1: Fill company info
      resp = await routes2.user.apply('127.0.0.1')
      resp.result.should.equal('ok')
      const c = await Company.findOne({ _id: draft!.company_id })
      c!.legalName.should.equal(
        Object(doc.data.businessInfo.items[0].content).legalName,
      )
      c!.entity.should.equal(
        Object(doc.data.businessInfo.items[4].content).selectedType,
      )
      // c.address.city.should.equal(doc.data.businessInfo.items[2].content.city)
      c!.ein.should.have.a.property('cipher')
      c!.draft.should.not.be.undefined

      // Step 3: check that cannot apply again
      resp = await routes2.user.apply('127.0.0.1')
      resp.result.should.equal('LogicalError')

      // Step 4: draft cannot be edited after apply or approve
      // resp = await routes.user.saveDraft(doc)
      // resp.body.result.should.equal('LogicalError')
    })

    it('authorized person should apply', async () => {
      const doc = {
        type: 'general_application',
        data: {
          businessInfo: {
            group: 'businessInfo',
            title: 'Business Details',
            items: [
              {
                identifier: 'businessName',
                title: '',
                filled: true,
                content: 'beta four',
              },
              {
                identifier: 'businessPhone',
                title: '',
                filled: true,
                content: '************',
              },
              {
                identifier: 'businessAddress',
                title: '',
                filled: true,
                content: {
                  address: '89 South St, New York, NY 10038, USA',
                  city: 'New York',
                  state: 'New York',
                  zip: '10038',
                },
              },
              {
                identifier: 'startDate',
                title: '',
                filled: true,
                content: '12/2000',
              },
              {
                identifier: 'type',
                title: '',
                filled: true,
                content: 'Partnership',
              },
              {
                identifier: 'ein',
                title: '',
                filled: true,
                content: '*********',
              },
            ],
          },
          businessOwner: {
            group: 'businessOwner',
            title: 'Business Owner',
            items: [
              {
                identifier: 'isOwner',
                title: '',
                filled: true,
                content: 'No',
              },
              {
                identifier: 'firstName',
                title: '',
                filled: true,
                content: 'Test',
              },
              {
                identifier: 'lastName',
                title: '',
                filled: true,
                content: 'User',
              },
              {
                identifier: 'email',
                title: '',
                filled: true,
                content: '<EMAIL>',
              },
              {
                identifier: 'phone',
                title: '',
                filled: true,
                content: '+***********',
              },
              {
                identifier: 'birthdate',
                title: '',
                filled: true,
                content: '10/10/2010',
              },
              {
                identifier: 'ssn',
                title: '',
                filled: true,
                content: {
                  cipher: '321321',
                  hash: '321321',
                  display: '321321',
                },
              },
              {
                identifier: 'address',
                title: '',
                filled: true,
                content: {
                  address: '89 South St, New York, NY 10038, USA',
                  city: 'New York',
                  state: 'New York',
                  zip: '10038',
                },
              },
            ],
          },
          finance: {
            group: 'finance',
            title: 'Finance',
            items: [
              {
                identifier: 'revenue',
                title: '',
                filled: true,
                content: 50.0,
              },
            ],
          },
          bank: {
            group: 'bank',
            title: 'Business Bank Details',
            items: [
              {
                identifier: 'details',
                title: '',
                filled: true,
                content: {
                  id: '',
                  name: 'FinBank',
                  voidedCheck: '',
                  accountholderName: 'Vijaya',
                  routingNumber: '*********',
                  accountNumber: '**************',
                  accountName: '',
                  thirdPartyId: '',
                  paymentMethodType: 'bank',
                  accountType: 'savings',
                  isPrimary: false,
                  status: 'notverified',
                  isManualEntry: true,
                  isDeactivated: false,
                },
              },
            ],
          },
        },
      }
      let resp = await routes.user.saveDraft(doc)
      resp.result.should.eq('ok')

      getUserMock.callsFake((uid) =>
        Promise.resolve({ uid, emailVerified: true }),
      )

      resp = await routes2.user.apply('127.0.0.1')
      resp.result.should.equal('LogicalError')
      resp.message.should.equal(
        'Authorized person is needed to submit application',
      )

      const isOwner = doc.data.businessOwner.items.find(
        (item) => item.identifier === 'isOwner',
      )
      if (isOwner) {
        isOwner.content = 'Yes'
      }

      resp = await routes.user.saveDraft(doc)
      resp.result!.should.be.equal('ok')
      resp = await routes2.user.apply('127.0.0.1')
      resp.result.should.equal('ok')
    })

    it('should fail on missing fields', async () => {
      const doc = {
        type: 'general_application',
        data: {
          businessInfo: {
            group: 'businessInfo',
            title: 'Business Details',
            items: [
              {
                identifier: 'businessPhone',
                title: '',
                filled: true,
                content: '9492883523',
              },
              {
                identifier: 'businessAddress',
                title: '',
                filled: true,
                content: {
                  address: '89 South St, New York, NY 10038, USA',
                  city: 'New York',
                  state: 'New York',
                  zip: '10038',
                },
              },
              {
                identifier: 'startDate',
                title: '',
                filled: true,
                content: '12/2000',
              },
              {
                identifier: 'type',
                title: '',
                filled: true,
                content: 'Partnership',
              },
              {
                identifier: 'ein',
                title: '',
                filled: true,
                content: '*********',
              },
            ],
          },
          businessOwner: {
            group: 'businessOwner',
            title: 'Business Owner',
            items: [
              {
                identifier: 'isOwner',
                title: '',
                filled: true,
                content: 'Yes',
              },
              {
                identifier: 'birthdate',
                title: '',
                filled: true,
                content: '10/10/2000',
              },
              {
                identifier: 'ssn',
                title: '',
                filled: true,
                content: {
                  cipher: '321321',
                  hash: '321321',
                  display: '321321',
                },
              },
              {
                identifier: 'address',
                title: '',
                filled: true,
                content: {
                  address: '89 South St, New York, NY 10038, USA',
                  city: 'New York',
                  state: 'New York',
                  zip: '10038',
                },
              },
            ],
          },
          finance: {
            group: 'finance',
            title: 'Finance',
            items: [
              {
                identifier: 'revenue',
                title: '',
                filled: true,
                content: 50.0,
              },
            ],
          },
          bank: {
            group: 'bank',
            title: 'Business Bank Details',
            items: [
              {
                identifier: 'details',
                title: '',
                filled: true,
                content: {
                  name: 'FinBank',
                  accountholderName: 'Vijaya',
                  routingNumber: '*********',
                  accountNumber: '**************',
                  paymentMethodType: 'bank',
                  accountType: 'savings',
                },
              },
            ],
          },
        },
      }
      let resp = await routes.user.saveDraft(doc)

      getUserMock.callsFake((uid) =>
        Promise.resolve({ uid, emailVerified: true }),
      )

      resp = await routes2.user.apply('127.0.0.1')
      resp.result.should.equal('LogicalError')
      resp.message.should.equal(
        'Draft has invalid fields: businessInfo.businessName',
      )
    })

    it('get info supplier', async () => {
      const doc = {
        type: 'general_application',
        data: {
          business: {
            group: 'business',
            title: 'Business Details',
            items: [
              {
                identifier: 'details',
                title: 'Business Details',
                filled: true,
                content: {
                  name: 'ASD Inc',
                  website: 'asd.com',
                  type: 'Corporation',
                  category: ['a', 'b'],
                  materialsOffer: [],
                  saleVolume: '',
                  orderVolume: '',
                  mainCustomerBase: '',
                  orderShipmentTime: '',
                  paymentTerms: '',
                  saleType: '',
                  onlineSale: '',
                  authorizedSigner: '',
                },
              },
            ],
          },
          bank: {
            group: 'bank',
            title: 'Bank Details',
            items: [
              {
                identifier: 'details',
                title: 'Bank Details',
                filled: true,
                content: {
                  bank: 'asd',
                  account: '**********',
                  routing: '123',
                  find: 'Suppliers',
                },
              },
            ],
          },
          registered: {
            group: 'registered',
            title: 'Registered Business Details',
            items: [
              {
                identifier: 'details',
                title: 'Registered Business Details',
                filled: true,
                content: {
                  address: 'address',
                  city: 'city',
                  state: 'state',
                  zip: '123456',
                  phone: '+***********',
                },
              },
            ],
          },
          ownership: {
            items: [
              {
                content: {
                  authorized: true,
                  phone: '+***********',
                  email: supplier1.info.email,
                },
              },
            ],
          },
        },
      }
      await routes.user.saveDraft(doc)
      const resp = await routes2.user.info()
      resp.should.have.property('result')
      resp.result!.should.be.equal('ok')
      resp.should.have.property('user')
      resp.user.should.not.have.property('type')
      resp.should.have.property('roles')
      resp.roles.should.be.an('array')
    })
  })
  describe('Approved', () => {
    let info: any
    beforeEach(async () => {
      const resp = await routes.company.info()
      resp.result.should.eq('ok')
      info = resp
      await Company.findOneAndUpdate(
        { _id: info.company.id },
        { status: 'approved' },
      )
    })
    describe('Account', () => {
      it('should add account', async () => {
        let resp = await routes.supplier.saveAccount(
          { phone: '**********' },
          true,
        )
        resp.should.have.property('id')
        let acc = await CustomerAccount.findById(resp.id)
        acc!.phone.should.equal('+***********')
        resp = await routes.supplier.saveAccount(
          {
            id: resp.id,
            phone: '**********',
          },
          true,
        )
        acc = await CustomerAccount.findById(resp.id)
        acc!.phone.should.equal('+1**********')
      })
      it('should add billing account', async () => {
        let resp = await routes.supplier.saveAccount(
          { phone: '**********' },
          true,
        )
        resp.should.have.property('id')
        const customerAccountId = resp.id
        const billingContactsArr = [
          {
            first_name: 'asd',
            last_name: 'weqr',
            phone: '**********',
            email: '<EMAIL>',
          },
          {
            first_name: 'assd',
            last_name: 'weqdr',
            phone: '**********',
            email: '<EMAIL>',
          },
        ]

        resp = await routes.supplier.saveBillingContact(
          customerAccountId,
          billingContactsArr,
        )
        resp.result.should.be.equal('ok')
        const billingContacts = await CustomerAccount.find({
          parent_id: customerAccountId,
        })
        billingContacts.length.should.equal(2)
        let acc = await CustomerAccount.findById(customerAccountId)
        acc!.phone.should.equal('+***********')
        resp = await routes.supplier.saveAccount(
          {
            id: resp.id,
            phone: '**********',
          },
          true,
        )
        acc = await CustomerAccount.findById(resp.id)
        acc!.phone.should.equal('+1**********')
      })
    })
    describe('User Management', () => {
      it('check invitation', async () => {
        const resp = await routes.supplier.employeeUpsert({
          login: '<EMAIL>',
          role: 'User',
          fullName: 'John Doe',
        })
        resp.result.should.be.equal('ok')
        mailSend.calledOnce.should.be.true

        const invitations = await Invitation.find()
        invitations.should.have.lengthOf(1)
        const invitation = invitations[0]
        invitation.login.should.be.equal('<EMAIL>')
        invitation.fullName.should.be.equal('John Doe')
        invitation.role.should.be.equal('User')

        const wrongInvitationCode = '5ffdb0a1fb263f4009f4e113'
        const wrongInvitationRes = await routes.supplier.invitation(
          wrongInvitationCode,
        )
        wrongInvitationRes.result.should.be.equal('LogicalError')

        const invitationId = invitation._id.toString()
        const invitationCheck = await routes.supplier.invitation(invitationId)
        invitationCheck.result.should.be.equal('ok')
      })

      it('accept invitation', async () => {
        const resp = await routes.supplier.employeeUpsert({
          login: '<EMAIL>',
          role: 'User',
          fullName: 'John Doe',
        })
        resp.result.should.be.equal('ok')

        let invitations = await Invitation.find()
        invitations.should.have.lengthOf(1)
        const invitation = invitations[0]

        const invitationId = invitation._id.toString()
        const invitationCheck = await routes.supplier.invitation(invitationId)
        invitationCheck.result.should.be.equal('ok')

        const acceptRes = await routes.supplier.invitationSubmit({
          invitationId: invitationId,
          password: '*********',
        })
        acceptRes.result.should.be.equal('ok')

        invitations = await Invitation.find()
        invitations.should.have.lengthOf(0)
      })

      it('reject invitation', async () => {
        const resp = await routes.supplier.employeeUpsert({
          login: '<EMAIL>',
          role: 'User',
          fullName: 'John Doe',
        })
        resp.result.should.be.equal('ok')

        let invitations = await Invitation.find()
        invitations.should.have.lengthOf(1)
        const invitation = invitations[0]
        const invitationId = invitation._id.toString()
        const acceptRes = await routes.supplier.invitationSubmit({
          invitationId: invitationId.toString(),
          action: 'reject',
        })
        acceptRes.result.should.be.equal('ok')

        invitations = await Invitation.find()
        invitations.should.have.lengthOf(0)
      })
    })
    describe('Invoice', () => {
      it('should add invoice', async () => {
        const resp = await routes.invoices.saveInvoice({
          invoice_number: '**********',
        })
        resp.should.have.property('id')
      })
      it('should fetch invoices', async () => {
        const resp = await routes.invoices.allInvoices()
        resp.should.have.property('totalCount')
        resp.should.have.property('items')
      })
      it('should fetch transactions', async () => {
        const resp = await routes.transactions.allTransactions()
        resp.should.have.property('count')
        resp.should.have.property('items')
      })

      it('should fetch invoices by amount search', async () => {
        await routes.invoices.saveInvoice({
          invoice_number: '**********',
          total_amount: 34567.89,
        })
        const listResp = await routes.invoices.allInvoices({
          search: '34567.89',
        })
        listResp.should.have.property('totalCount')
        listResp.should.have.property('items')
        listResp.totalCount.should.equal(1)
        listResp.items.length.should.equal(1)

        const listRespStr = await routes.invoices.allInvoices({
          search: '5ffdb0a1fb263f4009f4e113',
        })
        listRespStr.should.have.property('totalCount')
        listRespStr.should.have.property('items')
        listRespStr.totalCount.should.equal(0)
        listRespStr.items.length.should.equal(0)
      })
    })
    describe('Check Existing Customer', () => {
      let getUserMock: sinon.SinonStub
      beforeEach(() => {
        getUserMock = sinon.stub(admin.auth(), 'getUser')
      })
      afterEach(() => {
        getUserMock.restore()
      })
      it('should throw an error for no input', async () => {
        const resp = await routes.supplier.checkExistingCustomer('', '', '')
        resp.message.should.equal('Phone number is required')
      })

      it('should throw an error for incorrect phone input', async () => {
        await User.create({ login: '+***********' })
        await CustomerAccount.create({
          phone: '+***********',
          company_id: info.company._id,
        })

        const resp = await routes.supplier.checkExistingCustomer(
          '**********',
          'email',
          'id',
        )
        resp.should.have.property('data')
        resp.should.have.property('error')
      })
    })
    describe('Check Txns', () => {
      beforeEachFirebase()
      beforeEachMockEncryption()
      beforeEachMockSecretsManager()
      beforeEachMockSQS()
      beforeEachMockSFN()
      beforeEachMockEventBridgeClient()
      beforeEachMockSNS()
      let aiMock: sinon.SinonStub
      let azureSendServiceBusMessage: sinon.SinonStub
      beforeEach(async () => {
        process.env.LP_CBW_ACH_IDENTIFICATION = '*********'
        process.env.AZ_PAYMENT_SERVICE_QUEUE_CONNECTION_STRING =
          'Endpoint=sb://service-bus-namespace-payment-service-dev.servicebus.windows.net/;SharedAccessKeyName=payment-bus-queue-connection-auth-rule;SharedAccessKey=bdH5pRhWdbo/Kkdp0qkZQOTwM7pxDh8n3+ASbAgpYz4=;EntityPath=paymentrequestqueue-dev'
        process.env.AZ_PAYMENT_SERVICE_QUEUE_NAME = 'paymentrequestqueue-dev'

        const cbwApi = initCbwApiRequester()
        aiMock = sinon.stub(cbwApi, 'post').callsFake((path, { payload }) => {
          return Promise.resolve({
            transactionNumber: '123',
            transactionAmountCents: parseInt(payload.transactionAmount.amount),
            api: {
              reference: '123',
              dateTime: '',
              originalReference: payload.reference,
            },
            statusCode: '000',
            statusDescription: 'SUCCESS',
          })
        })
        azureSendServiceBusMessage = sinon
          .stub(AzureService, 'sendServiceBusMessage')
          .callsFake(() => Promise.resolve())
      })
      afterEach(async () => {
        aiMock.restore()
        azureSendServiceBusMessage.restore()
      })

      async function createInvoice(
        amount: number,
        {
          network = 'Visa',
          productCode = 'G',
          isRegulated = true,
          type = 'Credit',
          isl2eligible = true,
          isTravel = false,
        },
      ) {
        await CardProducts.create({
          productCode: 'G',
          productName: 'Visa Business',
          personalOrBusiness: 'business',
          cardType: 'credit',
          isbpsp: true,
          isl2eligible,
          isl3eligible: true,
          isTravel,
        })
        await CardPricingPackage.create({
          name: 'A',
          metadata: {
            ach: {
              merchant: { min: 1, max: 10, percentage: 1 },
              customer: { percentage: 1, amount: 1 },
            },
            creditCardVisa: {
              merchant: { percentage: 1, amount: 1 },
              customer: { percentage: 2, amount: 2 },
            },
            debitCardRegulated: {
              merchant: { percentage: 1, amount: 2 },
              customer: { percentage: 2, amount: 1 },
            },
            debitCardUnregulated: {
              merchant: { percentage: 2, amount: 3 },
              customer: { percentage: 3, amount: 2 },
            },
            creditCardMasterCard2: {
              merchant: { percentage: 3, amount: 4 },
              customer: { percentage: 4, amount: 3 },
            },
            creditCardVisa2: {
              merchant: { percentage: 8, amount: 9 },
              customer: { percentage: 9, amount: 8 },
            },
            creditCardTravel: {
              merchant: { percentage: 4, amount: 5 },
              customer: { percentage: 5, amount: 4 },
            },
            creditCardBusiness: {
              merchant: { percentage: 5, amount: 6 },
              customer: { percentage: 6, amount: 5 },
            },
            amex: {
              merchant: { percentage: 6, amount: 7 },
              customer: { percentage: 7, amount: 6 },
            },
          },
        })
        let mockAuth = _Authorization(supplier1.auth)
        let resp = await routes.company.addBankAccount({
          accountNumber: '**********',
          isPrimary: true,
          name: 'GreatBank',
          routingNumber: '123123',
          accountType: 'checking',
        })
        resp.result.should.eq('ok')
        resp = await routes.company.updateInfo({
          name: 'Mel',
          address: {
            address: 'Some street',
            city: 'Kansas',
            zip: '11111',
            state: 'KS',
          },
          phone: '**********',
          email: '<EMAIL>',
          ein: '**********',
          settings: { cardPricingPackageId: 'A' },
        })
        await routes.company.settings({ cardPricingPackageId: 'A' })
        const supplier_id = resp.id
        await Company.findOneAndUpdate(
          { _id: supplier_id },
          { status: 'approved' },
        )
        resp = await routes.supplier.saveAccount(
          {
            phone: constructor1.info.phone_number,
          },
          true,
        )
        const customer_account_id = resp.id
        resp = await routes.invoices.saveInvoice({
          status: 'PLACED',
          material_subtotal: amount,
          total_amount: amount,
          customer_account_id,
        })
        const inv_id: string = resp.id
        const op = await Operation.findOne({ owner_id: inv_id })
        op!.type.should.equal(dictionaries.OPERATION_TYPES.INVOICE.PAYMENT)
        mockAuth.restore()
        mockAuth = _Authorization(constructor1.auth)
        resp = await routes.company.addBankAccount({
          accountNumber: '*********1',
          isPrimary: true,
          name: 'GreatBank',
          routingNumber: '123123',
          accountType: 'checking',
        })
        resp.result.should.be.equal('ok')

        resp = await routes2.user.updateInfo({
          firstName: 'John',
          lastName: 'Doe',
          phone: '**********',
          email: '<EMAIL>',
          addresses: [
            {
              city: 'Kansas',
              zip: '11111',
              address: 'Some street',
              state: 'KS',
            },
          ],
        })
        resp.result.should.be.equal('ok')

        const creditCard = await BankAccount.create({
          accountNumber: {
            display: '********1111',
          },
          cardMetadata: {
            accountId: 'ASASx6587ghkjj',
            avsAuthorizeID: '234',
            avsCode: 'Y',
            avsNetworkRC: '44',
            avsResultText: 'NOT DECLINED',
            avsSecurityCode: 'M',
            expirationDate: '202303',
            token: '80E1iEMJ243WsTF0pBM',
            isPullEnabled: true,
            lastFour: '1111',
            isRegulated,
            network,
            productCode,
            type,
          },
          name: 'Visa - Card',
          paymentMethodType: 'card',
          isManualEntry: true,
          accountholderName: 'Test Card 77',
          billingAddress: {
            addressLine1: 'Traction Street',
            addressLine2: '3094 ',
            city: 'Spartanburg',
            stateCode: 'South Carolina',
            zipCode: '29303',
          },
        })

        const companyInfo = await routes.company.info()
        const company = await Company.findById(companyInfo.company.id)
        company!.bankAccounts!.push(creditCard)
        await company!.save()
        mockAuth.restore()
        return { supplier_id, inv_id }
      }

      it('verifying transaction amount returned for card', async () => {
        const { inv_id } = await createInvoice(100, {})
        let mockAuth = _Authorization(constructor1.auth)

        // mock transaction api call
        const createTransactionSuccessResponse = {
          SC: 201,
          transactionID: 'adfhgs98sdf89',
          network: 'Visa',
          networkRC: '00',
          status: 'COMPLETED',
          approvalCode: '000000',
          AVS: {
            codeAVS: 'Y',
            codeSecurityCode: 'M',
          },
          fees: {
            interchange: '0.25',
            network: '0.5',
            tabapay: '0.4',
          },
          card: {
            last4: '1111',
            expirationDate: '202202',
          },
        }
        const tabapayBaseUrl = 'https://api.sandbox.tabapay.net:10443'
        nock(tabapayBaseUrl)
          .post((uri) => uri.includes('transactions'), /"type":"pull"/i)
          .reply(200, createTransactionSuccessResponse)

        let resp = await routes.company.info()
        resp.result.should.be.equal('ok')
        resp = await routes.company.bankAccounts()
        const account = resp.bankAccounts[1]
        resp = await routes2.invoice.pay({
          invoiceIds: [inv_id],
          paymentMethod: 'card',
          accountId: account._id,
          account_id: account._id,
        })
        resp.result.should.be.equal('ok')
        mockAuth.restore()

        mockAuth = _Authorization(supplier1.auth)
        const resp1 = await routes.transactions.allTransactions({
          search: '',
          date: '',
        })
        resp1.should.have.property('count')
        resp1.should.have.property('items')
        resp1.items[0].operation.amount.should.equal(100)
        resp1.items[0].outTransaction.should.have.property('fee')
        resp1.items[0].outTransaction.fee.should.equal(17)
        resp1.items[0].pullTransaction.payment_method.should.equal('card')
        resp1.items[0].pullTransaction.metadata.transactionType.should.equal(
          'PULL',
        )
        resp1.items[0].transactionStatus.should.be.equal('PROCESSING')
        const transactions = await Transaction.find()
        console.log(transactions)
        transactions[0].fee.should.equal(17)
        transactions[0].amount.should.equal(100)
        mockAuth.restore()
      })

      it('verifying transaction amount returned for debit regulated card', async () => {
        const { inv_id } = await createInvoice(100, {
          network: 'Visa',
          type: 'Debit',
          isRegulated: true,
        })
        let mockAuth = _Authorization(constructor1.auth)

        // mock transaction api call
        const createTransactionSuccessResponse = {
          SC: 201,
          transactionID: 'adfhgs98sdf89',
          network: 'Visa',
          networkRC: '00',
          status: 'COMPLETED',
          approvalCode: '000000',
          AVS: {
            codeAVS: 'Y',
            codeSecurityCode: 'M',
          },
          fees: {
            interchange: '0.25',
            network: '0.5',
            tabapay: '0.4',
          },
          card: {
            last4: '1111',
            expirationDate: '202202',
          },
        }
        const tabapayBaseUrl = 'https://api.sandbox.tabapay.net:10443'
        nock(tabapayBaseUrl)
          .post((uri) => uri.includes('transactions'), /"type":"pull"/i)
          .reply(200, createTransactionSuccessResponse)

        let resp = await routes.company.info()
        resp.result.should.be.equal('ok')
        resp = await routes.company.bankAccounts()
        const account = resp.bankAccounts[1]
        resp = await routes2.invoice.pay({
          invoiceIds: [inv_id],
          paymentMethod: 'card',
          accountId: account._id,
          account_id: account._id,
        })
        resp.result.should.be.equal('ok')
        mockAuth.restore()
        mockAuth = _Authorization(supplier1.auth)
        const resp1 = await routes.transactions.allTransactions({
          search: '',
          date: '',
        })
        resp1.should.have.property('count')
        resp1.should.have.property('items')
        resp1.items[0].operation.amount.should.equal(100)
        resp1.items[0].outTransaction.should.have.property('fee')
        resp1.items[0].outTransaction.fee.should.equal(3)
        resp1.items[0].pullTransaction.payment_method.should.equal('card')
        resp1.items[0].pullTransaction.metadata.transactionType.should.equal(
          'PULL',
        )
        resp1.items[0].transactionStatus.should.be.equal('PROCESSING')
        const transactions = await Transaction.find()
        transactions[0].amount.should.equal(100)
        transactions[0].fee.should.equal(3)
        mockAuth.restore()
      })

      it('verifying transaction amount returned for debit unregulated card', async () => {
        const { inv_id } = await createInvoice(100, {
          network: 'Visa',
          type: 'Debit',
          isRegulated: false,
        })
        let mockAuth = _Authorization(constructor1.auth)

        // mock transaction api call
        const createTransactionSuccessResponse = {
          SC: 201,
          transactionID: 'adfhgs98sdf89',
          network: 'Visa',
          networkRC: '00',
          status: 'COMPLETED',
          approvalCode: '000000',
          AVS: {
            codeAVS: 'Y',
            codeSecurityCode: 'M',
          },
          fees: {
            interchange: '0.25',
            network: '0.5',
            tabapay: '0.4',
          },
          card: {
            last4: '1111',
            expirationDate: '202202',
          },
        }
        const tabapayBaseUrl = 'https://api.sandbox.tabapay.net:10443'
        nock(tabapayBaseUrl)
          .post((uri) => uri.includes('transactions'), /"type":"pull"/i)
          .reply(200, createTransactionSuccessResponse)

        let resp = await routes.company.info()
        resp.result.should.be.equal('ok')
        resp = await routes.company.bankAccounts()
        const account = resp.bankAccounts[1]
        resp = await routes2.invoice.pay({
          invoiceIds: [inv_id],
          paymentMethod: 'card',
          accountId: account._id,
          account_id: account._id,
        })
        resp.result.should.be.equal('ok')
        mockAuth.restore()
        mockAuth = _Authorization(supplier1.auth)
        const resp1 = await routes.transactions.allTransactions({
          search: '',
          date: '',
        })
        resp1.should.have.property('count')
        resp1.should.have.property('items')
        resp1.items[0].operation.amount.should.equal(100)
        resp1.items[0].outTransaction.should.have.property('fee')
        resp1.items[0].outTransaction.fee.should.equal(5)
        resp1.items[0].pullTransaction.payment_method.should.equal('card')
        resp1.items[0].pullTransaction.metadata.transactionType.should.equal(
          'PULL',
        )
        resp1.items[0].transactionStatus.should.be.equal('PROCESSING')
        const transactions = await Transaction.find()
        transactions[0].amount.should.equal(100)
        transactions[0].fee.should.equal(5)
        mockAuth.restore()
      })

      it('verifying transaction amount returned for amex card', async () => {
        const { inv_id } = await createInvoice(100, {
          network: 'Amex',
          type: 'Debit',
          isRegulated: false,
        })
        let mockAuth = _Authorization(constructor1.auth)

        // mock transaction api call
        const createTransactionSuccessResponse = {
          SC: 201,
          transactionID: 'adfhgs98sdf89',
          network: 'Visa',
          networkRC: '00',
          status: 'COMPLETED',
          approvalCode: '000000',
          AVS: {
            codeAVS: 'Y',
            codeSecurityCode: 'M',
          },
          fees: {
            interchange: '0.25',
            network: '0.5',
            tabapay: '0.4',
          },
          card: {
            last4: '1111',
            expirationDate: '202202',
          },
        }
        const tabapayBaseUrl = 'https://api.sandbox.tabapay.net:10443'
        nock(tabapayBaseUrl)
          .post((uri) => uri.includes('transactions'), /"type":"pull"/i)
          .reply(200, createTransactionSuccessResponse)

        let resp = await routes.company.info()
        resp.result.should.be.equal('ok')
        resp = await routes.company.bankAccounts()
        const account = resp.bankAccounts[1]
        resp = await routes2.invoice.pay({
          invoiceIds: [inv_id],
          paymentMethod: 'card',
          accountId: account._id,
          account_id: account._id,
        })
        resp.result.should.be.equal('ok')
        mockAuth.restore()
        mockAuth = _Authorization(supplier1.auth)
        const resp1 = await routes.transactions.allTransactions({
          search: '',
          date: '',
        })
        resp1.should.have.property('count')
        resp1.should.have.property('items')
        resp1.items[0].operation.amount.should.equal(100)
        resp1.items[0].outTransaction.should.have.property('fee')
        resp1.items[0].outTransaction.fee.should.equal(13)
        resp1.items[0].pullTransaction.payment_method.should.equal('card')
        resp1.items[0].pullTransaction.metadata.transactionType.should.equal(
          'PULL',
        )
        resp1.items[0].transactionStatus.should.be.equal('PROCESSING')
        const transactions = await Transaction.find()
        transactions[0].amount.should.equal(100)
        transactions[0].fee.should.equal(13)
        mockAuth.restore()
      })

      it('verifying transaction amount returned for l2 eligible card', async () => {
        const { inv_id } = await createInvoice(100, {
          network: 'MasterCard',
          type: 'Credit',
          isRegulated: true,
        })
        let mockAuth = _Authorization(constructor1.auth)

        // mock transaction api call
        const createTransactionSuccessResponse = {
          SC: 201,
          transactionID: 'adfhgs98sdf89',
          network: 'Visa',
          networkRC: '00',
          status: 'COMPLETED',
          approvalCode: '000000',
          AVS: {
            codeAVS: 'Y',
            codeSecurityCode: 'M',
          },
          fees: {
            interchange: '0.25',
            network: '0.5',
            tabapay: '0.4',
          },
          card: {
            last4: '1111',
            expirationDate: '202202',
          },
        }
        const tabapayBaseUrl = 'https://api.sandbox.tabapay.net:10443'
        nock(tabapayBaseUrl)
          .post((uri) => uri.includes('transactions'), /"type":"pull"/i)
          .reply(200, createTransactionSuccessResponse)

        let resp = await routes.company.info()
        resp.result.should.be.equal('ok')
        resp = await routes.company.bankAccounts()
        const account = resp.bankAccounts[1]
        resp = await routes2.invoice.pay({
          invoiceIds: [inv_id],
          paymentMethod: 'card',
          accountId: account._id,
          account_id: account._id,
        })
        resp.result.should.be.equal('ok')
        mockAuth.restore()
        mockAuth = _Authorization(supplier1.auth)
        const resp1 = await routes.transactions.allTransactions({
          search: '',
          date: '',
        })
        resp1.should.have.property('count')
        resp1.should.have.property('items')
        resp1.items[0].outTransaction.should.have.property('fee')
        resp1.items[0].outTransaction.fee.should.equal(7)
        resp1.items[0].pullTransaction.payment_method.should.equal('card')
        resp1.items[0].pullTransaction.metadata.transactionType.should.equal(
          'PULL',
        )
        resp1.items[0].transactionStatus.should.be.equal('PROCESSING')
        resp1.items[0].operation.amount.should.equal(100)
        const transactions = await Transaction.find()
        transactions[0].amount.should.equal(100)
        transactions[0].fee.should.equal(7)
        mockAuth.restore()
      })

      it('verifying transaction amount returned for l2 non eligible card', async () => {
        const { inv_id } = await createInvoice(100, {
          network: 'MasterCard',
          type: 'Credit',
          isRegulated: true,
          isl2eligible: false,
        })
        let mockAuth = _Authorization(constructor1.auth)

        // mock transaction api call
        const createTransactionSuccessResponse = {
          SC: 201,
          transactionID: 'adfhgs98sdf89',
          network: 'Visa',
          networkRC: '00',
          status: 'COMPLETED',
          approvalCode: '000000',
          AVS: {
            codeAVS: 'Y',
            codeSecurityCode: 'M',
          },
          fees: {
            interchange: '0.25',
            network: '0.5',
            tabapay: '0.4',
          },
          card: {
            last4: '1111',
            expirationDate: '202202',
          },
        }
        const tabapayBaseUrl = 'https://api.sandbox.tabapay.net:10443'
        nock(tabapayBaseUrl)
          .post((uri) => uri.includes('transactions'), /"type":"pull"/i)
          .reply(200, createTransactionSuccessResponse)

        let resp = await routes.company.info()
        resp.result.should.be.equal('ok')
        resp = await routes.company.bankAccounts()
        const account = resp.bankAccounts[1]
        resp = await routes2.invoice.pay({
          invoiceIds: [inv_id],
          paymentMethod: 'card',
          accountId: account._id,
          account_id: account._id,
        })

        resp.result.should.be.equal('ok')
        mockAuth.restore()
        mockAuth = _Authorization(supplier1.auth)
        const resp1 = await routes.transactions.allTransactions({
          search: '',
          date: '',
        })
        resp1.should.have.property('count')
        resp1.should.have.property('items')
        resp1.items[0].operation.amount.should.equal(100)
        resp1.items[0].outTransaction.should.have.property('fee')
        resp1.items[0].outTransaction.fee.should.equal(11)
        resp1.items[0].pullTransaction.payment_method.should.equal('card')
        resp1.items[0].pullTransaction.metadata.transactionType.should.equal(
          'PULL',
        )
        resp1.items[0].transactionStatus.should.be.equal('PROCESSING')
        const transactions = await Transaction.find()
        transactions[0].amount.should.equal(100)
        transactions[0].fee.should.equal(11)
        mockAuth.restore()
      })

      it('verifying travel transaction amount returned for l2 non eligible card', async () => {
        const { inv_id } = await createInvoice(100, {
          network: 'MasterCard',
          type: 'Credit',
          isRegulated: true,
          isl2eligible: false,
          isTravel: true,
        })
        let mockAuth = _Authorization(constructor1.auth)

        // mock transaction api call
        const createTransactionSuccessResponse = {
          SC: 201,
          transactionID: 'adfhgs98sdf89',
          network: 'Visa',
          networkRC: '00',
          status: 'COMPLETED',
          approvalCode: '000000',
          AVS: {
            codeAVS: 'Y',
            codeSecurityCode: 'M',
          },
          fees: {
            interchange: '0.25',
            network: '0.5',
            tabapay: '0.4',
          },
          card: {
            last4: '1111',
            expirationDate: '202202',
          },
        }
        const tabapayBaseUrl = 'https://api.sandbox.tabapay.net:10443'
        nock(tabapayBaseUrl)
          .post((uri) => uri.includes('transactions'), /"type":"pull"/i)
          .reply(200, createTransactionSuccessResponse)

        let resp = await routes.company.info()
        resp.result.should.be.equal('ok')
        resp = await routes.company.bankAccounts()
        const account = resp.bankAccounts[1]
        resp = await routes2.invoice.pay({
          invoiceIds: [inv_id],
          paymentMethod: 'card',
          account_id: account._id,
          accountId: account._id,
        })
        resp.result.should.be.equal('ok')
        mockAuth.restore()
        mockAuth = _Authorization(supplier1.auth)
        const resp1 = await routes.transactions.allTransactions({
          search: '',
          date: '',
        })
        resp1.should.have.property('count')
        resp1.should.have.property('items')
        resp1.items[0].operation.amount.should.equal(100)
        resp1.items[0].outTransaction.fee.should.equal(9)
        resp1.items[0].pullTransaction.payment_method.should.equal('card')
        resp1.items[0].pullTransaction.metadata.transactionType.should.equal(
          'PULL',
        )
        resp1.items[0].transactionStatus.should.be.equal('PROCESSING')
        const transactions = await Transaction.find()
        transactions[0].amount.should.equal(100)
        transactions[0].fee.should.equal(9)
        mockAuth.restore()
      })
    })

    describe('Customer', () => {
      let getUserMock: sinon.SinonStub
      beforeEach(() => {
        getUserMock = sinon.stub(admin.auth(), 'getUser')
      })
      afterEach(() => {
        getUserMock.restore()
      })
      it('Filter Customer', async () => {
        const resp1 = await routes.supplier.user({
          search: '<EMAIL>',
          limit: 10,
        })
        resp1.should.have.property('total')
        resp1.should.have.property('items')
      })
    })
  })
})

describe('Account', () => {
  beforeEachMockEncryption()
  let smsSend: sinon.SinonStub, mailSend: sinon.SinonStub
  beforeEach(async () => {
    tokenVerifier = _FirebaseTokenVerifier()
    auth = _Authorization(constructor1.auth)
    const resp = await routes.company.info()
    await Company.findOneAndUpdate(
      { _id: resp!.company.id },
      { status: 'approved', draft: { businessInfo_category: [] } },
    )
    smsSend = sinon
      .stub(Sms, 'send')
      .callsFake(() => Promise.resolve(undefined as any))
    mailSend = sinon
      .stub(emailService, 'send')
      .callsFake(() => Promise.resolve(undefined as any))
  })
  afterEach(() => {
    auth.restore()
    tokenVerifier.restore()
    smsSend?.restore()
    mailSend.restore()
  })
  it('get all account', async () => {
    const userResp = await routes.company.info()
    const mockCompany1 = await Company.create({ type: 'supplier' })

    await User.create({
      sub: '125|supplier',
      firebaseId: '125|supplier',
      login: '+***********',
      email: supplier1.info.email,
    })
    await UserRole.create({
      sub: '125|supplier',
      company_id: userResp.company.id,
      role: 'Owner',
    })
    await CustomerAccount.create({
      phone: '+***********',
      company_id: userResp.company.id,
      parent_id: '',
    })

    await User.create({
      sub: '126|supplier',
      firebaseId: '126|supplier',
      login: '+1**********',
      email: supplier2.info.email,
    })
    await UserRole.create({
      sub: '126|supplier',
      company_id: mockCompany1._id,
      role: 'Owner',
    })
    await CustomerAccount.create({
      phone: '+1**********',
      company_id: mockCompany1._id,
      parent_id: '',
    })

    const resp = await routes.supplier.allAccounts({ search: '' })
    resp.should.have.property('items')
    resp.should.have.property('count')
    resp.count.should.be.equal(1)
    resp.items[0].company_id.should.be.equal(userResp.company.id)
    resp.items[0].phone.should.be.equal('+***********')
  })
  describe('Check Existing Customer', () => {
    it('should throw an error for duplicated customer phone input', async () => {
      const obsMock = sinon
        .stub(onBoardingService, 'getCreditApplications')
        .resolves([])

      const userInfo = (await routes2.user.info()) as any

      const resp = await routes.supplier.checkExistingCustomer(
        userInfo.user.phone,
        userInfo.user.email,
        userInfo.user._id,
      )
      resp.should.have.property('data')
      resp.should.have.property('error')
      resp.error.should.be.equal(errors['invalid-customer'])

      obsMock.restore()
    })
  })
  describe('Invite', () => {
    let fbCreateSession: sinon.SinonStub
    beforeEach(() => {
      fbCreateSession = sinon.stub(admin.auth(), 'createSessionCookie')
    })
    afterEach(() => {
      fbCreateSession.restore()
    })
    it('should be part of same company with connector invite', async () => {
      let resp = await routes.supplier.saveAccount(
        {
          phone: '**********',
          connector: { customer_id: '123', integration_id: '123' },
        },
        true,
      )
      resp.should.have.property('id')
      resp.should.have.property('invitation')
      const inv1 = resp.invitation
      resp = await routes.supplier.saveAccount(
        {
          phone: '**********',
          connector: { customer_id: '123', integration_id: '123' },
        },
        true,
      )
      const inv2 = resp.invitation
      auth.restore()
      resp = await routes2.user.signup({
        idToken: '1',
        type: 'phone',
        phone: '+***********',
        email: '<EMAIL>',
        invitation: inv1.invitationId,
      } as any)
      resp.company.connector.customer_id.should.eq('123')
      const company_id = resp.company._id
      resp = await routes2.user.signup({
        idToken: '2',
        type: 'phone',
        phone: '+***********',
        email: '<EMAIL>',
        invitation: inv2.invitationId,
      } as any)
      resp.company.connector.customer_id.should.eq('123')
      resp.company._id.should.eq(company_id)
    })
    it('should set login as email in the invitation if setting is specified in branding', async () => {
      const authorization = _Authorization(constructor1.auth)
      const resp = await routes.company.info()
      await Branding.insertMany([
        {
          subdomain: 'nabsupply',
          company_id: resp.company.id,
          settings: {
            defaultSignupMethod: 'email',
          },
        },
      ])
      const resp2 = await routes.supplier.saveAccount(
        {
          phone: '+***********',
          email: '<EMAIL>',
        },
        true,
      )
      resp2.should.have.property('id')
      resp2.should.have.property('invitation')
      const inv = await Invitation.find({ _id: resp2.invitation?.invitationId })
      inv[0].login.should.eq('<EMAIL>')
      authorization.restore()
      auth.restore()
    })
  })
})
