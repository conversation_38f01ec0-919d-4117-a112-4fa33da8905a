import {
  BankAccount,
  Company,
  defaultPlans,
  Draft,
  Experian,
  FinicityTransactions,
  initCbwApiRequester,
  LoanApplication,
  LoanPaymentPlan,
  Operation,
  plaidService,
  Settings,
  Sms,
} from '@linqpal/common-backend'
import * as emailService from '@linqpal/common-backend/src/services/email.service'
import initLexisNexisApiRequester from '@linqpal/common-backend/src/services/lexisNexis/axios-instance'
import * as creditStatusNotification from '@linqpal/common-backend/src/services/loan/creditStatusNotification'
import * as brandingService from '@linqpal/common-backend/src/services/branding.service'
import {
  dictionaries,
  IExperianBusinessData,
  IExperianOwnersData,
  IFraud,
  IKnockoutScores,
  IKYB,
  IKYC,
  ScoringResult,
} from '@linqpal/models'
import { CBWKYB } from '../../../packages/common-backend/index'
import mapper from '../../../packages/common-backend/src/services/lexisNexis/mapper'
import chai from 'chai'
import lambda from 'lambda-tester'
import moment from 'moment'
import nock from 'nock'
import * as sinon from 'sinon'
import { Bluetape } from '../linqpal/decisionEngine/bluetape'
import { LoanDecision } from '../linqpal/decisionEngine/decision'
import {
  CheckFinicityData,
  ProcessFinicityData,
  ProcessManualData,
  VerifyFinicityAccount,
} from '../linqpal/decisionEngine/finicity'
import { KnockoutScore } from '../linqpal/decisionEngine/knockoutScore'
import { KYB } from '../linqpal/decisionEngine/KYB'
import { KYC } from '../linqpal/decisionEngine/KYC'
import { Human } from '../linqpal/decisionEngine/loan/Human'
import { IssueLoan } from '../linqpal/decisionEngine/loan/IssueLoan'
import { IAgreementFileDetails } from '@linqpal/common-backend/src/services/agreement/types'
import * as AgreementService from '@linqpal/common-backend/src/services/agreement/agreement.service'

import { createBuilder, createLoan } from './fixtures/bluetape_data'
import kyb_data from './fixtures/kyb_data'
import kyc_data from './fixtures/kyc_data'
import {
  beforeEachMockEncryption,
  beforeEachMockSecretsManager,
  beforeEachMockSFN,
  beforeEachMockSQS,
  beforeEachSendGrid,
} from './helper'
import { unifiedDraft } from './fixtures/builder_draft'
import { OwnerTypes } from '@linqpal/models/src/dictionaries/UnifiedApplication'
import _ from 'lodash'
import { creditStatus } from '../linqpal/decisionEngine/creditStatus'
import experianData from './fixtures/experian.data'
import de_outputs from './fixtures/de_outputs'
import mongoose from 'mongoose'
import * as smsService from '@linqpal/common-backend/src/services/sms.service'
import { ClientResponse } from '@sendgrid/mail'

chai.should()

function mockTransactions(customerId: string, accountId: string) {
  const date = moment().utc().startOf('month').subtract(8, 'month')
  return [
    {
      id: 1,
      amount: 100,
      customerId,
      accountId,
      transactionDate: date.clone().add(1, 'days').add(20, 'minutes').unix(),
    },
    {
      amount: -50,
      customerId,
      accountId,
      id: 2,
      transactionDate: date.clone().add(3, 'days').add(20, 'hours').unix(),
    },
    {
      amount: -10,
      customerId,
      accountId,
      id: 3,
      transactionDate: date.clone().add(1, 'month').add(20, 'hours').unix(),
    },
    {
      amount: 70,
      customerId,
      accountId,
      id: 4,
      transactionDate: date.clone().add(3, 'month').add(3, 'hours').unix(),
    },
    {
      amount: 30,
      customerId,
      accountId,
      id: 5,
      transactionDate: date.clone().add(5, 'month').add(6, 'hours').unix(),
    },
    {
      amount: -20,
      customerId,
      accountId,
      id: 6,
      transactionDate: date.clone().add(5, 'month').add(7, 'hours').unix(),
    },
    {
      amount: -30,
      customerId,
      accountId,
      id: 7,
      transactionDate: date.clone().add(8, 'month').add(2, 'hours').unix(),
    },
  ]
}

describe('Decision engine', () => {
  beforeEachMockEncryption()
  beforeEachMockSecretsManager()
  beforeEachMockSQS()
  beforeEachMockSFN()
  beforeEachSendGrid()
  let finicityMock: nock.Scope
  let cbwMock: sinon.SinonStub
  let mockAgreementSync: sinon.SinonStub
  let cbwkybMock: sinon.SinonStub
  let mapperMock: sinon.SinonStub
  let mockAssetsReportStatus: sinon.SinonStub
  let mockPostAssetsReport: sinon.SinonStub
  beforeEach(() => {
    sinon.restore()
    const mockData: IAgreementFileDetails = {
      url: 'url',
      fileName: 'fileName',
    }

    cbwkybMock = sinon
      .stub(CBWKYB, 'getBusinessInstantIdData')
      .callsFake(() => Promise.resolve())

    mapperMock = sinon
      .stub(mapper, 'mapCBWBusinessInstantId')
      .callsFake(() => Promise.resolve())

    mockAgreementSync = sinon
      .stub(AgreementService, 'createAgreementForLoanApplication')
      .callsFake(() => Promise.resolve(mockData))

    finicityMock = nock('https://api.finicity.com')
    nock('https://api.finicity.com')
      .persist(true)
      .post('/aggregation/v2/partners/authentication')
      .reply(200, '')
    const api = initLexisNexisApiRequester()
    cbwMock = sinon.stub(api, 'post')
    process.env.LP_CBW_FBO_IDENTIFICATION = '***********'
    mockAssetsReportStatus = sinon
      .stub(plaidService, 'assetsReportStatus')
      .callsFake(() =>
        Promise.resolve({ error: 'Mock error in get asset report status' }),
      )
    mockPostAssetsReport = sinon
      .stub(plaidService, 'postAssetsReport')
      .callsFake(() =>
        Promise.resolve({ error: 'Mock error in post asset report' }),
      )
  })
  afterEach(() => {
    nock.cleanAll()
    cbwMock.restore()
    mockAgreementSync.restore()
    cbwkybMock.restore()
    mapperMock.restore()
    mockAssetsReportStatus.restore()
    mockPostAssetsReport.restore()
  })
  it('should collect KYC data', async () => {
    const { builderCompanyId: company_id } = await createBuilder()
    let app = await LoanApplication.create({
      company_id,
      draft: { ...unifiedDraft },
    })
    const applicationId = app.id
    cbwMock.callsFake((e, x) => {
      if (x.policy === 'emailage') return Promise.resolve(kyc_data.emailage)
      return Promise.resolve(kyc_data.fraudpoint)
    })

    await lambda(KYC).event({ applicationId }).expectResult()
    app = (await LoanApplication.findById(applicationId))!
    app.outputs[0].data!.should.not.have.a.property('error')

    const fraud = app.outputs[0].data!.fraud as IFraud[]

    fraud.length.should.equal(3)
    fraud.filter((f) => f.owner.isPrincipal).length.should.equal(1)
    fraud
      .filter((f) => f.owner.type === OwnerTypes.INDIVIDUAL)
      .length.should.equal(3)
    fraud.every(
      (f) =>
        f.owner.firstName && f.owner.lastName && f.owner.key && f.owner.type,
    ).should.be.true
    fraud.every(
      (f) =>
        f.fraudPoint!.fraudPointScoreRiskLevel === '999' &&
        f.emailAge!.emailAgeEaScoreRiskLevel === '708',
    ).should.be.true
  })
  it('should collect KYB data', async () => {
    const { builderCompanyId: company_id } = await createBuilder()
    let app = await LoanApplication.create({
      company_id,
      draft: { ...unifiedDraft },
    })
    const applicationId = app.id
    cbwMock.callsFake(() => Promise.resolve(kyb_data))
    await lambda(KYB).event({ applicationId }).expectResult()
    app = (await LoanApplication.findById(applicationId))!
    console.log(JSON.stringify(app) + 'app')
    app.outputs[0].data!.should.not.have.a.property('error')
    const kyb = app.outputs[0].data!.KYB! as IKYB[]
    console.log(JSON.stringify(app.outputs[0].data!.KYB!) + 'kyb')

    kyb.length.should.equal(2)
    kyb.every((score) => score.BVI === '50' && _.isEqual(score.BRI, ['41']))
      .should.be.true
    kyb.filter((score) => score.owner.isPrincipal)!.length.should.equal(1)
    const kyc = app.outputs[0].data!.KYC! as IKYC[]

    kyc.length.should.equal(3)
    kyc.every(
      (score) =>
        score.CVI === '50' &&
        _.isEqual(score.CRI, ['81', '80']) &&
        score.B2E === '50',
    ).should.be.true
  })
  it('should collect BlueTape data', async () => {
    const { builderCompanyId: company_id } = await createBuilder()
    const companies = await Company.create([
      { name: 'Builder 1' },
      { name: 'Builder 2' },
    ])
    await LoanApplication.create({
      company_id,
      approvedAmount: 15000,
      status: 'approved',
    })
    await LoanApplication.create({
      company_id,
      approvedAmount: 10000,
      status: 'approved',
      invoiceDetails: { invoiceId: '123', paymentPlan: 30 },
    })
    let [app] = await LoanApplication.create([
      {
        company_id,
        draft: {
          businessInfo_ein: { hash: '112233' },
          personalInfo_ssn: { hash: '121314' },
          normalized: true,
        },
      },
      {
        company_id,
        draft: {
          businessInfo_ein: { hash: '112233' },
          normalized: true,
        },
      },
      {
        company_id: companies[0].id,
        draft: {
          personalInfo_ssn: { hash: '121314' },
          normalized: true,
        },
      },
      {
        company_id: companies[1].id,
        draft: {
          coOwnerInfo_coOwner1: { hash: '121314' },
          normalized: true,
        },
      },
    ])
    await lambda(Bluetape).event({ applicationId: app.id }).expectResult()
    app = (await LoanApplication.findById(app.id))!
    console.log(app.outputs[0])
    app.outputs[0].data!.should.not.have.a.property('error')
    app.outputs[0].data!.business_outstanding_balance!.should.eq(10000)
    app.outputs[0].data!.einMatches!.should.have.lengthOf(0)
    app.outputs[0].data!.ssnMatches!.should.have.lengthOf(2)
    app.outputs[0]
      .data!.ssnMatches!.every((m) => companies.some((c) => c.name === m.name))
      .should.eq(true)
  })
  it('should collect Experian data', async () => {
    const { builderCompanyId: company_id } = await createBuilder()
    let app = await LoanApplication.create({
      company_id,
      draft: { ...unifiedDraft },
    })
    const applicationId = app.id

    sinon.stub(Experian, 'init')
    sinon.stub(Experian, 'login')

    sinon
      .stub(Experian, 'reportsBop')
      .callsFake(() => Promise.resolve(experianData.reportsBop))
    sinon
      .stub(Experian, 'bankruptcies')
      .callsFake(() => Promise.resolve(experianData.bankruptcies))
    sinon
      .stub(Experian, 'judgments')
      .callsFake(() => Promise.resolve(experianData.judgments))
    sinon
      .stub(Experian, 'liens')
      .callsFake(() => Promise.resolve(experianData.liens))
    sinon
      .stub(Experian, 'trades')
      .callsFake(() => Promise.resolve(experianData.trades))
    sinon
      .stub(Experian, 'creditStatus')
      .callsFake(() => Promise.resolve(experianData.creditStatus))
    sinon
      .stub(Experian, 'search')
      .callsFake(() => Promise.resolve(experianData.search))

    await lambda(creditStatus).event({ applicationId }).expectResult()
    app = (await LoanApplication.findById(applicationId))!
    app.outputs[0].data!.should.not.have.a.property('error')

    const { businessData, ownersData } = app.outputs[0].data! as {
      businessData: IExperianBusinessData[]
      ownersData: IExperianOwnersData[]
    }

    businessData.length.should.equal(2)
    businessData.filter((d) => d.owner.isPrincipal).length.should.equal(1)
    businessData.every(
      (d) =>
        d.owner.type === OwnerTypes.ENTITY &&
        d.reliabilityCode === 100.3 &&
        d.tradelinesDebt === 19437 &&
        d.yearsOnFile === 2,
    ).should.be.true

    ownersData.length.should.equal(3)
    ownersData.filter((d) => d.owner.isPrincipal).length.should.equal(1)
    ownersData.every(
      (d) => d.owner.type === OwnerTypes.INDIVIDUAL && d.score === 805,
    ).should.be.true
  })
  it('should calc knockout score', async () => {
    let app = await LoanApplication.create({
      outputs: [
        {
          step: 'KYC',
          data: {
            fraudPoint: { fraudPointScoreRiskLevel: '700' },
            emailAge: {
              emailAgeDomainRiskLevel: 'Low',
              emailAgeEaScoreRiskLevel: '500',
              emailAgeIpRiskLevel: 'Low',
            },
          },
        },
        {
          step: 'KYB',
          data: {
            BVI: '50',
            CVI: '50',
            CRI: [],
            BRI: [],
            BusinessToExecLinkIndex: '50',
          },
        },
        {
          step: 'BlueTape',
          data: {},
        },
        {
          step: 'LoanDecision',
          data: { decision: { loan_revenue: 500000, businessAge: '11/1911' } },
        },
        {
          step: 'creditStatus',
          data: {
            reliabilityCode: 100,
            bankruptcyIndicator: false,
            judgmentBalance: 100,
            lienBalance: 100,
            accountBalanceDebt: 100,
            yearsOnFile: 1,
            tradelinesDebt: 5000,
            ownersData: [
              {
                pastDueAmount: 75727,
                inquiriesDuringLast6Months: 0,
                score: 700,
                lastBankruptcyDate: null,
              },
            ],
          },
        },
      ],
    })
    console.log(app)
    const applicationId = app.id
    const result = await lambda(KnockoutScore)
      .event({ applicationId })
      .expectResult()
    result.should.have.property('applicationId')
    result.data.status.should.eq('passed')
    app = (await LoanApplication.findById(applicationId))!
    console.log(app.outputs[5])
    const knockout = app.outputs[5].data!.knockout! as IKnockoutScores
    knockout.scores.length.should.be.greaterThan(0)
    knockout.scores.forEach((s) => {
      if (s.name === 'BusinessToExecLinkIndex') {
        s.score.should.eq(50)
      }
      s.pass.should.equal(1)
      s.should.have.a.property('name')
      s.should.have.a.property('score')
      s.should.have.a.property('pass')
    })
    await lambda(KnockoutScore).event({ applicationId }).expectResult()
    app = (await LoanApplication.findById(applicationId))!
    console.log(JSON.stringify(app.prevOutputs, null, ' '))
  })
  it('should calc knockout score for multi-owner applications', async () => {
    let app = await LoanApplication.create(de_outputs)

    const applicationId = app.id
    const result = await lambda(KnockoutScore)
      .event({ applicationId })
      .expectResult()

    result.should.have.property('applicationId')
    result.data.status.should.eq('passed')
    app = (await LoanApplication.findById(applicationId))!

    const knockout = app.outputs.find((o) => o.step === 'Knockout')?.data!
      .knockout as IKnockoutScores

    knockout.scores.length.should.equal(0)
    knockout.owners.length.should.equal(4)

    knockout.owners.forEach((owner) => {
      owner.scores.forEach((score) => {
        score.pass.should.equal(1)
        score.should.have.a.property('name')
        score.should.have.a.property('score')
      })
    })
  })
  it('should fail on knockout score for co-owner with bankruptcy', async () => {
    const outputs = de_outputs
    const bankruptBusinessKey =
      outputs.outputs[4].data.businessData![0].owner.key
    const bankruptOwnerKey = outputs.outputs[4].data.ownersData![0].owner.key

    outputs.outputs[4].data.businessData![0].bankruptcyIndicator = true
    outputs.outputs[4].data.ownersData![0].lastBankruptcyDate = moment()
      .subtract(1, 'year')
      .toString()

    let app = await LoanApplication.create(outputs)

    const applicationId = app.id
    await lambda(KnockoutScore).event({ applicationId }).expectResult()
    app = (await LoanApplication.findById(applicationId))!

    const knockout = app.outputs.find((o) => o.step === 'Knockout')?.data!
      .knockout as IKnockoutScores

    const bankruptBusiness = knockout.owners?.find(
      (o) => o.owner.key === bankruptBusinessKey,
    )
    const bankruptOwner = knockout.owners?.find(
      (o) => o.owner.key === bankruptOwnerKey,
    )

    const bankruptBusinessScore = bankruptBusiness?.scores?.find(
      (s) => s.name === 'companyBankruptcy',
    )
    const bankruptOwnerScore = bankruptOwner?.scores?.find(
      (s) => s.name === 'personalBankruptcy',
    )

    bankruptBusinessScore!.pass.should.equal(ScoringResult.Reject)
    bankruptOwnerScore!.pass.should.equal(ScoringResult.Reject)
  })
  it('should wait and process transactions', async () => {
    const customerId = '**********'
    const accId = '123'
    const company = await Company.create({ finicity: { customerId } })
    const account = await BankAccount.create({
      isPrimary: true,
      finicity: { accountId: accId },
    })
    company.bankAccounts!.push(account)
    await company.save()
    let app = await LoanApplication.create({ company_id: company.id })
    const applicationId = app.id
    finicityMock
      .post(
        `/aggregation/v1/customers/${customerId}/accounts/${accId}/transactions/historic`,
      )
      .reply(200, {})
    finicityMock
      .get(`/aggregation/v1/customers/${customerId}/accounts`)
      .times(3)
      .reply(200, {
        accounts: [
          {
            type: 'checking',
            balance: 1,
            aggregationStatusCode: 0,
            aggregationSuccessDate: moment().unix(),
          },
          {
            type: 'loan',
            balance: -1000,
            aggregationStatusCode: 0,
            aggregationSuccessDate: moment().unix(),
          },
          {
            type: 'savings',
            balance: 2,
            aggregationStatusCode: 0,
            aggregationSuccessDate: moment().unix(),
          },
        ],
      })
    finicityMock
      .get((url) =>
        url.includes(`/aggregation/v3/customers/${customerId}/transactions`),
      )
      .reply(200, {})
    const accountInterceptor = finicityMock.get(
      `/aggregation/v1/customers/${customerId}/accounts/${accId}`,
    )
    accountInterceptor.reply(200, { aggregationStatusCode: null })

    let result = await lambda(CheckFinicityData)
      .event({ applicationId })
      .expectResult()
    result.data.status.should.eq('not-ready')

    accountInterceptor.reply(200, {
      aggregationAttemptDate: 123,
      aggregationStatusCode: 0,
    })
    finicityMock
      .get((url) =>
        url.includes(`/aggregation/v3/customers/${customerId}/transactions`),
      )
      .reply(200, {
        found: 5,
        moreAvailable: 'false',
        transactions: mockTransactions(customerId, accId),
      })
    result = await lambda(CheckFinicityData)
      .event({ applicationId })
      .expectResult()
    result.data.status.should.eq('ready')
    const trans = await FinicityTransactions.find({ company_id: company.id })
    trans.length.should.eq(7)
    result = await lambda(ProcessFinicityData)
      .event({ applicationId })
      .expectResult()

    app = (await LoanApplication.findById(result.applicationId))!
    app.outputs[1].data!.cashFlow!.should.have.lengthOf(5)

    const date = moment().startOf('month')

    app.outputs[1].data!.cashFlow![0].date!.should.be.equal(
      date.clone().subtract(0, 'month').format('YYYY-MM'),
    )
    app.outputs[1].data!.cashFlow![0].balance.should.equal(3)
    app.outputs[1].data!.cashFlow![0].debit.should.be.equal(30)
    app.outputs[1].data!.cashFlow![0].credit.should.be.equal(0)
    app.outputs[1].data!.cashFlow![0].cashFlow.should.be.equal(-30)

    app.outputs[1].data!.cashFlow![1].date!.should.be.equal(
      date.clone().subtract(3, 'month').format('YYYY-MM'),
    )
    app.outputs[1].data!.cashFlow![1].balance.should.be.equal(33)
    app.outputs[1].data!.cashFlow![1].debit.should.be.equal(20)
    app.outputs[1].data!.cashFlow![1].credit.should.be.equal(30)
    app.outputs[1].data!.cashFlow![1].cashFlow.should.be.equal(10)

    app.outputs[1].data!.cashFlow![2].date!.should.be.equal(
      date.clone().subtract(5, 'month').format('YYYY-MM'),
    )
    app.outputs[1].data!.cashFlow![2].balance.should.be.equal(23)
    app.outputs[1].data!.cashFlow![2].debit.should.be.equal(0)
    app.outputs[1].data!.cashFlow![2].credit.should.be.equal(70)
    app.outputs[1].data!.cashFlow![2].cashFlow.should.be.equal(70)

    app.outputs[1].data!.cashFlow![3].date!.should.be.equal(
      date.clone().subtract(7, 'month').format('YYYY-MM'),
    )
    app.outputs[1].data!.cashFlow![3].balance.should.be.equal(-47)
    app.outputs[1].data!.cashFlow![3].debit.should.be.equal(10)
    app.outputs[1].data!.cashFlow![3].credit.should.be.equal(0)
    app.outputs[1].data!.cashFlow![3].cashFlow.should.be.equal(-10)

    app.outputs[1].data!.cashFlow![4].date!.should.be.equal(
      date.clone().subtract(8, 'month').format('YYYY-MM'),
    )
    app.outputs[1].data!.cashFlow![4].balance.should.be.equal(-37)
    app.outputs[1].data!.cashFlow![4].debit.should.be.equal(50)
    app.outputs[1].data!.cashFlow![4].credit.should.be.equal(100)
    app.outputs[1].data!.cashFlow![4].cashFlow.should.be.equal(50)
  })
  it('should wait manual transactions', async () => {
    const customerId = '**********'
    const accId = '123'
    const company = await Company.create({ finicity: { customerId } })
    const account = await BankAccount.create({
      isPrimary: true,
      finicity: { accountId: accId },
    })
    company.bankAccounts!.push(account)
    await company.save()
    const app = await LoanApplication.create({ company_id: company.id })
    const applicationId = app.id
    finicityMock
      .get(/transactions/)
      .times(2)
      .reply(200, {})
    finicityMock
      .get(`/aggregation/v1/customers/${customerId}/accounts`)
      .times(5)
      .reply(200, {
        accounts: [
          {
            aggregationSuccessDate: moment().unix(),
            aggregationStatusCode: 0,
          },
        ],
      })
    finicityMock
      .post(
        `/aggregation/v1/customers/${customerId}/accounts/${accId}/transactions/historic`,
      )
      .reply(200, {})
    const accountInterceptor = finicityMock.get(
      `/aggregation/v1/customers/${customerId}/accounts/${accId}`,
    )
    accountInterceptor.reply(200, {
      aggregationAttemptDate: null,
      aggregationStatusCode: null,
    })

    let result = await lambda(CheckFinicityData)
      .event({ applicationId })
      .expectResult()
    result.data.status.should.eq('not-ready')
    accountInterceptor.reply(200, {
      aggregationSuccessDate: moment().unix(),
      aggregationStatusCode: 0,
    })

    finicityMock
      .get((url) =>
        url.includes(`/aggregation/v3/customers/${customerId}/transactions`),
      )
      .reply(200, { found: 0, moreAvailable: 'false', transactions: [] })

    result = await lambda(CheckFinicityData)
      .event({ applicationId })
      .expectResult()
    result.data.status.should.eq('no-data')

    company.finicity = {
      customerId,
      transactionImportCompleted: true,
      transactionsImportedCount: 0,
      lastImport: moment().subtract(1, 'year').unix(),
    }
    console.log('finicity', company.finicity)
    await company.save()
    result = await lambda(CheckFinicityData)
      .event({ applicationId })
      .expectResult()
    result.data.status.should.eq('no-data')
  })
  it('should process manual transactions', async () => {
    const company = await Company.create({})
    await company.save()
    const company_id = company._id.toString()
    let app = await LoanApplication.create({ company_id })
    const applicationId = app.id

    let date = moment().utc().startOf('month').subtract(8, 'month')
    const transactions = [
      {
        transactionId: 1,
        company_id,
        amount: 100,
        balance: 100,
        transactionDate: date.clone().add(20, 'minutes').unix(),
      },
      {
        transactionId: 2,
        company_id,
        amount: -50,
        balance: 50,
        transactionDate: date.clone().add(3, 'days').add(20, 'hours').unix(),
      },
      {
        transactionId: 3,
        company_id,
        amount: -10,
        balance: 40,
        transactionDate: date.clone().add(1, 'month').add(20, 'hours').unix(),
      },
      {
        transactionId: 4,
        company_id,
        amount: 70,
        balance: 110,
        transactionDate: date.clone().add(3, 'month').add(3, 'hours').unix(),
      },
      {
        transactionId: 5,
        company_id,
        amount: 30,
        balance: 140,
        transactionDate: date.clone().add(5, 'month').add(6, 'hours').unix(),
      },
      {
        transactionId: 6,
        company_id,
        amount: -20,
        balance: 120,
        transactionDate: date.clone().add(5, 'month').add(7, 'hours').unix(),
      },
      {
        transactionId: 7,
        company_id,
        amount: -30,
        balance: 90,
        transactionDate: date.clone().add(8, 'month').add(2, 'hours').unix(),
      },
    ]
    await Promise.all(
      transactions.map(async (t) => FinicityTransactions.create(t)),
    )

    const result = await lambda(ProcessManualData)
      .event({ applicationId })
      .expectResult()

    date = moment().startOf('month').subtract(1, 'month')

    app = (await LoanApplication.findById(result.applicationId))!
    app.outputs[0].data!.cashFlow!.should.have.lengthOf(8)

    app.outputs[0].data!.cashFlow![0].date!.should.be.equal(
      date.clone().subtract(0, 'month').format('YYYY-MM'),
    )
    app.outputs[0].data!.cashFlow![0].cashFlow.should.be.equal(0)
    app.outputs[0].data!.cashFlow![0].balance.should.be.equal(120)

    app.outputs[0].data!.cashFlow![1].date!.should.be.equal(
      date.clone().subtract(1, 'month').format('YYYY-MM'),
    )
    app.outputs[0].data!.cashFlow![1].cashFlow.should.be.equal(0)
    app.outputs[0].data!.cashFlow![1].balance.should.be.equal(120)

    app.outputs[0].data!.cashFlow![2].date!.should.be.equal(
      date.clone().subtract(2, 'month').format('YYYY-MM'),
    )
    app.outputs[0].data!.cashFlow![2].cashFlow.should.be.equal(10)
    app.outputs[0].data!.cashFlow![2].balance.should.be.equal(120)

    app.outputs[0].data!.cashFlow![3].date!.should.be.equal(
      date.clone().subtract(3, 'month').format('YYYY-MM'),
    )
    app.outputs[0].data!.cashFlow![3].cashFlow.should.be.equal(0)
    app.outputs[0].data!.cashFlow![3].balance.should.be.equal(110)

    app.outputs[0].data!.cashFlow![4].date!.should.be.equal(
      date.clone().subtract(4, 'month').format('YYYY-MM'),
    )
    app.outputs[0].data!.cashFlow![4].cashFlow.should.be.equal(70)
    app.outputs[0].data!.cashFlow![4].balance.should.be.equal(110)

    app.outputs[0].data!.cashFlow![5].date!.should.be.equal(
      date.clone().subtract(5, 'month').format('YYYY-MM'),
    )
    app.outputs[0].data!.cashFlow![5].cashFlow.should.be.equal(0)
    app.outputs[0].data!.cashFlow![5].balance.should.be.equal(40)

    app.outputs[0].data!.cashFlow![6].date!.should.be.equal(
      date.clone().subtract(6, 'month').format('YYYY-MM'),
    )
    app.outputs[0].data!.cashFlow![6].cashFlow.should.be.equal(-10)
    app.outputs[0].data!.cashFlow![6].balance.should.be.equal(40)

    app.outputs[0].data!.cashFlow![7].date!.should.be.equal(
      date.clone().subtract(7, 'month').format('YYYY-MM'),
    )
    app.outputs[0].data!.cashFlow![7].cashFlow.should.be.equal(50)
    app.outputs[0].data!.cashFlow![7].balance.should.be.equal(50)
  })
  it('should compute loan decision', async () => {
    await Settings.create([
      { key: 'de_base_approval_amount_percentage', value: 20 },
      { key: 'de_max_approval_amount_percentage', value: 50 },
      { key: 'de_portfolio_amount', value: ********* },
      { key: 'de_max_concentration_amount_percentage', value: 1 },
      { key: 'de_approval_amount_limit', value: 1000000 },
      { key: 'de_min_approval_amount', value: 5000 },
      { key: 'de_company_score_threshold', value: 20 },
      { key: 'de_asset_coverage_threshold', value: 100 },
      { key: 'de_cash_flow_coverage_threshold', value: 30 },
      { key: 'de_debt_adjustor', value: 1.2 },
      { key: 'de_acceptable_debt_of_revenue', value: 15 },
    ])
    const company_id = new mongoose.Types.ObjectId()
    await Draft.create({
      company_id,
      type: 'loan_application',
      data: {
        businessFinance: {
          items: [
            { identifier: 'annualRevenue', content: '$250K to $500K' },
            { identifier: 'debt', content: 'Less than $10,000' },
          ],
        },
      },
    })

    const date = moment().startOf('month').subtract(1, 'month')

    let app = await LoanApplication.create({
      company_id,
      status: 'processing',
      outputs: [
        { step: 'KYC' },
        { step: 'BlueTape', data: { business_outstanding_balance: 1000 } },
        { step: 'KnockoutScore', data: { name: 'IP', pass: 1, score: 5 } },
        {
          step: 'ProcessFinicityData',
          data: {
            cashFlow: [
              {
                balance: 52477.0,
                cashFlow: 8685.0,
                debit: 0.0,
                credit: 8685.0,
                date: date.clone().subtract(0, 'month').format('YYYY-MM'),
              },
              {
                balance: 43792.0,
                cashFlow: 8586.0,
                debit: 0.0,
                credit: 8586.0,
                date: date.clone().subtract(1, 'month').format('YYYY-MM'),
              },
              {
                balance: 35206.0,
                cashFlow: 8573.0,
                debit: 0.0,
                credit: 8573.0,
                date: date.clone().subtract(2, 'month').format('YYYY-MM'),
              },
              {
                balance: 26633.0,
                cashFlow: 8321.0,
                debit: 0.0,
                credit: 8321.0,
                date: date.clone().subtract(3, 'month').format('YYYY-MM'),
              },
              {
                balance: 18312.0,
                cashFlow: 8229.0,
                debit: 0.0,
                credit: 8229.0,
                date: date.clone().subtract(4, 'month').format('YYYY-MM'),
              },
              {
                balance: 10083.0,
                cashFlow: 8080.0,
                debit: 0.0,
                credit: 8080.0,
                date: date.clone().subtract(5, 'month').format('YYYY-MM'),
              },
            ],
          },
        },
      ],
      draft: { ...unifiedDraft },
    })
    const applicationId = app.id
    const result = await lambda(LoanDecision)
      .event({ applicationId: applicationId })
      .expectResult()
    result.applicationId.should.equal(applicationId)
    result.data.loanApproved.should.equal(true)

    app = (await LoanApplication.findById(applicationId))!
    const data = app.outputs.find((o) => o.step === 'LoanDecision')!
    const decision = data.data!.decision!
    // decision.debt_factor.should.equal(1)
    // decision.asset_factor.should.equal(31083.83)
    // decision.cash_flow_factor.should.equal(8412.33)
    // decision.overall_lending_coverage.should.equal(56320.83)

    // decision.base_approval_amount.should.equal(11264.17)
    // decision.max_approval_amount.should.equal(28160.42)
    // decision.company_score.should.equal(0.5)
    // decision.company_specific_approval_amount.should.equal(19712.29)
    // decision.asset_coverage.should.equal(266.21)
    // decision.cash_flow_coverage.should.equal(42.68)
    decision.approved_amount!.should.equal(8000)

    decision.loan_revenue!.should.equal(100948)
    decision.annual_revenue!.should.equal(100948)
    decision.loan_debt!.should.equal(6000)
    decision.debt_amount!.should.equal(6000)
    decision.total_acceptable_debt_amount!.should.equal(15142.2)
    decision.available_credit_limit!.should.equal(8000)
    decision.debt_adjustor!.should.equal(1.2)
    decision.acceptable_debt_of_revenue!.should.equal(15)
    decision.revenue_est_provided_in_app!.should.equal(375000)
    decision.debt_estimate_provided_in_app!.should.equal(5000)
  })
  it('should calculate loan decision for empty cash flow', async () => {
    await Settings.create([
      { key: 'de_base_approval_amount_percentage', value: 20 },
      { key: 'de_max_approval_amount_percentage', value: 50 },
      { key: 'de_portfolio_amount', value: ********* },
      { key: 'de_max_concentration_amount_percentage', value: 1 },
      { key: 'de_approval_amount_limit', value: 1000000 },
      { key: 'de_min_approval_amount', value: 5000 },
      { key: 'de_company_score_threshold', value: 20 },
      { key: 'de_asset_coverage_threshold', value: 100 },
      { key: 'de_cash_flow_coverage_threshold', value: 30 },
      { key: 'de_debt_adjustor', value: 1.2 },
      { key: 'de_acceptable_debt_of_revenue', value: 15 },
    ])
    const company_id = new mongoose.Types.ObjectId()

    await Draft.create({
      company_id,
      type: 'general_application',
      data: {
        businessFinance: {
          items: [
            { identifier: 'annualRevenue', content: 'Less than $250K' },
            { identifier: 'debt', content: 'No Debt' },
          ],
        },
      },
    })

    let app = await LoanApplication.create({
      company_id,
      status: 'processing',
      outputs: [{ step: 'ProcessFinicityData', data: { cashFlow: [] } }],
      draft: { ...unifiedDraft, finance_debt: '0', finance_revenue: '125000' },
    })
    const applicationId = app.id
    const result = await lambda(LoanDecision)
      .event({ applicationId })
      .expectResult()
    result.applicationId.should.equal(applicationId)
    result.data.loanApproved.should.equal(false)

    app = (await LoanApplication.findById(applicationId))!
    const data = app.outputs.find((o) => o.step === 'LoanDecision')
    const decision = data!.data!.decision!

    // decision.debt_factor.should.equal(1)
    // decision.asset_factor.should.equal(1)
    // decision.cash_flow_factor.should.equal(1)
    // decision.overall_lending_coverage.should.equal(4)

    // decision.base_approval_amount.should.equal(0.8)
    // decision.max_approval_amount.should.equal(2)
    // decision.company_score.should.equal(0.5)
    // decision.company_specific_approval_amount.should.equal(1.4)
    // decision.asset_coverage.should.equal(71.43)
    // decision.cash_flow_coverage.should.equal(71.43)
    decision.approved_amount!.should.equal(0)
  })
  it('should test human lambda', async () => {
    const { builderCompanyId } = await createBuilder()
    let app = await LoanApplication.create({ company_id: builderCompanyId })
    await lambda(Human)
      .event({ applicationId: app.id, step: 'HumanApproval', taskToken: '123' })
      .expectResult()
    app = (await LoanApplication.findById(app.id))!
    app.progress.step.should.eq('HumanApproval')
    app.progress.action.should.eq('123')
  })
  describe('Loan', () => {
    let mockCBW: sinon.SinonStub
    let mockSmsSend: sinon.SinonStub,
      mockSmsSend2: sinon.SinonStub,
      mockSmsSend3: sinon.SinonStub,
      mockBranding: sinon.SinonStub
    let mockMailSend: sinon.SinonStub

    beforeEach(async () => {
      const cbwApi = initCbwApiRequester()
      mockCBW = sinon.stub(cbwApi, 'post').callsFake((path, { payload }) => {
        if (payload.transactionType === 'ADD_CARD') {
          return Promise.resolve({ card: { cardId: '123' } })
        }
        if (payload.transactionType === 'GET_CARD') {
          return Promise.resolve({ card: { cardId: '123' } })
        }
        if (payload.transactionType === 'GET_CVV') {
          return Promise.resolve({ card: { cvv: '123' } })
        }
        return Promise.resolve({
          transactionNumber: '123',
          transactionStatus: 'PROCESSED',
          transactionAmountCents: parseInt(payload.transactionAmount?.amount),
          api: {
            reference: '123',
            dateTime: '',
            originalReference: payload.reference,
          },
          statusCode: '000',
          statusDescription: 'SUCCESS',
        })
      })
      mockSmsSend = sinon.stub(creditStatusNotification, 'sendSMS')
      mockSmsSend2 = sinon
        .stub(smsService, 'send')
        .callsFake(async () => Promise.resolve())
      mockSmsSend3 = sinon.stub(Sms, 'smsUser')
      mockBranding = sinon
        .stub(brandingService, 'getBranding')
        .callsFake(() => Promise.resolve(null))
      process.env.LP_CBW_GL_IDENTIFICATION = '111111'
      process.env.LP_CBW_FBO_IDENTIFICATION = '222222'
      await LoanPaymentPlan.insertMany(defaultPlans)
      mockMailSend = sinon
        .stub(emailService.emailService, 'send')
        .callsFake(async () => Promise.resolve([{} as ClientResponse, {}]))
    })
    afterEach(() => {
      mockCBW.restore()
      mockSmsSend.restore()
      mockMailSend.restore()
      mockSmsSend2.restore()
      mockSmsSend3.restore()
      mockBranding.restore()
      nock.cleanAll()
    })

    it('should prequalify for loan', async () => {
      let { app, builderCompany } = await createLoan(false)
      await lambda(IssueLoan)
        .event({ applicationId: app.id, decision: { approvedAmount: 300 } })
        .expectResult()
      app = (await LoanApplication.findById(app.id))!
      app.status.should.equal(dictionaries.LOAN_APPLICATION_STATUS.APPROVED)
      app.approvedAmount.should.eq(300)
      const company = await Company.findById(builderCompany?._id)
      company?.credit.limit.should.equal(300)
      const op = await Operation.findOne({
        owner_id: app.id,
        type: dictionaries.OPERATION_TYPES.LOAN.ISSUE,
      })
      chai.expect(op).is.null
    })

    it('should not issue virtual card', async () => {
      const loan = await createLoan(
        true,
        true,
        {
          name: 'Payer',
        },
        false,
        false,
        false,
      )
      let app = loan.app
      const { invoice } = loan
      app.outputs.push({
        step: 'LoanDecision',
        data: { loanApproved: true, decision: { approved_amount: 300 } },
      })
      await app.save()
      invoice!.status = dictionaries.invoiceSchemaStatus.draft
      await invoice!.save()
      await lambda(IssueLoan).event({ applicationId: app.id }).expectError()
    })
  })

  describe('Account Verify', () => {
    const veryClose = () => {
      finicityMock
        .get(`/aggregation/v1/customers/${customerId}/accounts/${accId}/owner`)
        .reply(200, {
          ownerName: 'John Smith',
          ownerAddress: '78-7130 Kaleiopapa St',
        })
    }
    const almost = () => {
      finicityMock
        .get(`/aggregation/v1/customers/${customerId}/accounts/${accId}/owner`)
        .reply(200, { ownerName: 'John Smith', ownerAddress: '123' })
    }
    const poor = () => {
      finicityMock
        .get(`/aggregation/v1/customers/${customerId}/accounts/${accId}/owner`)
        .reply(200, { ownerName: 'Joe Shmidth', ownerAddress: '123' })
    }
    const nullData = () => {
      finicityMock
        .get(`/aggregation/v1/customers/${customerId}/accounts/${accId}/owner`)
        .reply(404, {
          code: 14001,
          message: 'Customer not found.',
        })
    }
    const customerId = '**********'
    const accId = '123'

    ;[
      { mocker: veryClose, result: 'verified' },
      { mocker: almost, result: 'manual' },
      { mocker: poor, result: 'failed' },
      { mocker: nullData, result: 'failed' },
    ].forEach((test) => {
      it(`${test.mocker.name} should be ${test.result}`, async () => {
        test.mocker()
        const { builderCompanyId: company_id, builderCompany: company } =
          await createBuilder()
        const account = await BankAccount.create({
          isPrimary: true,
          finicity: { accountId: accId },
          accountType: 'checking',
        })
        company!.finicity = { customerId, transactionsImportedCount: 0 }
        company!.bankAccounts!.push(account)
        await company!.save()
        const app = await LoanApplication.create({
          company_id,
          draft: { ...unifiedDraft },
        })

        const result = await lambda(VerifyFinicityAccount)
          .event({ applicationId: app.id })
          .expectResult()
        console.log(result)
        result.data.status.should.equal(test.result)
      })
    })
  })
})
