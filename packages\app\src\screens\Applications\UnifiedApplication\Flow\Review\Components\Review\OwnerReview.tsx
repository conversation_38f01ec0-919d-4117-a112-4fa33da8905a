import React, { FC, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { formatAddress } from '@linqpal/models/src/helpers/addressFormatter'
import { BtRadioGroup, BtText } from '@linqpal/components/src/ui'
import { Divider } from '@ui-kitten/components'
import { formatDomesticPhone } from '@linqpal/models/src/helpers/phoneFormatter'
import { OwnerIcon } from '../../../CoOwners/components/OwnerIcon'
import { OwnerPropertyRow } from './OwnerPropertyRow'
import { OwnerAddressStep } from '../../../BusinessOwner/OwnerAddressStep'
import { OwnerBirthdateStep } from '../../../BusinessOwner/OwnerBirthdateStep'
import { OwnerSsnStep } from '../../../BusinessOwner/OwnerSsnStep'
import { observer } from 'mobx-react-lite'
import { OwnerInvitationSentBadge } from '../../../../Components/OwnerInvitationSentBadge'
import { useUnifiedApplication } from '../../../../UnifiedApplicationContext'
import RootStore from '../../../../../../../store/RootStore'
import { Groups } from '@linqpal/models/src/applications/unified/UnifiedApplicationSteps'
import { Spacer } from '../../../../../../../ui/atoms'
import { ownerReviewStyles } from './ownerReviewStyles'

export const OwnerReview: FC = observer(() => {
  const { t } = useTranslation('application')
  //// const { sm } = useResponsive()

  const store = useUnifiedApplication()

  // const onValueUpdate = (updateInfo: ModelUpdateInfo) =>
  //   ApplicationStore.handleDocumentUpdate(
  //     updateInfo.value,
  //     updateInfo.identifier,
  //     updateInfo.group,
  //     updateInfo.filled,
  //     getGroupTitle(updateInfo.group) || '',
  //   )

  const owner = useMemo(() => {
    return {
      firstName: RootStore.userStore.user?.firstName,
      lastName: RootStore.userStore.user?.lastName,
      email: RootStore.userStore.user?.email || '',
      phone: RootStore.userStore.user?.phone?.slice(2) || '',
      percentOwned: store.currentUser.ownershipPercentage || 0,
      address: store.currentUser.address,
      birthdate: store.currentUser.birthdate || '',
      ssn: store.currentUser.ssn || '',
      isOwner: store.isOwner,
      isAuthorised: store.isAuthorized,
      isFilled: () => {
        const steps = store.getGroupSteps(Groups.businessOwner)
        return steps.every((step) => store.validateStep(step))
      },
    }
  }, [store])

  return (
    <>
      <Spacer height={25} />
      <BtText size={12} weight={500}>
        {t('Preview.OwnerTitle')}
      </BtText>
      <BtRadioGroup
        value={owner.isOwner}
        disabled={true}
        options={[
          { label: t('Owner.AuthorizedLabelYes'), value: true },
          { label: t('Owner.AuthorizedLabelNo'), value: false },
        ]}
        radioStyle={{ marginRight: 32 }}
        groupStyle={{ flexDirection: 'row' }}
      />

      {!store.isOwner ? (
        <>
          <Spacer height={10} />
          <BtText size={12} weight={500}>
            {t('Preview.AuthorizedSignerTitle')}
          </BtText>
          <BtRadioGroup
            value={owner.isAuthorised}
            disabled={true}
            options={[
              { label: t('Owner.AuthorizedLabelYes'), value: true },
              { label: t('Owner.AuthorizedLabelNo'), value: false },
            ]}
            radioStyle={{ marginRight: 32 }}
            groupStyle={{ flexDirection: 'row' }}
          />
          <Spacer height={10} />
        </>
      ) : null}

      {store.isOwner ? (
        <View style={ownerReviewStyles.container}>
          <OwnerIcon isOwnerFilled={owner.isFilled()} />
          <View style={ownerReviewStyles.properties}>
            {owner.firstName || owner.lastName ? (
              <BtText style={ownerReviewStyles.ownerName}>
                {`${owner.firstName} ${owner.lastName}`}
              </BtText>
            ) : null}
            <View style={{ width: '100%' }}>
              <OwnerPropertyRow
                label={t('Preview.Owner')}
                value={t('Preview.Percentage', {
                  percentage: owner.percentOwned,
                })}
              />
              <OwnerPropertyRow
                label={t('CoOwners.HomeAddress')}
                value={formatAddress(owner.address)}
              />
              <OwnerPropertyRow
                label={t('CoOwners.Birthday')}
                value={owner.birthdate}
              />
              <OwnerPropertyRow
                label={t('CoOwners.SSN')}
                value={owner.ssn}
                secured
              />
              <OwnerPropertyRow
                label={t('CoOwners.PhoneNumber')}
                value={formatDomesticPhone(owner.phone)}
              />
              <OwnerPropertyRow
                label={t('CoOwners.EmailAddress')}
                value={owner.email}
              />
            </View>
            <Spacer height={25} />
          </View>
        </View>
      ) : 2 - 2 !== 0 && store.isAuthorized ? (
        <View>
          <OwnerAddressStep.component />
          <Spacer height={8} />
          <OwnerBirthdateStep.component />
          <Spacer height={8} />
          <OwnerSsnStep.component />
          <Spacer height={25} />
        </View>
      ) : (
        <>
          <Spacer height={15} />
          <OwnerInvitationSentBadge />
        </>
      )}
      <Divider />
    </>
  )
})
