import React, { FC } from 'react'
import { observer } from 'mobx-react'
import { useTranslation } from 'react-i18next'
import {
  BtButton,
  BtDateInput,
  BtEncryptedAndSecuredBadge,
  BtInput,
  BtRadioGroup,
  BtSocialSecurityNumberInput,
  BtTaxIdInput,
} from '@linqpal/components/src/ui'
import { BtPercentageInput } from '@linqpal/components/src/ui/BtPercentageInput'
import { View } from 'react-native'
import { useResponsive } from '@linqpal/components/src/hooks'
import { BtPhoneInput } from '@linqpal/components/src/ui/BtPhoneInput'
import { OwnerTypes } from '@linqpal/models/src/dictionaries/UnifiedApplication'
import { CoOwnerAcceptanceWarning } from './CoOwnerAcceptanceWarning'
import { useUnifiedApplication } from '../../../UnifiedApplicationContext'
import { CoOwnerValidator } from '@linqpal/models'
import { validateDate } from '@linqpal/models/src/helpers/validations'
import { AddressEditor } from '../../../Components/AddressEditor'
import { Spacer } from '../../../../../../ui/atoms'

export const CoOwnerEditor: FC = observer(() => {
  const { t } = useTranslation('application')
  const { sm } = useResponsive()

  const store = useUnifiedApplication()

  const coOwner = store.coOwnersStore.currentCoOwner
  if (!coOwner) return null

  const coOwners = store.draft?.data?.coOwners || []

  const validatePercentage = (value: string | number | undefined) => {
    const percentage = Number(value || 0)

    const principalPercentage = store.currentUser.ownershipPercentage || 0

    const otherCoOwnersPercentage = coOwners
      .filter((c) => c.id !== coOwner.id)
      .reduce((sum, owner) => sum + (owner.percentOwned || 0), 0)

    const availablePercentage =
      100 - principalPercentage - otherCoOwnersPercentage

    if (percentage < CoOwnerValidator.MinCoOwnerPercentage) {
      return t('ValidationErrors.InvalidMinimumPercentage')
    } else if (percentage > availablePercentage) {
      return t('ValidationErrors.InvalidMaximumPercentage', {
        maxPercent: availablePercentage,
      })
    } else {
      return ''
    }
  }

  const validateEmail = (email?: string) => {
    if (coOwners.some((c) => coOwner.id !== c.id && c.email === email)) {
      return t('ValidationErrors.DuplicatedCoOwnerEmail')
    } else if (!new CoOwnerValidator(coOwner).validate('email')) {
      return t('ValidationErrors.InvalidEmail')
    } else return ''
  }

  const validateBirthday = (value) =>
    value.length !== 10 || validateDate(value, 1900)
      ? ''
      : t('ValidationErrors.InvalidDate')

  const canSave = () =>
    !validatePercentage(coOwner.percentOwned) &&
    (!coOwner.email || !validateEmail(coOwner.email))

  const handleSave = () => {
    if (!canSave()) return

    store.coOwnersStore.save(store.draft)
    store.tryReturnToReview()
  }

  const handleCancel = () => {
    store.coOwnersStore.cancelEdit()
    store.tryReturnToReview()
  }

  return (
    <>
      <Spacer height={16} />
      <BtEncryptedAndSecuredBadge />
      <Spacer height={25} />

      <BtRadioGroup
        value={coOwner?.type}
        onChange={(e) => (coOwner.type = e)}
        options={[
          {
            label: t('CoOwners.IndividualOwner'),
            value: OwnerTypes.INDIVIDUAL,
          },
          {
            label: t('CoOwners.EntityOwner'),
            value: OwnerTypes.ENTITY,
          },
        ]}
        radioStyle={{ marginRight: 32 }}
        groupStyle={{ flexDirection: 'row' }}
        testID="UnifiedApplication.CoOwner.Type"
      />
      <Spacer height={15} />

      <BtPercentageInput
        value={coOwner?.percentOwned || 0}
        onChangeText={(e) => (coOwner.percentOwned = e)}
        validate={validatePercentage}
        label={t('CoOwners.OwnershipPercentage')}
        testID="UnifiedApplication.CoOwner.Percentage"
      />

      {coOwner?.type === OwnerTypes.INDIVIDUAL ? (
        <View style={{ flexDirection: sm ? 'row' : 'column', marginTop: 22 }}>
          <BtInput
            value={coOwner?.firstName || ''}
            onChangeText={(e) => (coOwner.firstName = e)}
            label={t('CoOwners.FirstName')}
            style={{ flexGrow: 1 }}
            testID="UnifiedApplication.CoOwner.FirstName"
          />
          {sm ? <Spacer width={15} /> : <Spacer height={15} />}
          <BtInput
            value={coOwner?.lastName || ''}
            onChangeText={(e) => (coOwner.lastName = e)}
            label={t('CoOwners.LastName')}
            style={{ flexGrow: 1 }}
            testID="UnifiedApplication.CoOwner.LastName"
          />
        </View>
      ) : (
        <>
          <BtInput
            value={coOwner?.entityName || ''}
            onChangeText={(e) => (coOwner.entityName = e)}
            style={{ marginTop: 22 }}
            label={t('CoOwners.EntityName')}
            testID="UnifiedApplication.CoOwner.EntityName"
          />
          <View style={{ flexDirection: sm ? 'row' : 'column', marginTop: 22 }}>
            <BtInput
              value={coOwner?.firstName || ''}
              onChangeText={(e) => (coOwner.firstName = e)}
              label={t('CoOwners.AuthorizedRepresentativeFirstName')}
              style={{ flexGrow: 1 }}
              testID="UnifiedApplication.CoOwner.EntityAuthFirstName"
            />
            {sm ? <Spacer width={15} /> : <Spacer height={15} />}
            <BtInput
              value={coOwner?.lastName || ''}
              onChangeText={(e) => (coOwner.lastName = e)}
              label={t('CoOwners.AuthorizedRepresentativeLastName')}
              style={{ flexGrow: 1 }}
              testID="UnifiedApplication.CoOwner.EntityAuthLastName"
            />
          </View>
        </>
      )}
      <Spacer height={15} />

      <AddressEditor
        address={coOwner}
        label={
          coOwner?.type === OwnerTypes.INDIVIDUAL
            ? t('CoOwners.HomeAddress')
            : t('CoOwners.EntityAddress')
        }
        testID="UnifiedApplication.CoOwner.Address"
        collapsible={true}
      />
      <Spacer height={25} />

      {coOwner?.type === OwnerTypes.INDIVIDUAL ? (
        <>
          <BtDateInput
            value={coOwner?.birthday}
            onChangeText={(e) => (coOwner.birthday = e)}
            //eslint-disable-next-line i18next/no-literal-string
            format="MM/DD/YYYY"
            validate={validateBirthday}
            testID={'UnifiedApplication.CoOwner.Birthday'}
            label={t('CoOwners.Birthday')}
          />
          <Spacer height={15} />
        </>
      ) : null}

      {coOwner?.type === OwnerTypes.INDIVIDUAL ? (
        <BtSocialSecurityNumberInput
          value={coOwner?.ssn}
          onChangeText={(e) => (coOwner.ssn = e)}
          label={t('CoOwners.SSN')}
          validationError={t('ValidationErrors.InvalidSSN')}
          testID={'UnifiedApplication.CoOwner.SSN'}
        />
      ) : (
        <BtTaxIdInput
          value={coOwner.ein}
          onChangeText={(e) => (coOwner.ein = e)}
          label={t('CoOwners.EIN')}
          validationError={t('ValidationErrors.InvalidTaxID')}
          testID={'UnifiedApplication.CoOwner.EIN'}
        />
      )}
      <Spacer height={25} />

      <BtPhoneInput
        value={coOwner?.phone}
        onChangeText={(e) => (coOwner.phone = e.parsed)}
        label={t('CoOwners.PhoneNumber')}
        validationError={t('ValidationErrors.InvalidPhone')}
        testID={'UnifiedApplication.CoOwner.Phone'}
      />
      <Spacer height={25} />

      <BtInput
        value={coOwner?.email}
        onChangeText={(e: string) => (coOwner.email = e)}
        validateInput={(e?: string) => validateEmail(e)}
        label={t('CoOwners.EmailAddress')}
        testID="UnifiedApplication.CoOwner.Email"
      />

      {store.isCreditApp ? (
        <>
          <Spacer height={30} />
          <CoOwnerAcceptanceWarning />
        </>
      ) : null}

      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'flex-end',
          marginTop: 30,
        }}
      >
        <BtButton
          onPress={handleCancel}
          appearance={'ghost'}
          status={'basic'}
          style={{ marginRight: 20 }}
          testID="UnifiedApplication.CoOwner.Cancel"
        >
          {t('CoOwners.Cancel')}
        </BtButton>
        <BtButton
          onPress={handleSave}
          disabled={!canSave()}
          style={{ width: 150 }}
          testID="UnifiedApplication.CoOwner.Save"
        >
          {t('CoOwners.Save')}
        </BtButton>
      </View>
    </>
  )
})
