import React, { FC } from 'react'
import { BtInputBase as BtInput, BtSelect } from '@linqpal/components/src/ui'
import { observer } from 'mobx-react-lite'
import { useTranslation } from 'react-i18next'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { runInAction } from 'mobx'
import { Spacer } from '../../../../../ui/atoms'
import { IUnifiedApplicationEditor } from '../getUnifiedApplicationEditor'

const otherOption = 'Other'

const options = [
  'Sole Proprietorship',
  'Partnership',
  'LLC',
  'Corporation (S or C)',
  otherOption,
]

const BusinessTypeEditor: FC = () => {
  const { t } = useTranslation('application')
  const store = useUnifiedApplication()

  const typeInfo = store.draft.data.businessInfo.type || {}

  const selectedType = typeInfo.selectedType || ''
  const otherType = typeInfo.other || ''

  const handleSelect = (value: string) => {
    runInAction(() => {
      store.draft.data.businessInfo.type = {
        ...store.draft.data.businessInfo.type,
        selectedType: value,
      }
    })
  }

  const handleOtherChange = (value: string) => {
    runInAction(() => {
      store.draft.data.businessInfo.type = {
        ...store.draft.data.businessInfo.type,
        other: value,
      }
    })
  }

  return (
    <>
      <BtSelect
        size="large"
        options={options}
        testID="UnifiedApplication.BusinessInfo.Type"
        label={t('Business.TypeLabel')}
        value={selectedType}
        placeholder={t('Business.TypePlaceholder')}
        onChange={handleSelect}
        primitiveArray
      />

      {selectedType === otherOption && (
        <>
          <Spacer height={18} />
          <BtInput
            size="large"
            value={otherType}
            label={t('Business.OtherTypeLabel')}
            onChangeText={handleOtherChange}
            testID="UnifiedApplication.BusinessInfo.OtherType"
          />
        </>
      )}
    </>
  )
}

export const BusinessTypeStep: IUnifiedApplicationEditor = {
  options: {
    title: 'Business.Type',
  },
  component: observer(BusinessTypeEditor),
}
