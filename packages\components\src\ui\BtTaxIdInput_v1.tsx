import React, { FC } from 'react'
import { PatternFormat } from 'react-number-format'
import { NumberFormatValues } from 'react-number-format/types/types'
import { BtEncryptedInputProps } from './BtEncryptedInput'
import { BtSecureInput_v1 } from './BtSecureInput_v1'

interface BtTaxIdInputProps extends BtEncryptedInputProps {}

export const BtTaxIdInput_v1: FC<BtTaxIdInputProps> = ({
  size = 'large',
  value,
  label,
  required,
  validate,
  validationError,
  onChangeText,
  testID,
}) => {
  const handleValueChange = (formatValues: NumberFormatValues) => {
    if (onChangeText) {
      onChangeText(formatValues.value)
    }
  }

  const handleValidate = (text: string) => {
    if (validationError) {
      return value?.length !== 9 ? validationError : ''
    } else {
      return validate && validate(text)
    }
  }

  return (
    <PatternFormat
      format="#########"
      placeholder="*********"
      size={size}
      label={label}
      value={value}
      required={required}
      customInput={BtSecureInput_v1}
      keyboardType="number-pad"
      allowEmptyFormatting={false}
      validate={handleValidate}
      onValueChange={handleValueChange}
      valueIsNumericString
      testID={testID}
    />
  )
}
