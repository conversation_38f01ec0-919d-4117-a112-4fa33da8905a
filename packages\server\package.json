{"name": "@linqpal/server", "description": "BlueTape", "version": "0.0.1", "private": true, "scripts": {"tsc": "tsc", "sls-offline": "sls offline start --skipCacheInvalidation", "start": "nodemon --watch .env --watch . --exec \"ts-node server.ts --transpile-only \" -e js,ts,json", "e2e": "cross-env NODE_ENV=test mocha --exit --timeout 30000 --recursive --require test/setup.js e2e", "test": "cross-env NODE_ENV=test mocha --exit --timeout 30000 --recursive --require test/setup.js test", "postinstall": "cp ../../node_modules/ua-parser2/regexes.yaml ./hubspot", "test:coverage": "cross-env NODE_ENV=test  nyc --reporter lcov --reporter text mocha --timeout 120000 --exit --recursive --require test/setup.js test", "deploy": "serverless deploy --verbose"}, "dependencies": {"@aws-sdk/client-eventbridge": "3.236.0", "@aws-sdk/client-kms": "3.236.0", "@aws-sdk/client-lambda": "3.236.0", "@aws-sdk/client-s3": "3.236.0", "@aws-sdk/client-secrets-manager": "3.237.0", "@aws-sdk/client-sfn": "3.236.0", "@aws-sdk/client-sns": "3.236.0", "@aws-sdk/client-sqs": "3.236.0", "@linqpal/common-backend": "workspace:*", "@linqpal/models": "workspace:*", "@linqpal/services": "workspace:*", "@sendgrid/mail": "7.7.0", "@sentry/node": "6.8.0", "@sentry/tracing": "6.8.0", "axios": "0.21.1", "body-parser": "1.19.0", "cookie-parser": "1.4.6", "cors": "2.8.5", "crypto-js": "4.0.0", "docxtemplater": "3.28.0", "dotenv": "8.2.0", "express": "5.0.0-alpha.8", "express-jwt": "6.0.0", "express-jwt-authz": "2.4.1", "firebase-admin": "9.5.0", "i18next": "21.4.2", "jsonwebtoken": "8.5.1", "jsrsasign": "10.1.13", "jsrsasign-util": "1.0.4", "jwks-rsa": "1.9.0", "jwt-decode": "4.0.0", "libphonenumber-js": "1.10.61", "lodash": "4.17.20", "mathjs": "9.4.4", "md5": "2.3.0", "moment": "^2.29.4", "moment-business-days": "1.2.0", "moment-timezone": "^0.5.40", "mongoose": "8.16.4", "morgan": "1.9.1", "multer": "^1.4.2", "nock": "^13.0.11", "numbro": "2.3.5", "pizzip": "^3.1.1", "serverless-http": "2.7.0", "string-similarity": "4.0.4", "superagent": "3.8.3", "twilio": "3.55.0", "uuid": "3.4.0", "xlsx": "0.16.9"}, "devDependencies": {"@types/cookie-parser": "1.4.2", "@types/cors": "2.8.12", "@types/dotenv": "8.2.0", "@types/express": "4.17.13", "@types/morgan": "1.9.3", "@types/multer": "1.4.7", "@types/node": "^20.12.7", "@types/pizzip": "3.0.2", "@types/qs": "6.9.4", "@types/string-similarity": "4.0.0", "aws-sdk-client-mock": "2.0.1", "chai": "4.2.0", "chai-http": "4.3.0", "cross-env": "7.0.2", "debug": "2.6.9", "dotenv": "8.2.0", "mocha": "8.1.3", "mongodb-memory-server": "8.11.5", "nodemon": "2.0.6", "nyc": "15.1.0", "rimraf": "3.0.2", "serverless": "3.38.0", "serverless-bundle": "6.1.0", "serverless-domain-manager": "7.3.8", "serverless-dotenv-plugin": "6.0.0", "serverless-offline": "13.5.0", "serverless-plugin-warmup": "8.3.0", "sinon": "9.0.3", "ts-node": "^10.9.2", "typescript": "5.4.5"}, "mocha": {"extension": ["ts", "js"]}}