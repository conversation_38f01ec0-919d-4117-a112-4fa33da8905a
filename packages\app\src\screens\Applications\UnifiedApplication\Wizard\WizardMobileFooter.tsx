import { observer } from 'mobx-react'
import { StyleSheet, View } from 'react-native'
import { BtButton, BtText } from '@linqpal/components/src/ui'
import React, { FC } from 'react'
import { WizardBackButton } from './WIzardBackButton'
import { WizardNextButton } from './WizardNextButton'
import { useUnifiedApplication } from '../UnifiedApplicationContext'
import { UnifiedApplicationReviewStep } from '../Store/UnifiedApplicationReviewStore'
import { useTranslation } from 'react-i18next'
import { SubmitApplicationButton } from '../Flow/Review/Components/Review/SubmitApplicationButton'
import { GoToAgreementButton } from '../Flow/Review/Components/Review/GoToAgreementButton'
import { ConsentStatement } from '../Flow/Review/Components/Review/ConsentStatement'
import RootStore from '../../../../store/RootStore'

export const WizardMobileFooter: FC = observer(() => {
  const store = useUnifiedApplication()

  return (
    <>
      {store.isInReview && <ConsentStatement />}

      <View style={styles.buttonContainer}>
        <WizardBackButton />

        {store.isInReview ? (
          <>
            <PreviewButton />

            {store.hasSubmissionRights &&
              (store.isSupplierApp || store.isCreditApp ? (
                <GoToAgreementButton />
              ) : (
                <SubmitApplicationButton />
              ))}
          </>
        ) : (
          <WizardNextButton />
        )}
      </View>
    </>
  )
})

const PreviewButton: FC = observer(() => {
  const { t } = useTranslation('application')
  const store = useUnifiedApplication()

  if (
    !store.isInReview ||
    store.reviewStore.currentStep !== UnifiedApplicationReviewStep.REVIEW
  ) {
    return null
  }

  return (
    <BtButton
      onPress={() =>
        (store.reviewStore.currentStep = UnifiedApplicationReviewStep.PREVIEW)
      }
      status="basic"
      style={styles.previewButton}
      disabled={RootStore.isBusy}
      testID="UnifiedApplication.Wizard.Mobile.PreviewButton"
    >
      {() => (
        <BtText style={styles.previewButtonText}>
          {t('Review.ReviewButton')}
        </BtText>
      )}
    </BtButton>
  )
})

const styles = StyleSheet.create({
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    borderTopWidth: 1,
    borderTopColor: '#CCD6DD',
    padding: 20,
  },
  previewButton: {
    width: 160,
  },
  previewButtonText: {
    color: '#335C75',
    fontWeight: '700',
    fontSize: 16,
  },
})
