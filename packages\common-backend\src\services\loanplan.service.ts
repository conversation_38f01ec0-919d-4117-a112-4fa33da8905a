import { ILoanPaymentPlanModel } from '@linqpal/models'
import { frequency, LOAN_PLAN_TYPE } from '@linqpal/models/src/dictionaries'
import { LogicalError } from '@linqpal/models/src/types/exceptions'
import { Types } from 'mongoose'
import { LoanPaymentPlan } from '../models'
import { ILoanPaymentPlan } from '../models/types'
import { PaymentDelayCode } from '@linqpal/models/src/dictionaries/factoring'
import { PricingProduct } from '@linqpal/models/src/dictionaries/pricingProduct'

export const defaultPlans: Omit<ILoanPaymentPlanModel, '_id'>[] = [
  {
    id: '',
    name: '30',
    type: 'regular',
    days: 30,
    fee: 0,
    term: 1,
    frequency: frequency.MONTHLY,
    lmsTemplateId: '3476b83d-f7c7-492d-a986-a6e3b3b87b43',
    firstPaymentDelayDays: 30,
    paymentDelayCode: PaymentDelayCode.TD30,
    product: PricingProduct.LineOfCredit,
  },
  {
    id: '',
    name: '60',
    type: 'regular',
    days: 60,
    fee: 2,
    term: 5,
    frequency: frequency.WEEKLY,
    lmsTemplateId: '3ee4cbd6-935a-436e-b2d1-041ac9a5dc11',
    firstPaymentDelayDays: 32,
    paymentDelayCode: PaymentDelayCode.TD30,
    product: PricingProduct.LineOfCredit,
  },
  {
    id: '',
    name: '90',
    type: 'regular',
    days: 90,
    fee: 4,
    term: 9,
    frequency: frequency.WEEKLY,
    lmsTemplateId: '7ce0034d-800e-4ce0-9585-e9dd17338878',
    firstPaymentDelayDays: 34,
    paymentDelayCode: PaymentDelayCode.TD30,
    product: PricingProduct.LineOfCredit,
  },
  {
    id: '',
    name: '60vc',
    type: 'virtualcard',
    days: 60,
    fee: 2,
    term: 5,
    frequency: frequency.WEEKLY,
    lmsTemplateId: '3ee4cbd6-935a-436e-b2d1-041ac9a5dc11',
    firstPaymentDelayDays: 32,
    paymentDelayCode: PaymentDelayCode.TD30,
    product: PricingProduct.LineOfCredit,
  },
  {
    id: '',
    name: '90vc',
    type: 'virtualcard',
    days: 90,
    fee: 4,
    term: 9,
    frequency: frequency.WEEKLY,
    lmsTemplateId: '7ce0034d-800e-4ce0-9585-e9dd17338878',
    firstPaymentDelayDays: 34,
    paymentDelayCode: PaymentDelayCode.TD30,
    product: PricingProduct.LineOfCredit,
  },
  {
    id: '',
    name: '120vc',
    type: 'virtualcard',
    days: 120,
    fee: 6,
    term: 13,
    frequency: frequency.WEEKLY,
    lmsTemplateId: '7ce0034d-900e-4ce0-9585-e9dd17338878',
    firstPaymentDelayDays: 36,
    paymentDelayCode: PaymentDelayCode.TD30,
    product: PricingProduct.LineOfCredit,
  },
  {
    id: '',
    name: '',
    lmsTemplateId: '3476b83d-f7c7-492d-a986-a6e3b3b87b43',
    days: 30,
    fee: 0,
    type: 'custom',
    frequency: frequency.SINGLE,
    term: 1,
    firstPaymentDelayDays: 30,
    paymentDelayCode: PaymentDelayCode.TD30,
    product: PricingProduct.LineOfCredit,
  },
  {
    id: '',
    name: '',
    lmsTemplateId: '3ee4cbd6-935a-436e-b2d1-041ac9a5dc11',
    days: 60,
    fee: 2.4,
    type: 'custom',
    frequency: frequency.SINGLE,
    term: 1,
    firstPaymentDelayDays: 60,
    paymentDelayCode: PaymentDelayCode.TD60,
    product: PricingProduct.LineOfCredit,
  },
  {
    id: '',
    name: '',
    lmsTemplateId: '7ce0034d-800e-4ce0-9585-e9dd17338878',
    days: 90,
    fee: 4.8,
    type: 'custom',
    frequency: frequency.SINGLE,
    term: 1,
    firstPaymentDelayDays: 90,
    paymentDelayCode: PaymentDelayCode.TD60,
    product: PricingProduct.LineOfCredit,
  },
  {
    id: '',
    name: '',
    lmsTemplateId: '7ce0034d-800e-4ce0-9585-e9dd17338878',
    days: 120,
    fee: 9.6,
    type: 'custom',
    frequency: frequency.SINGLE,
    term: 1,
    firstPaymentDelayDays: 120,
    paymentDelayCode: PaymentDelayCode.TD120,
    product: PricingProduct.LineOfCredit,
  },
  {
    id: '',
    name: '',
    type: LOAN_PLAN_TYPE.NO_SUPPLIER,
    days: 60,
    fee: 4,
    term: 5,
    firstPaymentDelayDays: 30,
    lmsTemplateId: '64e315be-06a3-4385-a618-dd790b7e33a5',
    frequency: frequency.WEEKLY,
    paymentDelayCode: PaymentDelayCode.TD30,
    product: PricingProduct.LineOfCredit,
  },
  {
    id: '',
    name: '',
    type: LOAN_PLAN_TYPE.NO_SUPPLIER,
    days: 90,
    fee: 6,
    term: 9,
    firstPaymentDelayDays: 30,
    lmsTemplateId: '4a20fc85-e1e9-4945-a864-5ccf23e9f93a',
    frequency: frequency.WEEKLY,
    paymentDelayCode: PaymentDelayCode.TD30,
    product: PricingProduct.LineOfCredit,
  },
  {
    id: '',
    name: '',
    type: LOAN_PLAN_TYPE.NO_SUPPLIER,
    days: 120,
    fee: 8,
    term: 13,
    firstPaymentDelayDays: 30,
    lmsTemplateId: 'e8d133d7-4cb5-4480-afff-8cacd3742315',
    frequency: frequency.WEEKLY,
    paymentDelayCode: PaymentDelayCode.TD30,
    product: PricingProduct.LineOfCredit,
  },
]

export const getPaymentPlans = async () => {
  let plans = await LoanPaymentPlan.find({})

  // default plans can be removed, to be reviewed
  if (!plans || plans.length === 0) {
    const plansToCreate = defaultPlans.map((plan) => ({
      ...plan,
      id: undefined,
    }))

    await LoanPaymentPlan.insertMany(plansToCreate)
  }

  plans = await LoanPaymentPlan.find({}).sort({ days: 1 })
  return plans
}

export const getPlan = async (value: string): Promise<ILoanPaymentPlan> => {
  if (!value) throw new LogicalError('Plan not found')

  let plan = await LoanPaymentPlan.find({ name: value })

  if (!plan.length && Types.ObjectId.isValid(value))
    plan = await LoanPaymentPlan.find({ _id: value })

  if (!plan.length) throw new LogicalError('Plan not found')

  return plan[0]
}
