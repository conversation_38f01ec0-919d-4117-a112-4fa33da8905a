import { CustomerAccountType } from '../dictionaries/customerAccountType'
import { ICustomerSettings } from '../types/routes'

export interface CustomerAccountInfo {
  id: string
  company_id: string
  name: string
  companyName: string
  first_name: string
  last_name: string
  type: CustomerAccountType
  email: string
  phone: string
  address: string
  isDeleted: boolean
  hasApprovedApplications: boolean
  settings: ICustomerSettings
}
