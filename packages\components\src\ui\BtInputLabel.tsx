import React, { FC } from 'react'
import { BtText, BtTextProps } from './BtText'
import { StyleProp, TextStyle } from 'react-native'

export interface BtInputLabelProps
  extends Omit<BtTextProps, 'size' | 'weight' | 'color'> {
  required?: boolean
  labelStyle?: StyleProp<TextStyle>
}

export const BtInputLabel: FC<BtInputLabelProps> = ({
  children,
  required,
  labelStyle,
  ...btTextProps
}) => (
  <BtText
    {...btTextProps}
    size={12}
    weight="500"
    color="#003353"
    style={[{ marginBottom: 5 }, labelStyle]}
  >
    {children}
    {required && <span style={{ color: '#EC002A' }}> *</span>}
  </BtText>
)
