import React, { FC, useEffect } from 'react'
import { StyleSheet, View } from 'react-native'
import { observer } from 'mobx-react'
import { ApplicationReview } from './Components/ApplicationReview'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { UnifiedApplicationReviewStep } from '../../Store/UnifiedApplicationReviewStore'
import { ApplicationPreview } from './Components/ApplicationPreview'
import { UnifiedApplicationStore } from '../../Store/UnifiedApplicationStore'
import { ApplicationAgreement } from './Components/ApplicationAgreement'
import {
  IUnifiedApplicationEditor,
  IUnifiedApplicationStepOptions,
} from '../getUnifiedApplicationEditor'

const ReviewStepComponent: FC = () => {
  const store = useUnifiedApplication()

  useEffect(() => {
    switch (store.reviewStore.currentStep) {
      case UnifiedApplicationReviewStep.REVIEW:
        store.setStepOptions({
          ...defaultOptions,
          title: 'Review.Heading',
          canGoBack: false,
          showGroupTitle: true,
        })
        break
      case UnifiedApplicationReviewStep.PREVIEW:
        store.setStepOptions({
          ...defaultOptions,
          title: 'Preview.Heading',
          canGoBack: true,
          showGroupTitle: true,
        })
        break
      case UnifiedApplicationReviewStep.AGREEMENT:
        store.setStepOptions({
          ...defaultOptions,
          title: 'Agreement.Heading',
          canGoBack: true,
          showGroupTitle: false,
        })
        break
    }
  }, [store, store.reviewStore.currentStep])

  const renderCurrentState = () => {
    switch (store.reviewStore.currentStep) {
      case UnifiedApplicationReviewStep.REVIEW:
        return <ApplicationReview doc={{} as any} flowController={{} as any} />
      case UnifiedApplicationReviewStep.PREVIEW:
        return <ApplicationPreview />
      case UnifiedApplicationReviewStep.AGREEMENT:
        return <ApplicationAgreement />
      default:
        return null
    }
  }

  return <View style={styles.container}>{renderCurrentState()}</View>
}

const defaultOptions: IUnifiedApplicationStepOptions = {
  title: 'Review.Heading',
  titleStyle: { textAlign: 'center' },
  canGoBack: false,
  showNavigationButtons: false,
  canSkip: false,
  onMoveBack: (store: UnifiedApplicationStore): boolean => {
    return store.reviewStore.moveBack()
  },
}

export const ReviewStep: IUnifiedApplicationEditor = {
  component: observer(ReviewStepComponent),
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tempButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 16,
    backgroundColor: '#f0f0f0',
  },
  tempButton: {
    flex: 1,
    marginHorizontal: 4,
  },
})
