import {
  AwsService,
  Azure,
  Company,
  connectToDatabase,
  CustomerAccount,
  getEnvironmentVariables,
  invoicesService,
  RequesterError,
  UserRole,
} from '@linqpal/common-backend'
import {
  dictionaries,
  EInvoiceAddressType,
  EInvoiceType,
  exceptions,
  utils,
} from '@linqpal/models'
import { handleErrorMessages, handleSuccessMessage } from './tasks'
import { EmailContent, EventData } from './types'
import stringSimilarity from 'string-similarity'
import * as math from 'mathjs'
import {
  ICompany,
  ICustomerAccount,
} from '@linqpal/common-backend/src/models/types'
import { AnalyzeResult } from '@linqpal/common-backend/src/services/azure/api'
import { UnPromisify } from '../types/helperTypes'
import { isFinite } from 'lodash'
import Hashids from 'hashids'
import moment from 'moment-timezone'

const confidenceThreshold = 0.6

async function getInvoiceUrl(data: EventData) {
  return AwsService.getPreSignedUrl({ ...data, method: 'get' })
}

async function extractInvoiceData(invoiceUrl: string, modelId?: string) {
  Azure.init()

  let resultUrl: string
  try {
    ;({ resultUrl } = await Azure.recognizeInvoice(invoiceUrl, modelId))
  } catch (err) {
    console.error(err)
    const requesterErr = err as RequesterError
    const status = requesterErr.originalError.response?.status
    throw status === 400
      ? new exceptions.LogicalError('Incorrect request params')
      : err
  }
  let res: AnalyzeResult
  let timeout = 0
  let attempts = 0
  const getAnalyzeResults = async () => {
    if (timeout === 0) return Azure.getAnalyzeResults(resultUrl, modelId)
    return new Promise<AnalyzeResult>((resolve, reject) => {
      setTimeout(async () => {
        try {
          resolve(await Azure.getAnalyzeResults(resultUrl, modelId))
        } catch (e) {
          reject(e)
        }
      }, timeout)
    })
  }
  do {
    res = await getAnalyzeResults()
    timeout += 1000 * attempts
    ++attempts
  } while (res && ['running', 'notStarted'].includes(res.status))
  const { fields } = res.analyzeResult.documentResults[0]
  console.log(JSON.stringify(fields, null, '  '))
  const threshold = modelId ? 0.1 : confidenceThreshold
  const _type =
    fields.DocumentType &&
    fields.DocumentType.valueString &&
    fields.DocumentType.confidence >= threshold
      ? fields.DocumentType.valueString.toLowerCase()
      : 'invoice'
  // Get base values from the ocr invoice
  let invoice_total =
    fields.InvoiceTotal &&
    fields.InvoiceTotal.valueNumber &&
    fields.InvoiceTotal?.confidence >= threshold
      ? fields.InvoiceTotal.valueNumber || 0
      : 0
  let amount_due =
    fields.AmountDue &&
    fields.AmountDue?.valueNumber &&
    fields.AmountDue?.confidence >= threshold
      ? fields.AmountDue.valueNumber || 0
      : 0
  const deposit =
    fields.Deposit &&
    fields.Deposit?.valueNumber &&
    fields.Deposit?.confidence >= threshold
      ? fields.Deposit.valueNumber || 0
      : 0
  const total_tax =
    fields.TotalTax && fields.TotalTax.confidence >= threshold
      ? fields.TotalTax.valueNumber || 0
      : 0
  let invoice_due_date =
    fields.DueDate && fields.DueDate.confidence >= threshold
      ? fields.DueDate.valueDate
      : undefined
  const shippingTo = fields.ShippingTo ? fields.ShippingTo.valueString : ''
  const shippingAddress = fields.ShippingAddress
    ? fields.ShippingAddress.valueString
    : ''

  // Calculate amount what needs to be saved
  let total_amount: number
  let tax_amount: number
  let sub_total: number

  if (
    fields.Operations?.valueArray &&
    fields.Operations.valueArray.length > 0
  ) {
    amount_due = 0
    invoice_total = 0
    const bal_due = fields.Operations.valueArray.find(
      (o) => o.valueObject?.Action.text === 'BAL DUE ASGNMENT',
    )
    if (bal_due) {
      amount_due = bal_due.valueObject.Amount.valueNumber
      invoice_due_date = bal_due.valueObject.Date.valueDate
    }
  }
  // Business Logic reference on Miro: https://miro.com/app/board/uXjVPLtp1mU=/
  if (amount_due > 0) {
    if (amount_due === invoice_total) {
      total_amount = invoice_total
      tax_amount = total_tax
      sub_total = total_amount - tax_amount
    } else {
      total_amount = amount_due
      tax_amount = 0
      sub_total = total_amount
    }
  } else {
    if (deposit !== 0) {
      // It is negative, when someone has already paid a deposit to the invoice
      total_amount = invoice_total
      tax_amount = 0
      sub_total = total_amount
    } else {
      total_amount = invoice_total
      tax_amount = total_tax
      sub_total = total_amount - tax_amount
    }
  }

  total_amount = math.round(total_amount || 0, 2)
  tax_amount = math.round(tax_amount || 0, 2)
  let type = EInvoiceType.INVOICE
  switch (_type) {
    case 'statement':
      type = EInvoiceType.STATEMENT
      break
    case 'order':
    case 'sale order':
      type = EInvoiceType.SALE_ORDER
      break
    case 'quotation':
    case 'quote':
      type = EInvoiceType.QUOTE
      break
  }
  console.log('Taken by name ==>', fields.TakenByName)
  return {
    type,
    invoice_due_date,
    business_name:
      fields.CustomerName && fields.CustomerName.confidence >= threshold
        ? fields.CustomerName.valueString
        : undefined,
    invoice_number:
      fields.InvoiceId && fields.InvoiceId.confidence >= threshold
        ? fields.InvoiceId.valueString
        : undefined,
    quote_number:
      fields.QuoteId && fields.QuoteId.confidence >= threshold
        ? fields.QuoteId.valueString?.replace(/^#+/, '')
        : undefined,
    invoice_date:
      fields.InvoiceDate && fields.InvoiceDate.confidence >= threshold
        ? fields.InvoiceDate.valueDate
        : undefined,
    expiration_date:
      fields.ExpirationDate && fields.ExpirationDate.confidence >= threshold
        ? fields.ExpirationDate.valueDate
        : undefined,
    material_subtotal: isFinite(sub_total)
      ? math.round(sub_total, 2)
      : undefined,
    total_amount: isFinite(total_amount)
      ? math.round(total_amount, 2)
      : undefined,
    tax_amount: isFinite(tax_amount) ? math.round(tax_amount, 2) : undefined,
    attention: shippingTo ?? '',
    address: shippingAddress ?? '',
    addressType: shippingTo
      ? EInvoiceAddressType.DELIVERY
      : EInvoiceAddressType.PICKUP,
    taken_by_name:
      fields.TakenByName && fields.TakenByName.confidence >= threshold
        ? fields.TakenByName.valueString
        : undefined,
  }
}

type InvoiceData = UnPromisify<ReturnType<typeof extractInvoiceData>>

async function searchCustomer(companyId: string, business_name: string) {
  const customers: ICustomerAccount[] = await CustomerAccount.find({
    company_id: companyId,
    isDeleted: false,
  })

  const matched = new Map<number, ICustomerAccount>()

  for (const customer of customers) {
    const firstName = customer.first_name?.trim().toLowerCase() || ''
    const lastName = customer.last_name?.trim().toLowerCase() || ''

    const result = stringSimilarity.findBestMatch(
      business_name.toLowerCase(),
      [
        firstName.concat(lastName),
        firstName.concat(' ', lastName),
        lastName.concat(', ', firstName),
        customer.name?.trim().toLowerCase() || '',
        customer.display_name?.trim().toLowerCase() || '',
      ].filter(Boolean),
    )

    if (result.bestMatch?.rating > 0.8) {
      console.log(business_name, result)
      matched.set(result.bestMatch.rating, customer)
    }
  }

  if (matched.size === 0) return null
  const max = math.max([...matched.keys()])
  return matched.get(max)
}

const requiredInvoiceFields = {
  business_name: 'Business name',
  invoice_date: 'Date invoice',
  material_subtotal: 'Amount',
} as const

export default async function recognizeInvoice(
  data: EventData & EmailContent & { companyId: string },
) {
  await getEnvironmentVariables()
  await connectToDatabase()

  console.log(data)
  const { bucket, key, email, companyId } = data
  const invoiceUrl = await getInvoiceUrl({ bucket, key })
  console.log(invoiceUrl)

  const company = await Company.findOne<ICompany>({ _id: companyId })
  if (!company) throw new exceptions.LogicalError('Company not found')
  console.log('Company settings', company?.settings)

  if (email === 'batch_upload') {
    const invoiceData = await extractInvoiceData(
      invoiceUrl,
      company?.settings.OCRModelId,
    )
    console.log(invoiceData)

    let { business_name, invoice_number } = invoiceData
    if (!invoice_number) {
      const hashids = new Hashids(undefined, 5)
      invoice_number = hashids.encode([moment().unix()])
      invoiceData.invoice_number = invoice_number
    }
    if (business_name) {
      const customer = await searchCustomer(companyId, business_name)

      console.log(customer, 'customer result')
      Object.assign(invoiceData, { customer: customer?.toObject() })
    }

    await AwsService.putS3File(
      bucket,
      `${key}.json`,
      JSON.stringify(invoiceData),
    )
    console.log(invoiceData, '--- end ---')
    return
  } else {
    const invoiceData = await extractInvoiceData(
      invoiceUrl,
      company?.settings.OCRModelId,
    )
    const { business_name } = invoiceData
    console.log(invoiceData)
    if (!business_name) {
      return handleErrorMessages({
        email,
        attachmentsCount: 1,
        reason: `Business name cannot be read from attachment. The invoice cannot be processed.`,
      })
    }

    const customer = await searchCustomer(companyId, business_name)
    console.log(customer, 'customer result')

    const invoiceKey = utils.generateFileKey({
      fileKeyPrimary: 'invoices',
      companyId,
      fileName: key,
    })

    await AwsService.copyS3File({
      sourceBucket: bucket,
      sourceKey: key,
      destinationBucket: `${process.env.LP_MODE}.uw1.linqpal-user-assets`,
      destinationKey: invoiceKey,
    })

    const users = await UserRole.aggregate([
      {
        $match: {
          company_id: companyId,
          $or: [{ role: 'Owner' }, { role: 'Admin' }],
        },
      },
      {
        $lookup: {
          from: 'users',
          localField: 'sub',
          foreignField: 'sub',
          as: 'user',
        },
      },
      { $project: { user: { $first: '$user' } } },
      { $match: { user: { $ne: null } } },
    ])

    if (users.length === 0) {
      return handleErrorMessages({
        email,
        attachmentsCount: 1,
        reason: 'No user found',
      })
    }
    console.log('userInfo', JSON.stringify(users[0]))
    const missing_fields = Object.keys(requiredInvoiceFields).filter(
      (k) =>
        !invoiceData[k as keyof InvoiceData] &&
        invoiceData[k as keyof InvoiceData] !== 0,
    )

    console.log(missing_fields, 'missing-files ------------------------')
    let { invoice_number } = invoiceData
    if (!invoice_number) {
      const hashids = new Hashids(undefined, 5)
      invoice_number = hashids.encode([moment().unix()])
    }
    const companyUserRoles = await UserRole.find({
      company_id: companyId,
    }).populate('user')
    const companyUsers = companyUserRoles.map((role) => (role as any).user)
    console.log(
      'company users and taken by name',
      companyUsers,
      invoiceData.taken_by_name,
    )
    const takenByUser = companyUsers.find(
      (user) =>
        `${user?.firstName} ${user?.lastName}` === invoiceData.taken_by_name,
    )

    const invoiceStatus =
      invoiceData.material_subtotal === 0
        ? dictionaries.invoiceSchemaStatus.paid
        : missing_fields.length > 0 || !customer || !invoice_number
        ? dictionaries.invoiceSchemaStatus.draft
        : dictionaries.invoiceSchemaStatus.placed

    const invoiceId: string = await invoicesService.saveInvoice(
      {
        ...invoiceData,
        company_id: companyId,
        customer_account_id: customer?.id,
        status: invoiceStatus,
        invoice_document: invoiceKey,
        document_name: key.split('/').pop(),
        invoice_number,
        takenById: takenByUser?._id,
      } as any,
      company,
      users[0].user,
      process.env.LP_MODE === 'prod'
        ? 'https://app.bluetape.com'
        : process.env.LP_MODE === 'dev'
        ? 'http://localhost:3000'
        : `https://${process.env.LP_MODE}.bluetape.com`,
      true,
    )

    await handleSuccessMessage({
      ...data,
      invoiceNumber: invoice_number,
      documentType: invoiceData.type,
      invoiceStatus,
      errors: missing_fields,
    })
    return { invoiceId }
  }
}
