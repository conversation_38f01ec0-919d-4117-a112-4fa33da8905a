import React, { createContext, useContext, useState } from 'react'
import { LenderApplicationStore } from './Store/LenderApplicationStore'

const LenderApplicationContext = createContext<LenderApplicationStore | null>(
  null,
)

export const useLenderApplication = () => {
  const context = useContext(LenderApplicationContext)

  if (!context) {
    throw new Error(
      'useLenderApplication must be used within a LenderApplicationProvider',
    )
  }

  return context
}

export const LenderApplicationProvider = ({ children }) => {
  const [store] = useState(() => new LenderApplicationStore())

  return (
    <LenderApplicationContext.Provider value={store}>
      {children}
    </LenderApplicationContext.Provider>
  )
}
