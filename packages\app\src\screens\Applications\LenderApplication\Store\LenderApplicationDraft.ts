import {
  ILenderApplicationData,
  ILenderApplicationDraft,
} from '@linqpal/models/src/applications/lender/ILenderApplicationDraft'
import { makeAutoObservable } from 'mobx'
import { LenderApplicationValidator } from '@linqpal/models/src/applications/lender/LenderApplicationValidator'

export class LenderApplicationDraft implements ILenderApplicationDraft {
  private _validator: LenderApplicationValidator

  private _id: string | undefined

  data: ILenderApplicationData

  initialStep: string

  currentStep: string

  visitedSteps: string[]

  get id(): string | undefined {
    return this._id
  }

  set id(value: string | undefined) {
    this._id = value
  }

  constructor(
    draft?: Partial<ILenderApplicationDraft> & {
      initialStep?: string
      currentStep?: string
      visitedSteps?: string[]
    },
  ) {
    this._id = draft?.id

    this.initialStep = draft?.initialStep || ''
    this.currentStep = draft?.currentStep || draft?.initialStep || ''

    this.data = {
      sponsor: draft?.data?.sponsor || {},
      businessEntity: draft?.data?.businessEntity || {},
      currentProject: draft?.data?.currentProject || {},
      previousProjects: draft?.data?.previousProjects || [],
    }

    this.visitedSteps = draft?.visitedSteps || []

    this._validator = new LenderApplicationValidator(this)

    makeAutoObservable(this, {
      id: false,
    })
  }

  toJson(): ILenderApplicationDraft {
    return {
      id: this._id,
      initialStep: this.initialStep,
      currentStep: this.currentStep,
      visitedSteps: this.visitedSteps,
      data: this.data,
    }
  }

  validate(path: string): boolean {
    return this._validator.validate(path)
  }
}
