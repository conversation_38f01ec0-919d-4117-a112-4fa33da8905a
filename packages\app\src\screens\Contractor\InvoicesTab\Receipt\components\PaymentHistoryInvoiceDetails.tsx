import React from 'react'
import { StyleSheet } from 'react-native'
import { observer } from 'mobx-react'
import { useTranslation } from 'react-i18next'
import { Divider } from 'react-native-paper'
import CompanyHeader from '../../Invoice/CompanyHeader'
import { OtherInvoiceInfo } from './OtherInvoiceInfo'
import ReceiptStore from '../ReceiptStore'
import useIsMobile from '../../../PayablesTab/hooks/useIsMobile'
import {
  PaidInvoiceStatuses,
  PaymentProcessingInvoiceStatuses,
} from '../../../PayablesTab/enums'

export const PaymentHistoryInvoiceDetails = observer(() => {
  const { t } = useTranslation('global')
  const isMobile = useIsMobile()
  const { invoice } = ReceiptStore
  const builderCreatedInvoice = !!invoice?.supplierInvitationDetails
  const company = builderCreatedInvoice
    ? {
        phone: invoice?.supplierInvitationDetails?.phone,
        name: invoice.supplierInvitationDetails?.name,
      }
    : {
        phone: invoice?.company?.phone,
        name: invoice.company?.name,
      }

  const remainingAmount =
    PaymentProcessingInvoiceStatuses.includes(invoice.status) ||
    PaidInvoiceStatuses.includes(invoice.status)
      ? 0
      : invoice.totalRemainingAmount

  return (
    <>
      <CompanyHeader
        company={company}
        totalAmount={remainingAmount}
        style={[styles.companyHeader, { width: isMobile ? '100%' : 360 }]}
        amountLabel={t('PaymentHistoryInvoice.totalAmountDue')}
        status={invoice.status}
        showInvoiceStatus
      />

      <Divider style={{ marginBottom: 14, width: isMobile ? '100%' : 360 }} />
      <OtherInvoiceInfo invoice={invoice} isMobile={isMobile} />
    </>
  )
})

const styles = StyleSheet.create({
  text: {
    fontSize: 18,
    fontWeight: '600',
    color: '#335C75',
  },
  companyHeader: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 5,
    paddingTop: 20,
    flexDirection: 'row',
    marginBottom: 14,
  },
})
