import { InvoicePaymentType } from '@linqpal/models'

// casing is aligned with DB to handle sorting and consistency with InvoicesHistory.js
export interface IInvoiceTableItem {
  invoice_number: string
  payersInfo?: string[]
  customer_name: string
  contact: string
  status: string
  ar_advance_status: string
  payment_type: InvoicePaymentType
  totalRemainingAmount: number
  totalPaidAmount: number
  totalProcessingAmount: number
  customerFeeAndInterest: string
  invoice_date: string
  invoice_due_date: string
  total_amount: string
  invoice: any // a reference to a raw api result
}

export interface IInvoiceActionRequiredItem {
  invoice_number: string
  payersInfo?: string[]
  customer_name: string
  contact: string
  status: string
  ar_advance_status: string
  payment_type: InvoicePaymentType
  invoice_date: string
  invoice_due_date: string
  total_amount: string
  invoice: any
}
