import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react'
import { View } from 'react-native'
import { routes } from '@linqpal/models'
import { BtButton, BtButtonProps, BtText } from '@linqpal/components/src/ui'
import { PlaidLinkOptions, usePlaidLink } from 'react-plaid-link'

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
interface Props extends BtButtonProps {
  onConnectSuccess?: () => void
  text: string
  disabled?: boolean
  style: any
  highlightRountingNumber?: string
}

export type PlaidConnectButtonRef = { createToken: () => void }

export const PlaidConnectButton = forwardRef<PlaidConnectButtonRef, Props>(
  (
    { onConnectSuccess, text, disabled, highlightRountingNumber, ...rest },
    ref,
  ) => {
    // TODO: VK: use usePlaidConnect() hook
    const [linkToken, setLinkToken] = useState<string>('')

    const createToken = useCallback(() => {
      routes.plaid.createLinkToken(highlightRountingNumber).then((res) => {
        setLinkToken(res.link_token)
      })
    }, [highlightRountingNumber])

    useImperativeHandle(
      ref,
      () => ({
        createToken,
      }),
      [createToken],
    )

    const onSuccess = useCallback((publicToken) => {
      routes.plaid
        .exchangePublicToken(publicToken)
        .then(() => {
          onConnectSuccess && onConnectSuccess()
        })
        .catch((err) => console.log(err))
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])

    const config: PlaidLinkOptions = useMemo(
      () => ({
        token: linkToken,
        onSuccess,
        onExit: (err, metadata) => {
          console.log(
            `Exited early. Error: ${JSON.stringify(
              err,
            )} Metadata: ${JSON.stringify(metadata)}`,
          )
        },
        // onEvent: (eventName, metadata) => {
        //   console.log(
        //     `Event ${eventName}, Metadata: ${JSON.stringify(metadata)}`,
        //   )
        // },
      }),
      [linkToken, onSuccess],
    )

    const { open } = usePlaidLink(config)

    useEffect(() => {
      !!open && open()
    }, [open])

    return (
      <View>
        <BtButton
          onPress={() => createToken()}
          disabled={disabled}
          {...rest}
          testID={'PlaidConnectButton'}
        >
          {(props) => <BtText {...props}>{text}</BtText>}
        </BtButton>
      </View>
    )
  },
)
