import { makeAutoObservable } from 'mobx'
import {
  ApplicationUserRole,
  IApplicationUser,
  IUnifiedApplicationData,
  IUnifiedApplicationDraft,
} from '../../../../../../models/src/applications/unified/IUnifiedApplicationDraft'
import { UnifiedApplicationValidator } from '@linqpal/models'
import { IUnifiedApplicationOptions } from '@linqpal/models/src/applications/unified/IUnifiedApplicationOptions'

export class UnifiedApplicationDraft implements IUnifiedApplicationDraft {
  private _validator: UnifiedApplicationValidator

  data: IUnifiedApplicationData

  users: IApplicationUser[]

  initialStep: string

  currentStep: string

  visitedSteps: string[]

  constructor(data?: IUnifiedApplicationDraft) {
    this.initialStep = data?.initialStep || ''
    this.currentStep = data?.currentStep || data?.initialStep || ''

    this.data = {
      businessInfo: data?.data?.businessInfo || {},
      finance: data?.data?.finance || {},
      coOwners: data?.data?.coOwners || [],
      primaryBankAccount: data?.data?.primaryBankAccount,
    }

    this.users = data?.users || []
    this.visitedSteps = data?.visitedSteps || []

    this._validator = new UnifiedApplicationValidator(this)

    makeAutoObservable(this)
  }

  get invitedOwner(): IApplicationUser | undefined {
    return this.users.find(
      (user) => user.role === ApplicationUserRole.Owner && !user.id,
    )
  }

  get totalOwnershipPercentage(): number {
    const ownerUser = this.users.find(
      (user) => user.role === ApplicationUserRole.Owner,
    )
    const businessOwnerPercentage = ownerUser
      ? ownerUser.ownershipPercentage || 0
      : 0

    return this.totalCoOwnersPercentage + businessOwnerPercentage
  }

  get totalCoOwnersPercentage(): number {
    const coOwners = this.data?.coOwners || []
    if (!coOwners || coOwners.length === 0) return 0

    return coOwners.reduce(
      (sum, coOwner) => sum + (coOwner.percentOwned || 0),
      0,
    )
  }

  validate(path: string, options: IUnifiedApplicationOptions): boolean {
    return this._validator.validate(path, options)
  }
}
