import { IBankAccount, ICompany } from '../../models/types'

export const getSourceBankAccount = (
  bankAccount: Partial<IBankAccount> | undefined,
  builderCompany: ICompany,
): Partial<IBankAccount> | undefined => {
  let result: Partial<IBankAccount> | undefined = bankAccount

  result =
    bankAccount ||
    builderCompany.bankAccounts?.find((e) => e.isPrimaryForCredit) ||
    builderCompany.bankAccounts?.find((e) => e.isPrimary) ||
    builderCompany.bankAccounts![0]

  return result
}

export const isPaymentMethodCard = (
  bankAccount: Partial<IBankAccount> | undefined,
  cardToken: string | undefined,
): boolean => {
  if (bankAccount?.paymentMethodType === 'card') return true
  if (cardToken) return true

  return false
}
