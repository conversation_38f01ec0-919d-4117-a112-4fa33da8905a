import React, { FC } from 'react'
import { BtButton, BtText } from '@linqpal/components/src/ui'
import { View } from 'react-native'
import { OwnerTypes } from '@linqpal/models/src/dictionaries/UnifiedApplication'
import { formatAddress } from '@linqpal/models/src/helpers/addressFormatter'
import { Divider } from '@ui-kitten/components'
import { useTranslation } from 'react-i18next'
import { observer } from 'mobx-react'
import { formatDomesticPhone } from '@linqpal/models/src/helpers/phoneFormatter'
import { useUnifiedApplication } from '../../../UnifiedApplicationContext'
import { CoOwnerValidator, ICoOwner } from '@linqpal/models'
import { ownerReviewStyles } from '../../Review/Components/Review/ownerReviewStyles'
import { OwnerPropertyRow } from '../../Review/Components/Review/OwnerPropertyRow'
import { OwnerIcon } from './OwnerIcon'
import { Spacer } from '../../../../../../ui/atoms'
import { Steps } from '@linqpal/models/src/applications/unified/UnifiedApplicationSteps'

export const CoOwnersReview: FC = observer(() => {
  const { t } = useTranslation('application')

  const store = useUnifiedApplication()
  const coOwners = store.draft.data?.coOwners || []

  return (
    <>
      {coOwners.map((coOwner: ICoOwner) => (
        <>
          <View style={ownerReviewStyles.container} key={coOwner.id}>
            <OwnerIcon
              isOwnerFilled={new CoOwnerValidator(coOwner).validateCoOwner()}
            />
            <View style={ownerReviewStyles.properties}>
              {coOwner.firstName || coOwner.lastName || coOwner.entityName ? (
                <BtText style={ownerReviewStyles.ownerName}>
                  {coOwner.type === OwnerTypes.INDIVIDUAL
                    ? `${coOwner.firstName} ${coOwner.lastName}`
                    : coOwner.entityName}
                </BtText>
              ) : null}
              <View>
                <OwnerPropertyRow
                  label={t('Preview.CoOwner')}
                  value={t('Preview.Percentage', {
                    percentage: coOwner.percentOwned,
                  })}
                />
                <OwnerPropertyRow
                  label={t(
                    coOwner.type === OwnerTypes.INDIVIDUAL
                      ? 'CoOwners.HomeAddress'
                      : 'Preview.LegalAddress',
                  )}
                  value={formatAddress(coOwner)}
                />
                {coOwner.type === OwnerTypes.INDIVIDUAL && (
                  <OwnerPropertyRow
                    label={t('CoOwners.Birthday')}
                    value={coOwner.birthday}
                  />
                )}
                {coOwner.type === OwnerTypes.INDIVIDUAL && (
                  <OwnerPropertyRow
                    label={t('CoOwners.SSN')}
                    value={coOwner.ssn}
                    secured
                  />
                )}
                {coOwner.type === OwnerTypes.ENTITY && (
                  <OwnerPropertyRow
                    label={t('Preview.EIN')}
                    value={coOwner.ein}
                    secured
                  />
                )}
                <OwnerPropertyRow
                  label={t('CoOwners.PhoneNumber')}
                  value={formatDomesticPhone(coOwner.phone)}
                />
                <OwnerPropertyRow
                  label={t('CoOwners.EmailAddress')}
                  value={coOwner.email}
                />
                {coOwner.type === OwnerTypes.ENTITY && (
                  <OwnerPropertyRow
                    label={t('Preview.AuthorizedRepresentative')}
                    value={`${coOwner.firstName} ${coOwner.lastName}`}
                  />
                )}
              </View>
            </View>
            <View style={ownerReviewStyles.editButtonContainer}>
              <BtButton
                appearance={'ghost'}
                size={'small'}
                status={'basic'}
                onPress={() => {
                  store.goToStep(Steps.coOwners.coOwners)
                  store.coOwnersStore.edit(coOwner)
                }}
                // eslint-disable-next-line i18next/no-literal-string
                iconLeft="edit-outline"
              >
                {t('Review.Edit')}
              </BtButton>
            </View>
          </View>
          <Spacer height={20} />
          <Divider />
        </>
      ))}
    </>
  )
})
