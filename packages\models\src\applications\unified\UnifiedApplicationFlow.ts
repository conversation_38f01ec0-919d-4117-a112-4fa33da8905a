import { Steps as $ } from './UnifiedApplicationSteps'
import {
  ApplicationUserRole,
  IUnifiedApplicationDraft,
} from './IUnifiedApplicationDraft'
import { Flow } from '../FlowController'
import { MAIN_ID } from '../../dictionaries'
import { ApplicationType } from '../../dictionaries/applicationType'
import { CoOwnerValidator } from './validation/CoOwnerValidator'
import { CompanyStatus } from '../../mst'
import { IUnifiedApplicationOptions } from './IUnifiedApplicationOptions'

const choice = <TDocument, TOptions>(
  path: string,
  selector: (document: TDocument, options: TOptions) => string,
) => ({ path, selector })

export class UnifiedApplicationFlow {
  definition: Flow<IUnifiedApplicationDraft, IUnifiedApplicationOptions> = [
    /****************
     * Business Info
     ****************/
    choice('businessInfo.firstStepChoice', (draft) => draft?.initialStep),
    $.businessInfo.email, // TODO: VK: Unified: ApplicationStore.updateUserEmail(),
    $.businessInfo.category, // TODO: VK: Unified: ApplicationStore.updateCompanyCategory(),
    choice('businessInfo.categoryChoice', (draft) => {
      return draft.data?.businessInfo?.category === MAIN_ID.SUB_CONTRACTOR
        ? $.businessInfo.trade
        : $.businessInfo.businessName
    }),
    $.businessInfo.trade,
    $.businessInfo.businessName,
    $.businessInfo.businessPhone,
    $.businessInfo.businessAddress,
    choice('businessInfo.businessAddressChoice', (_, options) =>
      options.type === ApplicationType.InHouseCredit
        ? $.businessInfo.type
        : $.businessInfo.startDate,
    ),
    $.businessInfo.startDate,
    $.businessInfo.type,
    $.businessInfo.ein,

    /****************
     * Finance
     ****************/
    $.finance.revenue,
    choice('finance.revenueChoice', (_, options) =>
      options.type === ApplicationType.InHouseCredit
        ? $.bank.primaryAccount
        : $.finance.debt,
    ),
    $.finance.debt,
    choice('finance.debtChoice', (_, options) =>
      options.type === ApplicationType.Supplier
        ? $.finance.arAdvanceRequestedLimit
        : $.finance.creditLimit,
    ),
    $.finance.creditLimit,
    choice(
      'finance.gotoBusinessOwner',
      () => 'businessOwner.alreadySubmittedChoice',
    ),
    $.finance.arAdvanceRequestedLimit,

    /****************
     * Business Owner
     ****************/
    choice('businessOwner.alreadySubmittedChoice', (_, options) =>
      // as in legacy version - if Supplier app is submitted, we skip isOwner and related
      // review - should flow behave the same way if Credit app is submitted before Supplier?
      this.hasSupplierAppSubmitted(options)
        ? $.businessOwner.address
        : $.businessOwner.isOwner,
    ),
    $.businessOwner.isOwner,
    choice('businessOwner.isOwnerChoice', (draft, options) =>
      this.isOwner(draft, options)
        ? $.businessOwner.ownershipPercentage
        : $.businessOwner.isAuthorized,
    ),
    $.businessOwner.ownershipPercentage,
    choice('businessOwner.goToOwnerAddress', () => $.businessOwner.address), // will be replaced with actual logic
    $.businessOwner.isAuthorized,
    choice('businessOwner.isAuthorizedChoice', (draft, options) =>
      this.isAuthorized(draft, options)
        ? $.businessOwner.address
        : $.businessOwner.authorizedDetails,
    ),
    $.businessOwner.authorizedDetails, // TODO: VK: Unified: ApplicationStore.inviteUser('authorisedDetails')
    $.businessOwner.address,
    $.businessOwner.birthdate,
    $.businessOwner.ssn,

    /****************
     * Co-owners
     ****************/
    choice('coOwners.coOwnersChoice', (draft, options) => {
      return this.canEditCoOwners(draft, options)
        ? $.coOwners.coOwners
        : $.bank.primaryAccount
    }),
    $.coOwners.coOwners,

    /****************
     * Bank
     ****************/
    $.bank.primaryAccount,

    /****************
     * Review
     ****************/
    $.review.review,
  ]

  private isOwner(
    draft: IUnifiedApplicationDraft,
    options: IUnifiedApplicationOptions,
  ): boolean {
    return draft.users.some(
      (user) =>
        user.id === options.userId && user.role === ApplicationUserRole.Owner,
    )
  }

  private isAuthorized(
    draft: IUnifiedApplicationDraft,
    options: IUnifiedApplicationOptions,
  ): boolean {
    return draft.users.some(
      (user) =>
        user.id === options.userId &&
        user.role === ApplicationUserRole.Authorized,
    )
  }

  private hasSupplierAppSubmitted(options: IUnifiedApplicationOptions) {
    return [
      CompanyStatus.Applied,
      CompanyStatus.Approved,
      CompanyStatus.Rejected,
    ].includes(options.companyStatus as CompanyStatus)
  }

  private canEditCoOwners(
    draft: IUnifiedApplicationDraft,
    options: IUnifiedApplicationOptions,
  ): boolean {
    const owner = draft.users.find(
      (user) =>
        user.id === options.userId && user.role === ApplicationUserRole.Owner,
    )

    const ownerPercentage = owner?.ownershipPercentage || 0

    const canHaveCoOwners =
      100 - ownerPercentage >= CoOwnerValidator.MinCoOwnerPercentage
    const hasCoOwners = draft.data.coOwners?.length > 0

    return canHaveCoOwners || hasCoOwners
  }
}
