import { observer } from 'mobx-react'
import { colors } from '@linqpal/components/src/theme'
import { StyleSheet, TouchableOpacity } from 'react-native'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { useResponsive } from '@linqpal/components/src/hooks'
import { useUnifiedApplication } from '../UnifiedApplicationContext'
import { BtPlainText } from '@linqpal/components/src/ui'
import { BlackArrowBack, IconArrowBack } from '../../../../assets/icons'

export const WizardBackButton = observer(() => {
  const { sm } = useResponsive()
  const { t } = useTranslation('application')

  const store = useUnifiedApplication()

  if (!store.canGoBack) return null

  const color = sm ? colors.accentText : colors.primary

  const handlePress = () => {
    const editorBackHandler = store.stepOptions.onMoveBack

    if (!editorBackHandler || !editorBackHandler(store)) {
      store.moveBack()
    }
  }

  return (
    <div
      style={{
        position: 'sticky',
        top: 0,
        zIndex: 1000,
        backgroundColor: 'white',
        marginTop: 20,
      }}
    >
      <TouchableOpacity
        style={styles.wrapper}
        onPress={handlePress}
        disabled={store.isSubmitting}
        testID="UnifiedApplication.Wizard.BackButton"
      >
        {sm ? (
          <IconArrowBack stroke={color} />
        ) : (
          <BlackArrowBack stroke={color} style={{ height: 20 }} />
        )}

        <BtPlainText style={sm ? styles.label : styles.labelMobile}>
          {t('Back')}
        </BtPlainText>
      </TouchableOpacity>
    </div>
  )
})

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  label: {
    fontWeight: '500',
    color: colors.accentText,
    marginTop: -2,
    marginLeft: 15,
  },
  labelMobile: {
    fontWeight: '700',
    marginTop: -2,
    color: colors.primary,
    marginLeft: 5,
  },
})
