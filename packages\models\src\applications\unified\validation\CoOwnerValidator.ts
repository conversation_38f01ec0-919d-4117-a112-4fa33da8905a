import { ICoOwner } from '../IUnifiedApplicationDraft'
import { OwnerTypes } from '../../../dictionaries/UnifiedApplication'
import {
  validateDate,
  validateEmail,
  validateSsn,
  validateUSPhoneNumber,
  validateZip,
} from '../../../helpers/validations'

export class CoOwnerValidator {
  private coOwner: ICoOwner

  public static readonly MinCoOwnerPercentage = 25

  constructor(coOwner: ICoOwner) {
    this.coOwner = coOwner
  }

  validate(field: keyof ICoOwner): boolean {
    switch (field) {
      // Common fields
      case 'type':
        return Object.values(OwnerTypes).includes(this.coOwner.type as any)

      case 'percentOwned':
        return (
          this.coOwner.percentOwned >= CoOwnerValidator.MinCoOwnerPercentage &&
          this.coOwner.percentOwned <= 100
        )

      case 'address':
        return !!this.coOwner.address

      case 'city':
        return !!this.coOwner.city

      case 'state':
        return !!this.coOwner.state

      case 'zip':
        return !!this.coOwner.zip && validateZip(this.coOwner.zip)

      case 'phone':
        return !!this.coOwner.phone && validateUSPhoneNumber(this.coOwner.phone)

      case 'email':
        return !!this.coOwner.email && validateEmail(this.coOwner.email)

      // Individual owner fields
      case 'firstName':
        return !!this.coOwner.firstName

      case 'lastName':
        return !!this.coOwner.lastName

      case 'birthday':
        return this.coOwner.type === OwnerTypes.INDIVIDUAL
          ? !!this.coOwner.birthday && validateDate(this.coOwner.birthday, 1900)
          : true // Not required for entity

      case 'ssn':
        return this.coOwner.type === OwnerTypes.INDIVIDUAL
          ? !!this.coOwner.ssn && validateSsn(this.coOwner.ssn)
          : true // Not required for entity

      // Entity owner fields
      case 'entityName':
        return this.coOwner.type === OwnerTypes.ENTITY
          ? !!this.coOwner.entityName
          : true // Not required for individual

      case 'ein':
        return this.coOwner.type === OwnerTypes.ENTITY
          ? !!this.coOwner.ein && validateSsn(this.coOwner.ein) // EIN uses same validation as SSN
          : true // Not required for individual

      default:
        throw new Error(`Unknown CoOwner validation field: ${field}`)
    }
  }

  public validateCoOwner(): boolean {
    const fields: Array<keyof ICoOwner> = [
      'type',
      'percentOwned',
      'address',
      'city',
      'state',
      'zip',
      'phone',
      'email',
      'firstName',
      'lastName',
      'birthday',
      'ssn',
      'entityName',
      'ein',
    ]

    return fields.every((field) => this.validate(field))
  }
}
