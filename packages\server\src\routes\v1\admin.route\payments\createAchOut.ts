import { cbw, Operation } from '@linqpal/common-backend'
import {
  IBankAccount,
  ICompany,
} from '@linqpal/common-backend/src/models/types'
import {
  OPERATION_STATUS,
  OPERATION_TYPES,
} from '@linqpal/models/src/dictionaries'
import moment from 'moment'
import { adminRequired } from '../../../../services/auth.service'
import transactional from '../../../../services/transactional.service'
import { Request, Response, NextFunction } from 'express'
import { ControllerItem } from 'src/routes/controllerItem'

interface ReqBody {
  company: ICompany
  bankAccount: IBankAccount
  amount: number
  reason?: string | null
}

export const createAchOut: ControllerItem = {
  middlewares: {
    pre: [adminRequired, ...transactional.pre],
    post: transactional.post,
  },
  async post(
    req: Request<unknown, unknown, ReqBody>,
    _res: Response,
    next: NextFunction,
  ) {
    const customer = await cbw.getRecipient(
      req.body.company._id.toString(),
      req.body.bankAccount._id?.toString() || null,
    )

    const amount = req.body.amount

    const [operation] = await Operation.create(
      [
        {
          amount,
          date: moment().toDate(),
          type: OPERATION_TYPES.ACH.OUT,
          status: OPERATION_STATUS.PLACED,
          metadata: { payee_id: req.body.company._id, payment_method: 'ach' },
        },
      ],
      { session: req.session },
    )

    await cbw.achOut(
      [operation],
      {
        fee: 0.0,
        currency: 'USD',
        transactionDateTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        reason: req.body.reason || 'ACH OUT',
        ...customer,
      },
      req.session,
    )

    return next()
  },
}
