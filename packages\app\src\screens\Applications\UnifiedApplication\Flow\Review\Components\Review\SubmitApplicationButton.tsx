import React from 'react'
import { observer } from 'mobx-react'
import { BtButton } from '@linqpal/components/src/ui'
import { useUnifiedApplication } from '../../../../UnifiedApplicationContext'
import { StyleSheet } from 'react-native'
import { useTranslation } from 'react-i18next'
import RootStore from '../../../../../../../store/RootStore'

export const SubmitApplicationButton = observer(() => {
  const { t } = useTranslation('application')
  const store = useUnifiedApplication()

  return (
    <BtButton
      onPress={() => store.submitApplication()}
      disabled={!store.canSubmit || store.isSubmitting || RootStore.isBusy}
      style={styles.button}
      loading={store.isSubmitting}
      testID="UnifiedApplication.SubmitApplicationButton"
    >
      {t('Review.AgreeSubmit')}
    </BtButton>
  )
})

const styles = StyleSheet.create({
  button: {
    width: 320,
    marginBottom: 36,
  },
})
