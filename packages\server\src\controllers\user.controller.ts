import {
  Company,
  crypt,
  CustomerAccount,
  emailService,
  getApiURL,
  getBranding,
  Invitation,
  signupService,
  User,
  UserRole,
} from '@linqpal/common-backend'
import { KeysOfType } from '@linqpal/common-backend/src/helpers/helperTypes'
import {
  ICompany,
  IInvitation,
  IUser,
} from '@linqpal/common-backend/src/models/types'
import { exceptions, RoleStatus } from '@linqpal/models'
import {
  customerStatus,
  invitationTypes,
  UtmSourceType,
} from '@linqpal/models/src/dictionaries'
import { CustomerAccountType } from '@linqpal/models/src/dictionaries/customerAccountType'
import { MAIN_ID } from '@linqpal/models/src/dictionaries/onboarding'
import md5 from 'crypto-js/md5'
import admin from 'firebase-admin'
import { parsePhoneNumber } from 'libphonenumber-js'
import isArray from 'lodash/isArray'
import isNull from 'lodash/isNull'
import isObjectLike from 'lodash/isObjectLike'
import isString from 'lodash/isString'
import mongoose, { ClientSession, Types } from 'mongoose'
import * as querystring from 'querystring'
import invitationController from './invitation.controller'
import EmailBuilder from '@linqpal/common-backend/src/helpers/EmailBuilder'
import {
  getCustomersData,
  prepareDataAndUpsertCustomers,
} from '@linqpal/common-backend/src/services/customer/customer-status.service'

export async function create(
  data: {
    login?: string | null
    firstName?: string | null
    lastName?: string | null
    phone?: string | null
    email?: string
    firebaseId: string
    company_name?: string
    connector?: { customer_id: string; integration_id: string }
    settings?: {
      ip: string
      ua: string
      ip_based_city: string
      ip_based_state: string
      opt_for_marketing_messages: boolean
    }
    isBusiness?: boolean
    invitation?: string | null
    invoiceId?: string | null
    guest_customer_id?: string | null
    source_url?: string
  },
  session: mongoose.ClientSession | null = null,
) {
  console.log('data received for auth', data)
  const {
    phone = '',
    email: emailRaw = '',
    login,
    invitation,
    company_name,
    connector: connectorRaw,
    invoiceId,
    guest_customer_id,
    isBusiness,
    source_url,
    ...rest
  } = data
  const email = emailRaw.toLowerCase()
  let connector = connectorRaw
  let inv: IInvitation | undefined
  if (!login) {
    return Promise.reject(new exceptions.LogicalError('Login not provided'))
  }

  console.log('Checking user exists in the system via login:', login)
  let user = await User.findOne({ login }).session(session)

  if (!user) {
    console.log('Checking user exists in the system via email:', email)
    const uEmail = await User.findOne({ email }).session(session)

    if (email && uEmail) {
      return Promise.reject(new exceptions.LogicalError('email-already-in-use'))
    }

    const sub = await signupService.generateUniqueSub(2, session)
    console.log('sub was generated for user:', sub)
    ;[user] = await User.create([{ ...rest, login, email, phone, sub }], {
      session,
    })
  }
  console.log('User created', user)

  let userRoles = await UserRole.find({
    sub: user?.sub,
    status: mongoose.trusted({ $ne: RoleStatus.Deactivated }),
    company_id: mongoose.trusted({ $ne: null }),
  })
    .session(session)
    .populate('company')

  console.log('Creating user role')

  let customerQuery: any

  if (!userRoles.length && user && invitation) {
    console.log('Creating user role via invitation', invitation)

    inv = await invitationController.getInvitation(invitation, session)

    console.log('Creating user role via invitation', inv)

    switch (inv?.type) {
      case invitationTypes.SUPPLIER_INVITE_USER:
      case invitationTypes.GETPAID_INVITE:
      case invitationTypes.CREDIT_INVITE:
        userRoles = await UserRole.create(
          [
            {
              sub: user.sub,
              company_id: inv?.company_id,
              role: inv?.role,
              status: RoleStatus.Active,
            },
          ],
          { session },
        )
        break
      case invitationTypes.SUPPLIER_INVITE_CUSTOMER:
        connector = inv?.metadata?.connector
        break
      default:
        break
    }

    if (inv) {
      customerQuery = Object.assign(
        {},
        inv.phone ? { phone: inv.phone } : {},
        inv.email ? { email: inv.email } : {},
        inv.company_id ? { company_id: inv.company_id.toString() } : {},
      )
    }
    if (userRoles?.length && inv) {
      await updateCustomerStatus(session, customerQuery)
    }

    if (inv?.type === invitationTypes.GUEST_PAYER_INVITE) {
      await Invitation.deleteMany({
        login: inv.login,
        type: invitationTypes.GUEST_PAYER_INVITE,
      }).session(session)
    } else {
      await Invitation.findByIdAndDelete(invitation).session(session)
    }

    console.log('User role created from invitation', userRoles, invitation)
  }

  if (!inv) {
    customerQuery = Object.assign(
      {},
      phone ? { phone } : {},
      email ? { email } : {},
    )
  }

  const { customer_id, integration_id } = connector || {}
  if (user && !userRoles?.length && customer_id && integration_id) {
    const company = await Company.findOne({
      'connector.customer_id': customer_id,
      'connector.integration_id': integration_id,
    }).session(session)
    if (company) {
      userRoles = await UserRole.create(
        [
          {
            sub: user.sub,
            company_id: company?.id,
            role: 'User',
            status: RoleStatus.Active,
          },
        ],
        { session },
      )
      // dirty fix for migration, do not save company in user role
      ;(userRoles[0] as any).company = company

      await updateCustomerStatus(session, customerQuery)
    }
    console.log(
      'User role created from integration',
      userRoles,
      customer_id,
      integration_id,
    )
  }

  if (!userRoles.length) {
    let userRoleAdded = false

    const billingContact = await CustomerAccount.findOne({
      ...customerQuery,
      isDeleted: false,
    }).session(session)

    if (billingContact?.parent_id) {
      console.log('Get parent company id from billing account', billingContact)

      const parentContactCompanyId = await getParentCompanyId(
        billingContact.parent_id,
        session,
      )

      if (parentContactCompanyId) {
        console.log(
          'Creating user role from parent contact company',
          parentContactCompanyId,
        )

        await UserRole.create(
          [
            {
              sub: user?.sub,
              company_id: parentContactCompanyId,
              role: 'User',
              status: 'Active',
            },
          ],
          { session },
        )
        userRoleAdded = true
      }
      console.log('User role created from billing account', billingContact)
    }

    if (!userRoleAdded) {
      const [company] = await Company.create(
        [{ name: company_name, connector, isBusiness }],
        { session },
      )
      console.log('New company created', company)

      const defaultOnboardingType = isBusiness
        ? { onboardingType: [MAIN_ID.GENERAL_CONTRACTOR] }
        : { onboardingType: [MAIN_ID.DEVELOPER_PROPERTY_OWNER_NO] } // Only for users signing up from invite / invoie link
      if (inv?.type === invitationTypes.SUPPLIER_INVITE_CUSTOMER || invoiceId) {
        await company
          .updateOne({
            $set: {
              settings: {
                ...defaultOnboardingType,
                invitedBy: (inv && inv.company_id) || '',
              },
            },
          })
          .session(session)
      }
      userRoles = await UserRole.create(
        [
          {
            sub: user?.sub,
            company_id: company._id,
            role: 'Owner',
            status: 'Active',
          },
        ],
        { session },
      )
      console.log('New user role created', userRoles)

      const commonData = {
        firstName: rest.firstName,
        lastName: rest.lastName,
        email: email,
        phone: phone,
        company_name: company_name,
        customerQuery,
      }

      if (guest_customer_id) {
        const guestCustomer = await CustomerAccount.findById(
          guest_customer_id,
        ).session(session)
        if (guestCustomer) {
          console.log('Update guest customer account', guestCustomer)

          await updateCustomerAccount(
            guestCustomer,
            session,
            commonData,
            company,
          )
        }
      }
      if (source_url && !inv && !(customer_id && integration_id)) {
        const brandingInfo = await getBranding({ url: source_url })
        const utmSourceType = brandingInfo?.branding
          ? UtmSourceType.DirectReferral
          : UtmSourceType.DirectTraffic
        const utmSourceUrl = brandingInfo?.branding ? source_url : undefined
        let utmCampaign: string | undefined
        let supplierCompany: any = null

        if (brandingInfo?.branding?.company_id) {
          supplierCompany = await Company.findById(
            brandingInfo.branding.company_id,
          ).session(session)

          utmCampaign =
            supplierCompany?.settings?.customerSettings?.sourceCampaign ||
            supplierCompany?.legalName ||
            supplierCompany?.name ||
            undefined

          await handleBrandedCustomer(
            session,
            commonData,
            company,
            supplierCompany,
          )

          if (
            supplierCompany?.settings?.customerSettings?.startLOCAfterSignUp
          ) {
            await User.updateOne(
              { _id: user._id },
              {
                $set: {
                  'settings.startLOCAfterSignUp': true,
                },
              },
              { session: session ?? undefined },
            )
          }
        }

        const finalUtmSourceType =
          supplierCompany?.settings?.customerSettings?.source || utmSourceType

        await updateUserConversion(
          session,
          user,
          finalUtmSourceType,
          utmSourceUrl,
          utmCampaign,
        )
      }

      await processCustomerAccounts(session, commonData, company)
    }

    console.log('Get company from user role')

    userRoles = await UserRole.find({
      sub: user?.sub,
      status: RoleStatus.Active,
    })
      .populate('company')
      .session(session)
  }
  console.log('Get company from user role', userRoles)

  // dirty fix for mongoose migration, do not save company in user role
  const company = (userRoles[0] as any).company

  return { user, company }
}

interface Validation {
  length: number
  format: RegExp
  encrypt: boolean
  minLength?: number
}

export const validations: Record<string, Record<string, Validation>> = {
  supplier_application: {
    ssn: { length: 9, format: /^\d+$/, encrypt: true },
    ein: { length: 9, format: /^\d+$/, encrypt: true },
    account: { length: 20, format: /^\d+$/, encrypt: true, minLength: 6 },
  },
  general_application: {
    ssn: { length: 9, format: /^\d+$/, encrypt: true },
    ein: { length: 9, format: /^\d+$/, encrypt: true },
  },
} as const

// set status for customer account
async function updateCustomerStatus(session: ClientSession | null, query: any) {
  const result = await CustomerAccount.findOne({
    ...query,
    isDeleted: false,
  }).session(session)

  if (result) {
    const customerId = new Types.ObjectId(result?.id.toString())
    const customer = await getCustomersData([customerId], session)
    await prepareDataAndUpsertCustomers(customer, session)
  }
}

async function getParentCompanyId(
  parentAccountId: string,
  session: mongoose.ClientSession | null,
): Promise<string | null> {
  const result = await CustomerAccount.aggregate([
    {
      $match: {
        _id: new Types.ObjectId(parentAccountId),
      },
    },
    {
      $lookup: {
        from: User.collection.name,
        localField: 'email',
        foreignField: 'email',
        as: 'user',
      },
    },
    { $unwind: { path: '$user', preserveNullAndEmptyArrays: false } },
    {
      $lookup: {
        from: UserRole.collection.name,
        localField: 'user.sub',
        foreignField: 'sub',
        as: 'role',
      },
    },
    { $unwind: { path: '$role', preserveNullAndEmptyArrays: false } },
    {
      $addFields: {
        company_id: {
          $convert: {
            input: '$role.company_id',
            to: 'objectId',
            onError: null,
            onNull: null,
          },
        },
      },
    },
    {
      $lookup: {
        from: Company.collection.name,
        localField: 'company_id',
        foreignField: '_id',
        as: 'company',
      },
    },
    { $unwind: { path: '$company', preserveNullAndEmptyArrays: false } },
    {
      $project: {
        company_id: '$company._id',
      },
    },
  ]).session(session)

  return result[0]?.company_id || null
}

async function updateCustomerAccount(
  customerAccount: any,
  session: any,
  commonData: any,
  company: any,
) {
  const { firstName, lastName, email, phone, company_name } = commonData
  customerAccount.first_name = firstName || ''
  customerAccount.last_name = lastName || ''
  customerAccount.email = email
  if (phone) {
    customerAccount.phone = phone
  }
  customerAccount.name = company_name as string
  customerAccount.status = customerStatus.active
  customerAccount.isGuest = false

  await customerAccount.save({ session })
  console.log('Updated customer account', customerAccount)

  // Update billing contacts
  const billingContacts = await CustomerAccount.find({
    parent_id: customerAccount._id,
  }).session(session)

  if (billingContacts?.length) {
    await Promise.all(
      billingContacts.map(async (bc) => {
        const billingUser = await User.findOne({
          $or: [{ login: bc.phone }, { login: bc.email }],
        }).session(session)

        if (billingUser?.sub) {
          await UserRole.findOneAndUpdate(
            { sub: billingUser.sub },
            { company_id: company._id, role: 'User' },
          ).session(session)
        }
      }),
    )
  }
}

async function updateUserConversion(
  session: any,
  user: any,
  utmSourceType: UtmSourceType,
  source_url: string | undefined,
  utm_campaign: string | undefined = undefined,
) {
  console.log(`Updating conversion tracking with utm_source: ${utmSourceType}`)

  await conversion({
    user,
    conversionObj: {
      utm_source: utmSourceType,
      source_url: source_url || null,
      utm_campaign: utm_campaign || null,
    },
    session,
  })

  console.log(`Updated conversion tracking: ${utmSourceType}`)
}

async function handleBrandedCustomer(
  session: any,
  commonData: any,
  company: any,
  supplierCompany: ICompany,
) {
  const existingCustomer = await CustomerAccount.findOne({
    company_id: supplierCompany._id.toString(),
    $or: [
      ...(commonData.email ? [{ email: commonData.email }] : []),
      ...(commonData.phone ? [{ phone: commonData.phone }] : []),
    ],
    isDeleted: false,
  }).session(session)
  if (!existingCustomer) {
    console.log('Creating new customer account under supplier')

    await CustomerAccount.create(
      [
        {
          company_id: supplierCompany._id.toString(),
          email: commonData.email,
          phone: commonData.phone,
          first_name: commonData.firstName,
          last_name: commonData.lastName,
          name: commonData.company_name,
          type: CustomerAccountType.TradeCredit,
          customer: company._id,
          status: customerStatus.active,
          settings: {
            acceptAchPayment: true,
            sendFinalPaymentWhenLoanIsPaid:
              !!supplierCompany?.settings?.sendFinalPaymentWhenLoanIsPaid,
          },
        },
      ],
      { session },
    )

    console.log('Created customer account under supplier via branded link')
  } else {
    console.log('Customer already exists under supplier, no action needed')
  }
}

async function processCustomerAccounts(
  session: any,
  commonData: any,
  company: any,
) {
  const { customerQuery } = commonData

  const customerAccounts = await CustomerAccount.find({
    ...customerQuery,
    isDeleted: false,
  }).session(session)

  if (customerAccounts?.length) {
    await Promise.all(
      customerAccounts.map((account) =>
        updateCustomerAccount(account, session, commonData, company),
      ),
    )
  }
}

export function secureDocumentFields(document: {
  type: keyof typeof validations
  data: any
}) {
  const val = validations[document.type]
  const encrypts = val
    ? Object.keys(val).filter((k) => val[k as keyof typeof val].encrypt)
    : []
  if (document.data && encrypts.length > 0) {
    Object.keys(document.data).forEach((group) => {
      document.data[group]?.items?.forEach((item: any) =>
        secureEncrypted(item.content, encrypts, item),
      )
    })
  }
  return document
}

export const decryptDocumentFields = async (document: {
  type: keyof typeof validations
  data: any
}) => {
  if (!document.data) return document

  const validation = validations[document.type]
  const encryptedFields = validation
    ? Object.keys(validation).filter((k) => validation[k].encrypt)
    : []

  if (!encryptedFields.length) return document

  for (const group of Object.keys(document.data)) {
    const groupItems = document.data[group]?.items || []
    for (const item of groupItems) {
      if (!item?.content) continue
      // decrypt flat fields like businessInfo.ein
      if (encryptedFields.includes(item.identifier) && item.content.cipher) {
        item.content = await crypt.decrypt(item.content.cipher)
      } else {
        // co-owner has deeper hierarchy: coOwnerInfo.coOwner1.ssn
        for (const field of encryptedFields) {
          if (
            item.content.hasOwnProperty(field) &&
            item.content[field]?.cipher
          ) {
            const encryptedValue = item.content[field].cipher
            item.content[field] = await crypt.decrypt(encryptedValue)
          }
        }
      }
    }
  }

  return document
}

function secureEncrypted(
  content: any,
  encrypts: (string | undefined)[],
  item: { identifier: string; content: string } | undefined | null,
) {
  if (isObjectLike(content)) {
    if (encrypts.indexOf(item?.identifier) !== -1) {
      if (item) {
        item.content = content.display || ''
      }
    } else {
      Object.keys(content).forEach((key) => {
        const value = content[key]
        if (isArray(value)) {
          value.forEach((v) => secureEncrypted(v, encrypts, null))
        }
        if (encrypts.indexOf(key) === -1) return
        if (isNull(value)) {
          content[key] = ''
        } else if (isObjectLike(value)) {
          content[key] = value.display || ''
        }
      })
    }
  }
}

export function verifyValue<T>(
  content: string,
  identifier: KeysOfType<T, Validation>,
  val: T,
) {
  const validation: Validation = val[identifier] as unknown as Validation
  if (content && !validation.format.test(content)) {
    throw new exceptions.LogicalError(`${identifier} does not match format`)
  }
  if (content) {
    if (
      validation.minLength &&
      (content.length < validation.minLength ||
        content.length > validation.length)
    ) {
      throw new exceptions.LogicalError(
        `"${identifier}" should have length greater than ${validation.minLength} and less than ${validation.length}`,
      )
    }
    if (!validation.minLength && content.length !== validation.length) {
      throw new exceptions.LogicalError(
        `${identifier} should have length ${validation.length}`,
      )
    }
  }
}

export async function encryptValidate(
  content: any,
  identifier: string,
  val: any,
  items: any[],
  inner = false,
  documentItems: any[] = [],
) {
  if (isObjectLike(content)) {
    await Promise.all(
      Object.keys(content).map(async (key) => {
        let value = content[key]
        if (isArray(value)) {
          const item = items?.find((i) => i.identifier === identifier)
          const inner_items = !inner ? item?.content[key] : item?.get(key)
          await Promise.all(
            value.map(async (v) =>
              encryptValidate(v, v.identifier, val, inner_items, true, []),
            ),
          )
        }

        if (!val[key] || !isString(value)) return

        if (value && value === new Array(val[key].length + 1).join('*')) {
          const item = items?.find((i) => i.identifier === identifier)
          if (item) {
            content[key] = !inner ? item?.content[key] : item[key]
            value = content[key]
          }
        }

        if (!isString(value) || !value) return
        verifyValue(value, key, val)
        const cipher = await crypt.encrypt(value)
        content[key] = {
          cipher,
          hash: md5(value).toString(),
          display: new Array(val[key].length + 1).join('*'),
        }
      }),
    )
  } else {
    let value = content
    if (!isString(content) || !val[identifier]) return
    if (
      content &&
      content === new Array(val[identifier].length + 1).join('*')
    ) {
      const item = items?.find((i) => i.identifier === identifier)
      const documentItem = documentItems?.find(
        (i) => i.identifier === identifier,
      )
      if (item) {
        documentItem.content = item.content
        value = item.content
      }
    }
    if (!isString(value) || !value) return

    verifyValue(content, identifier, val)
    const item = documentItems?.find((i) => i.identifier === identifier)
    const cipher = await crypt.encrypt(content)
    item.content = {
      cipher,
      hash: md5(content).toString(),
      display: new Array(val[identifier].length + 1).join('*'),
    }
  }
}

function validateEmail(email = '') {
  return !!email && /^[^@]+@([^@.]+\.)+[^@.]+$/.test(email)
}

export async function getFirebaseUser(login: string | null | undefined) {
  try {
    if (!login) {
      return false
    } else if (validateEmail(login)) {
      return await admin.auth().getUserByEmail(login)
    } else {
      const phoneNumber = parsePhoneNumber(login, 'US').number
      return await admin.auth().getUserByPhoneNumber(phoneNumber.toString())
    }
  } catch (e: any) {
    if (e.code !== 'auth/user-not-found') {
      throw new exceptions.LogicalError(e.message)
    }
  }
  return null
}

export async function checkUserExistsInSystem(login: string | undefined) {
  const fbUser = await getFirebaseUser(login)
  if (!fbUser) {
    return false
  }
  const user = await User.findOne({ firebaseId: fbUser.uid })

  if (!user?.sub) {
    return false
  }

  return UserRole.findOne({ sub: user.sub })
}

export const conversion = async ({
  user,
  conversionObj,
  session = null,
}: {
  user: IUser
  conversionObj: any
  session?: mongoose.ClientSession | null
}) => {
  const currentUtmSource = user.settings?.conversion?.utm_source

  const mergedConversion = {
    ...user.settings?.conversion,
    ...conversionObj,
    ...(currentUtmSource && conversionObj.utm_source
      ? { utm_source: currentUtmSource }
      : {}),
  }
  await User.updateOne(
    { firebaseId: user.firebaseId },
    {
      $set: {
        'settings.conversion': mergedConversion,
      },
    },
    { session: session ?? undefined },
  ).catch((e) => {
    throw new exceptions.LogicalError(
      'User Settings Conversion Update Error: ' + e.message,
    )
  })
  return
}

export async function sendVerificationEmail(
  email: string,
  uid: string,
  host: string,
) {
  const url = `${host || getApiURL()}/email?`
  const signature = await crypt.sign(uid)
  const qstring = querystring.stringify({
    mode: 'verifyEmail',
    oobCode: signature,
    firebaseId: uid,
  })
  const link = `${url}${qstring}`
  const result = await getBranding({ url: host })
  console.log(link)
  const name = result?.branding?.name || 'BlueTape'
  try {
    const emailMessage = EmailBuilder.getSubjectAndBody({
      key: 'sendVerification',
      data: { name, url: link },
    })
    const msg = {
      to: email,
      subject: emailMessage.subject,
      html: emailMessage.body,
    }

    await emailService.send(msg)
  } catch (e: any) {
    console.log(e)
    throw new exceptions.LogicalError(e.message)
  }
}

export async function checkEmailIsVerified(userId: string) {
  const firebaseUser = await admin.auth().getUser(userId)
  return firebaseUser.emailVerified
}
