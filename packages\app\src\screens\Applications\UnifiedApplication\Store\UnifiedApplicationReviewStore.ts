import { makeAutoObservable } from 'mobx'

export enum UnifiedApplicationReviewStep {
  REVIEW,
  PREVIEW,
  AGREEMENT,
}

export class UnifiedApplicationReviewStore {
  private _currentStep = UnifiedApplicationReviewStep.REVIEW

  private _previousStep = UnifiedApplicationReviewStep.REVIEW

  private _isAgreementAccepted = false

  private _isAgreementRead = true

  constructor() {
    makeAutoObservable(this)
  }

  public get currentStep(): UnifiedApplicationReviewStep {
    return this._currentStep
  }

  public set currentStep(value: UnifiedApplicationReviewStep) {
    this._previousStep = this._currentStep
    this._currentStep = value
  }

  public get isAgreementAccepted(): boolean {
    return this._isAgreementAccepted
  }

  public set isAgreementAccepted(value: boolean) {
    this._isAgreementAccepted = value
  }

  public get isAgreementRead(): boolean {
    return this._isAgreementRead
  }

  public set isAgreementRead(value: boolean) {
    this._isAgreementRead = value
  }

  public moveBack(): boolean {
    if (this._currentStep === UnifiedApplicationReviewStep.REVIEW) {
      return false // cannot move back from review step
    }

    this._currentStep = this._previousStep
    return true
  }
}
