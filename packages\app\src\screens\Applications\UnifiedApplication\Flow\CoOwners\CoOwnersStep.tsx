import React, { FC, useEffect } from 'react'
import { BtButton, BtPlainText } from '@linqpal/components/src/ui'
import { observer } from 'mobx-react'
import { StyleSheet, View } from 'react-native'
import { useTranslation } from 'react-i18next'
import { Divider } from '@ui-kitten/components'
import { OwnerTypes } from '@linqpal/models/src/dictionaries/UnifiedApplication'
import { CoOwnerEditor } from './components/CoOwnerEditor'
import { CoOwnersReview } from './components/CoOwnersReview'
import { AddCoOwnerButton } from './components/AddCoOwnerButton'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { CoOwnerValidator, ICoOwner } from '@linqpal/models'
import { OwnerIcon } from './components/OwnerIcon'
import { useResponsive } from '../../../../../utils/hooks'
import { UnifiedApplicationStore } from '../../Store/UnifiedApplicationStore'
import {
  IUnifiedApplicationEditor,
  IUnifiedApplicationStepOptions,
} from '../getUnifiedApplicationEditor'

const CoOwnersEditor: FC = () => {
  const { t } = useTranslation('application')
  const { sm } = useResponsive()

  const store = useUnifiedApplication()
  const coOwnersStore = store.coOwnersStore

  const coOwners = store.draft?.data?.coOwners || []

  useEffect(() => {
    if (store.isInReview) return

    const newOptions = !coOwnersStore.currentCoOwner
      ? {
          title: 'CoOwners.Title',
          showNavigationButtons: true,
        }
      : {
          title: 'CoOwners.CoOwnerDetailsTitle',
          showNavigationButtons: false,
        }

    store.setStepOptions({
      ...defaultOptions,
      ...newOptions,
    })
  }, [coOwnersStore.currentCoOwner, store])

  return (
    <>
      {store.isInReview ? (
        <CoOwnersReview />
      ) : (
        <>
          {coOwnersStore.currentCoOwner === null && (
            <>
              {coOwners.map((coOwner: ICoOwner) => (
                <>
                  <View style={styles.coOwnersList} key={coOwner.id}>
                    <OwnerIcon
                      isOwnerFilled={new CoOwnerValidator(
                        coOwner,
                      ).validateCoOwner()}
                    />

                    <View style={styles.coOwnerDetails}>
                      <BtPlainText
                        style={styles.coOwnerType}
                        onPress={() => coOwnersStore.edit(coOwner)}
                      >
                        {coOwner.type === OwnerTypes.INDIVIDUAL
                          ? t('CoOwners.IndividualOwner')
                          : t('CoOwners.EntityOwner')}
                      </BtPlainText>

                      {coOwner.firstName ||
                      coOwner.lastName ||
                      coOwner.entityName ? (
                        <BtPlainText
                          style={styles.ownerName}
                          onPress={() => coOwnersStore.edit(coOwner)}
                        >
                          {coOwner.type === OwnerTypes.INDIVIDUAL
                            ? `${coOwner.firstName} ${coOwner.lastName}`
                            : coOwner.entityName}
                        </BtPlainText>
                      ) : null}

                      <BtPlainText
                        style={styles.coOwnerPercentage}
                        onPress={() => coOwnersStore.edit(coOwner)}
                      >
                        {`${coOwner.percentOwned}% ${t(
                          'CoOwners.OwnerPercentagePostfix',
                        )}`}
                      </BtPlainText>
                    </View>

                    <View style={styles.coOwnerButtons}>
                      <BtButton
                        appearance={'ghost'}
                        size={'small'}
                        status={'basic'}
                        onPress={() => coOwnersStore.edit(coOwner)}
                        // eslint-disable-next-line i18next/no-literal-string
                        iconLeft="edit-outline"
                      >
                        {t('Review.Edit')}
                      </BtButton>
                      <BtButton
                        size="small"
                        appearance={'ghost'}
                        status={'basic'}
                        onPress={() => coOwnersStore.remove(coOwner)}
                        // eslint-disable-next-line i18next/no-literal-string
                        iconLeft="trash-2-outline"
                      >
                        {t('Bank.DeleteManualButton')}
                      </BtButton>
                    </View>
                  </View>
                  <Divider />
                </>
              ))}

              <View style={styles.footer}>
                <View style={styles.totalPercentageContainer}>
                  <BtPlainText style={styles.percentageAmount}>
                    {t('CoOwners.TotalPercentage')}
                  </BtPlainText>
                  <BtPlainText
                    size={16}
                    color={
                      store.draft.totalOwnershipPercentage > 100
                        ? '#DB081C'
                        : ''
                    }
                  >
                    {store.draft.totalOwnershipPercentage}%
                  </BtPlainText>
                </View>
                {sm && (
                  <AddCoOwnerButton onPress={() => coOwnersStore.createNew()} />
                )}
              </View>
              <Divider />

              {!sm && (
                <AddCoOwnerButton onPress={() => coOwnersStore.createNew()} />
              )}
            </>
          )}

          {coOwnersStore.currentCoOwner && <CoOwnerEditor />}
        </>
      )}
    </>
  )
}

const styles = StyleSheet.create({
  coOwnersList: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  coOwnerDetails: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginTop: 10,
  },
  footer: {
    flexDirection: 'row',
    paddingTop: 20,
    paddingBottom: 25,
  },
  coOwnerType: {
    fontSize: 14,
    fontWeight: '400',
    color: 'gray',
  },
  ownerName: {
    fontSize: 16,
    fontWeight: '700',
    marginTop: 5,
  },
  coOwnerPercentage: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 5,
    marginBottom: 10,
  },
  coOwnerButtons: {
    flexDirection: 'row',
    marginLeft: 'auto',
  },
  totalPercentageContainer: {
    flexDirection: 'row',
    marginTop: 5,
  },
  percentageAmount: {
    fontSize: 16,
    fontWeight: '700',
  },
})

const defaultOptions: IUnifiedApplicationStepOptions = {
  title: 'CoOwners.Title',
  onMoveBack: (store: UnifiedApplicationStore) => {
    if (store.coOwnersStore.currentCoOwner) {
      store.coOwnersStore.cancelEdit()
      store.tryReturnToReview()

      return true
    }

    return false
  },
}

export const CoOwnersStep: IUnifiedApplicationEditor = {
  // no options - editor will manage options lifecycle

  // we can go from review to specific co-owner.
  // in this case we set store.currentStep and store.coOwners.current in one action
  // React do render from child to parent, so options set in this editor useEffect would be
  // overwritten by WizardStepEditor useEffect on new editor
  // so we don't have options here, to avoid this overwrite
  component: observer(CoOwnersEditor),
}
