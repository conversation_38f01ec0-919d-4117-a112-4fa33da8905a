import {
  cbw,
  defaultPlans,
  Invoice,
  issueVirtualCard,
  LMS,
  LoanPaymentPlan,
  Operation,
  VirtualCard,
  VirtualCardModel,
} from '@linqpal/common-backend'
import { dictionaries } from '@linqpal/models'
import chai from 'chai'
import * as sinon from 'sinon'
import {
  beforeEachMockEncryption,
  beforeEachMockSecretsManager,
} from './helper'
import nock from 'nock'
import { createLoan } from './fixtures/bluetape_data'
import moment from 'moment'
import 'moment-business-days'
import { virtualCard } from '../cbw/virtualCard'
import mongoose from 'mongoose'
import { loanBuyback } from '../cbw'
import { IAgreementFileDetails } from '@linqpal/common-backend/src/services/agreement/types'
import * as AgreementService from '@linqpal/common-backend/src/services/agreement/agreement.service'
import { LmsFundingSource } from '@linqpal/common-backend/src/services/lms.service'

chai.should()

const card_1 = {
  cardId: '80c242e8-be55-4024-b175-5b824c335b2c',
  cardType: 'VIRTUAL',
  cardStatus: 'ACTIVATED',
  cardMaskNumber: '5433*******6636',
  cardExpiryDate: '202204',
  mccGroupId: '6231fe4d446025b18bf01ced',
  countryGroupId: '6231fe0b446025b18bf01c90',
}

describe('VirtualCard use', () => {
  let requesterStub: sinon.SinonStub
  let mockLoanLMS: nock.Scope,
    clock: sinon.SinonFakeTimers,
    mockCreateLoan: sinon.SinonStub,
    mockSync: sinon.SinonStub,
    mockAgreementSync: sinon.SinonStub
  beforeEachMockEncryption()
  beforeEachMockSecretsManager()

  const mockRequesterPost =
    (remaining: number, limit: number) => (url: string, data: any) => {
      let resp: any
      switch (data.payload.transactionType) {
        case 'ADD_CARD':
          resp = { card: { cardId: card_1.cardId } }
          break
        case 'GET_CARD':
          resp = {
            card: {
              ...card_1,
              limits: [
                { remaining, value: limit, type: 'VOLUME' },
                { type: 'COUNT', remaining: '1', value: '1' },
              ],
            },
          }
          break
        case 'GL_TRANSFER':
          resp = { api: { reference: '123' }, transactionStatus: 'PROCESSED' }
          break
        default:
          console.error(data)
          resp = {}
      }
      return Promise.resolve(resp)
    }

  beforeEach(async () => {
    process.env.LP_MODE = 'dev'
    process.env.LP_CBW_OP_IDENTIFICATION = 'OP123'
    process.env.LP_CBW_ACH_CUSTOMER_ID = '234'
    process.env.LP_CBW_GL_IDENTIFICATION = 'GL345'
    process.env.LP_CBW_GL2_IDENTIFICATION = 'VC456'

    const mockData: IAgreementFileDetails = {
      url: 'url',
      fileName: 'fileName',
    }

    mockAgreementSync = sinon
      .stub(AgreementService, 'createAgreementForLoanApplication')
      .callsFake(() => Promise.resolve(mockData))

    const requester = cbw.initCbwApiRequester()
    requesterStub = sinon.stub(requester, 'post')

    mockLoanLMS = nock(
      'https://ca-loan-service-dev.bravesea-34bfcf76.westus.azurecontainerapps.io',
    ).persist(true)
    mockLoanLMS
      .post((uri) => uri.includes('Customers'))
      .reply(200, { d: { id: '123' } })
    mockLoanLMS
      .post((uri) => uri.includes('Loans'))
      .reply(200, { d: { id: '123' } })
    mockLoanLMS
      .patch((uri) => uri.includes('Loan'))
      .reply(200, { d: { id: '123' } })
    mockLoanLMS.put(() => true).reply(200, {})
    clock = sinon.useFakeTimers(moment().toDate())
    mockCreateLoan = sinon.stub(LMS, 'createLoan').callsFake(() =>
      Promise.resolve({
        id: '123',
        companyId: 'company-123',
        amount: 1000,
        refundAmount: 0,
        fee: 100,
        loanTemplateId: 'template-123',
        loanParameters: [],
        loanReceivables: [
          {
            id: 'receivable-123',
            expectedDate: '10/10/2022',
            expectedAmount: 1000,
            paidAmount: 0,
            processingAmount: 0,
            adjustAmount: 0,
            type: 'Installment' as any,
            status: 'None' as any,
            scheduleStatus: 'Current' as any,
          },
        ],
        activeReceivables: [],
        payments: [],
        status: 'Started' as any,
        isDeleted: false,
        isOverdue: false,
        startDate: '10/10/2022',
        nextPaymentDate: '10/10/2022',
        nextPaymentAmount: 1000,
        isAnyPaymentMissed: false,
        creditId: 'credit-123',
        activeLoanTemplate: {
          code: 'test',
          legacyId: null,
          product: 'LineOfCredit' as any,
        },
        term: '90',
        loanOrigin: 'normal' as any,
        fundingSource: LmsFundingSource.Arcadia,
        downPaymentStatus: 'NotRequired' as any,
        downPaymentStatusAt: '10/10/2022',
      }),
    )
    mockSync = sinon
      .stub(LMS, 'syncLoanApplicationFields')
      .callsFake(() => Promise.resolve())
    await LoanPaymentPlan.insertMany(defaultPlans)
  })

  afterEach(() => {
    requesterStub.restore()
    nock.cleanAll()
    clock.restore()
    mockCreateLoan.restore()
    mockSync.restore()
    mockAgreementSync.restore()
  })
  const cases = [
    // { remaining: 100, limit: 100 },
    { remaining: 80, limit: 100 },
  ]
  cases.forEach((item) => {
    it.skip(`should issue loan for ${
      item.limit - item.remaining
    }`, async () => {
      requesterStub.callsFake(mockRequesterPost(item.remaining, item.limit))
      const { app, invoice, builderCompany } = await createLoan(true, true, {
        name: 'Payer',
      })
      app.status = dictionaries.LOAN_APPLICATION_STATUS.APPROVED
      app.approvedAmount = 100
      app.issueDate = moment().toDate()
      await app.save()
      const session = await mongoose.startSession()
      let vc: VirtualCardModel | null
      try {
        session.startTransaction()
        vc = await issueVirtualCard(
          { company_id: builderCompany!.id, invoice_id: invoice?.id },
          app,
          session,
        )
        app.invoiceDetails.cardId = vc.cardId
        await app.save()
        await session.commitTransaction()
      } finally {
        await session.endSession()
      }
      clock = sinon.useFakeTimers(moment().businessAdd(1, 'days').toDate())
      await virtualCard()
      let op = await Operation.findOne({
        owner_id: app.id,
        type: mongoose.trusted({
          $in: [dictionaries.OPERATION_TYPES.LOAN.ISSUE],
        }),
      })
      console.log(app, op)

      if (item.remaining < item.limit) {
        chai.expect(op).is.not.null
        clock = sinon.useFakeTimers(moment().add(10, 'days').toDate())
        vc = await VirtualCard.findOne({ cardId: app.invoiceDetails.cardId })
        mockCreateLoan.called.should.be.true
        mockCreateLoan.getCall(0).args[3].should.eq(vc!.usedAmount)
        await virtualCard()
        const c = await Operation.find({
          owner_id: app.id,
          type: mongoose.trusted({
            $in: [dictionaries.OPERATION_TYPES.LOAN.ISSUE],
          }),
        })
        c.length.should.eq(1)
        clock = sinon.useFakeTimers(moment().businessAdd(8, 'days').toDate())
        const res = await loanBuyback()
        res.length.should.eq(1)
        res[0]!.toString().should.eq(app.id)
      } else {
        chai.expect(op).is.null
        vc = await VirtualCard.findOne({
          cardId: app.invoiceDetails.cardId,
        })
        chai.expect(vc).is.not.null
        vc?.status.should.eq(dictionaries.VirtualCardStatus.ACTIVE)

        clock = sinon.useFakeTimers(moment().businessAdd(5, 'days').toDate())
        await virtualCard()
        op = await Operation.findOne({
          owner_id: app.id,
          type: mongoose.trusted({
            $in: [dictionaries.OPERATION_TYPES.LOAN.ISSUE],
          }),
        })
        chai.expect(op).is.null

        const inv = await Invoice.findById(app.invoiceDetails.invoiceId)
        inv?.status.should.eq(dictionaries.invoiceStatus.cancelled)
        vc = await VirtualCard.findOne({
          cardId: app.invoiceDetails.cardId,
        })
        vc?.status.should.eq(dictionaries.VirtualCardStatus.EXPIRED)
      }
    })
  })
})
