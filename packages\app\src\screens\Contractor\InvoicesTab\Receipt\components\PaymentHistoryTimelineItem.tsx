import React, { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { Text } from 'react-native-paper'
import { View, StyleSheet } from 'react-native'
import moment from 'moment'
import { composeStyle } from '@linqpal/common-frontend/src/helpers'
import { VerticalTransactionProgress } from './VerticalTransactionProgress'
import { Row, Col } from '../../../../../ui/atoms/Grid'
import { TRANSACTION_STATUS } from '@linqpal/models/src/dictionaries'
import { currencyMask } from '../../../../../utils/helpers/masking'
import { TransactionStatusLabel } from './TransactionStatusLabel'
import { Spacer } from '@linqpal/components/src/ui'
import useIsMobile from '../../../PayablesTab/hooks/useIsMobile'

export interface ITransactionItem {
  id: string
  operation_id: string
  fee: number
  manualPaymentData: {
    manual_payment_method: string
    userId: string
  }
  amount: number
  payment_method: 'card' | 'ach'
  date: string
  type: string
  status: string
}

interface TimelineItemProps {
  transaction: ITransactionItem
  isLastTransaction: boolean
  oneTransaction: boolean
  isFullPayment?: boolean
}

const redHighlight = '#EC002A'
const defaultColor = '#19262F'

const Label: React.FC<{
  isFullPayment?: boolean
}> = ({ isFullPayment }) => {
  const { t } = useTranslation('global')

  const label = isFullPayment
    ? t('PaymentHistoryInvoice.payment')
    : t('PaymentHistoryInvoice.partialPayment')

  return <Text style={styles.labelStyle}>{label}</Text>
}

const TransactionType: React.FC<{ transaction: ITransactionItem }> = ({
  transaction,
}) => {
  const { t } = useTranslation('global')
  const { manualPaymentData, payment_method } = transaction

  const paymentType = useMemo(() => {
    if (manualPaymentData?.manual_payment_method) {
      return t('PaymentHistoryInvoice.externalPayment')
    } else if (manualPaymentData?.userId && payment_method === 'ach') {
      return t('PaymentHistoryInvoice.manualPaymentByBluetape')
    } else {
      return t('PaymentHistoryInvoice.manualPayment')
    }
  }, [manualPaymentData, payment_method, t])

  return (
    <Text
      style={{
        fontWeight: '400',
        fontSize: 14,
        color: defaultColor,
        lineHeight: 20,
        fontFamily: 'Inter',
      }}
    >
      {paymentType}
    </Text>
  )
}

const ItemDate: React.FC<{ date: string }> = ({ date }) => {
  return (
    <Text style={styles.date}>
      {moment(date).format('MM/DD/YYYY')} {'  '}
    </Text>
  )
}

const Amount: React.FC<{ transaction: ITransactionItem }> = ({
  transaction,
}) => {
  const { status, amount = 0, fee = 0 } = transaction
  const negativeTransaction = [
    TRANSACTION_STATUS.CANCELED,
    TRANSACTION_STATUS.ERROR,
  ].some((el) => el === status)

  const amtColor = useMemo(() => {
    let color = defaultColor
    if (negativeTransaction) color = redHighlight
    return color
  }, [negativeTransaction])

  return (
    <Text style={composeStyle(styles.amount, { color: amtColor })}>
      {currencyMask(amount + fee)}
    </Text>
  )
}

export const TransactionTimelineItem: React.FC<TimelineItemProps> = ({
  transaction,
  isLastTransaction,
  oneTransaction,
  isFullPayment,
}) => {
  const { date } = transaction
  const isMobile = useIsMobile()

  return (
    <View
      style={[styles.timelineItemContainer, { width: isMobile ? '100%' : 345 }]}
    >
      {!oneTransaction && (
        <VerticalTransactionProgress
          shouldHideLine={isLastTransaction}
          status={transaction.status}
        />
      )}
      <Col
        style={[
          styles.itemColStyle,
          !isLastTransaction && styles.itemColSpacingStyle,
        ]}
      >
        <Row style={styles.itemRowStyle}>
          <Label isFullPayment={isFullPayment} />
          <Amount transaction={transaction} />
        </Row>

        <Spacer height={3} />

        <Row style={styles.itemRowStyle}>
          <TransactionType transaction={transaction} />
          <ItemDate date={date} />
        </Row>
        <Spacer height={10} />
        <View style={styles.statusContainer}>
          <TransactionStatusLabel status={transaction.status} />
        </View>
      </Col>
    </View>
  )
}

const styles = StyleSheet.create({
  date: {
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 2,
    marginRight: -10,
    color: '#19262F',
    textAlign: 'right',
  },
  amount: {
    fontWeight: '700',
    fontSize: 14,
    textAlign: 'right',
    lineHeight: 24,
    fontFamily: 'Inter',
  },
  itemRowStyle: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemColStyle: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    marginLeft: 13,
    marginTop: -3,
  },
  itemColSpacingStyle: {
    marginBottom: 20,
  },
  statusContainer: {
    alignSelf: 'flex-start',
    alignItems: 'flex-start',
    marginLeft: -10,
  },
  labelStyle: {
    fontWeight: '700',
    fontSize: 14,
    color: '#19262F',
    lineHeight: 20,
    fontFamily: 'Inter',
  },
  timelineItemContainer: {
    flexDirection: 'row',
    width: 350,
  },
})
