import { InvoiceNote, User } from '@linqpal/common-backend'
import { Types } from 'mongoose'
import { exceptions } from '@linqpal/models'
import { Request } from 'express'

export async function getNotes(invoiceId: string) {
  return InvoiceNote.aggregate([
    {
      $match: { invoiceId },
    },
    {
      $lookup: {
        from: User.collection.name,
        localField: 'sub',
        foreignField: 'sub',
        as: 'owner',
      },
    },
    {
      $project: {
        id: 1,
        invoiceId: 1,
        message: 1,
        owner: 1,
        _createdAt: 1,
        _updatedAt: 1,
      },
    },
    { $sort: { _createdAt: -1, _updatedAt: -1 } },
  ])
}

export async function saveNote(
  req: Request,
  {
    id = null,
    invoiceId,
    message,
  }: { id: string | null; invoiceId: string; message: string },
) {
  if (id) {
    const invoiceNote = await InvoiceNote.findOne({
      _id: new Types.ObjectId(id),
    })
    if (invoiceNote) {
      if (invoiceNote.sub === req.user!.sub) {
        return invoiceNote.updateOne({ message })
      } else {
        throw new exceptions.LogicalError('You have no access to this note')
      }
    } else {
      throw new exceptions.LogicalError('Note not found')
    }
  } else {
    return InvoiceNote.create({
      invoiceId,
      message,
      sub: req.user!.sub,
    })
  }
}

export async function deleteNote(req: Request, id: string) {
  const invoiceNote = await InvoiceNote.findOne({
    _id: new Types.ObjectId(id),
  })
  if (invoiceNote) {
    if (invoiceNote.sub === req.user!.sub) {
      await invoiceNote.deleteOne()
    } else {
      throw new exceptions.LogicalError('You have no access to this note')
    }
  }
}
