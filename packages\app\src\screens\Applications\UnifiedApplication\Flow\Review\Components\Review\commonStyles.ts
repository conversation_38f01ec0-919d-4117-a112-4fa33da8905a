import { StyleSheet } from 'react-native'
import { commonColors } from '@linqpal/common-frontend/src/theme'

export const commonStyles = StyleSheet.create({
  groupLineWrapper: {
    height: 76,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomColor: '#CCD6DD',
    borderBottomWidth: 1,
  },
  groupLineText: {
    marginLeft: 24,
    fontWeight: '600',
    fontSize: 20,
    lineHeight: 26,
    color: '#335C75',
    flex: 1,
  },
  editGroupLink: {
    color: commonColors.accentText,
    fontWeight: '600',
    fontSize: 16,
    lineHeight: 24,
  },
})
