import {
  BankAccount,
  Company,
  crypt,
  CustomerAccount,
  Draft,
  Invoice,
  LoanApplication,
  User,
  UserRole,
} from '@linqpal/common-backend'
import { CompanyStatus, dictionaries } from '@linqpal/models'
import builderDraft from './builder_draft'
import cryptService from '@linqpal/common-backend/src/services/crypt.service'
import { ICompany, IInvoice } from '@linqpal/common-backend/src/models/types'

export async function createSupplier() {
  const email = '<EMAIL>'
  let company: ICompany | null
  let user = await User.findOne({ login: email })
  if (!user) {
    user = await User.create({
      sub: '234',
      firebaseId: '234',
      settings: { ip: 'ip' },
      login: email,
      phone: '+***********',
      email,
      name: 'Name',
    })
    company = await Company.create({
      type: 'supplier',
      name: 'Company',
      email,
      phone: '+***********',
      ein: { cipher: await crypt.encrypt('*********') },
      settings: { loanPricingPackageId: 'packageA' },
      address: {
        address: 'Street',
        city: 'City',
        state: 'California',
        zip: '90077',
      },
    })
    await UserRole.create({ sub: '234', company_id: company.id, role: 'Owner' })
    const account = await BankAccount.create({
      isPrimary: true,
      accountNumber: {
        cipher: await crypt.encrypt('*********'),
        display: '123',
      },
      accountType: 'checking',
      name: 'Bank',
      routingNumber: '0000000',
      status: 'verified',
    })
    company.bankAccounts!.push(account)
    company.status = CompanyStatus.Approved
    await company.save()
  } else {
    const role = await UserRole.findOne({ sub: '234' })
    company = await Company.findById(role?.company_id).populate('bankAccounts')
  }
  return {
    supplierCompanyId: company!.id,
    supplierCompany: company,
    supplier: user,
  }
}

export async function createBuilder() {
  let company: ICompany | null
  let user = await User.findOne({ login: '+***********' })
  if (!user) {
    user = await User.create({
      sub: '123',
      firebaseId: '123',
      settings: { ip: 'ip' },
      login: '+***********',
      email: '<EMAIL>',
      phone: '+***********',
      firstName: 'Builder',
      lastName: 'Builderson',
    })
    company = await Company.create({
      type: 'contractor',
      address: {
        address: 'Street',
        city: 'City',
        state: 'California',
        zip: '90077',
      },
      email: '<EMAIL>',
      phone: '+***********',
      name: 'Builder inc',
    })
    await UserRole.create({ sub: '123', company_id: company.id, role: 'Owner' })
    await Draft.create({
      company_id: company.id,
      data: builderDraft,
      type: 'loan_application',
    })
    const account = await BankAccount.create({
      isPrimary: true,
      accountNumber: {
        cipher: await cryptService.encrypt('*********'),
        display: '***123',
      },
      routingNumber: '*********',
      accountType: 'savings',
      name: 'Bank',
      status: 'verified',
    })
    company.bankAccounts!.push(account.id)
    await company.save()
  } else {
    const role = await UserRole.findOne({ sub: '123' })
    company = await Company.findById(role?.company_id)
  }

  return {
    builderCompanyId: company!.id,
    builderCompany: company,
    builder: user,
  }
}

export async function createCustomerAccount(
  supplierCompanyId: string,
  builderCopmanyUserPhone: string,
) {
  const customerAccount = await CustomerAccount.create({
    phone: builderCopmanyUserPhone,
    company_id: supplierCompanyId,
    settings: {},
  })

  return customerAccount
}

export async function createLoan(
  withInvoice = true,
  invoiceApproved = true,
  supplierInvitationDetails: any = null,
  multipleInvoice = false,
  paymentCancelled = false,
  withCompany = true,
) {
  const { builderCompanyId, builder, builderCompany } = await createBuilder()
  const { supplierCompanyId, supplierCompany, supplier } =
    await createSupplier()
  const customerAccount = await createCustomerAccount(
    supplierCompanyId,
    builder.phone || '',
  )
  const app = await LoanApplication.create({
    company_id: builderCompanyId,
    invoiceDetails: {},
    fundingSource: 'arcadia',
    draft: {
      personalInfo_firstName: 'Name',
      personalInfo_lastName: 'Last',
      personalInfo_phone: '+***********',
      businessInfo_ein: {
        hash: '123456789abcdef',
      },
      businessOwner_email: '<EMAIL>',
      businessOwner_phone: '+***********',
    },
    metadata: {
      payment_cancelled: paymentCancelled,
    },
  })
  if (withInvoice) {
    let invoice2: IInvoice | undefined
    let invoiceId: [string, string]
    const invoice = await Invoice.create({
      company_id: withCompany ? supplierCompanyId : '',
      total_amount: 2000,
      status: dictionaries.invoiceSchemaStatus.placed,
      approved: invoiceApproved,
      supplierInvitationDetails,
      payer_id: builderCompanyId,
      invoice_number: 'INV1',
      customer_account_id: customerAccount._id,
    })
    invoiceId = invoice.id
    if (multipleInvoice) {
      invoice2 = await Invoice.create({
        company_id: withCompany ? supplierCompanyId : '',
        total_amount: 1000,
        status: dictionaries.invoiceSchemaStatus.placed,
        approved: invoiceApproved,
        supplierInvitationDetails,
        payer_id: builderCompanyId,
        invoice_number: 'INV2',
        customer_account_id: customerAccount._id,
      })
      invoiceId = [invoice.id, invoice2.id]
    }
    app.invoiceDetails = {
      invoiceId,
      paymentPlan: '30',
      cardId: undefined,
    }
    await app.save()
    return {
      app,
      invoice,
      builder,
      supplierCompany,
      builderCompany,
      invoice2,
      supplier,
    }
  }
  return { app, builder, supplierCompany, builderCompany, supplier }
}
