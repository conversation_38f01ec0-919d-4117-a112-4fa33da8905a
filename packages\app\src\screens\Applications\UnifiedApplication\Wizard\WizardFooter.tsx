import { observer } from 'mobx-react'
import React, { FC } from 'react'
import { PersonalInformationCollectedWarning } from './PersonalInformationCollectedWarning'
import { useUnifiedApplication } from '../UnifiedApplicationContext'

interface IProps {
  children?: React.ReactNode
}

export const WizardFooter: FC<IProps> = observer(({ children }) => {
  const store = useUnifiedApplication()

  return (
    <div style={styles.wrapper}>
      {!store.isInReview && <PersonalInformationCollectedWarning />}
      {children}
    </div>
  )
})

const styles = {
  wrapper: {
    position: 'sticky' as const,
    bottom: 0,
    alignSelf: 'baseline',
    width: '100%',
    backgroundColor: 'white',
  },
}
