import {
  invoiceStatus,
  statusBadge,
} from '@linqpal/models/src/dictionaries/invoiceStatus'
import {
  IconAuthorized,
  IconCollected,
  IconDraft,
  IconDrawCancelled,
  IconDrawDueNext,
  IconDrawPaidOff,
  IconTransactionInProgress,
} from '../../../../assets/icons'
import React from 'react'
import { IStatusUIParams } from '../StatusLabel'
import { css } from 'styled-components'

export function getSalesDocumentStatusUIOptions(
  status: string,
): IStatusUIParams {
  // common statuses for invoices, quotes, proformas, sales orders

  // TODO: VK: get rid of legacy statusBadge
  const label = statusBadge[status]?.label || status

  switch (status) {
    case invoiceStatus.draft:
      return {
        label,
        icon: <IconDraft />,
        style: css`
          color: #9baebc;
        `,
      }
    case invoiceStatus.seen:
    case invoiceStatus.due:
    case invoiceStatus.placed:
      return {
        label,
        icon: <IconDrawDueNext />,
        style: css`
          color: #0385c8;
        `,
      }
    case invoiceStatus.paymentProcessing:
    case invoiceStatus.authorizationInReview:
    case invoiceStatus.applicationProcessing:
    case invoiceStatus.pendingDisbursement:
      return {
        label,
        icon: <IconTransactionInProgress />,
        style: css`
          color: #ff7926;
        `,
      }
    case invoiceStatus.authorized:
      return {
        label,
        icon: <IconAuthorized />,
        style: css`
          color: #7549e0;
        `,
      }
    case invoiceStatus.collected:
      return {
        label,
        icon: <IconCollected />,
        style: css`
          color: #0ec06b;
        `,
      }
    case invoiceStatus.invoiced:
    case invoiceStatus.creditApplied:
    case invoiceStatus.paid:
      return {
        label,
        icon: <IconDrawPaidOff />,
        style: css`
          color: #0ec06b;
        `,
      }
    case invoiceStatus.pastDue:
    case invoiceStatus.applicationCancelled:
    case invoiceStatus.applicationRejected:
    case invoiceStatus.cancelled:
    case invoiceStatus.expired:
    case invoiceStatus.rejected:
    case invoiceStatus.dismissed:
    case invoiceStatus.paymentFailed:
    case invoiceStatus.paymentError:
      return {
        label,
        icon: <IconDrawCancelled />,
        style: css`
          color: #ec002a;
        `,
      }
    default:
      return { label: status }
  }
}
