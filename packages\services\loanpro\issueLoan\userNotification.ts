import {
  AgreementService,
  Company,
  emailService,
  getLoginUrl,
  LMS,
  Logger,
  Notification,
  Sms,
} from '@linqpal/common-backend'
import type {
  ICompany,
  IInvoice,
  ILoanApplication,
} from '@linqpal/common-backend/src/models/types'
import { dictionaries, EInvoiceType } from '@linqpal/models'
import moment from 'moment'
import mongoose, { ClientSession } from 'mongoose'
import EmailNotifications, {
  EmailNotification,
} from '@linqpal/common-backend/src/helpers/EmailNotifications'
import { logPromiseErrors } from '@linqpal/common-backend/src/helpers/logging'
import {
  ILoan,
  ReceivableType,
} from '@linqpal/common-backend/src/services/lms.service'
import { formatPersonName } from '@linqpal/models/src/helpers/personNameFormatter'

const logger = new Logger({
  module: 'LoanPro',
  subModule: 'userNotification',
})

export const userNotification = async (
  invoices: IInvoice[],
  app: ILoanApplication,
  firstPaymentDate: string,
  session: ClientSession | null,
) => {
  // review - do we need it here? there is another call is in approveApplication step
  await AgreementService.createAgreementForLoanApplication(app.id, false, null)

  if (app.metadata?.repayment?.autoTradeCreditEnabled) return // notification are sent before pending disbursement

  const invoice = invoices[0]

  const [supplier, loan, loginUrl] = await Promise.all([
    Company.findById(invoice.company_id).session(session),
    LMS.getLoanInfo(app.lms_id),
    getLoginUrl(invoice.company_id),
  ])

  if (!supplier) return
  if (invoice.type === EInvoiceType.QUOTE) return // quote notifications are handled on authorization / invoice assigment

  // prettier-ignore
  const results = await Promise.allSettled([
    createWebNotification(app, invoices, supplier, session),
    sendCustomerEmail(app, invoices, loan, supplier),
    sendCustomerSms(app, supplier, firstPaymentDate, loginUrl),
  ])

  logPromiseErrors(results, logger)
}

async function sendCustomerEmail(
  app: ILoanApplication,
  invoices: IInvoice[],
  loan: ILoan | null,
  supplier: ICompany,
) {
  if (!loan) {
    logger.warn({ app }, `no loan found for app ${app.id}`)
    return
  }

  let emailMessage: EmailNotification
  const supplierName = supplier.legalName ?? supplier.name

  const totalInvoiceAmount = invoices.reduce(
    (amount: number, inv: IInvoice) => amount + inv.total_amount,
    0,
  )
  const drawAmount = totalInvoiceAmount + loan.fee

  const payments =
    loan.loanReceivables
      ?.filter((r) => r.type !== ReceivableType.LoanFee)
      ?.map((r) => ({ dueDate: moment.utc(r.expectedDate) })) ?? []

  const downPaymentAmount = loan.loanParameters?.[0]?.downPaymentAmount ?? 0

  // remove down payment receivable if not covered by loan fee
  if (payments.length > 1) {
    if (downPaymentAmount > 0 && downPaymentAmount > loan.fee) {
      payments.shift()
    }
  }

  if (invoices.length === 1) {
    emailMessage = EmailNotifications.drawApprovedForSingleInvoice({
      supplierName,
      customerName: '',
      invoiceNumber: invoices[0].invoice_number,
      invoiceAmount: totalInvoiceAmount,
      drawAmount: drawAmount,
      payments,
    })
  } else if (invoices.length > 1) {
    emailMessage = EmailNotifications.drawApprovedForGroupedInvoices({
      supplierName,
      customerName: '',
      totalInvoiceAmount: totalInvoiceAmount,
      drawAmount: drawAmount,
      invoices: invoices.map((i) => ({
        number: i.invoice_number,
        amount: i.total_amount,
      })),
      payments,
    })
  }

  return emailService.sendToCompanyUsersPersonalized(
    app.company_id,
    (firstName, lastName) => ({
      ...emailMessage,
      dynamicTemplateData: {
        ...emailMessage.dynamicTemplateData,
        customerName: formatPersonName(firstName, lastName),
      },
    }),
  )
}

function sendCustomerSms(
  app: ILoanApplication,
  supplier: ICompany,
  firstPaymentDate: string,
  loginUrl: string,
) {
  const supplierName = supplier.legalName || supplier.name
  const paymentDate = moment(firstPaymentDate).format('MM/DD/YY')

  const message = `BlueTape: Your credit application has been approved! Your invoice to ${supplierName} has been paid. Your loan payment is due on ${paymentDate}, click the link to login and view your loan: ${loginUrl} `

  return Sms.smsUser(app.company_id, message)
}

async function createWebNotification(
  app: ILoanApplication,
  invoices: IInvoice[],
  supplier: ICompany,
  session: ClientSession | null,
) {
  const notificationData = {
    receiver: { company_id: app.company_id },
    type: dictionaries.notificationTypes.loan,
    content: dictionaries.notificationText.loanIssued,
    metadata: {
      alertType: 'success',
      invoice_id: new mongoose.Types.ObjectId(invoices[0].id),
      invoiceNumber: invoices.map((i) => i.invoice_number).join(', '),
      amount: app.approvedAmount,
      companyName: supplier?.name,
      autoApprove: false,
      loanId: app.lms_id,
    },
    isRead: false,
    isViewed: false,
  }

  const existingNotification = await Notification.findOne({
    'metadata.invoice_id': notificationData.metadata.invoice_id,
    'receiver.company_id': notificationData.receiver.company_id,
    'metadata.alertType': notificationData.metadata.alertType,
    'metadata.loanId': notificationData.metadata.loanId,
    type: dictionaries.notificationTypes.loan,
  }).session(session)

  if (existingNotification) {
    await Notification.updateOne(
      { _id: existingNotification._id },
      {
        $set: {
          content: notificationData.content,
          metadata: notificationData.metadata,
          isRead: false,
          isViewed: false,
        },
      },
    ).session(session)
  } else {
    const newNotification = new Notification(notificationData)
    await newNotification.save({ session })
  }
}
