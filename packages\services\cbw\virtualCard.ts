import {
  AgreementService,
  Company,
  connectToDatabase,
  deactivateVirtualCard,
  deactivateVirtualCardLimit,
  Draft,
  getEnvironmentVariables,
  getPlan,
  getVirtualCard,
  Invoice,
  LMS,
  LoanApplication,
  loanDisburse,
  Logger,
  Operation,
  Transaction,
  VirtualCard,
  VirtualCardModel,
} from '@linqpal/common-backend'
import moment from 'moment-timezone'
import { ILoanApplication } from '@linqpal/common-backend/src/models/types'
import { dictionaries, exceptions, IEncrypted } from '@linqpal/models'
import mongoose from 'mongoose'
import 'moment-business-days'
import { transactionStatus } from '@linqpal/common-backend/src/services/cbw/transactionStatus'
import { readDraft } from '@linqpal/models/src/helpers/draftReader'
import { DraftNormalizer } from '@linqpal/models/src/helpers/draft/DraftNormalizer'
import { compatibilityService } from '@linqpal/common-backend/src/services/compatibility/compatibility.service'
import { LoanOrigin } from '@linqpal/common-backend/src/services/lms.service'
import { FundingSources } from '@linqpal/models/src/dictionaries/fundingSources'

const logger = new Logger({ module: 'LoanPro', subModule: 'virtualCard' })

export const virtualCard = async () => {
  await getEnvironmentVariables()
  await connectToDatabase()
  const log = logger.startTransaction()

  log.info(
    'Started virtual card flow. Start getting list of virtual cards from database',
  )

  const vcs = await VirtualCard.aggregate(getPipeline())

  log.info({ count: vcs.length }, 'Got list of virtual cards from database')

  if (vcs.length === 0) {
    log.debug('Found 0 loan virtual cards')
    return []
  }
  const results = vcs.map(processLoan)
  return Promise.all(results)
}

async function processLoan(item: any) {
  const log = logger.startTransaction()
  log.info({ cardId: item.id }, 'Start processing loan for virtual card')
  const session = await mongoose.startSession()
  session.startTransaction()

  let isVcUsed = false

  try {
    log.info(
      { loanId: item.loan_id },
      'processLoan: Start getting loan application from database',
    )

    const app = await LoanApplication.findById(item.loan._id).session(session)

    log.info(
      { loanId: item.loan_id, appId: app?.id },
      'processLoan: Got loan application',
    )

    if (!app) {
      log.debug(
        { loanId: item.loan._id },
        'processLoan: Loan application not found',
      )
      return
    }

    log.info(
      { cardId: item.cardId },
      'processLoan: Start getting virtual card from cbw',
    )

    const card = await getVirtualCard(item.cardId)

    log.info({ card }, 'processLoan: Got virtual card from cbw')

    if (!card) {
      log.debug({ cardId: item.cardId }, 'processLoan: Card not found in cbw')
      return
    }

    const volumeLimit = card.limits.find((i) => i.type === 'VOLUME')

    log.info(
      { cardId: item.cardId, volumeLimit },
      'processLoan: Found volume limit for cbw card',
    )

    if (!volumeLimit) {
      log.debug(
        { cardId: item.cardId },
        'processLoan: Card has no volume limit defined',
      )
      return
    }

    log.info(
      { virtualCardId: item._id },
      'processLoan: Start getting virtual card from database',
    )

    const vc = await VirtualCard.findById(item._id).session(session)

    log.info(
      { virtualCardId: vc?._id },
      'processLoan: Got virtual card from database',
    )

    if (!vc) {
      log.debug(
        { loanId: item._id },
        'processLoan: Virtual card not found in database',
      )
      return
    }

    log.info(
      { invoiceId: app.invoiceDetails?.invoiceId },
      'processLoan: Start getting invoice from database',
    )

    const invoice = await Invoice.findById(
      app.invoiceDetails?.invoiceId,
    ).session(session)

    log.info(
      { invoiceId: invoice?.id },
      'processLoan: Got invoice from database',
    )

    if (!invoice) {
      log.debug({ appId: app._id }, 'processLoan: Invoice not found')
      return
    }

    log.info(
      { volumeLimit, cardId: item.cardId },
      'processLoan: Start checking volume limit',
    )

    if (volumeLimit.remaining === volumeLimit.value) {
      log.info(
        { volumeLimit, cardId: item.cardId },
        'processLoan: Volume limit value is equal to remaining value. cbw card is not used',
      )

      const date = moment().businessSubtract(5, 'days').endOf('day').toDate()
      log.info(
        {
          cardId: item.cardId,
          expiryDate: date,
          loanIssueDate: item.loan.issueDate,
        },
        'processLoan: Start checking if cbw card is expired',
      )

      if (
        item.loan.issueDate < date ||
        card.cardStatus === dictionaries.VirtualCardStatus.EXPIRED
      ) {
        log.info(
          { cardId: item.cardId },
          'processLoan: Start cbw card is expired process',
        )

        if (card.cardStatus === dictionaries.VirtualCardStatus.ACTIVE) {
          await deactivateVirtualCard(card.cardId)
          log.info(
            { cardId: item.cardId },
            'processLoan: Deactivated card in cbw',
          )
        }
        vc.status = dictionaries.VirtualCardStatus.EXPIRED
        await vc.save()
        app.status = dictionaries.LOAN_APPLICATION_STATUS.EXPIRED
        await app.save()
        invoice.status = dictionaries.invoiceStatus.cancelled
        await invoice.save()
        await Company.findByIdAndUpdate(
          app.company_id,
          {
            $inc: { 'credit.balance': -app.approvedAmount },
          },
          { session },
        )

        log.info(
          {
            cardId: item.cardId,
            companyId: app.company_id,
            invoiceId: invoice._id,
            appId: app._id,
            vc_id: vc._id,
          },
          'processLoan: End processing expired cbw card. Updated loan application, invoice, virtual card and company',
        )
      }
    } else if (item.issuance.length === 0) {
      log.info(
        { cardId: item.cardId },
        'processLoan: cbw card is used. Check if card is active',
      )

      if (card.cardStatus === dictionaries.VirtualCardStatus.ACTIVE) {
        try {
          log.info(
            { cardId: item.cardId },
            'processLoan: cbw card is active. Start deactivating virtual card limit',
          )
          await deactivateVirtualCardLimit(card.cardId)
        } catch (e) {
          log.error(
            { cardId: item.cardId, error: e },
            'processLoan: Error deactivating card limit in cbw. Start deactivating virtual card',
          )
          await deactivateVirtualCard(card.cardId)
        }
        vc.status = dictionaries.VirtualCardStatus.EXPIRED
      }
      if (!vc.useDate) {
        vc.useDate = moment().toDate()
      }
      vc.usedAmount = Number(
        (Number(volumeLimit.value) - Number(volumeLimit.remaining)).toFixed(2),
      )
      await vc.save()

      log.info(
        { cardId: item.cardId, virtualCardId: vc.id },
        'processLoan: Finished deactivating virtual card limit in cbw',
      )

      log.info(
        { cardId: item.cardId },
        'processLoan: Start create virtual card loan process in LMS',
      )

      await createLoan(vc, app)
      isVcUsed = true

      log.info(
        { cardId: item.cardId },
        'processLoan: Created virtual card loan in LMS. Start disbursing loan',
      )

      const resp = await loanDisburse(
        app,
        vc.usedAmount,
        session,
        process.env.LP_CBW_GL2_IDENTIFICATION,
      )
      if (
        resp.transactionStatus === dictionaries.CbwTransactionStatus.Rejected ||
        resp.transactionStatus === dictionaries.CbwTransactionStatus.Pending
      ) {
        vc.status = dictionaries.VirtualCardStatus.ACTIVE
        await vc.save()
      } else {
        invoice.status = dictionaries.invoiceSchemaStatus.paid
        await invoice.save()
      }

      log.info(
        { appId: app._id, invoiceId: invoice.id },
        'processLoan: Disbursed loan and updated invoice in database.',
      )

      log.info(
        { appId: app._id, virtualCardId: vc.id },
        'processLoan: End create loan virtual card process',
      )
    } else if (vc.status === dictionaries.VirtualCardStatus.ACTIVE) {
      if (
        item.issuance.filter(
          (o: { status: string }) =>
            o.status === dictionaries.OPERATION_STATUS.PROCESSING,
        ).length > 0
      ) {
        const t = await Transaction.findOne({
          operation_id: item.issuance[0]._id,
          type: dictionaries.TRANSACTION_TYPES.LOAN.DISBURSEMENT,
          status: dictionaries.TRANSACTION_STATUS.PROCESSING,
        }).session(session)
        if (t) {
          const extRef = `${process.env.LP_MODE}_${t._id.toString()}`
          const resp = await transactionStatus(
            process.env.LP_CBW_GL2_IDENTIFICATION,
            null,
            extRef,
          )
          switch (resp.transactionStatus) {
            case dictionaries.CbwTransactionStatus.Rejected:
              t.status = dictionaries.TRANSACTION_STATUS.ERROR
              await t.save()
              await Operation.findByIdAndUpdate(item.issuance[0]._id, {
                status: dictionaries.OPERATION_STATUS.FAIL,
              }).session(session)
              break
            case dictionaries.CbwTransactionStatus.Processed:
              t.status = dictionaries.TRANSACTION_STATUS.SUCCESS
              await t.save()
              await Operation.findByIdAndUpdate(item.issuance[0]._id, {
                status: dictionaries.OPERATION_STATUS.SUCCESS,
              }).session(session)
              vc.status = dictionaries.VirtualCardStatus.EXPIRED
              await vc.save()
              break
          }
          return
        }
      }
      // Deactivate old issued cards
      log.info(
        { cardId: item.cardId },
        'processLoan: Start deactivating old issued cards',
      )
      try {
        try {
          log.info(
            { cardId: item.cardId },
            'processLoan: Card is active. Start deactivating  virtual card limit in cbw',
          )
          await deactivateVirtualCardLimit(card.cardId)
        } catch (e) {
          log.error(
            { cardId: item.cardId, error: e },
            'processLoan: Error deactivating card limit in cbw. Start deactivating virtual card',
          )
          await deactivateVirtualCard(card.cardId)
        }
      } catch (e) {
        log.error(
          { cardId: item.cardId, error: e },
          'processLoan: Error deactivating old issued cards',
        )
      }
      vc.status = dictionaries.VirtualCardStatus.EXPIRED

      if (!vc.useDate) {
        vc.useDate = moment().toDate()
      }
      vc.usedAmount = Number(
        (Number(volumeLimit.value) - Number(volumeLimit.remaining)).toFixed(2),
      )
      await vc.save()

      log.info(
        { cardId: item.cardId, virtualCard: vc },
        'processLoan: Finished deactivating virtual card limit in cbw',
      )
    }

    log.info(
      { cardId: item.cardId, appId: app._id },
      'processLoan: Start commiting transaction',
    )

    log.info(
      { cardId: item.cardId, appId: app._id },
      'processLoan: Transaction commited. End processing loan',
    )

    return app._id
  } catch (e) {
    log.error(
      { error: e },
      'processLoan: Error occurred. Start aborting transaction',
    )
    if (session.inTransaction()) {
      await session.abortTransaction()
    }
  } finally {
    if (session.inTransaction()) {
      await session.commitTransaction()
    }

    try {
      log.info(
        { cardId: item.cardId, appId: item.loan._id },
        'regenerate agreement for VC based on loanApplicationId',
      )

      if (isVcUsed) {
        await AgreementService.regenerateBnplAgreement(item.loan._id)
      }
    } catch (e) {
      log.error(
        { error: e },
        'processLoan: Error occurred. Unable to create agreement for loan',
      )
    }

    await session.endSession()
  }

  return null
}

function getPipeline() {
  return [
    {
      $match: {
        status: dictionaries.VirtualCardStatus.ACTIVE,
      },
    },
    {
      $lookup: {
        from: LoanApplication.collection.name,
        localField: 'cardId',
        foreignField: 'invoiceDetails.cardId',
        as: 'loan',
      },
    },
    { $unwind: '$loan' },
    {
      $match: {
        'loan.status': dictionaries.LOAN_APPLICATION_STATUS.APPROVED,
      },
    },
    {
      $lookup: {
        from: Operation.collection.name,
        as: 'issuance',
        let: { loan_id: { $toString: '$loan._id' } },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$owner_id', '$$loan_id'] },
                  {
                    $in: [
                      '$status',
                      [
                        dictionaries.OPERATION_STATUS.SUCCESS,
                        dictionaries.OPERATION_STATUS.PROCESSING,
                      ],
                    ],
                  },
                  { $eq: ['$type', dictionaries.OPERATION_TYPES.LOAN.ISSUE] },
                ],
              },
            },
          },
        ],
      },
    },
  ]
}

async function createLoan(card: VirtualCardModel, app: ILoanApplication) {
  const log = logger.startTransaction()
  const payerCompany = await Company.findById(card.company_id)
  if (!payerCompany) throw new exceptions.LogicalError('Company not found')
  const { invoiceDetails } = app

  const loanPlan = await getPlan(invoiceDetails.paymentPlan)
  const loanTemplate = loanPlan.lmsTemplateId

  let draft = app.draft
  if (!draft) {
    // all general applications have drafts,
    // but some legacy application may not have draft attached
    const rawDraft = await Draft.findOne({
      company_id: payerCompany.id,
      type: 'loan_application',
    })

    if (rawDraft) {
      draft = DraftNormalizer.normalize(rawDraft)
    }
  }

  const unifiedDraft = readDraft(draft)
  const einHash = (unifiedDraft?.businessInfo_ein as IEncrypted)?.hash

  if (!einHash) {
    throw new exceptions.LogicalError('Ein was not found')
  }

  log.info(
    {
      companyId: card.company_id,
      appId: app._id,
      loanTemplate: loanTemplate,
      einHash: einHash,
    },
    'createLoan: Got data for creating loan',
  )
  if (!loanPlan) {
    throw new exceptions.LogicalError('Loan plan is not selected')
  }
  if (!app.lms_id) {
    log.info(
      { appId: app._id },
      'createLoan: Application loan is not created in LMS. Start creating loan in LMS process',
    )

    const drawApprovalDetails =
      await compatibilityService.getCreateLoanDrawApprovalDetails(app)

    const { id } = await LMS.createLoan(
      card.company_id,
      drawApprovalDetails.companyName,
      loanTemplate,
      card.usedAmount || 0,
      einHash,
      LoanOrigin.Normal,
      drawApprovalDetails.debtInvestor ?? FundingSources.Arcadia,
      drawApprovalDetails.projectId,
      drawApprovalDetails.merchantId,
      drawApprovalDetails.merchantName,
      drawApprovalDetails.drawApprovalId,
      drawApprovalDetails.loanPayables,
    )
    app.usedAmount = card.usedAmount
    app.lms_id = id
    app.issueDate = card.useDate || app.issueDate
    await app.save()
    log.info(
      { appId: app._id, lmsId: app.lms_id },
      'createLoan: Created loan in LMS',
    )
  }
  log.info(
    { appId: app._id, lmsId: app.lms_id },
    'createLoan: Start activating loan in LMS and syncing application fields',
  )

  await LMS.activateLoan(app.lms_id)
  await LMS.syncLoanApplicationFields(app)

  log.info(
    { appId: app._id, lmsId: app.lms_id },
    'createLoan: End activating loan in LMS and syncing application fields',
  )
}
