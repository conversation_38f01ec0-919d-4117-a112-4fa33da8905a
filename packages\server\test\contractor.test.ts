import { CompanyStatus, routes, routes2 } from '@linqpal/models'
import chai from 'chai'
import chaiHttp from 'chai-http'
import { _FirebaseTokenVerifier, FakeUsers } from './_firebase_admin'
import { _Authorization } from './_axios'
import sinon from 'sinon'
import {
  BankAccount,
  CardPricingPackage,
  CardProducts,
  Company,
  Draft,
  getPaymentPlans,
  Invitation,
  Invoice,
  LoanApplication,
  Sms,
  UserRole,
} from '@linqpal/common-backend'
import mongoose from 'mongoose'
import moment from 'moment'
import { LOAN_APPLICATION_STATUS } from '@linqpal/models/src/dictionaries'
import { beforeEachMockSFN } from './helper'

chai.use(chaiHttp)
chai.should()
let tokenVerifier: sinon.SinonStub, auth: ReturnType<typeof _Authorization>
const { constructor1 } = FakeUsers

describe('Contractor', () => {
  let smsSend: sinon.SinonStub
  beforeEach(async () => {
    tokenVerifier = _FirebaseTokenVerifier()
    auth = _Authorization(constructor1.auth)
    smsSend = sinon.stub(Sms, 'send').callsFake(async () => undefined)
  })
  afterEach(() => {
    auth.restore()
    tokenVerifier.restore()
    smsSend.restore()
  })
  describe('Invite Colleague', () => {
    it('Invite', async () => {
      // invalid phone number
      await routes.invitation.invite({
        firstName: 'John',
        lastName: 'Doe',
        phone: 'qweqwe',
      })

      // no first name
      let resp = await routes.invitation.invite({ phone: '7027276804' })
      resp.result.should.be.equal('LogicalError')

      // Right data
      resp = await routes.invitation.invite({
        firstName: 'John',
        lastName: 'Doe',
        phone: '7027276804',
      })
      resp.result.should.be.equal('ok')
      smsSend.calledOnce.should.be.equal(true)

      const invitations = await Invitation.find()
      invitations.should.have.lengthOf(1)
      const invitation = invitations[0]
      invitation.login.should.be.equal('+***********')
      invitation.fullName.should.be.equal('John Doe')

      const wrongInvitationCode = '5ffdb0a1fb263f4009f4e113'
      resp = await routes.invitation.check(wrongInvitationCode)
      resp.result.should.be.equal('LogicalError')

      const invitationId = invitation._id.toString()
      resp = await routes.invitation.check(invitationId)
      resp.result.should.be.equal('ok')
    })

    it('Accept', async () => {
      let resp = await routes.invitation.invite({
        firstName: 'John',
        lastName: 'Doe',
        phone: '7027276804',
      })
      resp.result.should.be.equal('ok')
      smsSend.calledOnce.should.be.equal(true)

      let invitations = await Invitation.find()
      invitations.should.have.lengthOf(1)
      const invitation = invitations[0]
      const invitationId = invitation._id.toString()

      resp = await routes.invitation.accept({ code: invitationId })
      resp.result.should.be.equal('ok')
      invitations = await Invitation.find()
      invitations.should.have.lengthOf(0)

      const userRoles = await UserRole.find()
      userRoles.should.have.lengthOf(1)
      const userRole = userRoles[0]
      userRole.company_id.should.be.equal(invitation.company_id)
    })

    it('Reject', async () => {
      let resp = await routes.invitation.invite({
        firstName: 'John',
        lastName: 'Doe',
        phone: '7027276804',
      })
      resp.result.should.be.equal('ok')
      smsSend.calledOnce.should.be.equal(true)

      let invitations = await Invitation.find()
      invitations.should.have.lengthOf(1)
      const invitation = invitations[0]
      const invitationId = invitation._id.toString()

      resp = await routes.invitation.reject(invitationId)
      resp.result.should.be.equal('ok')
      invitations = await Invitation.find()
      invitations.should.have.lengthOf(0)
    })
  })

  describe('Get Pricing Package Fee', () => {
    it('Debit Card Regulated', async () => {
      const pp = await routes.company.cardPricingPackage()
      pp.result.should.be.equal('ok')
      pp.should.have.property('pricingPackage')
      const pricingPackages = await CardPricingPackage.find()
      pricingPackages.length.should.eq(5)

      const debitCardRegulated = await BankAccount.create({
        accountNumber: {
          display: '********1111',
        },
        cardMetadata: {
          accountId: 'ASASx6587ghkjj',
          avsAuthorizeID: '234',
          avsCode: 'Y',
          avsNetworkRC: '44',
          avsResultText: 'NOT DECLINED',
          avsSecurityCode: 'M',
          expirationDate: '202303',
          isPullEnabled: true,
          isRegulated: true,
          lastFour: '1111',
          network: 'Visa',
          token: '80E1iEMJ243WsTF0pBM',
          type: 'Debit',
        },
        name: 'Visa - Card',
        paymentMethodType: 'card',
        isManualEntry: true,
        accountholderName: 'Test Card 77',
        billingAddress: {
          addressLine1: 'Traction Street',
          addressLine2: '3094 ',
          city: 'Spartanburg',
          stateCode: 'South Carolina',
          zipCode: '29303',
        },
      })
      const company = await Company.create({
        name: 'Test supplier',
        type: 'supplier',
        bankAccounts: [
          new mongoose.Types.ObjectId(debitCardRegulated._id.toString()),
        ],
        email: '<EMAIL>',
        phone: '+***********',
        settings: {
          cardPricingPackageId: 'packageC',
        },
      })
      const invoice = await Invoice.create({
        company_id: company._id,
        total_amount: 100,
        expiration_date: moment('2021-08-11T00:00:00.000Z').format(),
        status: 'PLACED',
      })

      const resp = await routes2.invoice.getPricingPackageFee({
        invoiceIds: [invoice._id.toString()],
        accountId: debitCardRegulated._id.toString(),
      })
      resp.result!.should.be.equal('ok')
      resp.should.have.property('pricingPackage')
      const { pricingPackage } = resp
      pricingPackage!.totalAmount.should.eq(100)
      pricingPackage!.fee.should.eq(0)
    })

    it('Credit Card Personal', async () => {
      await CardProducts.create({
        productCode: 'G',
        productName: 'Visa Business',
        personalOrBusiness: 'business',
        cardType: 'credit',
        isbpsp: true,
        isl2eligible: true,
        isl3eligible: true,
        isTravel: false,
      })
      const pp = await routes.company.cardPricingPackage()
      pp.result.should.be.equal('ok')
      pp.should.have.property('pricingPackage')
      const pricingPackages = await CardPricingPackage.find()
      pricingPackages.length.should.eq(5)

      const debitCardRegulated = await BankAccount.create({
        accountNumber: {
          display: '********1111',
        },
        cardMetadata: {
          accountId: 'ASASx6587ghkjj',
          avsAuthorizeID: '234',
          avsCode: 'Y',
          avsNetworkRC: '44',
          avsResultText: 'NOT DECLINED',
          avsSecurityCode: 'M',
          expirationDate: '202303',
          isPullEnabled: true,
          isRegulated: true,
          lastFour: '1111',
          network: 'Visa',
          token: '80E1iEMJ243WsTF0pBM',
          type: 'Credit',
          productCode: 'G',
        },
        name: 'Visa - Card',
        paymentMethodType: 'card',
        isManualEntry: true,
        accountholderName: 'Test Card 77',
        billingAddress: {
          addressLine1: 'Traction Street',
          addressLine2: '3094 ',
          city: 'Spartanburg',
          stateCode: 'South Carolina',
          zipCode: '29303',
        },
      })
      const company = await Company.create({
        name: 'Test supplier',
        type: 'supplier',
        bankAccounts: [
          new mongoose.Types.ObjectId(debitCardRegulated._id.toString()),
        ],
        email: '<EMAIL>',
        phone: '+***********',
        settings: {
          cardPricingPackageId: 'packageC',
        },
      })
      const invoice = await Invoice.create({
        company_id: company._id,
        total_amount: 100,
        expiration_date: moment('2021-08-11T00:00:00.000Z').format(),
        status: 'PLACED',
      })

      const resp = await routes2.invoice.getPricingPackageFee({
        invoiceIds: [invoice._id.toString()],
        accountId: debitCardRegulated._id.toString(),
      })
      resp.result!.should.be.equal('ok')
      resp.should.have.property('pricingPackage')
      const { pricingPackage } = resp
      pricingPackage!.totalAmount.should.eq(102.5)
      pricingPackage!.fee.should.eq(2.5)
    })
    it('should get correct ACH discount', async () => {
      const bank = await BankAccount.create({
        accountNumber: {
          display: '********1111',
        },
        name: 'FinBank',
        paymentMethodType: 'bank',
        isManualEntry: true,
        accountholderName: 'Test Bank',
        billingAddress: {
          addressLine1: 'Traction Street',
          addressLine2: '3094 ',
          city: 'Spartanburg',
          stateCode: 'South Carolina',
          zipCode: '29303',
        },
      })
      const company = await Company.create({
        name: 'Test supplier',
        type: 'supplier',
        bankAccounts: [new mongoose.Types.ObjectId(bank._id.toString())],
        email: '<EMAIL>',
        phone: '+***********',
        settings: {
          cardPricingPackageId: 'packageC',
          achDiscount: {
            percentage: 10,
            validityInDays: 5,
          },
        },
      })
      const invoice = await Invoice.create({
        company_id: company._id,
        total_amount: 100,
        invoice_due_date: moment().add(1, 'days'),
        expiration_date: moment('2021-08-11T00:00:00.000Z').format(),
        status: 'PLACED',
      })

      let resp = await routes2.invoice.getPricingPackageFee({
        invoiceIds: [invoice._id.toString()],
        accountId: bank._id.toString(),
      })
      resp.result!.should.be.equal('ok')
      resp.should.have.property('pricingPackage')
      const { pricingPackage } = resp
      pricingPackage!.discount?.should.eq(10)
      pricingPackage!.fee.should.eq(0)

      // No discount when its past due
      const invoicePastdue = await Invoice.create({
        company_id: company._id,
        total_amount: 100,
        invoice_due_date: moment().subtract(1, 'days'),
        expiration_date: moment('2021-08-11T00:00:00.000Z').format(),
        status: 'PLACED',
      })
      resp = await routes2.invoice.getPricingPackageFee({
        invoiceIds: [invoicePastdue._id.toString()],
        accountId: bank._id.toString(),
      })
      resp.result!.should.be.equal('ok')
      resp.should.have.property('pricingPackage')
      resp.pricingPackage!.discount?.should.eq(0)
      resp.pricingPackage!.fee.should.eq(0)

      // No discount when its due but validity is expired
      const invoicedue = await Invoice.create({
        company_id: company._id,
        total_amount: 100,
        invoice_date: moment().subtract(6, 'days'),
        invoice_due_date: moment().add(10, 'days'),
        expiration_date: moment('2021-08-11T00:00:00.000Z').format(),
        status: 'PLACED',
      })
      resp = await routes2.invoice.getPricingPackageFee({
        invoiceIds: [invoicedue._id.toString()],
        accountId: bank._id.toString(),
      })
      resp.result!.should.be.equal('ok')
      resp.should.have.property('pricingPackage')
      resp.pricingPackage!.discount?.should.eq(0)

      // Boundary date check
      await Invoice.updateOne(
        { _id: invoicedue._id },
        { invoice_date: moment().subtract(5, 'days') },
      )
      resp = await routes2.invoice.getPricingPackageFee({
        invoiceIds: [invoicedue._id.toString()],
        accountId: bank._id.toString(),
      })
      resp.result!.should.be.equal('ok')
      resp.should.have.property('pricingPackage')
      resp.pricingPackage!.discount?.should.eq(10)

      await Invoice.updateOne(
        { _id: invoicedue._id },
        { invoice_due_date: moment() },
      )
      resp = await routes2.invoice.getPricingPackageFee({
        invoiceIds: [invoicedue._id.toString()],
        accountId: bank._id.toString(),
      })
      resp.result!.should.be.equal('ok')
      resp.should.have.property('pricingPackage')
      resp.pricingPackage!.discount?.should.eq(10)
    })
  })

  describe('LoanPro', () => {
    describe('Upgrade payment plan', () => {
      let upgradePlanStub: sinon.SinonStub

      beforeEach(async () => {
        await getPaymentPlans()
      })

      afterEach(() => {
        upgradePlanStub?.restore()
      })

      it.skip('should upgrade a 30 day loan', async () => {
        const company = await Company.create({ status: CompanyStatus.Approved })
        await UserRole.create({
          sub: constructor1.info.sub,
          company_id: company._id,
          role: 'Owner',
        })
        const loanApplication = await LoanApplication.create({
          company_id: company._id,
          invoiceDetails: { paymentPlan: '30' },
          status: 'approved',
        })

        const response = await routes.contractor.upgradePlan(
          loanApplication.id,
          '60',
        )
        response.should.have.status(200)
        response.body.should.have.property('loan')
        response.body.should.have.property('loanApplication')
        upgradePlanStub.calledOnce.should.be.equal(true)
      })

      it.skip('should return an error if the loan is not a 30 day loan', async () => {
        const company = await Company.create({ status: CompanyStatus.Approved })
        await UserRole.create({
          sub: constructor1.info.sub,
          company_id: company._id,
          role: 'Owner',
        })
        const loanApplication = await LoanApplication.create({
          company_id: company._id,
          invoiceDetails: { paymentPlan: '60' },
          status: 'approved',
        })

        const response = await routes.contractor.upgradePlan(
          loanApplication.id,
          '60',
        )
        response.should.have.status(400)
        upgradePlanStub.calledOnce.should.be.equal(false)
      })

      it.skip('should return an error when trying to upgrade to a 30 day loan', async () => {
        const company = await Company.create({ status: CompanyStatus.Approved })
        await UserRole.create({
          sub: constructor1.info.sub,
          company_id: company._id,
          role: 'Owner',
        })
        const loanApplication = await LoanApplication.create({
          company_id: company._id,
          invoiceDetails: { paymentPlan: '60' },
          status: 'approved',
        })

        const response = await routes.contractor.upgradePlan(
          loanApplication.id,
          '30',
        )
        response.should.have.status(400)
        upgradePlanStub.calledOnce.should.be.equal(false)
      })

      it.skip('should return an error if the loan is not approved', async () => {
        const company = await Company.create({ status: CompanyStatus.Approved })
        await UserRole.create({
          sub: constructor1.info.sub,
          company_id: company._id,
          role: 'Owner',
        })
        const loanApplication = await LoanApplication.create({
          company_id: company._id,
          invoiceDetails: { paymentPlan: '30' },
          status: 'pending',
        })

        const response = await routes.contractor.upgradePlan(
          loanApplication.id,
          '90',
        )
        response.should.have.status(400)
        upgradePlanStub.calledOnce.should.be.equal(false)
      })

      it.skip('should return an error if the loan does not belong to user', async () => {
        const company = await Company.create({ status: CompanyStatus.Approved })
        await UserRole.create({
          sub: constructor1.info.sub,
          company_id: company._id,
          role: 'Owner',
        })
        const loanApplication = await LoanApplication.create({
          company_id: 'random id',
          invoiceDetails: { paymentPlan: '30' },
          status: 'approved',
        })

        const response = await routes.contractor.upgradePlan(
          loanApplication.id,
          '60',
        )
        response.should.have.status(403)
        upgradePlanStub.calledOnce.should.be.equal(false)
      })
      it('should get card fees for Visa, regulated', async () => {
        const method = await BankAccount.create({
          accountNumber: {
            display: '********1111',
          },
          cardMetadata: {
            accountId: 'ASASx6587ghkjj',
            avsAuthorizeID: '234',
            avsCode: 'Y',
            avsNetworkRC: '44',
            avsResultText: 'NOT DECLINED',
            avsSecurityCode: 'M',
            expirationDate: '202303',
            isPullEnabled: true,
            isRegulated: true,
            lastFour: '1111',
            network: 'Visa',
            token: '80E1iEMJ243WsTF0pBM',
            type: 'Debit',
          },
          name: 'Visa - Card',
          paymentMethodType: 'card',
          isManualEntry: true,
          accountholderName: 'Test Card 77',
          billingAddress: {
            addressLine1: 'Traction Street',
            addressLine2: '3094 ',
            city: 'Spartanburg',
            stateCode: 'South Carolina',
            zipCode: '29303',
          },
        })
        const res = await routes.contractor.calculateCardFees(method.id)
        res.should.have.property('fees')
        res.fees.should.be.equal(0)
      })
      it('should get card fees for Amex', async () => {
        const method = await BankAccount.create({
          accountNumber: {
            display: '********1111',
          },
          cardMetadata: {
            accountId: 'ASASx6587ghkjj',
            avsAuthorizeID: '234',
            avsCode: 'Y',
            avsNetworkRC: '44',
            avsResultText: 'NOT DECLINED',
            avsSecurityCode: 'M',
            expirationDate: '202303',
            isPullEnabled: true,
            isRegulated: true,
            lastFour: '1111',
            network: 'Amex',
            token: '80E1iEMJ243WsTF0pBM',
            type: 'Debit',
          },
          name: 'Amex - Card',
          paymentMethodType: 'card',
          isManualEntry: true,
          accountholderName: 'Test Card 77',
          billingAddress: {
            addressLine1: 'Traction Street',
            addressLine2: '3094 ',
            city: 'Spartanburg',
            stateCode: 'South Carolina',
            zipCode: '29303',
          },
        })
        const res = await routes.contractor.calculateCardFees(method.id)
        res.should.have.property('fees')
        res.fees.should.be.equal(3.8)
      })
      it('should get card fees for Amex', async () => {
        const method = await BankAccount.create({
          accountNumber: {
            display: '********1111',
          },
          cardMetadata: {
            accountId: 'ASASx6587ghkjj',
            avsAuthorizeID: '234',
            avsCode: 'Y',
            avsNetworkRC: '44',
            avsResultText: 'NOT DECLINED',
            avsSecurityCode: 'M',
            expirationDate: '202303',
            isPullEnabled: true,
            isRegulated: true,
            lastFour: '1111',
            network: 'Visa',
            token: '80E1iEMJ243WsTF0pBM',
            type: 'Credit',
          },
          name: 'Visa - Card',
          paymentMethodType: 'card',
          isManualEntry: true,
          accountholderName: 'Test Card 77',
          billingAddress: {
            addressLine1: 'Traction Street',
            addressLine2: '3094 ',
            city: 'Spartanburg',
            stateCode: 'South Carolina',
            zipCode: '29303',
          },
        })
        const res = await routes.contractor.calculateCardFees(method.id)
        res.should.have.property('fees')
        res.fees.should.be.equal(3.5)
      })
      it('no card fees if bank is choosen', async () => {
        const method = await BankAccount.create({
          accountNumber: {
            display: '********1111',
          },
          name: 'FinBank',
          paymentMethodType: 'bank',
          isManualEntry: true,
          accountholderName: 'Test Bank',
          billingAddress: {
            addressLine1: 'Traction Street',
            addressLine2: '3094 ',
            city: 'Spartanburg',
            stateCode: 'South Carolina',
            zipCode: '29303',
          },
        })
        const res = await routes.contractor.calculateCardFees(method.id)
        res.should.have.property('fees')
        res.fees.should.be.equal(0)
      })
    })
  })
  // eslint-disable-next-line jest/no-disabled-tests
  describe.skip('Call decision engine', () => {
    let contractorComp: any
    beforeEachMockSFN()
    beforeEach(async () => {
      contractorComp = await Company.create({
        name: 'Test contractor',
        credit: {
          LoCnumber: new mongoose.Types.ObjectId(),
        },
      })
    })
    it('should be success - for supplier sent invoice', async () => {
      await Draft.create({
        company_id: contractorComp._id,
        data: {
          businessInfo: {
            group: 'Business info',
            title: 'Business',
            items: [{ identifier: 'name', filled: true, content: 'test inc' }],
          },
        },
        type: 'loan_application',
      })
      const comp = await Company.create({
        name: 'Test supplier',
      })
      const invoice = await Invoice.create({
        company_id: comp._id,
        invoice_number: 'test inv 2',
      })
      const app = await LoanApplication.create({
        company_id: contractorComp._id,
        invoiceDetails: {
          invoiceId: [invoice._id],
          paymentPlan: '60',
          isSentBack: false,
        },
        status: 'new',
      })
      const resp = await routes.contractor.callDecisionEngine(app.id)
      resp.should.have.property('id')
      resp.id.should.be.equal(app.id)
      const updated = await LoanApplication.findById(app.id)
      chai.expect(updated!.status).to.equal(LOAN_APPLICATION_STATUS.PROCESSING)
    })
    it('should be success - for uploaded invoice', async () => {
      await Draft.create({
        company_id: contractorComp._id,
        data: {
          businessInfo: {
            group: 'Business info',
            title: 'Business',
            items: [{ identifier: 'name', filled: true, content: 'test inc' }],
          },
        },
        type: 'loan_application',
      })
      const invoice = await Invoice.create({
        company_id: '',
        invoice_number: 'test inv 2',
      })
      const app = await LoanApplication.create({
        company_id: contractorComp._id,
        invoiceDetails: {
          invoiceId: [invoice._id],
          paymentPlan: '60',
          isSentBack: false,
        },
        status: 'new',
      })
      const resp = await routes.contractor.callDecisionEngine(app.id)
      resp.should.have.property('id')
      resp.id.should.be.equal(app.id)
      const updated = await LoanApplication.findById(app.id)
      chai.expect(updated!.status).to.equal(LOAN_APPLICATION_STATUS.PROCESSING)
    })
    it('should throw when validations fail', async () => {
      const app = await LoanApplication.create({
        company_id: contractorComp._id,
        invoiceDetails: {
          invoiceId: '631a4d823b9171630f45df0e',
          paymentPlan: '',
        },
        status: 'new',
      })
      let resp = await routes.contractor.callDecisionEngine(app.id)
      resp.should.have.property('result')
      resp.result.should.be.equal('LogicalError')
      resp.code.should.be.equal('error/no-payment-plan-selected')

      const app2 = await LoanApplication.create({
        company_id: contractorComp._id,
        invoiceDetails: { paymentPlan: '20' },
        status: 'new',
      })
      resp = await routes.contractor.callDecisionEngine(app2.id)
      resp.should.have.property('result')
      resp.result.should.be.equal('LogicalError')
      resp.code.should.be.equal('error/wrong-payment-plan')
    })
    it('should throw when previous rejection validation fails', async () => {
      await LoanApplication.create({
        company_id: contractorComp._id,
        invoiceDetails: {
          invoiceId: '631a4d823b9171630f45df0e',
          paymentPlan: '30',
        },
        status: LOAN_APPLICATION_STATUS.REJECTED,
        submitDate: moment().subtract(2, 'months').toDate(),
      })
      await LoanApplication.create({
        company_id: contractorComp._id,
        invoiceDetails: {
          invoiceId: '631a4d823b9171630f45df0e',
          paymentPlan: '60',
        },
        status: LOAN_APPLICATION_STATUS.CANCELED,
        submitDate: moment().subtract(1, 'months').toDate(),
      })
      const app = await LoanApplication.create({
        company_id: contractorComp._id,
        invoiceDetails: {
          invoiceId: '631a4d823b9171630f45df0e',
        },
        status: LOAN_APPLICATION_STATUS.NEW,
      })
      const resp = await routes.contractor.callDecisionEngine(app.id)
      resp.should.have.property('result')
      resp.result.should.be.equal('LogicalError')
      resp.code.should.be.equal('last-app-was-rejected')
    })
    it('should reset isSentBack when decision engine is called again', async () => {
      await Draft.create({
        company_id: contractorComp._id,
        data: {
          businessInfo: {
            group: 'Business info',
            title: 'Business',
            items: [{ identifier: 'name', filled: true, content: 'test inc' }],
          },
        },
        type: 'general_application',
      })
      const app = await LoanApplication.create({
        company_id: contractorComp._id,
        status: 'new',
        isSentBack: true,
      })
      const resp = await routes.contractor.callDecisionEngine(app.id)
      resp.should.have.property('id')
      resp.id.should.be.equal(app.id)
      const updated = await LoanApplication.findById(app.id)
      chai.expect(updated).should.not.be.null
      updated!.isSentBack?.should.be.equal(false)
    })
  })
})
