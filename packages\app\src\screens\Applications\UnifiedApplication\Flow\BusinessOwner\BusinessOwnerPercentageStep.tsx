import React, { FC } from 'react'
import { observer } from 'mobx-react-lite'
import { useTranslation } from 'react-i18next'
import { BtPercentageInput } from '@linqpal/components/src/ui/BtPercentageInput'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { runInAction } from 'mobx'
import { IUnifiedApplicationEditor } from '../getUnifiedApplicationEditor'

const BusinessOwnerPercentageEditor: FC = () => {
  const { t } = useTranslation('application')
  const store = useUnifiedApplication()

  const validatePercentage = (value: string | undefined) => {
    const percentage = Number(value)
    const maxPercentage = 100 - store.draft.totalCoOwnersPercentage

    return percentage > maxPercentage
      ? t('ValidationErrors.InvalidMaximumPercentage', {
          maxPercent: maxPercentage,
        })
      : ''
  }

  const handleChange = (value: string) => {
    runInAction(() => {
      store.currentUser.ownershipPercentage = Number(value)
    })
  }

  const value = store.currentUser.ownershipPercentage
    ? store.currentUser.ownershipPercentage.toString()
    : ''

  return (
    <BtPercentageInput
      value={value}
      onChangeText={handleChange}
      validate={validatePercentage}
      label={t('Owner.PercentageLabel')}
      testID="UnifiedApplication.BusinessOwner.OwnershipPercentage"
    />
  )
}

export const BusinessOwnerPercentageStep: IUnifiedApplicationEditor = {
  options: {
    title: 'Owner.OwnerPercentage',
  },
  component: observer(BusinessOwnerPercentageEditor),
}
