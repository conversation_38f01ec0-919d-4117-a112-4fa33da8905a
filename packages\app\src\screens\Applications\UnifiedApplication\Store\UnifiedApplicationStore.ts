import { makeAutoObservable, observable, toJS } from 'mobx'
import {
  CompanyStatus,
  IUIBankAccount,
  IUnifiedApplicationDraft,
  routes2,
} from '@linqpal/models'
import { UnifiedApplicationDraft } from './UniffiedApplicationDraft'
import { ApplicationType } from '@linqpal/models/src/dictionaries/applicationType'
import {
  ApplicationUserRole,
  IApplicationUser,
} from '../../../../../../models/src/applications/unified/IUnifiedApplicationDraft'
import {
  UnifiedApplicationReviewStep,
  UnifiedApplicationReviewStore,
} from './UnifiedApplicationReviewStore'
import { CoOwnersStore } from './CoOwnersStore'
import { UnifiedApplicationFlow } from '../../../../../../models/src/applications/unified/UnifiedApplicationFlow'
import { FlowController } from '../../../../../../models/src/applications/FlowController'
import RootStore from '../../../../store/RootStore'
import {
  Groups,
  Steps,
} from '@linqpal/models/src/applications/unified/UnifiedApplicationSteps'
import ApplicationStore from '../../../GeneralApplication/Application/ApplicationStore'
import { IUnifiedApplicationOptions } from '@linqpal/models/src/applications/unified/IUnifiedApplicationOptions'
import { IUnifiedApplicationStepOptions } from '../Flow/getUnifiedApplicationEditor'

export class UnifiedApplicationStore {
  private _flow: UnifiedApplicationFlow = new UnifiedApplicationFlow()

  private _flowController: FlowController<
    IUnifiedApplicationDraft,
    IUnifiedApplicationOptions
  >

  private _applicationOptions: IUnifiedApplicationOptions

  private _stepOptions: IUnifiedApplicationStepOptions = {}

  private _draft: UnifiedApplicationDraft

  private _previousStep = ''

  private _isSubmitting = false

  private _reviewStore = new UnifiedApplicationReviewStore()

  private _coOwnersStore = new CoOwnersStore()

  private _isPrimaryBankAccountChanged = false

  private _currentUser!: IApplicationUser

  constructor() {
    this._flow = new UnifiedApplicationFlow()
    this._draft = new UnifiedApplicationDraft()
    this._flowController = new FlowController(this._flow.definition)

    this._applicationOptions = {
      type: ApplicationType.Credit,
      userId: RootStore.userStore.user!.id,
      companyStatus: RootStore.userStore.company?.status || CompanyStatus.New,
    }

    makeAutoObservable(this)
  }

  public get reviewStore() {
    return this._reviewStore
  }

  public get coOwnersStore() {
    return this._coOwnersStore
  }

  public get currentStep(): string {
    return this._draft?.currentStep || ''
  }

  public get previousStep(): string {
    return this._previousStep
  }

  public get currentGroup(): string {
    const currentStep = this._draft?.currentStep || ''
    const group = currentStep.split('.')[0] || ''

    return group
  }

  public get draft() {
    return this._draft
  }

  public get type(): ApplicationType {
    return this._applicationOptions.type
  }

  public get currentUserId(): string {
    if (!this.currentUser.id) {
      throw new Error('Current draft user is not initialized')
    }

    return this.currentUser.id
  }

  public get isSupplierApp(): boolean {
    return this._applicationOptions.type === ApplicationType.Supplier
  }

  public get isCreditApp(): boolean {
    return this._applicationOptions.type === ApplicationType.Credit
  }

  public get isInHouseCreditApp(): boolean {
    return this._applicationOptions.type === ApplicationType.InHouseCredit
  }

  public get isOwner(): boolean {
    return this.currentUser.role === ApplicationUserRole.Owner
  }

  public get isAuthorized(): boolean {
    return this.currentUser.role === ApplicationUserRole.Authorized
  }

  public get currentUser(): IApplicationUser {
    return this._currentUser
  }

  public get isCurrentStepValid(): boolean {
    return this.currentStep ? this.validateStep(this.currentStep) : false
  }

  public get isInReview(): boolean {
    return this.currentStep === Steps.review.review
  }

  public get hasSubmissionRights(): boolean {
    // TODO: VK: Unified: re-implement hasSubmissionRights
    return 2 - 2 === 0 || ApplicationStore.hasSubmissionRights
  }

  public get canGoBack(): boolean {
    if (!this._stepOptions.canGoBack) {
      return false
    }

    const firstStep = this._flowController.getFirstStep(
      this._draft,
      this._applicationOptions,
    )

    if (this.currentStep === firstStep) {
      // allow going back to review from the very first step if user has reached review
      return this._draft.visitedSteps.includes(Steps.review.review)
    }

    return true
  }

  public get canSubmit(): boolean {
    const steps = this.getFlowSteps()

    const allStepsValid = steps.every((step) =>
      this._draft.validate(step, this._applicationOptions),
    )

    const shouldReadAgreement = this.isSupplierApp || this.isCreditApp

    // TODO: VK: Unified: re-implement hasSubmissionRights here
    return (
      this.hasSubmissionRights &&
      allStepsValid &&
      this.reviewStore.isAgreementAccepted &&
      (!shouldReadAgreement || this.reviewStore.isAgreementRead)
    )
  }

  public get isSubmitting(): boolean {
    return this._isSubmitting
  }

  public get stepOptions(): IUnifiedApplicationStepOptions {
    return this._stepOptions
  }

  public get isPrimaryBankAccountChanged(): boolean {
    return this._isPrimaryBankAccountChanged
  }

  private setCurrentStep(step: string) {
    // if we come from preview to co-owners and then go back to review,
    // we should return to detailed Preview mode
    // if we come from review to review and then go to the next step (not review)
    // we should return to default Review mode
    if (
      this._previousStep === Steps.review.review &&
      step !== Steps.review.review
    ) {
      this.reviewStore.currentStep = UnifiedApplicationReviewStep.REVIEW
    }

    this._previousStep = this.currentStep
    this._draft.currentStep = step
  }

  public setStepOptions(
    options: IUnifiedApplicationStepOptions | null | undefined,
  ) {
    this._stepOptions = {
      ...options,
      canGoBack: options?.canGoBack ?? true,
      canSkip: options?.canSkip ?? true,
      showGroupTitle: options?.showGroupTitle ?? true,
      showNavigationButtons: options?.showNavigationButtons ?? true,
    }
  }

  public setIsPrimaryBankAccountChanged(value: boolean) {
    this._isPrimaryBankAccountChanged = value
  }

  public setPrimaryBankAccount(bankAccount: IUIBankAccount | undefined) {
    const bankAccountSnapshot = toJS(bankAccount)

    this._applicationOptions.bankAccount = bankAccountSnapshot
    this._draft.data.primaryBankAccount = bankAccountSnapshot
  }

  public goToStep(path: string) {
    this._previousStep = this.currentStep
    this._draft.currentStep = path
  }

  public moveNext() {
    const newStep = this._flowController.getNextStep(
      this.currentStep,
      this._draft,
      this._applicationOptions,
    )

    this.updateVisitedSteps(newStep)
    this.setCurrentStep(newStep)
  }

  public moveBack() {
    let newStep = this._flowController.getPreviousStep(
      this.currentStep,
      this._draft,
      this._applicationOptions,
    )

    if (newStep === this.currentStep) {
      // reached start of the flow - return to review
      newStep = Steps.review.review
    }

    this.setCurrentStep(newStep)
  }

  public tryReturnToReview() {
    if (this._previousStep === Steps.review.review) {
      this.setCurrentStep(Steps.review.review)
    }
  }

  public skipStep() {
    const newStep = this._flowController.findSkipStep(
      this.currentStep,
      this._draft,
      this._applicationOptions,
    )

    this.updateVisitedSteps(newStep)
    this.setCurrentStep(newStep)
  }

  public getFlowSteps() {
    const steps = this._flowController.getFlowSteps(
      this._draft,
      this._applicationOptions,
    )

    // TODO: VK: Unified: Review step is different - consider removing it from the flow definition
    return steps.filter((step) => step !== Steps.review.review)
  }

  public getFlowGroups() {
    const groups = this._flowController.getFlowGroups(
      this._draft,
      this._applicationOptions,
    )

    return groups.filter((group) => group !== Groups.review)
  }

  public getGroupSteps(group: string) {
    return this._flowController.getGroupSteps(
      group,
      this._draft,
      this._applicationOptions,
    )
  }

  public validateStep(step: string) {
    return this._draft.validate(step, this._applicationOptions)
  }

  public editGroup(group: string, fromBeginning = true) {
    const groupSteps = this.getGroupSteps(group)
    const firstInvalidIndex = groupSteps.findIndex(
      (step) => !this.validateStep(step),
    )

    const allValid = firstInvalidIndex === -1
    const noneValid = firstInvalidIndex === 0

    if (fromBeginning || noneValid || allValid) {
      const newStep = this._flowController.getFirstGroupStep(
        group,
        this._draft,
        this._applicationOptions,
      )
      this.setCurrentStep(newStep)
    } else {
      const newStep = groupSteps[firstInvalidIndex]
      this.setCurrentStep(newStep)
    }
  }

  // region API calls

  public async loadDraft() {
    const companyId = RootStore.userStore.company?.id
    if (!companyId) return

    // TODO: VK: Unified: review type
    this._applicationOptions = {
      type: ApplicationType.Credit,
      userId: RootStore.userStore.id,
      companyStatus: RootStore.userStore.company?.status || CompanyStatus.New,
    }

    const draftResponse = await routes2.application.getUnifiedApplicationDraft({
      companyId,
    })

    if (draftResponse.draft) {
      this._draft = new UnifiedApplicationDraft(draftResponse.draft)

      // TODO: VK: Unified: load primary bank account with draft
      // to avoid dependency on paymentMethodStore data availability
      const primaryBankAccount =
        RootStore.screensStore.paymentMethodsStore.paymentMethods.find(
          (p) => p._id === this._draft.data?.primaryBankAccount?._id,
        )

      this._applicationOptions.bankAccount = toJS(primaryBankAccount)

      const flowSteps = this.getFlowSteps()

      if (!flowSteps.includes(this.currentStep)) {
        this._draft.currentStep = Steps.review.review
        this._previousStep = Steps.review.review
      }
    } else {
      // TODO: VK: calculate initial steps as in old ApplicationStore
      const initialStep = Steps.businessInfo.email

      this._draft = new UnifiedApplicationDraft({
        initialStep,
        currentStep: initialStep,
        data: {
          businessInfo: {
            businessName: {},
          },
          finance: {},
          coOwners: [],
        },
        users: [],
        visitedSteps: [],
      })

      console.log('new unified application, initial step', initialStep)
    }

    // Ensure current user exists in draft
    this.ensureCurrentUser()

    return Promise.resolve()
  }

  public async saveDraft() {
    // call on next / back / skip / close / submit. Enqueue save requests, check conflicts
    console.log('saving draft', toJS(this.draft))
    return Promise.resolve()
  }

  public async submitApplication() {
    this._isSubmitting = true
    // TODO: VK: Unified: do actual work
    this._isSubmitting = false

    return Promise.resolve()
  }

  // endregion API calls

  private ensureCurrentUser() {
    const currentUserId = this._applicationOptions.userId

    let currentUser = this._draft.users.find(
      (user) => user.id === currentUserId,
    )

    if (!currentUser) {
      currentUser = observable({
        id: currentUserId,
        firstName: RootStore.userStore.user?.firstName || '',
        lastName: RootStore.userStore.user?.lastName || '',
        email: RootStore.userStore.user?.email || '',
      })

      this._draft.users.push(currentUser)
    }

    this._currentUser = currentUser
  }

  private updateVisitedSteps(newStep: string) {
    const visitedStep =
      newStep === Steps.review.review
        ? newStep // put review to visited when entering it, there is no next step
        : this.currentStep

    // Add previous step to visited steps if not already there
    if (!this._draft.visitedSteps.includes(visitedStep)) {
      this._draft.visitedSteps.push(visitedStep)
    }
  }
}
