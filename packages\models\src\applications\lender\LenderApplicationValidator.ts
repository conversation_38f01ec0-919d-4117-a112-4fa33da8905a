import {
  ILenderApplicationDraft,
  StatementQuestions,
} from './ILenderApplicationDraft'
import { Steps as $ } from './LenderApplicationSteps'
import {
  validateBirthdate,
  validateEin,
  validateEmail,
  validateSsn,
  validateUSPhoneNumber,
  validateZip,
} from '../../helpers/validations'

// this validator is designed to be independent of the underlying data structure.
// Main principle - it's validating not fields, but logical application steps,
// so if single composite field in application is filled in three steps we can run three step validations on same draft field
// and otherwise, if single step is responsible for multiple fields, we can validate it in single step validation
//
// This validator is intended to be used on UI and in backend.
// so we can be sure that exactly the same validation is used
// when moving to the next step, when submitting on UI and when submitting on backend
// UI may have its own extra validation logic e.g. to display extra messages
// but final decision should be done by this validation
// For convenience, static methods can be used on UI to validate field value before it's assigned to the draft

// !!! If you need to validate against data not present in the draft,
// use extra ILenderApplicationOptions parameter like it done in UnifiedApplicationValidator

export class LenderApplicationValidator {
  private _draft: ILenderApplicationDraft

  constructor(draft: ILenderApplicationDraft) {
    this._draft = draft
  }

  validate(path: string /*, options: ILenderApplicationOptions */): boolean {
    switch (path) {
      // Sponsor
      case $.sponsor.loanOfficer:
        return !!this._draft.data?.sponsor?.loanOfficer
      case $.sponsor.personalInformation:
        return this.validateSponsorPersonalInfo()
      case $.sponsor.homeAddress:
        return this.validateSponsorHomeAddress()
      case $.sponsor.maritalStatus:
        return !!this._draft.data?.sponsor?.maritalStatus
      case $.sponsor.statements:
        return this.validateSponsorStatements()
      case $.sponsor.citizenship:
        const citizenship = this._draft.data?.sponsor?.citizenship
        if (!citizenship) return false

        // Valid if user is US citizen OR has selected legal residency status
        return (
          citizenship.isUsCitizen === true || !!citizenship.legalResidencyStatus
        )

      // Business Entity
      case $.businessEntity.entityInformation:
        return this.validateBusinessEntityInformation()
      case $.businessEntity.address:
        return false
      case $.businessEntity.isCreatedForProject:
        return false
      case $.businessEntity.dateIncorporated:
        return false
      case $.businessEntity.representative:
        return false
      case $.businessEntity.generalContractorName:
        return false
      case $.businessEntity.bankAccountId:
        return false

      // Current Project
      case $.currentProject.hasMultipleProducts:
        return false
      case $.currentProject.loanType:
        return false
      case $.currentProject.mainAddress:
        return false
      case $.currentProject.products:
        return false
      case $.currentProject.loanPurpose:
        return false
      case $.currentProject.loanTerm:
        return false
      case $.currentProject.willCompletePermits:
        return false
      case $.currentProject.originalPurchasePrice:
        return false
      case $.currentProject.originalPurchaseDate:
        return false
      case $.currentProject.payOffAmount:
        return false
      case $.currentProject.subordinateDebtType:
        return false
      case $.currentProject.subordinateDebtBalance:
        return false
      case $.currentProject.financialDetails:
        return false

      // Previous Projects
      case $.previousProjects.projects:
        return false

      // Review
      case $.review.review:
        return false

      default:
        throw new Error(`Unknown validation path: ${path}`)
    }
  }

  private validateSponsorPersonalInfo() {
    const personalInfo = this._draft.data?.sponsor?.personalInfo
    if (!personalInfo) return false

    // Validate personal info fields
    return (
      !!personalInfo.fullName &&
      validateEmail(personalInfo.email) &&
      validateUSPhoneNumber(personalInfo.phone) &&
      validateBirthdate(personalInfo.birthdate) &&
      validateSsn(personalInfo.ssn)
    )
  }

  private validateSponsorHomeAddress() {
    const address = this._draft.data?.sponsor?.homeAddress
    if (!address) return false

    // Validate address fields
    return (
      !!address.city &&
      !!address.state &&
      !!address.street &&
      validateZip(address.zip)
    )
  }

  private validateSponsorStatements() {
    const statements = this._draft.data?.sponsor?.statements
    if (!statements || statements.length === 0) {
      return false
    }

    // Check that all required statements have been answered
    const requiredStatementIds = [
      StatementQuestions.RecentForeclosureOrRelief,
      StatementQuestions.RecentBankruptcy,
      StatementQuestions.CurrentFederalDefault,
      StatementQuestions.FelonyConviction,
      StatementQuestions.PendingLitigation,
      StatementQuestions.OutstandingJudgments,
      StatementQuestions.LawsuitDefendant,
      StatementQuestions.PortfolioLitigation,
    ]

    return requiredStatementIds.every((statementId) =>
      statements.some(
        (statement) =>
          statement.statementId === statementId &&
          typeof statement.answer === 'boolean',
      ),
    )
  }

  private validateBusinessEntityInformation() {
    const businessEntity = this._draft.data?.businessEntity
    if (!businessEntity) return false

    // Validate business entity information fields
    return (
      !!businessEntity.name &&
      !!businessEntity.type &&
      validateEin(businessEntity.ein)
    )
  }
}
