import {
  IPostLenderApplicationRequest,
  IPostLenderApplicationResponse,
} from '@linqpal/models/src/routes2/application/types'
import { TCompoundRoute } from '@linqpal/models/src/routes2/types'
import { authMiddleware } from '../middlewares'
import { Draft } from '@linqpal/common-backend'
import { ApplicationDraftType } from '@linqpal/models/src/helpers/draft/models/ApplicationDraftType'
import { ILenderApplicationDraft } from '@linqpal/models/src/applications/lender/ILenderApplicationDraft'

export const postLenderApplicationDraft: TCompoundRoute<
  IPostLenderApplicationRequest,
  IPostLenderApplicationResponse
> = {
  middlewares: {
    pre: [...authMiddleware.pre],
    post: [],
  },
  post: async (
    data: IPostLenderApplicationRequest,
    req: any,
  ): Promise<IPostLenderApplicationResponse> => {
    if (!data.companyId) throw new Error('companyId is required')

    const upsertData = {
      sub: req.user.sub,
      company_id: data.companyId,
      type: ApplicationDraftType.LenderApplication,
      firstQuestion: data.draft.initialStep,
      current: data.draft.currentStep,
      visitedSteps: data.draft.visitedSteps,
      lenderApplication: data.draft.data,
    }

    const draft = await Draft.findOneAndUpdate(
      {
        company_id: data.companyId,
        type: ApplicationDraftType.LenderApplication,
      },
      upsertData,
      { upsert: true, new: true },
    )

    const response: ILenderApplicationDraft = {
      initialStep: draft.firstQuestion,
      currentStep: draft.current,
      data: draft.lenderApplication ?? {
        sponsor: {},
        businessEntity: {},
        currentProject: {},
        previousProjects: [],
      },
      visitedSteps: draft.visitedSteps,
    }

    return { draft: response }
  },
}
