import { authRequired } from '../../services/auth.service'
import { CustomerAccount, User } from '@linqpal/common-backend'
import invitationController from '../../controllers/invitation.controller'
import { parsePhoneNumber } from 'libphonenumber-js'
import { exceptions } from '@linqpal/models'
import { ControllerItem } from '../controllerItem'

export const middlewares = { pre: [authRequired()] }

export const save: ControllerItem = {
  post: async (req, res, next) => {
    let { id, company_id, customer, status, phone, ...data } = req.body
    if (!phone) {
      throw new exceptions.LogicalError('Phone number is required')
    } else
      try {
        phone = parsePhoneNumber(phone, 'US').number
        if (data.business_phone) {
          data.business_phone = parsePhoneNumber(
            data.business_phone,
            'US',
          ).number
        }
      } catch {
        throw new exceptions.LogicalError('Invalid phone number')
      }

    const found = await CustomerAccount.findOne({
      company_id: req.company!._id,
      phone,
    })
    if (found && (!id || id !== found._id.toString())) {
      throw new exceptions.LogicalError('Phone number exists')
    }

    //const customer_event = id ? 'Updated' : 'Created'
    if (id) {
      const ca = await CustomerAccount.findOne({
        _id: id,
        company_id: req.company!._id,
      }).session(req.session)
      await ca!.updateOne({ phone, ...data }).session(req.session)
    } else {
      const [ca] = await CustomerAccount.create(
        [{ company_id: req.company!._id, phone, ...data }],
        { session: req.session },
      )
      id = ca._id
    }

    const account = await CustomerAccount.findOne({ _id: id })
      .populate({ path: 'user' })
      .session(req.session)
    let invitation = {}
    const user = await User.findOne({ login: phone })

    if (!user) {
      invitation = await invitationController.supplierAddAccount(req, account!)
    }

    //await emitCustomerEvent(id, customer_event)

    res.locals.result = { id, invitation }
    next()
  },
}
/*
async function emitCustomerEvent(
  customerId: string,
  eventType: 'Created' | 'Updated',
) {
  try {
    await AwsService.sendSQSMessage(
      'customer-syncback',
      JSON.stringify({ customerId, eventType }),
    )
  } catch (e) {
    console.error(e)
  }
}*/

export const getForInvoiceSignup: ControllerItem = {
  middlewares: { pre: [authRequired(false)] },
  get: async (req, res, next) => {
    const customerAccount = await CustomerAccount.findById(req.query.id)
    if (customerAccount) {
      res.locals.result = {
        account: {
          first_name: customerAccount.first_name,
          last_name: customerAccount.last_name,
          email: customerAccount.email,
          phone: customerAccount.phone,
        },
      }
    } else {
      res.locals.result = { account: {} }
    }
    next()
  },
}
