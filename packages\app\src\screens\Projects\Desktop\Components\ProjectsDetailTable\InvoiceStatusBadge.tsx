import React from 'react'
import { View, ViewStyle } from 'react-native'

import { BtText } from '@linqpal/components/src/ui'
import { useTranslation } from 'react-i18next'
import TooltipView from '../../../../../ui/molecules/TooltipView'
import {
  IconCancelled,
  IconPaymentPosted,
  IconDismissed,
  IconDraft,
  IconExpired,
  IconPaid,
  IconPastDue,
  IconPlaced,
} from '../../../../../assets/icons'
import { invoiceStatus } from '@linqpal/models/src/dictionaries/invoiceStatus'
import startCase from 'lodash/startCase'
import { toCamelCase } from '../../../../../utils/helpers/string'

export const invoiceStatusOptions = [
  { value: '', label: 'All status' },
  ...Object.entries(invoiceStatus).map(([key, value]) => ({
    value,
    label: startCase(key),
  })),
]

type Props = {
  status: string
  wrapperStyle?: ViewStyle
}

export const InvoiceStatusBadge = React.memo(
  ({ status = invoiceStatus.draft }: Props) => {
    const { t } = useTranslation('global')

    return (
      <View style={{ flexDirection: 'row' }} testID="invoice_status_badge">
        {status === invoiceStatus.draft && (
          <TooltipView helper={t('invoiceStatusTooltip.invoice-not-sent')}>
            <IconDraft />
          </TooltipView>
        )}
        {status === invoiceStatus.expired && (
          <TooltipView
            helper={t('invoiceStatusTooltip.invoice-expired-add-new-one')}
          >
            <IconExpired />
          </TooltipView>
        )}
        {(status === invoiceStatus.placed || status === invoiceStatus.seen) && (
          <TooltipView helper={t('invoiceStatusTooltip.invoice-sent')}>
            <IconPlaced />
          </TooltipView>
        )}
        {status === invoiceStatus.paid && <IconPaid />}
        {status === invoiceStatus.dismissed && (
          <TooltipView
            helper={t('invoiceStatusTooltip.customer-dismissed-invoice')}
          >
            <IconDismissed />
          </TooltipView>
        )}
        {status === invoiceStatus.rejected && <IconDismissed />}
        {[invoiceStatus.cancelled, invoiceStatus.paymentFailed].includes(
          status as any,
        ) && <IconCancelled />}
        {status === invoiceStatus.pastDue && <IconPastDue />}
        {status === invoiceStatus.paymentPosted && <IconPaymentPosted />}
        {status === invoiceStatus.paymentError && <IconPaymentPosted />}
        {status === invoiceStatus.paymentProcessing && <IconPaymentPosted />}
        <BtText style={{ marginLeft: 10 }}>
          {invoiceStatusOptions.find(
            (o) =>
              o.value ===
              (status === invoiceStatus.paymentPosted
                ? invoiceStatus.paymentProcessing
                : status),
          )?.label ?? toCamelCase(status)}
        </BtText>
      </View>
    )
  },
)
