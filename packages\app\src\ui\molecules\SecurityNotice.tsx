import React from 'react'
import { StyleSheet, View } from 'react-native'
import { Text } from 'react-native-paper'
import Svg, {
  Defs,
  LinearGradient,
  Stop,
  Text as SvgText,
} from 'react-native-svg'
import { useTranslation } from 'react-i18next'
import { SecuredIcon } from '../../assets/icons'

interface IProps {
  description?: string
}

const SecurityNotice: React.FC<IProps> = ({ description }) => {
  const { t } = useTranslation('global')

  return (
    <View style={styles.container}>
      <View style={styles.titleContainer}>
        <View style={styles.iconWrapper}>
          <SecuredIcon width={24} height={24} />
        </View>
        <View style={styles.gradientTextContainer}>
          <GradientText
            text={t(
              'integrations.pay-as-guest.payment-method-section.data-are-secured',
            )}
            fontSize={16}
            weight={'700'}
          />
        </View>
      </View>

      {/* Details Text */}
      <Text style={styles.text}>
        {description ??
          t(
            'integrations.pay-as-guest.payment-method-section.data-are-secured-details',
          )}
      </Text>
    </View>
  )
}

const GradientText: React.FC<{
  text: string
  fontSize: number
  weight: string
}> = ({ text, fontSize, weight }) => {
  return (
    <Svg height="20" width="100%">
      <Defs>
        <LinearGradient id="gradient" x1="0" y1="0" x2="1" y2="0">
          {/* eslint-disable-next-line i18next/no-literal-string*/}
          <Stop offset="0%" stopColor="#8400AF" />
          {/* eslint-disable-next-line i18next/no-literal-string*/}
          <Stop offset="100%" stopColor="#00679D" />
        </LinearGradient>
      </Defs>
      <SvgText
        x="0"
        y="15"
        fill="url(#gradient)"
        fontSize={fontSize}
        fontWeight={weight}
        // eslint-disable-next-line i18next/no-literal-string
        fontFamily="Inter"
      >
        {text}
      </SvgText>
    </Svg>
  )
}

const styles = StyleSheet.create({
  container: {
    marginTop: 20,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  iconWrapper: {
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8, // Adjust spacing between the icon and the text
  },
  gradientTextContainer: {
    flex: 1,
  },
  text: {
    fontSize: 14,
    fontFamily: 'Inter',
    fontWeight: '500',
    color: '#001929',
    lineHeight: 20,
  },
})

export default SecurityNotice
