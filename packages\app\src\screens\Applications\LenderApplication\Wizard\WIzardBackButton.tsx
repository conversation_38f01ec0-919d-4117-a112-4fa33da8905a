import { observer } from 'mobx-react'
import { colors } from '@linqpal/components/src/theme'
import { StyleSheet, TouchableOpacity } from 'react-native'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { BtPlainText } from '@linqpal/components/src/ui'
import { useLenderApplication } from '../LenderApplicationContext'
import { Spacer } from '../../../../ui/atoms'
import { Steps } from '@linqpal/models/src/applications/lender/LenderApplicationSteps'
import { Icon } from '@ui-kitten/components'

export const WizardBackButton = observer(() => {
  const { t } = useTranslation('application')

  const store = useLenderApplication()

  if (!store.canGoBack) return <Spacer height={44} />

  return (
    <div
      style={{
        position: 'sticky',
        top: 0,
        zIndex: 1000,
        backgroundColor: 'white',
        marginTop: 20,
      }}
    >
      <TouchableOpacity
        style={styles.wrapper}
        onPress={() => store.moveBack()}
        disabled={store.isSubmitting}
        testID="LenderApplication.Wizard.BackButton"
      >
        <Icon
          name={'arrow-back-outline'}
          width={22}
          height={22}
          fill={colors.accentText}
        />

        <BtPlainText style={styles.label}>
          {store.currentStep === Steps.review.review
            ? t('LenderApplication.Wizard.BackButtonReview')
            : t('LenderApplication.Wizard.BackButton')}
        </BtPlainText>
      </TouchableOpacity>
    </div>
  )
})

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  label: {
    fontWeight: '700',
    fontSize: 16,
    lineHeight: 24,
    color: colors.accentText,
    marginLeft: 5,
  },
})
