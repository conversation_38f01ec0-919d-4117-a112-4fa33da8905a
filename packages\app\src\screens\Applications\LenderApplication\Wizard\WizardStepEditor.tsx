import React, { FC, useEffect, useMemo } from 'react'
import { observer } from 'mobx-react'
import { View } from 'react-native'
import {
  getLenderApplicationEditor,
  ILenderApplicationEditor,
} from '../Flow/getLenderApplicationEditor'
import { WizardScrollArea } from '../../Components/WizardScrollArea'
import { useLenderApplication } from '../LenderApplicationContext'
import { Spacer } from '../../../../ui/atoms'
import { useResponsive } from '../../../../utils/hooks'
import { WizardStepTitle } from './WizardStepTitle'
import { WizardStepDescription } from './WizardStepDescription'
import { WizardBackButton } from './WIzardBackButton'
import { WizardHeader } from './WizardHeader'
import { WizardProgressBar } from './WizardProgressBar'
import { WizardDesktopButtons } from './WizardDesktopButtons'

export const WizardStepEditor: FC = observer(() => {
  const { sm } = useResponsive()

  const store = useLenderApplication()

  const editor: ILenderApplicationEditor = useMemo(() => {
    return store.currentStep
      ? getLenderApplicationEditor(store.currentStep)
      : { options: {}, component: () => <></> }
  }, [store.currentStep])

  useEffect(() => {
    store.setStepOptions(editor.options)
  }, [store, editor.options])

  console.log('render')

  return (
    <View style={{ flex: 1 }}>
      <WizardHeader />
      <WizardProgressBar />

      <WizardScrollArea currentStep={store.currentStep}>
        <WizardBackButton />

        <Spacer height={sm ? 20 : 10} />

        <WizardStepTitle />
        <WizardStepDescription />

        <Spacer height={24} />

        <editor.component />

        <WizardDesktopButtons />
      </WizardScrollArea>
    </View>
  )
})
