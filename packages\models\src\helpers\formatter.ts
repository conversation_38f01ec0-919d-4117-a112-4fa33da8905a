import numbro from 'numbro'
import { isNumber } from 'lodash'

export function toCurrency(
  amount: number | null | undefined,
  defaultValue?: number | string,
): string {
  if (!amount && defaultValue !== undefined) {
    return isNumber(defaultValue)
      ? numbro(defaultValue).formatCurrency('0,0.00')
      : defaultValue
  }

  return numbro(amount ?? 0).formatCurrency('0,0.00')
}

export function toPercentage(
  amount: number | null | undefined,
  type: 'fraction' | 'whole' = 'whole',
): string {
  // use fraction for 0.05 (5%)
  // use whole for 5 (5%)
  const percentage = type === 'whole' ? (amount ?? 0) / 100 : amount ?? 0

  return numbro(percentage).format({
    output: 'percent',
    mantissa: 2,
    trimMantissa: true,
  })
}
