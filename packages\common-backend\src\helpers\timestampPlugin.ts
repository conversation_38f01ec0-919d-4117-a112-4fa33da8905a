import { Schema } from 'mongoose'

export default function timestampsPlugin(schema: Schema) {
  if ((schema as any).options.hasOwnProperty('timestamps')) {
    return
  }
  schema.add({ createdAt: { type: Date, index: true } })
  schema.add({ updatedAt: { type: Date, index: true } })
  schema.pre(['updateOne', 'findOneAndUpdate'], function () {
    const options = (this as any).options
    if (!(options && options.hasOwnProperty('timestamps')))
      this.set({ updatedAt: new Date() })
  })
  schema.pre(['save'], function () {
    if (!this.createdAt) {
      this.set({ createdAt: new Date() })
    }
    if (process.env.NODE_ENV === 'test') {
      if (!this.updatedAt) {
        this.set({ updatedAt: new Date() })
      }
      return
    }
    if (!(this.options && (this.options as any).hasOwnProperty('timestamps')))
      this.set({ updatedAt: new Date() })
  })
}
