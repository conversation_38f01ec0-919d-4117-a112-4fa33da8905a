import { dictionaries } from '@linqpal/models'
import moment from 'moment-timezone'
import { LoanApplication } from '../../models'
import { ILoanApplication } from '../../models/types'
import { mapToCbw } from './mapToCbw'
import { mapToArcadia } from './mapToArcadia'
import { ObjectId, PipelineStage } from 'mongoose'
import { ILoan } from '../lms.service'

async function appToReport(app: ILoanApplication, mapping?: string) {
  switch (mapping) {
    case 'arcadia':
      return mapToArcadia(app)
    default:
      return mapToCbw(app)
  }
}

export async function getLoanApplicationReport(
  dateStart: Date,
  dateEnd: Date,
  mapping?: string,
) {
  const startDate = moment(dateStart)
    .tz('America/Chicago')
    .startOf('day')
    .toDate()
  const endDate = moment(dateEnd).tz('America/Chicago').endOf('day').toDate()

  const pipeline: PipelineStage[] = [
    {
      $match: {
        status: {
          $in: [
            dictionaries.LOAN_APPLICATION_STATUS.APPROVED,
            dictionaries.LOAN_APPLICATION_STATUS.CLOSED,
          ],
        },
        lms_id: { $exists: true },
        $expr: {
          $and: [{ $ne: ['$lms_id', null] }],
        },
        issueDate: { $gte: startDate, $lte: endDate },
      },
    },
  ]
  if (mapping && mapping !== 'cbw') {
    pipeline.push({ $match: { fundingSource: mapping } })
  }
  if (mapping === 'cbw') {
    pipeline.push({
      $match: {
        $or: [
          { fundingSource: mapping },
          { fundingSource: null },
          { fundingSource: { $exists: false } },
        ],
      },
    })
  }
  const aggregate = LoanApplication.aggregate(pipeline)

  const apps = await aggregate.exec()
  return Promise.all(apps.map((a) => appToReport(a, mapping)))
}

export const LoanServicingAccounts = {
  cbw: {
    prod: {
      FUNDING: '***************',
      COLLECTION: '***************',
    },
    nonProd: {
      FUNDING: '***************',
      COLLECTION: '***************',
    },
  },
  arcadia: {
    prod: {
      FUNDING: '***************',
      COLLECTION: '***************',
    },
    nonProd: {
      FUNDING: '***************',
      COLLECTION: '***************',
    },
  },
}

export async function changeLoanApplicationLmsStatus(
  loanId: string,
  status: string,
): Promise<(ILoanApplication & { _id: ObjectId }) | null> {
  const filter = { lms_id: loanId }

  const result = await LoanApplication.findOneAndUpdate(filter, {
    lmsLoanStatus: status,
  })
  return result
}

export async function changeLoanApplicationLmsFieldsForSync(loan: ILoan) {
  const filter = { lms_id: loan.id }
  const result = await LoanApplication.findOneAndUpdate(filter, {
    lmsLoanStatus: loan.status,
    isOverdue: loan.isOverdue,
    remainingAmount: loan?.loanDetails?.loanOutstandingAmount,
    nextPaymentAmount: loan?.loanDetails?.nextPaymentAmount,
    nextPaymentDate: moment(loan?.loanDetails?.nextPaymentDate).format(
      'YYYY-MM-DD',
    ),
    lastPaymentDate: moment(loan?.lastPaymentDate).format('YYYY-MM-DD'),
    processingAmount: loan?.loanDetails?.totalProcessingPaymentsAmount,
    pastDueAmount: loan?.loanDetails?.lateAmount,
  })

  return result
}

export {
  default as loanPortfolioReport,
  mapToPortfolioReport,
} from './loanPortfolioReport'
export { default as lineOfCreditAccountsReport } from './lineOfCreditAccountsReport'
export { default as transactionsReport } from './transactionsReport'
export { default as lateLoansReport } from './lateLoansReport'
export { default as loanActivityReport } from './loanActivityReport'
export { default as eomReport } from './eomReport'
export * from './loanFinalPayment'
export { default as loanExperianReport } from './loanExperianReport'
export * from './loanApplicationDuplicates'
