import React, { FC, useMemo, useRef, useState } from 'react'
import { observer } from 'mobx-react'
import { useTranslation } from 'react-i18next'
import { IUIBankAccount, routes, UIBankAccountModel } from '@linqpal/models'
import {
  BtAutoComplete,
  BtButton,
  BtEncryptedInput,
  BtLink,
  BtText,
} from '@linqpal/components/src/ui'
import { StyleSheet, View } from 'react-native'
import {
  PlaidConnectButton,
  PlaidConnectButtonRef,
} from './Components/PlaidConnectButton'
import { toJS } from 'mobx'
import { BankAccountForm } from './Components/BankAccountForm'
import { BankAccountList } from './Components/BankAccountList'
import { UnsupportedBankAccountModal } from './Components/UnsupportedBankAccountModal'
import { SkipConnectBankAccountModal } from './Components/SkipConnectBankAccountModal'
import { DeleteBankAccountModal } from './Components/DeleteBankAccountModal'
import { Divider } from '@ui-kitten/components'
import {
  GetPaidLabels,
  IHCLabels,
  LOCLabels,
} from './Components/BankAccountLabels'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { ApplicationType } from '@linqpal/models/src/dictionaries/applicationType'
import { UnifiedApplicationStore } from '../../Store/UnifiedApplicationStore'
import { Spacer } from '../../../../../ui/atoms'
import RootStore from '../../../../../store/RootStore'
import {
  CreateBankAccountModel,
  defaultCreateBankAccountModel,
} from './Components/BankAccountFormModel'
import { useResponsive } from '../../../../../utils/hooks'
import Loading from '../../../../Loading'
import {
  Alert,
  Description,
  Question,
} from '../../../../GeneralApplication/Application/components'
import { IUnifiedApplicationEditor } from '../getUnifiedApplicationEditor'

const defaultBankSearchText = 'AAA'

// TODO: VK: Unified: Mostly taken from the legacy version as-is. To be refactored

const PrimaryBankAccountEditor: FC = () => {
  const { t } = useTranslation('application')
  const { sm } = useResponsive()

  const store = useUnifiedApplication()

  const [selectedBank, setSelectedBank] = useState<any>()
  const [manualBankAccount, setManualBankAccount] =
    useState<CreateBankAccountModel>(defaultCreateBankAccountModel)

  const [warningMessage, setWarningMessage] = useState<string>()
  const [searchValue, setSearchValue] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isManualFormVisible, setIsManualFormVisible] = useState(false)
  const [confirmManualAccount, setConfirmManualAccount] = useState(false)
  const [bankAccountToDelete, setBankAccountToDelete] = useState<string>()
  const plaidConnectButtonRef = useRef<PlaidConnectButtonRef>(null)

  // from legacy version, clarify expectations for IHC
  const isLoanApplication = store.type !== ApplicationType.Supplier

  const {
    userStore,
    screensStore: { paymentMethodsStore },
    isBusy,
  } = RootStore

  const switchNavigationButtons = (visible: boolean) => {
    store.setStepOptions({
      ...store.stepOptions,
      showNavigationButtons: visible,
    })
  }

  const bankAccounts = paymentMethodsStore.paymentMethods.filter(
    (pm) => pm.paymentMethodType === 'bank',
  )

  const updateBankAccountModel = (bankAccount: IUIBankAccount | undefined) => {
    store.setPrimaryBankAccount(bankAccount)
  }

  const searchBanks = (
    params: Parameters<typeof routes.company.allInstitutions>[0],
  ) => {
    if (params.search.length >= defaultBankSearchText.length) {
      return routes.plaid
        .searchInstitution(params.search)
        .then((response) => response.items)
    } else {
      return Promise.resolve([])
    }
  }

  const getPlaidBanks = async () => {
    const getBanks = () => {
      return paymentMethodsStore.paymentMethods?.filter(
        (bank) => !bank.isManualEntry,
      )
    }
    const connectedBanks = getBanks()
    await paymentMethodsStore.fetchPaymentMethods()
    const plaidBanks = getBanks()

    const addedBank = plaidBanks.find(
      (fb) => !connectedBanks.some((cb) => cb._id === fb._id),
    )
    if (!addedBank) {
      setWarningMessage(t('Bank.UnsupportedAccountsWarning'))
      return
    }

    updateBankAccountModel(addedBank)
    store.setIsPrimaryBankAccountChanged(true)
  }

  const handleSearchValueChange = (newValue: string) => {
    setSearchValue(newValue)
    if (!newValue) {
      setSelectedBank(undefined)
    }
  }

  const handleSelectBankAccount = (bankAccount: IUIBankAccount) => {
    updateBankAccountModel(bankAccount)
    store.setIsPrimaryBankAccountChanged(true)
  }

  const handleSelectBank = (bank) => {
    setSelectedBank(bank)
    setSearchValue(bank.name)
  }

  const handleSuccessfulBankAccountConnect = async () => {
    setIsLoading(true)
    try {
      await userStore.fetchUser()
      await getPlaidBanks()

      setSelectedBank(undefined)
      setSearchValue('')
    } catch (error) {
      console.error(error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCloseWarningModal = () => {
    setWarningMessage(undefined)
  }

  const handleCancelAddBankAccount = () => {
    switchNavigationButtons(true)
    setIsManualFormVisible(false)
    setManualBankAccount(defaultCreateBankAccountModel)
  }

  const handleAddManualBankAccount = async () => {
    switchNavigationButtons(true)
    setIsManualFormVisible(false)

    try {
      const primary = store.isCreditApp
        ? { isPrimaryForCredit: true }
        : { isPrimary: true }
      const response = await routes.company.addBankAccount({
        ...manualBankAccount,
        ...primary,
        isManualEntry: true,
      })

      await paymentMethodsStore.fetchPaymentMethods()

      const newManualBankAccount = paymentMethodsStore.manuallyAddedBanks.find(
        (b) => b.id === response.id,
      )
      if (newManualBankAccount) {
        updateBankAccountModel(newManualBankAccount)
      }

      setManualBankAccount({
        ...defaultCreateBankAccountModel,
        name: searchValue,
      })

      setSelectedBank(undefined)
      setSearchValue('')
    } catch (error) {
      console.error(error)
    }
  }

  const handleClickEnterManually = () => {
    const shouldConfirmManualAccount =
      isSelectedBankSupportedByPlaid && !store.isInHouseCreditApp

    if (shouldConfirmManualAccount) {
      setConfirmManualAccount(true)
    } else {
      handleSkipConnectAccepted()
    }
  }

  const handleCloseSkipConnect = () => {
    setConfirmManualAccount(false)
  }

  const handleSkipConnectAccepted = () => {
    switchNavigationButtons(false)
    setIsManualFormVisible(true)
    setConfirmManualAccount(false)
    setManualBankAccount((prev) => ({ ...prev, name: searchValue }))
  }

  const handleLoginToBank = () => {
    setConfirmManualAccount(false)
    plaidConnectButtonRef.current?.createToken()
  }

  const handleManualBankAccountChange = (newValue: CreateBankAccountModel) => {
    setManualBankAccount(newValue)
  }

  const handleClickDeleteManualBankAccount = (id: string) => {
    setBankAccountToDelete(id)
  }

  const handleCloseDeleteManualBankAccountModal = () => {
    setBankAccountToDelete(undefined)
  }

  const handleDeleteManualBankAccount = async () => {
    if (!bankAccountToDelete) return

    try {
      setIsDeleting(true)

      await routes.company.removeBankAccount(bankAccountToDelete)

      await paymentMethodsStore.fetchPaymentMethods()

      updateBankAccountModel(undefined)
    } catch (error) {
      console.error(error)
    } finally {
      setIsDeleting(false)
      setBankAccountToDelete(undefined)
    }
  }

  const getLabels = () => {
    const labelProps = { selectedBank, hasAccountAdded }

    if (store.isSupplierApp) {
      return GetPaidLabels(labelProps)
    } else if (store.isInHouseCreditApp) {
      return IHCLabels(labelProps)
    } else {
      return LOCLabels(labelProps)
    }
  }

  const isManualBankAccountValid = useMemo(
    () => UIBankAccountModel.create(toJS(manualBankAccount)).isFilled,
    [manualBankAccount],
  )

  const isSelectedBankSupportedByPlaid = selectedBank?.name === searchValue

  const hasAccountAdded = bankAccounts.length > 0

  if (isLoading) {
    return (
      <View style={{ height: 100 }}>
        <Loading />
      </View>
    )
  }

  if (!store.isInReview) {
    if (!sm && isManualFormVisible) {
      return (
        <>
          <BankAccountForm
            review={false}
            model={manualBankAccount}
            onChange={handleManualBankAccountChange}
          />
          <Spacer height={16} />
          <BtButton
            testID="BankAccountFormAdd"
            disabled={!isManualBankAccountValid}
            onPress={handleAddManualBankAccount}
          >
            {t('Bank.AcceptManual')}
          </BtButton>
          <Spacer height={16} />
          <BtButton
            testID="BankAccountFormCancel"
            status={'basic'}
            onPress={handleCancelAddBankAccount}
          >
            {t('Bank.CancelManual')}
          </BtButton>
        </>
      )
    } else {
      const labels = getLabels()

      return (
        <View>
          <View>
            <Question t={t} sm={sm} title={labels.title} />
            <Description description={labels.description} sm={sm} t={t} />
            {/* TODO: KK: Need to move these labels to BankAccountLabels */}
            {hasAccountAdded && (
              <BtText
                style={{
                  fontFamily: 'Inter, sans-serif',
                  color: '#335C75',
                  fontSize: sm ? 18 : 16,
                  fontWeight: '600',
                }}
              >
                {isLoanApplication
                  ? t('Bank.BankListLoanSelectTitle')
                  : t('Bank.BankListGetPaidSelectTitle')}
              </BtText>
            )}
            {hasAccountAdded && (
              <BtText
                style={{
                  fontFamily: 'Inter, sans-serif',
                  color: '#335C75',
                  fontSize: sm ? 16 : 14,
                  marginTop: 10,
                  marginBottom: 30,
                }}
              >
                {isLoanApplication
                  ? t('Bank.BankListLoanSelectDescription')
                  : t('Bank.BankListGetPaidSelectDescription')}
              </BtText>
            )}
          </View>
          <BankAccountList
            bankAccounts={bankAccounts}
            selectedBankAccountId={
              store.draft.data.primaryBankAccount?.id || ''
            }
            onSelect={handleSelectBankAccount}
            onDelete={handleClickDeleteManualBankAccount}
          />
          {hasAccountAdded && (
            <>
              <Divider
                style={{
                  backgroundColor: '#CCD6DD',
                  marginTop: 10,
                  marginBottom: 20,
                }}
              />
              <BtText
                style={{
                  color: '#335C75',
                  fontSize: sm ? 24 : 16,
                  fontWeight: '600',
                  marginBottom: 18,
                }}
              >
                {t('Bank.SearchAnotherBankTitle')}
              </BtText>
            </>
          )}
          <BtEncryptedInput
            size="large"
            disabled={isBusy}
            value={searchValue}
            label={
              store.isSupplierApp
                ? t('Bank.GetPaid.SearchBankLabel')
                : t('Bank.SearchBankLabel')
            }
            testID="bank_details_search"
            InputComponent={BtAutoComplete}
            labelSelector={(option) => option.name}
            placeholder={t('Bank.DetailsPlaceHolder')}
            searchParams={{ limit: 25, onlyFinciity: true }}
            //eslint-disable-next-line i18next/no-literal-string
            placement="bottom end"
            apiSearchFn={searchBanks}
            onSelect={handleSelectBank}
            onChangeText={handleSearchValueChange}
            defaultSearchText={defaultBankSearchText}
          />
          <Spacer height={24} />
          {!!searchValue && (
            <View
              style={{
                alignItems: 'center',
              }}
            >
              {isSelectedBankSupportedByPlaid && (
                <>
                  <PlaidConnectButton
                    style={{ marginTop: 20 }}
                    ref={plaidConnectButtonRef}
                    disabled={isBusy}
                    text={t('Bank.LoginToBankAccount')}
                    onConnectSuccess={handleSuccessfulBankAccountConnect}
                    highlightRountingNumber={selectedBank.routing_numbers[0]}
                  />
                  <Spacer height={10} />
                </>
              )}
              <BtLink
                disabled={isBusy}
                title={t('Bank.EnterBankDetailsManually')}
                testID="EnterBankDetailsManually"
                onPress={handleClickEnterManually}
                textStyle={
                  isBusy
                    ? [styles.linkText, styles.disabledLinkText]
                    : styles.linkText
                }
              >
                {t('Bank.EnterBankDetailsManually')}
              </BtLink>
            </View>
          )}
          <Alert
            title={t('Bank.EnterBankDetailsManually')}
            visible={isManualFormVisible}
            buttons={[
              {
                testID: 'BankAccountFormCancel',
                label: t('Bank.CancelManual'),
                onPress: handleCancelAddBankAccount,
              },
              {
                testID: 'BankAccountFormAdd',
                label: t('Bank.AcceptManual'),
                disabled: !isManualBankAccountValid,
                onPress: handleAddManualBankAccount,
              },
            ]}
            onClose={handleCancelAddBankAccount}
          >
            <BankAccountForm
              review={false}
              model={manualBankAccount}
              onChange={handleManualBankAccountChange}
            />
          </Alert>
          <SkipConnectBankAccountModal
            isVisible={confirmManualAccount}
            isLoanApplication={isLoanApplication}
            onLoginToBank={handleLoginToBank}
            onEnterManually={handleSkipConnectAccepted}
            onClose={handleCloseSkipConnect}
          />
          <UnsupportedBankAccountModal
            message={warningMessage}
            isVisible={!!warningMessage}
            onClose={handleCloseWarningModal}
          />
          <DeleteBankAccountModal
            isVisible={!!bankAccountToDelete}
            isLoading={isDeleting}
            onConfirm={handleDeleteManualBankAccount}
            onClose={handleCloseDeleteManualBankAccountModal}
          />
        </View>
      )
    }
  } else {
    return (
      <>
        <Spacer height={24} />
        <BankAccountList
          bankAccounts={bankAccounts}
          selectedBankAccountId={store.draft.data.primaryBankAccount?.id || ''}
          onSelect={handleSelectBankAccount}
        />
      </>
    )
  }
}

const styles = StyleSheet.create({
  linkText: {
    marginTop: 10,
    marginBottom: 20,
    fontWeight: '600',
    fontSize: 14,
    lineHeight: 24,
  },
  disabledLinkText: {
    color: '#99ADBA',
  },
})

export const PrimaryBankAccountStep: IUnifiedApplicationEditor = {
  options: {
    onMoveNext: (store: UnifiedApplicationStore) => {
      const bankAccount = toJS(store.draft.data.primaryBankAccount)

      // TODO: VK: Unified: save just id in draft, read full object from store.applicationOptions or patmentMethodsStore
      if (store.isPrimaryBankAccountChanged && bankAccount) {
        if (store.isCreditApp) {
          bankAccount.isPrimaryForCredit = true
        } else {
          bankAccount.isPrimary = true
        }

        store.setIsPrimaryBankAccountChanged(false)

        // this will actually update primary account, not add new (it should be added already)
        // consider adding a separate method to make an account primary to make it more obvious
        routes.company.addBankAccount(bankAccount)
      }
    },
  },
  component: observer(PrimaryBankAccountEditor),
}
