import { Schema } from 'mongoose'
import {
  BusinessEntityType,
  LegalResidencyStatus,
  MaritalStatus,
} from '@linqpal/models/src/applications/lender/ILenderApplicationDraft'

const AddressSchema = new Schema(
  {
    city: { type: String, required: false },
    state: { type: String, required: false },
    street: { type: String, required: false },
    apartment: { type: String, required: false },
    zip: { type: String, required: false },
  },
  { _id: false },
)

const PersonalInfoSchema = new Schema(
  {
    fullName: { type: String, required: false },
    email: { type: String, required: false },
    phone: { type: String, required: false },
    birthdate: { type: String, required: false },
    ssn: { type: String, required: false }, // Will be encrypted
  },
  { _id: false },
)

const StatementSchema = new Schema(
  {
    statementId: { type: String, required: false },
    answer: { type: Boolean, required: false },
  },
  { _id: false },
)

const CitizenshipSchema = new Schema(
  {
    isUsCitizen: { type: Boolean, required: false },
    legalResidencyStatus: {
      type: String,
      enum: Object.values(LegalResidencyStatus),
      required: false,
    },
  },
  { _id: false },
)

const SponsorSchema = new Schema(
  {
    loanOfficer: { type: String, required: false },
    personalInfo: PersonalInfoSchema,
    homeAddress: AddressSchema,
    maritalStatus: {
      type: String,
      enum: Object.values(MaritalStatus),
      required: false,
    },
    statements: [StatementSchema],
    citizenship: CitizenshipSchema,
  },
  { _id: false },
)

const RepresentativeSchema = new Schema(
  {
    firstName: { type: String, required: false },
    lastName: { type: String, required: false },
    phone: { type: String, required: false },
    email: { type: String, required: false },
  },
  { _id: false },
)

const BusinessEntitySchema = new Schema(
  {
    name: { type: String, required: false },
    type: {
      type: String,
      enum: Object.values(BusinessEntityType),
      required: false,
    },
    ein: { type: String, required: false }, // Will be encrypted
    address: AddressSchema,
    isCreatedForProject: { type: Boolean, required: false },
    dateIncorporated: { type: Date, required: false },
    representative: RepresentativeSchema,
    isGeneralContractor: { type: Boolean, required: false },
    generalContractorName: { type: String, required: false },
    bankAccountId: { type: String, required: false },
  },
  { _id: false },
)

const ProductSchema = new Schema(
  {
    address: AddressSchema,
    type: { type: String, required: false },
    numberOfUnits: { type: Number, required: false },
    budget: { type: Number, required: false },
  },
  { _id: false },
)

const FinancialDetailsSchema = new Schema(
  {
    projectValue: { type: Number, required: false },
    perUnitValue: { type: Number, required: false },
    totalFinishedValue: { type: Number, required: false },
    perUnitFinishedValue: { type: Number, required: false },
  },
  { _id: false },
)

const CurrentProjectSchema = new Schema(
  {
    hasMultipleProducts: { type: Boolean, required: false },
    loanType: {
      type: String,
      enum: ['New Construction', 'Counstruction Completion', 'Rehab', 'Bridge'],
      required: false,
    },
    mainAddress: AddressSchema,
    products: [ProductSchema],
    loanPurpose: {
      type: String,
      enum: ['Purchase', 'Refinance'],
      required: false,
    },
    loanTerm: { type: String, required: false },
    willCompletePermits: { type: Boolean, required: false },
    originalPurchasePrice: { type: Number, required: false },
    originalPurchaseDate: { type: Date, required: false },
    payOffAmount: { type: Number, required: false },
    subordinateDebtType: {
      type: String,
      enum: ['None', 'Seller Carry', 'Entity Ownership'],
      required: false,
    },
    subordinateDebtBalance: { type: Number, required: false },
    financialDetails: FinancialDetailsSchema,
  },
  { _id: false },
)

const ProjectSchema = new Schema(
  {
    entityTitle: { type: String, required: false },
    numberOfHomesCompleted: { type: Number, required: false },
    numberOfUnits: { type: Number, required: false },
    address: AddressSchema,
    acquisitionPrice: { type: Number, required: false },
    dispositionPrice: { type: Number, required: false },
    type: { type: String, required: false },
    structure: { type: String, required: false },
    loanType: { type: String, required: false },
  },
  { _id: false },
)

const LenderApplicationSchema = new Schema(
  {
    sponsor: SponsorSchema,
    businessEntity: BusinessEntitySchema,
    currentProject: CurrentProjectSchema,
    previousProjects: [ProjectSchema],
  },
  { _id: false },
)

export { LenderApplicationSchema }
