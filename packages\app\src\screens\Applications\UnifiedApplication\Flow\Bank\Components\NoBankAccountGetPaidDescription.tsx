import React from 'react'
import { BtText } from '@linqpal/components/src/ui'
import { Trans, useTranslation } from 'react-i18next'
import { StyleSheet } from 'react-native'
import { useResponsive } from '../../../../../../utils/hooks'

export const NoBankAccountGetPaidDescription: React.FC = () => {
  const { sm } = useResponsive()
  const { t } = useTranslation('application')
  return (
    <BtText style={sm ? styles.text : styles.mobileText}>
      <Trans
        t={t}
        i18nKey="Bank.GetPaid.NotSelected.Description"
        components={{
          bold: (
            <BtText
              style={[sm ? styles.text : styles.mobileText, styles.boldText]}
            />
          ),
        }}
      />
    </BtText>
  )
}

const styles = StyleSheet.create({
  mobileText: {
    fontFamily: 'Inter, sans-serif',
    fontSize: 14,
    fontWeight: '500',
    color: '#626D75',
    lineHeight: 20,
    letterSpacing: 0,
    marginBottom: 28,
    marginTop: 16,
  },
  text: {
    fontFamily: 'Inter, sans-serif',
    fontSize: 18,
    fontWeight: '500',
    color: '#626D75',
    lineHeight: 26,
    letterSpacing: 0,
    marginTop: 24,
    marginBottom: 38,
  },
  boldText: {
    fontWeight: '700',
    color: '#626D75',
  },
})
