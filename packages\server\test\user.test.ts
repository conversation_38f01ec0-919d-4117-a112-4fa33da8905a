import { InvoiceDetails, routes, routes2 } from '@linqpal/models'
import {
  Company,
  crypt,
  CustomerAccount,
  defaultPlans,
  Draft,
  Invitation,
  Invoice,
  LoanApplication,
  LoanPaymentPlan,
  signupService,
  UserRole,
  VirtualCard,
} from '@linqpal/common-backend'
import chai from 'chai'
import chaiHttp from 'chai-http'
import { _Authorization } from './_axios'
import { _FirebaseTokenVerifier, FakeUsers } from './_firebase_admin'
import MD5 from 'crypto-js/md5'
import md5 from 'crypto-js/md5'
import * as sinon from 'sinon'
import * as admin from 'firebase-admin'
import './hooks'
import { beforeEachMockEncryption, beforeEachSendGrid } from './helper'
import { MAIN_ID } from '../../models/src/dictionaries/onboarding'
import moment from 'moment'
import {
  invitationTypes,
  LOAN_APPLICATION_STATUS,
} from '@linqpal/models/src/dictionaries'
import { ApplicationType } from '@linqpal/models/src/dictionaries/applicationType'
import { onBoardingService } from '@linqpal/common-backend/src/services/onBoarding/onBoarding.service'

chai.use(chaiHttp)
chai.should()
let tokenVerifier: ReturnType<typeof _FirebaseTokenVerifier>,
  authSignup: {
    verifyIdToken: () => void
    createSessionCookie: (idToken: string) => string
  },
  authUser: ReturnType<typeof _Authorization>

const { constructor1 } = FakeUsers
describe('Signup', () => {
  beforeEachMockEncryption()
  beforeEachSendGrid()

  let generateUniqueSubStub: sinon.SinonStub<any[], any>
  let fbMock: sinon.SinonStub
  beforeEach(() => {
    generateUniqueSubStub = sinon
      .stub(signupService, 'generateUniqueSub')
      .resolves('123')
    authSignup = {
      verifyIdToken: () => ({ uid: '123' }),
      createSessionCookie: (idToken: string) => `cookie-${idToken}`,
    }
    fbMock = sinon.stub(admin, 'auth').get(() => () => authSignup)
  })
  afterEach(() => {
    generateUniqueSubStub.restore()
    fbMock.restore()
  })
  it('should check', async () => {
    const resp = await routes2.user.check({
      login: '+***********',
    })
    resp.exists.should.be.false
  })
  it('should sign up', async () => {
    const res = await routes2.user.signup({
      type: 'code',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+***********',
      idToken: '123',
      opt_for_marketing_messages: true,
      isBusiness: true,
      company_name: 'biz1',
    })
    res.user?.firstName?.should.eq('John')
    res.user?.login?.should.eq('+***********')
    res.user?.settings?.opt_for_marketing_messages?.should.be.true
    const resp = await routes2.user.check({
      login: '+***********',
    })
    resp.exists.should.be.true

    const company = await Company.findOne({ name: 'biz1' })
    company?.should.be.not.null
  })
  it('should sign up from invoice link with default onboarding set', async () => {
    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
    })
    const com = await Company.create({
      name: 'Test supplier',
      type: 'supplier',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      settings: { loanPricingPackageId: 'A' },
    })
    const inv = await Invoice.create({
      customer_account_id: acc._id,
      company_id: com.id,
    })
    const response = await routes2.user.signup({
      type: 'code',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+***********',
      idToken: '123',
      opt_for_marketing_messages: true,
      company_name: 'biz1',
      isBusiness: true,
      invoiceId: inv.id,
    })
    response.user?.firstName?.should.eq('John')
    response.user?.login?.should.eq('+***********')
    response.user?.settings?.opt_for_marketing_messages?.should.be.true
    const resp = await routes2.user.check({
      login: '+***********',
    })
    resp.exists.should.be.true

    const company = await Company.findOne({ name: 'biz2' })
    company?.should.be.not.null
    company?.settings?.onboardingType?.length.should.equal(1)
    company?.settings?.onboardingType?.should.include(
      MAIN_ID.GENERAL_CONTRACTOR,
    )
  })
  it('should sign up from invoice link when not a business', async () => {
    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
    })
    const com = await Company.create({
      name: 'Test supplier',
      type: 'supplier',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      settings: { loanPricingPackageId: 'A' },
    })
    const inv = await Invoice.create({
      customer_account_id: acc._id,
      company_id: com.id,
    })
    const response = await routes2.user.signup({
      type: 'password',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+***********',
      idToken: '123',
      opt_for_marketing_messages: true,
      company_name: '',
      isBusiness: false,
      invoiceId: inv.id,
    })
    response.user?.firstName?.should.eq('John')
    response.user?.login?.should.eq('<EMAIL>')
    response.user?.settings?.opt_for_marketing_messages?.should.be.true
    const resp = await routes2.user.check({
      login: '<EMAIL>',
    })
    resp.exists.should.be.true

    response.company?.should.be.not.null
    response.company?.settings?.onboardingType?.length.should.equal(1)
    response.company?.settings?.onboardingType?.should.include(
      MAIN_ID.DEVELOPER_PROPERTY_OWNER_NO,
    )
  })
  it('should sign up from user invitation link (supplier invites another user)', async () => {
    const comp = await Company.create({
      name: 'Supplier inc',
      type: 'supplier',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      settings: { loanPricingPackageId: 'A' },
    })
    const inv = await Invitation.create({
      firstName: 'Test',
      lastName: 'User',
      type: invitationTypes.SUPPLIER_INVITE_USER,
      company_id: comp._id,
      role: 'User',
    })
    const response = await routes2.user.signup({
      type: 'code',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+***********',
      idToken: '123',
      opt_for_marketing_messages: true,
      isBusiness: true,
      company_name: 'biz3',
      invitation: inv?.id,
    })
    response.result!.should.be.equal('ok')
    response.should.have.property('user')
    response.user.firstName?.should.eq('John')
    response.user.login?.should.eq('+***********')
    response.user.settings?.opt_for_marketing_messages?.should.be.true
    const resp = await routes2.user.check({
      login: '+***********',
    })
    resp.exists.should.be.true

    const userRole = await UserRole.find({ sub: response.user.sub })
    userRole[0].company_id.should.equal(comp.id)
  })
  it('should sign up from customer invitation link with default onboarding set (supplier invites customer)', async () => {
    const inv = await Invitation.create({
      firstName: 'Test',
      lastName: 'User',
      type: invitationTypes.SUPPLIER_INVITE_CUSTOMER,
      company_id: '636375f68d04128ae38f4f44',
    })
    const response = await routes2.user.signup({
      type: 'code',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+***********',
      idToken: '123',
      opt_for_marketing_messages: true,
      isBusiness: true,
      company_name: 'biz3',
      invitation: inv?.id,
    })
    response.result!.should.be.equal('ok')
    response.should.have.property('user')
    response.user.firstName?.should.eq('John')
    response.user.login?.should.eq('+***********')
    response.user.settings?.opt_for_marketing_messages?.should.be.true
    const resp = await routes2.user.check({
      login: '+***********',
    })
    resp.exists.should.be.true

    response.company.should.be.not.null
    response.company.settings.onboardingType?.length.should.equal(1)
    response.company.settings.should.haveOwnProperty('onboardingType')
    response.company.settings.onboardingType?.should.not.be.undefined
    response.company.settings.onboardingType?.should.include(
      'generalContractor',
    )
    response.company.settings.should.haveOwnProperty('invitedBy')
    response.company.settings.invitedBy?.should.equal(
      '636375f68d04128ae38f4f44',
    )
  })
})
describe('User', () => {
  let obsMock: sinon.SinonStub
  beforeEachMockEncryption()
  beforeEach(async () => {
    obsMock = sinon
      .stub(onBoardingService, 'getCreditApplications')
      .resolves([])
    tokenVerifier = _FirebaseTokenVerifier()
    authUser = _Authorization(constructor1.auth)
    await LoanPaymentPlan.insertMany(defaultPlans)
  })
  afterEach(() => {
    authUser.restore()
    tokenVerifier.restore()
    obsMock.restore()
  })
  it('get info', async () => {
    const resp = await routes2.user.info()
    resp.should.have.property('result')
    resp.result!.should.be.equal('ok')
    resp.should.have.property('user')
    resp.should.have.property('settings')
    resp.company.should.have.property('isCustomerOfSupplier')
    resp.company.isCustomerOfSupplier!.should.be.equal(false)
    resp.settings.should.have.property('walletConnected')
    resp.settings.walletConnected!.should.be.equal(false)
    resp.user.should.not.have.property('type')
    resp.should.have.property('roles')
    resp.roles.should.be.an('array')
  })
  it("get info - for a supplier's customer", async () => {
    const company = await Company.create({})

    await CustomerAccount.create({
      company_id: company._id.toString(),
      phone: constructor1.info.phone_number,
    })

    const resp = await routes2.user.info()
    resp.should.have.property('result')
    resp.result!.should.be.equal('ok')
    resp.should.have.property('user')
    resp.company.should.have.property('isCustomerOfSupplier')
    resp.company.isCustomerOfSupplier!.should.be.equal(true)
    resp.should.have.property('settings')
    resp.settings.should.have.property('walletConnected')
    resp.settings.walletConnected!.should.be.equal(false)
    resp.user.should.not.have.property('type')
    resp.should.have.property('roles')
    resp.roles.should.be.an('array')
  })
  it('update info', async () => {
    const response = await routes2.user.updateInfo({
      name: 'abc',
      email: '<EMAIL>',
      phone: '**********',
      login: 'wert',
    })
    response.result!.should.be.equal('ok')
    response.should.have.property('user')
    response.user.name!.should.be.equal('abc')
    response.user.login!.should.be.equal(constructor1.info.phone_number)
    response.user.phone!.should.be.equal('+***********')
  })
  it('gets roles', async () => {
    const resp = await routes.user.roles()
    resp.should.have.property('roles')
    resp.roles.should.be.an('array')
  })
  it('saves drafts', async () => {
    await routes2.user.loanApplication()
    let resp = await routes.user.draft(ApplicationType.Credit, undefined)
    const doc = resp
    doc._id.should.not.be.undefined
    doc.type.should.equal('loan_application')
    doc.company_id.should.not.be.undefined
    resp = await routes.user.saveDraft(doc)
    resp.should.have.property('_id')
    resp._id.should.equal(doc._id)
    doc.data = {
      bank: {
        group: 'bank',
        items: [
          {
            identifier: '123',
            filled: false,
            content: {
              name: 'xyz',
            },
          },
        ],
      },
      business: {
        group: 'business',
      },
      businessInfo: {
        group: 'businessInfo',
      },
    }
    resp = await routes.user.saveDraft(doc)
    resp._id.should.equal(doc._id)

    resp = await routes.user.draft(ApplicationType.Credit, undefined)
    resp._id.should.equal(doc._id)
    // Should save only the loan application sections
    resp.data.businessInfo.should.not.be.undefined
    resp.data.bank.should.not.be.undefined
    chai.expect(resp.data.business).to.be.undefined

    const id = doc._id
    delete doc._id
    resp = await routes.user.saveDraft(doc)
    resp._id.should.equal(id)
  })
  it('should delete draft and loan application', async () => {
    const loanApp = await routes2.user.loanApplication()
    let resp = await routes.user.draft(ApplicationType.Credit, undefined)
    const doc = resp
    doc._id.should.not.be.undefined
    doc.type.should.equal('loan_application')
    doc.company_id.should.not.be.undefined
    resp = await routes.user.saveDraft(doc)
    resp.should.have.property('_id')
    resp._id.should.equal(doc._id)

    resp = await routes.user.deleteDraft(resp._id, loanApp.id)
    const draft = await Draft.findById(resp._id)
    const loanApplication = await LoanApplication.findById(loanApp.id)
    chai.expect(draft).to.be.null
    chai.expect(loanApplication).to.be.null
  })
  it('should create new loanApplication, when approved application with no invoice exists (prequalified credit)', async () => {
    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
    })
    const com = await Company.create({
      name: 'Test supplier',
      type: 'supplier',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      settings: { loanPricingPackageId: 'A' },
    })
    const inv = await Invoice.create({
      customer_account_id: acc._id,
      company_id: com.id,
    })
    const invoiceDetails = await InvoiceDetails.create({
      invoiceId: inv._id.toString(),
    })

    const resp = await routes2.user.loanApplication()
    resp.id.should.not.be.undefined

    await LoanApplication.findOneAndUpdate(
      { _id: resp.id },
      { status: 'approved' },
    )
    const resp2 = await routes2.user.loanApplication(invoiceDetails)
    resp2.id.should.not.equal(resp.id)
  })
  it('should create or update loan application', async () => {
    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
    })
    const com = await Company.create({
      name: 'Test supplier',
      type: 'supplier',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      settings: { loanPricingPackageId: 'A' },
    })
    // When there is no existing loan application in new status, creates it
    const inv = await Invoice.create({
      customer_account_id: acc._id,
      company_id: com.id,
    })
    console.log(inv)
    const invoiceDetails = await InvoiceDetails.create({
      invoiceId: inv._id.toString(),
    })
    const resp = await routes2.user.loanApplication(invoiceDetails)
    console.log(resp)
    const { id } = resp
    id.should.not.be.undefined

    // When there is a loan application in new status and user chooses another invoice, update the invoice id
    const inv2 = await Invoice.create({
      customer_account_id: acc._id,
      company_id: com.id,
    })
    const invoiceDetails2 = await InvoiceDetails.create({
      invoiceId: inv2._id.toString(),
    })
    const resp2 = await routes2.user.loanApplication(invoiceDetails2)
    resp2.result!.should.eq('ok')
    await LoanApplication.findByIdAndDelete({ _id: id })

    // When there is no invoice, should create loan application successfully
    const resp3 = await routes2.user.loanApplication()
    console.log(resp3)
    resp3.id.should.not.be.undefined
    await LoanApplication.findByIdAndDelete({ _id: resp3.id })

    // If it's a builder uploaded invoice, with company id empty, should create loan application without any error
    const inv3 = await Invoice.create({
      customer_account_id: '',
      company_id: '',
    })
    const invoiceDetails3 = await InvoiceDetails.create({
      invoiceId: inv3._id.toString(),
    })
    const resp4 = await routes2.user.loanApplication(invoiceDetails3)
    resp4.result!.should.eq('ok')
  })
  it('should throw an error if the last application was rejected and 3 months have not passed to apply for a new one', async () => {
    const plan = await LoanPaymentPlan.find({ name: '30' })
    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
    })
    const com = await Company.create({
      name: 'Test supplier',
      type: 'supplier',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      settings: { loanPricingPackageId: 'A' },
    })
    const inv = await Invoice.create({
      customer_account_id: acc._id,
      company_id: com.id,
    })
    const invoiceDetails = await InvoiceDetails.create({
      invoiceId: inv._id.toString(),
      paymentPlan: plan[0]._id.toString(),
    })
    const resp = await routes2.user.loanApplication(invoiceDetails)
    const { id } = resp
    id.should.not.be.undefined

    await LoanApplication.findOneAndUpdate(
      { _id: resp.id },
      {
        status: LOAN_APPLICATION_STATUS.REJECTED,
        decisionDate: moment().toDate(),
        submitDate: moment().toDate(),
        'invoiceDetails.invoiceId': null,
      },
    )
    const resp2 = await routes2.user.loanApplication()
    resp2.should.have.property('result')
    resp2.result!.should.be.equal('LogicalError')
    resp2.should.have.property('code')
    resp2.code!.should.be.equal('last-app-was-rejected')
  })
  it('should update correct payment plan', async () => {
    const plan = await LoanPaymentPlan.find({ name: '30' })
    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
    })
    const com = await Company.create({
      name: 'Test supplier',
      type: 'supplier',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      settings: { loanPricingPackageId: 'A' },
    })
    const inv = await Invoice.create({
      customer_account_id: acc._id,
      company_id: com.id,
    })
    const invoiceDetails = await InvoiceDetails.create({
      invoiceId: inv._id.toString(),
    })
    let resp = await routes2.user.loanApplication(invoiceDetails)
    const { id } = resp
    id.should.not.be.undefined

    // Should not set empty plan
    resp = await routes.user.updatePlan([inv._id.toString()], '')
    resp.result!.should.be.equal('LogicalError')
    resp.should.have.property('code')
    resp.code!.should.be.equal('error/no-payment-plan-selected')

    // Should not set wrong plan
    resp = await routes.user.updatePlan([inv._id.toString()], '20')
    resp.result!.should.be.equal('LogicalError')
    resp.should.have.property('code')
    resp.code!.should.be.equal('error/wrong-payment-plan')

    // Should set correct plan
    resp = await routes.user.updatePlan(
      [inv._id.toString()],
      plan[0]._id.toString(),
      id,
    )
    resp.result!.should.be.equal('ok')
    const updated = await LoanApplication.findOne({ id })
    updated?.should.not.be.null
    updated?.should.have.property('invoiceDetails')
    updated?.invoiceDetails.paymentPlan.should.be.equal(plan[0]._id.toString())
  })
  it('should return virtual card info, when card is unused', async () => {
    const plan = await LoanPaymentPlan.find({ name: '60vc' })
    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
    })
    const com = await Company.create({
      name: 'Test supplier',
      type: 'supplier',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      settings: { loanPricingPackageId: 'A' },
    })
    const inv = await Invoice.create({
      customer_account_id: acc._id,
      company_id: com.id,
    })
    await VirtualCard.create({
      company_id: com.id,
      invoice_id: inv.id,
      name: 'test',
      amount: 5,
      cardId: '80c242e8-be55-4024-b175-5b824c335b2c',
      useDate: '',
      usedAmount: '',
    })
    const invoiceDetails = await InvoiceDetails.create({
      invoiceId: inv.id,
      paymentPlan: plan[0]._id.toString(),
      cardId: '80c242e8-be55-4024-b175-5b824c335b2c',
    })

    const app = await routes2.user.loanApplication(invoiceDetails)
    const a = await LoanApplication.findById(app.id)
    chai.expect(a?.fundingSource).eq('arcadia')

    const resp = await routes.user.draft(ApplicationType.Credit, undefined)
    resp._id.should.not.be.undefined
    resp.virtualCardDetails.unused.should.equal(true)
  })
  it('should return virtual card info when card is used', async () => {
    const plan = await LoanPaymentPlan.find({ name: '60vc' })
    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
    })
    const com = await Company.create({
      name: 'Test supplier',
      type: 'supplier',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      settings: { loanPricingPackageId: 'A' },
    })
    const inv = await Invoice.create({
      customer_account_id: acc._id,
      company_id: com.id,
    })
    await VirtualCard.create({
      company_id: com.id,
      invoice_id: inv.id,
      name: 'test',
      amount: 5,
      cardId: '80c242e8-be55-4024-b175-5b824c335b2c',
      useDate: '12/02/2022',
      usedAmount: '5',
    })
    const invoiceDetails = await InvoiceDetails.create({
      invoiceId: inv.id,
      paymentPlan: plan[0]._id.toString(),
      cardId: '80c242e8-be55-4024-b175-5b824c335b2c',
    })

    const app = await routes2.user.loanApplication(invoiceDetails)
    app.result!.should.eq('ok')

    const resp = await routes.user.draft(ApplicationType.Credit, undefined)
    resp._id.should.not.be.undefined
    resp.virtualCardDetails.unused.should.equal(false)
  })
  it('should return correct loan application status', async () => {
    const plan = await LoanPaymentPlan.find({ name: '60vc' })
    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
    })
    const com = await Company.create({
      name: 'Test supplier',
      type: 'supplier',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      settings: { loanPricingPackageId: 'A' },
    })
    const inv = await Invoice.create({
      customer_account_id: acc._id,
      company_id: com.id,
    })
    const invoiceDetails = await InvoiceDetails.create({
      invoiceId: inv.id,
      paymentPlan: plan[0]._id.toString(),
    })

    const app = await routes2.user.loanApplication(invoiceDetails)
    app.result!.should.eq('ok')

    const app2 = await routes2.user.loanApplication(invoiceDetails)

    await LoanApplication.findByIdAndUpdate(app2.id.toString(), {
      status: LOAN_APPLICATION_STATUS.PROCESSING,
    })
    let resp = await routes.user.draft(ApplicationType.Credit, undefined)
    resp._id.should.not.be.undefined
    resp.loanStatus.should.equal(LOAN_APPLICATION_STATUS.PROCESSING)

    await LoanApplication.findByIdAndUpdate(app2.id.toString(), {
      status: LOAN_APPLICATION_STATUS.ERROR,
    })
    resp = await routes.user.draft(ApplicationType.Credit, undefined)
    resp._id.should.not.be.undefined
    resp.loanStatus.should.equal(LOAN_APPLICATION_STATUS.PROCESSING)

    await LoanApplication.findByIdAndUpdate(app2.id.toString(), {
      status: LOAN_APPLICATION_STATUS.APPROVED,
    })
    resp = await routes.user.draft(ApplicationType.Credit, undefined)
    resp._id.should.not.be.undefined
    resp.loanStatus.should.equal(LOAN_APPLICATION_STATUS.APPROVED)
  })
  it('should return rejected loan status when one of the applications in last 3 months was rejected', async () => {
    const plan = await LoanPaymentPlan.find({ name: '60vc' })
    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
    })
    const com = await Company.create({
      name: 'Test supplier',
      type: 'supplier',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      settings: { loanPricingPackageId: 'A' },
    })
    const inv = await Invoice.create({
      customer_account_id: acc._id,
      company_id: com.id,
    })
    const invoiceDetails = await InvoiceDetails.create({
      invoiceId: inv.id,
      paymentPlan: plan[0]._id.toString(),
    })

    const app = await routes2.user.loanApplication(invoiceDetails)
    await LoanApplication.findByIdAndUpdate(app.id.toString(), {
      status: LOAN_APPLICATION_STATUS.REJECTED,
      submitDate: moment().toDate(),
      decisionDate: moment().toDate(),
      'invoiceDetails.invoiceId': null,
    })
    await routes2.user.loanApplication(invoiceDetails)

    const resp = await routes.user.draft(ApplicationType.Credit, undefined)
    resp._id.should.not.be.undefined
    resp.loanStatus.should.equal(LOAN_APPLICATION_STATUS.REJECTED)
    resp.loanApplicationId.should.equal(app.id)
  })
  it.skip('validates and encrypts ssn', async () => {
    const doc = {
      _id: '123',
      type: 'general_application',
      data: {
        group: {
          items: [
            {
              identifier: 'item',
              content: {
                ssn: '123-456789',
                ein: '123456789',
                innerItems: [{ identifier: 'inner-item', ssn: '012345678' }],
              },
            },
          ],
        },
      },
    }
    //Should accept only numbers
    let resp = await routes.user.saveDraft(doc)
    resp.result.should.eq('LogicalError')
    resp.message.should.equal('ssn does not match format')

    //Should accept only 9 digits
    doc.data.group.items[0].content.ssn = '**********'
    resp = await routes.user.saveDraft(doc)
    resp.result.should.eq('LogicalError')
    resp.message.should.equal('ssn should have length 9')

    //Should save ssn encrypted
    doc.data.group.items[0].content.ssn = '123456789'
    resp = await routes.user.saveDraft(doc)
    const id = resp._id
    doc._id = id
    let draft = await Draft.findById(id)

    let draftData: any = draft ? draft.toObject() : null
    draftData.should.haveOwnProperty('data')
    draftData.data
      .get('group')
      .items[0].content.ssn.hash.should.equal(MD5('123456789').toString())
    draftData.data
      .get('group')
      .items[0].content.ssn.cipher.should.equal(
        Buffer.from('123456789').toString('base64'),
      )
    draftData.data
      .get('group')
      .items[0].content.ssn.display.should.equal('*********')

    //Should save inner array ssn encrypted
    draftData.data
      .get('group')
      .items[0].content.innerItems[0].ssn.hash.should.equal(
        MD5('012345678').toString(),
      )
    draftData.data
      .get('group')
      .items[0].content.innerItems[0].ssn.cipher.should.equal(
        Buffer.from('012345678').toString('base64'),
      )
    draftData.data
      .get('group')
      .items[0].content.innerItems[0].ssn.display.should.equal('*********')

    //Should return display value
    resp = await routes.user.draft(ApplicationType.Supplier, undefined)
    resp.data.group.items[0].content.ssn.should.equal('*********')
    resp.data.group.items[0].content.innerItems[0].ssn.should.equal('*********')

    doc.data = resp.data
    resp = await routes.user.saveDraft(doc)
    resp.result.should.eq('ok')
    draft = await Draft.findById(id)
    draftData = draft ? draft.toObject() : null
    draftData.should.haveOwnProperty('data')
    draftData.data
      .get('group')
      .items[0].content.ssn.cipher.should.equal(
        Buffer.from('123456789').toString('base64'),
      )
    draftData.data
      .get('group')
      .items[0].content.innerItems[0].ssn.cipher.should.equal(
        Buffer.from('012345678').toString('base64'),
      )
  })

  it.skip('validates and encrypts ssn for loan application', async () => {
    const doc = {
      _id: '123',
      type: 'general_application',
      data: {
        personalInfo: {
          group: 'personalInfo',
          items: [
            {
              identifier: 'ssn',
              content: '123-456789',
            },
          ],
        },
      },
    }

    //Should accept only numbers
    let resp = await routes.user.saveDraft(doc)
    resp.result.should.eq('LogicalError')
    resp.message.should.equal('ssn does not match format')

    //Should accept only 9 digits
    doc.data.personalInfo.items[0].content = '**********'
    resp = await routes.user.saveDraft(doc)
    resp.result.should.eq('LogicalError')
    resp.message.should.equal('ssn should have length 9')

    //Should save ssn encrypted
    doc.data.personalInfo.items[0].content = '123456789'
    resp = await routes.user.saveDraft(doc)
    const id = resp._id
    doc._id = id
    let draft = await Draft.findById(id)
    let draftData: any = draft ? draft.toObject() : null
    draftData.should.haveOwnProperty('data')
    draftData.data
      .get('personalInfo')
      .items[0].content.hash.should.equal(MD5('123456789').toString())
    draftData.data
      .get('personalInfo')
      .items[0].content.cipher.should.equal(
        Buffer.from('123456789').toString('base64'),
      )
    draftData.data
      .get('personalInfo')
      .items[0].content.display.should.equal('*********')

    //Should return display value
    resp = await routes.user.draft(ApplicationType.Credit, undefined)
    resp.data.personalInfo.items[0].content.should.equal('*********')

    doc.data = resp.data
    resp = await routes.user.saveDraft(doc)
    resp.result.should.eq('ok')
    draft = await Draft.findById(id)
    draftData = draft ? draft.toObject() : null
    draftData.should.haveOwnProperty('data')
    draftData.data
      .get('personalInfo')
      .items[0].content.cipher.should.equal(
        Buffer.from('123456789').toString('base64'),
      )
  })
  it('should revoke session on logout', async () => {
    const mockVerify = sinon
      .stub(admin.auth(), 'verifySessionCookie')
      .callsFake(() =>
        Promise.resolve({
          sub: '1234',
          aud: 'test',
          auth_time: 123,
          exp: 123,
          firebase: { identities: { password: {} }, sign_in_provider: 'test' },
          uid: '1234',
          iat: 123,
          iss: '123',
        }),
      )
    const mockRevoke = sinon
      .stub(admin.auth(), 'revokeRefreshTokens')
      .callsFake(() => Promise.resolve())
    await routes.user.logout()
    mockRevoke.calledOnceWithExactly('1234').should.be.true
    mockVerify.restore()
    mockRevoke.restore()
  })
})

describe('Expired token', () => {
  it('should return authorization error when token expired', async () => {
    const mockFirebaseAuth = _FirebaseTokenVerifier()
    const mockVerify = sinon
      .stub(admin.auth(), 'verifyIdToken')
      .callsFake(() => Promise.reject('Expired'))
    const resp = await routes2.user.info()
    resp.result!.should.eq('AuthenticationError')
    mockVerify.restore()
    mockFirebaseAuth.restore()
  })
})
