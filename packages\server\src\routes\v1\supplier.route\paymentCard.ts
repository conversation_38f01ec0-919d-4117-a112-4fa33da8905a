import {
  bankAccountsService,
  cardsService,
  Logger,
} from '@linqpal/common-backend'
import { handleTabaPayError } from '@linqpal/common-backend/src/services/tabapay/errorCodes'
import axios from 'axios'
import { ControllerItem } from 'src/routes/controllerItem'

const logger = new Logger({
  module: 'BlueTape',
  subModule: 'supplierPaymentCard',
})

function logAxiosError(err: any) {
  const { config, response, code, message } = err
  const fullUrl = config?.baseURL
    ? `${config.baseURL}${config.url}`
    : config?.url

  logger.error(`
    Request Info:
      Full URL: ${fullUrl}
      URL: ${config?.url}
      Base URL: ${config?.baseURL}
      Method: ${config?.method}
      Request Data: ${config?.data}
      Request Headers: ${JSON.stringify(config?.headers)}

    Response Info:
      Status Code: ${response?.status}
      Response Message: ${response?.statusText}
      Response Data: ${JSON.stringify(response?.data)}

    Error Info:
      Error Code: ${code}
      Error Message: ${message}
  `)
}

export default {
  post: async (req, res) => {
    const { customerId } = req.query
    if (!customerId || typeof customerId !== 'string') {
      res.status(400).send({
        result: 'failed',
        error: 'Please provide customer id',
        type: 'customer_account_invalid',
      })
      return
    }

    try {
      const result = await cardsService.createPaymentCard(
        req.body,
        req.company?.id || '',
        req.user || ({ sub: '', firebaseId: '' } as any),
        customerId,
      )

      res.status(result.statusCode).send(result)
    } catch (err: any) {
      if (axios.isAxiosError(err)) {
        logAxiosError(err)
      } else {
        logger.error('Unknown error:', err)
      }

      if (err?.response?.data) {
        const tabapayError = err.response.data
        if (tabapayError.SC && tabapayError.EC && tabapayError.EM) {
          return handleTabaPayError(tabapayError, res, logger)
        }
      }

      logger.error(
        `unable to add payment card for contractor '${
          req.company?.name || 'unknown'
        }' and customer account id: ${customerId}`,
      )
      res.status(500).send({
        result: 'failed',
        error: 'Unable to add card now. Please try again later.',
        type: 'network_error',
      })
    }
  },

  delete: async (req, res, next) => {
    const { id, customerId } = req.query
    if (!id || typeof id !== 'string') {
      res.status(400).send({ error: 'Id not provided' })
      return
    }

    if (!customerId || typeof customerId !== 'string') {
      res.status(400).send({ error: 'Customer Id not provided' })
      return
    }

    await bankAccountsService.deleteBankAccount(
      id,
      req.company?.id || '',
      customerId,
    )
    next()
  },
} as ControllerItem
