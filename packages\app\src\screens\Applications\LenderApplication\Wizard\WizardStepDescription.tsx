import { BtPlainText } from '@linqpal/components/src/ui'
import React, { FC } from 'react'
import { StyleSheet } from 'react-native'
import { useTranslation } from 'react-i18next'
import { observer } from 'mobx-react'
import { useResponsive } from '../../../../utils/hooks'
import { useLenderApplication } from '../LenderApplicationContext'

export const WizardStepDescription: FC = observer(() => {
  const { sm } = useResponsive()
  const { t } = useTranslation('application')

  const store = useLenderApplication()

  const description = store.stepOptions?.description
  if (!description) return null

  return (
    <BtPlainText
      style={[
        styles.description,
        sm ? styles.descriptionDesktop : styles.descriptionMobile,
      ]}
    >
      {t(description as any)}
    </BtPlainText>
  )
})

const styles = StyleSheet.create({
  description: {
    fontSize: 16,
    fontWeight: '600',
  },
  descriptionDesktop: {
    lineHeight: 30,
  },
  descriptionMobile: {
    marginBottom: 12,
  },
})
