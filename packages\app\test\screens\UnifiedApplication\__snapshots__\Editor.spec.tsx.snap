// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Unified Application flow Should pass the application flow 1`] = `
<View
  style={
    Object {
      "flex": 1,
    }
  }
>
  <View
    style={
      Object {
        "flex": 1,
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
          "borderBottomColor": "#EBEEEF",
          "borderBottomWidth": 1,
          "flexDirection": "row",
          "height": 72,
          "justifyContent": "space-between",
          "paddingHorizontal": 20,
          "width": "100%",
        }
      }
    >
      <View
        accessibilityState={
          Object {
            "disabled": false,
          }
        }
        accessible={true}
        focusable={true}
        onClick={[Function]}
        onResponderGrant={[Function]}
        onResponderMove={[Function]}
        onResponderRelease={[Function]}
        onResponderTerminate={[Function]}
        onResponderTerminationRequest={[Function]}
        onStartShouldSetResponder={[Function]}
        style={
          Object {
            "alignItems": "center",
            "borderColor": "#E6EBEE",
            "borderRadius": 6,
            "borderWidth": 1,
            "height": 40,
            "justifyContent": "center",
            "opacity": 1,
            "width": 40,
          }
        }
      >
        <View
          style={
            Object {
              "height": 12,
              "style": undefined,
              "width": 12,
            }
          }
        />
      </View>
      <Text
        appearance="default"
        category="p1"
        ellipsizeMode="tail"
        style={
          Array [
            Object {
              "color": "#003353",
              "fontFamily": "System",
              "fontSize": 15,
              "fontWeight": "400",
            },
            Object {
              "color": "#001929",
              "flex": 1,
              "fontFamily": "Inter, sans-serif",
              "fontSize": 20,
              "fontWeight": "600",
              "textAlign": "center",
            },
          ]
        }
      >
        Credit Request
      </Text>
      <View
        style={
          Object {
            "height": 12,
            "width": 50,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "padding": 20,
        }
      }
    >
      <Text
        appearance="default"
        category="p1"
        ellipsizeMode="tail"
        style={
          Array [
            Object {
              "color": "#003353",
              "fontFamily": "System",
              "fontSize": 15,
              "fontWeight": "400",
            },
            Object {
              "color": "#00A0F3",
              "fontFamily": "Inter, sans-serif",
              "fontSize": 20,
              "fontWeight": "600",
            },
          ]
        }
        testID="ApplicationTitle"
      >
        Business Details
      </Text>
    </View>
    <View
      style={
        Object {
          "flexDirection": "row",
          "height": 3,
          "width": "100%",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#00A0F3",
            "flex": 0,
            "height": "100%",
          }
        }
      />
      <View
        style={
          Object {
            "backgroundColor": "#CCD6DD",
            "flex": 1,
            "height": "100%",
          }
        }
      />
    </View>
    <RCTScrollView
      contentContainerStyle={
        Object {
          "alignItems": "center",
          "flexGrow": 1,
        }
      }
      showsVerticalScrollIndicator={false}
    >
      <View>
        <View
          style={
            Object {
              "flex": 1,
              "maxWidth": 700,
              "minWidth": 700,
              "paddingHorizontal": 0,
              "paddingVertical": 20,
            }
          }
        >
          <View
            style={
              Object {
                "height": 20,
                "width": 0,
              }
            }
          />
          <View>
            <Text
              appearance="default"
              category="p1"
              ellipsizeMode="tail"
              style={
                Array [
                  Object {
                    "color": "#003353",
                    "fontFamily": "System",
                    "fontSize": 15,
                    "fontWeight": "400",
                  },
                  Object {
                    "color": "#003353",
                    "fontFamily": "Inter, sans-serif",
                    "fontSize": 24,
                    "fontWeight": "600",
                    "lineHeight": 36,
                    "marginBottom": 10,
                  },
                ]
              }
            >
              What is the registered business name?
            </Text>
          </View>
          <View>
            <Text
              appearance="default"
              category="p1"
              ellipsizeMode="tail"
              style={
                Array [
                  Object {
                    "color": "#003353",
                    "fontFamily": "System",
                    "fontSize": 15,
                    "fontWeight": "400",
                  },
                  Object {
                    "color": "#335C75",
                    "fontFamily": "Inter, sans-serif",
                    "fontSize": 16,
                    "fontWeight": "600",
                    "lineHeight": 30,
                    "marginBottom": 24,
                  },
                ]
              }
            >
              Please enter the legal name this business was registered with.
            </Text>
          </View>
          <View
            style={
              Object {
                "height": 4,
                "width": 0,
              }
            }
          />
          <View
            accessible={true}
            collapsable={false}
            focusable={false}
            nativeID="animatedComponent"
            onClick={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
            style={
              Object {
                "opacity": 1,
              }
            }
            testID="BusinessNameInput"
          >
            <Text
              appearance="default"
              category="p1"
              ellipsizeMode="tail"
              style={
                Array [
                  Object {
                    "color": "#003353",
                    "fontFamily": "System",
                    "fontSize": 15,
                    "fontWeight": "400",
                  },
                  Object {
                    "color": "#003353",
                    "fontSize": 12,
                    "fontWeight": "500",
                    "marginBottom": 5,
                  },
                ]
              }
            >
              Business Name
            </Text>
            <View
              style={
                Array [
                  Object {
                    "backgroundColor": "#FFFFFF",
                    "borderColor": "#668598",
                    "borderRadius": 4,
                    "borderWidth": 1,
                    "minHeight": 48,
                    "paddingHorizontal": 8,
                    "paddingVertical": 11,
                  },
                  Object {
                    "alignItems": "center",
                    "flexDirection": "row",
                    "width": "100%",
                  },
                ]
              }
            >
              <TextInput
                appearance="default"
                autoCapitalize="none"
                autoCorrect={false}
                disabled={false}
                editable={true}
                onBlur={[Function]}
                onChangeText={[Function]}
                onFocus={[Function]}
                onMouseEnter={[Function]}
                onMouseLeave={[Function]}
                placeholderTextColor="#8F9BB3"
                size="large"
                status="basic"
                style={
                  Array [
                    Object {
                      "color": "#003353",
                      "fontFamily": "System",
                      "fontSize": 15,
                      "fontWeight": "normal",
                      "marginHorizontal": 8,
                    },
                    Object {
                      "flexBasis": "auto",
                      "flexGrow": 1,
                      "flexShrink": 1,
                    },
                    null,
                    undefined,
                  ]
                }
                value=""
              />
            </View>
          </View>
          <View
            style={
              Object {
                "height": 4,
                "width": 0,
              }
            }
          />
          <Text
            appearance="default"
            category="p1"
            ellipsizeMode="tail"
            style={
              Array [
                Object {
                  "color": "#003353",
                  "fontFamily": "System",
                  "fontSize": 15,
                  "fontWeight": "400",
                },
                Object {
                  "color": "#335C75",
                  "fontFamily": "Inter, sans-serif",
                  "fontSize": 16,
                  "fontWeight": "600",
                  "lineHeight": 30,
                  "marginBottom": 24,
                  "marginTop": 24,
                },
              ]
            }
          >
            If you operate under a DBA, please enter it:
          </Text>
          <View
            accessible={true}
            collapsable={false}
            focusable={false}
            nativeID="animatedComponent"
            onClick={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
            style={
              Object {
                "opacity": 1,
              }
            }
            testID="DbaInput"
          >
            <Text
              appearance="default"
              category="p1"
              ellipsizeMode="tail"
              style={
                Array [
                  Object {
                    "color": "#003353",
                    "fontFamily": "System",
                    "fontSize": 15,
                    "fontWeight": "400",
                  },
                  Object {
                    "color": "#003353",
                    "fontSize": 12,
                    "fontWeight": "500",
                    "marginBottom": 5,
                  },
                ]
              }
            >
              DBA
            </Text>
            <View
              style={
                Array [
                  Object {
                    "backgroundColor": "#FFFFFF",
                    "borderColor": "#668598",
                    "borderRadius": 4,
                    "borderWidth": 1,
                    "minHeight": 48,
                    "paddingHorizontal": 8,
                    "paddingVertical": 11,
                  },
                  Object {
                    "alignItems": "center",
                    "flexDirection": "row",
                    "width": "100%",
                  },
                ]
              }
            >
              <TextInput
                appearance="default"
                autoCapitalize="none"
                autoCorrect={false}
                disabled={false}
                editable={true}
                onBlur={[Function]}
                onChangeText={[Function]}
                onFocus={[Function]}
                onMouseEnter={[Function]}
                onMouseLeave={[Function]}
                placeholderTextColor="#8F9BB3"
                size="large"
                status="basic"
                style={
                  Array [
                    Object {
                      "color": "#003353",
                      "fontFamily": "System",
                      "fontSize": 15,
                      "fontWeight": "normal",
                      "marginHorizontal": 8,
                    },
                    Object {
                      "flexBasis": "auto",
                      "flexGrow": 1,
                      "flexShrink": 1,
                    },
                    null,
                    undefined,
                  ]
                }
                value=""
              />
            </View>
          </View>
          <View
            style={
              Object {
                "flexDirection": "row",
                "justifyContent": "flex-end",
                "marginTop": 20,
                "width": "100%",
              }
            }
          >
            <View
              accessible={true}
              focusable={true}
              onClick={[Function]}
              onResponderGrant={[Function]}
              onResponderMove={[Function]}
              onResponderRelease={[Function]}
              onResponderTerminate={[Function]}
              onResponderTerminationRequest={[Function]}
              onStartShouldSetResponder={[Function]}
              style={
                Object {
                  "alignItems": "center",
                  "backgroundColor": "transparent",
                  "borderColor": "transparent",
                  "borderRadius": 8,
                  "borderWidth": 1,
                  "flexDirection": "row",
                  "justifyContent": "center",
                  "marginRight": 20,
                  "minHeight": 48,
                  "minWidth": 48,
                  "opacity": 1,
                  "paddingHorizontal": 10,
                  "paddingVertical": 14,
                }
              }
              testID="ApplicationSkipButton"
            >
              <Text
                appearance="default"
                category="p1"
                ellipsizeMode="tail"
                style={
                  Array [
                    Object {
                      "color": "#003353",
                      "fontFamily": "System",
                      "fontSize": 15,
                      "fontWeight": "400",
                    },
                    Object {
                      "color": "#668598",
                      "fontSize": 16,
                      "fontWeight": "700",
                    },
                  ]
                }
              >
                Skip for now
              </Text>
            </View>
            <View
              accessibilityState={
                Object {
                  "disabled": true,
                }
              }
              accessible={true}
              focusable={true}
              onClick={[Function]}
              onResponderGrant={[Function]}
              onResponderMove={[Function]}
              onResponderRelease={[Function]}
              onResponderTerminate={[Function]}
              onResponderTerminationRequest={[Function]}
              onStartShouldSetResponder={[Function]}
              style={
                Object {
                  "alignItems": "center",
                  "backgroundColor": "#99D9FA",
                  "borderColor": "#99D9FA",
                  "borderRadius": 8,
                  "borderWidth": 1,
                  "flexDirection": "row",
                  "justifyContent": "center",
                  "minHeight": 48,
                  "minWidth": 48,
                  "opacity": 1,
                  "paddingHorizontal": 10,
                  "paddingVertical": 14,
                  "width": 150,
                }
              }
              testID="ApplicationNextButton"
            >
              <Text
                appearance="default"
                category="p1"
                style={
                  Array [
                    Object {
                      "color": "#003353",
                      "fontFamily": "System",
                      "fontSize": 15,
                      "fontWeight": "400",
                    },
                    Object {
                      "color": "#FFFFFF",
                      "fontFamily": "System",
                      "fontSize": 16,
                      "fontWeight": "bold",
                      "marginHorizontal": 10,
                    },
                  ]
                }
              >
                Next
              </Text>
            </View>
          </View>
        </View>
        <div
          style={
            Object {
              "alignSelf": "baseline",
              "backgroundColor": "white",
              "bottom": 0,
              "position": "sticky",
              "width": "100%",
            }
          }
        />
      </View>
    </RCTScrollView>
  </View>
</View>
`;
