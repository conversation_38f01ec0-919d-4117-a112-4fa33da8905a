import { Trans, useTranslation } from 'react-i18next'
import { composeStyle } from '@linqpal/components/src/helpers'
import { StyleSheet, View } from 'react-native'
import { BtLink, BtText } from '@linqpal/components/src/ui'
import React, { FC } from 'react'
import { colors } from '@linqpal/components/src/theme'
import { observer } from 'mobx-react-lite'
import { useUnifiedApplication } from '../UnifiedApplicationContext'
import { useResponsive } from '../../../../utils/hooks'
import { PersonWithCheck } from '../../../../assets/icons'
import { Steps } from '@linqpal/models/src/applications/unified/UnifiedApplicationSteps'

export const OwnerInvitationSentBadge: FC = observer(() => {
  const { t } = useTranslation('application')
  const { sm } = useResponsive()

  const store = useUnifiedApplication()

  if (!store.draft.invitedOwner) return null

  const { email, firstName, lastName } = store.draft.invitedOwner

  const getResponsiveStyle = (style: any) => {
    return composeStyle(style, !sm && styles.mobile)
  }

  return (
    <View style={styles.wrapper}>
      <PersonWithCheck
        style={{ marginLeft: 3, marginRight: 20, marginTop: 3 }}
      />
      <View style={styles.textWrapper}>
        <Trans
          t={t}
          i18nKey="Review.InviteSent"
          values={{ name: `${firstName} ${lastName}` }}
          components={{
            styled: <BtText style={getResponsiveStyle(styles.boldText)} />,
            simple: <BtText style={getResponsiveStyle(styles.text)} />,
          }}
        />
        <View style={styles.view4}>
          <BtText style={getResponsiveStyle(styles.boldText)}>{email}</BtText>
          <BtLink
            title={t('Review.Edit')}
            textStyle={getResponsiveStyle(styles.linkText)}
            onPress={() =>
              store.goToStep(Steps.businessOwner.authorizedDetails)
            }
          />
        </View>
      </View>
    </View>
  )
})

const styles = StyleSheet.create({
  wrapper: {
    width: '100%',
    borderRadius: 12,
    backgroundColor: '#F5F7F8',
    padding: 16,
    marginBottom: 45,
    flexShrink: 1,
    flexDirection: 'row',
  },
  text: {
    fontWeight: '500',
    fontSize: 16,
    lineHeight: 22,
    width: '100%',
  },
  boldText: {
    fontWeight: '600',
    fontSize: 16,
    lineHeight: 22,
    color: '#003353',
  },
  textWrapper: {
    flexShrink: 1,
  },
  view4: {
    marginTop: 12,
    flexDirection: 'row',
  },
  linkText: {
    color: colors.accentText,
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 22,
    marginLeft: 15,
  },
  mobile: {
    fontSize: 14,
  },
})
