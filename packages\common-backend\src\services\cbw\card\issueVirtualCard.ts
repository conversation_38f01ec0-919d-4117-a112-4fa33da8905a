import { dictionaries, exceptions } from '@linqpal/models'
import { IVirtualCard } from '@linqpal/models/src/routes2/card/types'
import moment from 'moment'
import { v4 } from 'uuid'
import {
  Company,
  Invoice,
  LoanApplication,
  Notification,
  VirtualCard,
  VirtualCardModel,
} from '../../../models'
import { ICompany, IInvoice, ILoanApplication } from '../../../models/types'
import { initCbwApiRequester } from '../axios-instance'
import { getCBWProduct } from '../helper'
import {
  statesHashMap,
  VirtualCardStatus,
} from '@linqpal/models/src/dictionaries'
import { getOwningUser } from '../../company.service'
import mongoose from 'mongoose'
import { States } from '@linqpal/models/src/dictionaries/states'
import {
  isValidPhoneNumber,
  NationalNumber,
  parsePhoneNumber,
} from 'libphonenumber-js'

interface IData extends Pick<VirtualCardModel, 'company_id' | 'invoice_id'> {}

export interface AddCardResult {
  card: Pick<
    IVirtualCard,
    'cardId' | 'cardType' | 'cardStatus' | 'cardMaskNumber' | 'cardExpiryDate'
  >
}

interface CountryGroupResponse {
  countryGroup?: {
    id: string
  }
}

interface MccGroupRespnse {
  mccGroup?: {
    id: string
  }
}

async function getCountryGroupId(name: string): Promise<string | undefined> {
  const requester = initCbwApiRequester()
  const { countryGroup } = await requester.post<CountryGroupResponse>('', {
    id: v4().replace(/[^\d]/gi, ''),
    method: 'ledger.CARD.request',
    payload: {
      ...getCBWProduct(),
      reference: v4().replace(/[^\d]/gi, ''),
      transactionType: 'GET_COUNTRYGROUP',
      customerId: process.env.LP_CBW_ACH_CUSTOMER_ID,
      channel: 'PULSE',
      countryGroup: {
        groupName: name,
      },
    },
  })
  return countryGroup?.id
}

async function getMccGroupId(name: string): Promise<string | undefined> {
  const requester = initCbwApiRequester()
  const { mccGroup } = await requester.post<MccGroupRespnse>('', {
    id: v4().replace(/[^\d]/gi, ''),
    method: 'ledger.CARD.request',
    payload: {
      ...getCBWProduct(),
      reference: v4().replace(/[^\d]/gi, ''),
      transactionType: 'GET_MCCGROUP',
      customerId: process.env.LP_CBW_ACH_CUSTOMER_ID,
      channel: 'PULSE',
      mccGroup: {
        groupName: name,
      },
    },
  })
  return mccGroup?.id
}

function sanitizeAlphaNum(str: string | undefined | null) {
  return str
    ?.replace(/[^\w\s]/gi, '')
    .replace(' ', '')
    .substring(0, 40)
    .trim()
}

export async function issueVirtualCard(
  data: IData,
  loanApp?: ILoanApplication | null,
  session: mongoose.ClientSession | null = null,
): Promise<VirtualCardModel> {
  const invoice = await Invoice.findById<IInvoice>(data.invoice_id).session(
    session,
  )

  if (!invoice) throw new exceptions.LogicalError('invoice/not-found')

  if (
    invoice.status !== dictionaries.invoiceSchemaStatus.draft &&
    invoice.status !== dictionaries.invoiceSchemaStatus.placed
  ) {
    throw new exceptions.LogicalError('Invalid invoice')
  }
  invoice.status = dictionaries.invoiceSchemaStatus.placed
  await invoice.save()

  if (!loanApp) {
    loanApp = await LoanApplication.findOne<ILoanApplication>({
      'invoiceDetails.invoiceId': invoice.id,
      company_id: data.company_id,
    }).session(session)
  }
  if (!loanApp) throw new exceptions.LogicalError('Loan application not found')
  if (loanApp.status !== dictionaries.LOAN_APPLICATION_STATUS.APPROVED)
    throw new exceptions.LogicalError('Loan application not approved')

  const company: ICompany | null = await Company.findById(
    loanApp.company_id,
  ).session(session)
  if (!company) throw new exceptions.LogicalError('Company not found')
  if (!loanApp.draft) throw new exceptions.LogicalError('Draft not found')
  const user = await getOwningUser(company.id)

  const businessName = loanApp.draft?.businessInfo_businessName?.legalName
  let businessTelephoneNumber: string | NationalNumber =
    loanApp.draft.businessInfo_businessPhone || user?.phone || ''
  const address = loanApp.draft.businessInfo_businessAddress

  if (isValidPhoneNumber(businessTelephoneNumber as string, 'US')) {
    const parsed = parsePhoneNumber(businessTelephoneNumber as string, 'US')
    businessTelephoneNumber = parsed.nationalNumber
  }

  const [virtualCard] = await VirtualCard.create(
    [
      {
        ...data,
        name: businessName || 'Noname',
        amount: loanApp.approvedAmount,
        status: VirtualCardStatus.ACTIVE,
      },
    ],
    { session },
  )

  const requester = initCbwApiRequester()

  if (!company.cbw?.cardHolderId) {
    const holder = await requester.post('', {
      id: v4().replace(/[^\d]/gi, ''),
      method: 'ledger.CARD.request',
      payload: {
        ...getCBWProduct(),
        reference: new Date().getTime().toString(),
        transactionType: 'ADD_CARD_HOLDER_WITH_NON_PRIMARY_ADDRESS',
        customerId: process.env.LP_CBW_ACH_CUSTOMER_ID,
        accountNumber: process.env.LP_CBW_GL2_IDENTIFICATION,
        channel: 'PULSE',
        cardHolder: {
          firstName: sanitizeAlphaNum(businessName || user?.firstName),
          lastName: '',
          phoneNumber: businessTelephoneNumber,
          emailId: company.email || user?.email,
          addressLine1: address?.address,
          addressLine2: '',
          city: sanitizeAlphaNum(address?.city),
          state: address?.state
            ? statesHashMap[address.state as States]
            : undefined,
          zipCode: address?.zip,
        },
      },
    })
    if (!company.cbw) {
      company.cbw = {}
    }
    company.cbw.cardHolderId = holder.cardHolderId
    company.markModified('cbw')
    await company.save()
  }

  const expire = moment().add(6, 'month')

  const resp: AddCardResult = await requester.post('', {
    id: v4().replace(/[^\d]/gi, ''),
    method: 'ledger.CARD.request',
    payload: {
      ...getCBWProduct(),
      reference: `${process.env.LP_MODE}_${virtualCard._id}`,
      transactionType: 'ADD_CARD',
      customerId: process.env.LP_CBW_ACH_CUSTOMER_ID,
      accountNumber: process.env.LP_CBW_GL2_IDENTIFICATION,
      channel: 'PULSE',
      card: {
        cardType: 'VIRTUAL',
        expiryYear: expire.format('YYYY'),
        expiryMonth: expire.format('MM'),
        singleUseCard: 'NO',
        cardName: company.name,
        cardHolderId: company.cbw.cardHolderId,
        countryGroupId: await getCountryGroupId('USBASED'),
        mccGroupId: await getMccGroupId('SUPPLIERS'),
      },
    },
  })
  virtualCard.cardId = resp.card.cardId
  await virtualCard.save()

  await requester.post('', {
    method: 'ledger.CARD.request',
    id: v4().replace(/[^\d]/gi, ''),
    payload: {
      ...getCBWProduct(),
      reference: new Date().getTime().toString(),
      transactionType: 'SET_LIMIT',
      customerId: process.env.LP_CBW_ACH_CUSTOMER_ID,
      accountNumber: process.env.LP_CBW_GL2_IDENTIFICATION,
      channel: 'PULSE',
      cardLimit: {
        cardId: virtualCard.cardId,
        cycleType: 'LIFE_TIME',
        type: 'VOLUME',
        value: Math.ceil(virtualCard.amount).toFixed(0),
      },
    },
  })

  await Notification.create(
    {
      receiver: { company_id: loanApp.company_id },
      type: dictionaries.notificationTypes.virtualcard,
      content: dictionaries.notificationText.cardIssued,
      metadata: {
        alertType: 'success',
        invoice_id: invoice._id,
        amount: loanApp.approvedAmount,
        companyName: invoice.supplierInvitationDetails?.name,
      },
      isRead: false,
      isViewed: false,
    },
    { session },
  )
  return virtualCard
}
