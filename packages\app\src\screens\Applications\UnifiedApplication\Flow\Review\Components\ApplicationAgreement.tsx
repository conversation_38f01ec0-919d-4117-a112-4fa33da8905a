import React, { FC, useEffect, useRef, useState } from 'react'
import { useUnifiedApplication } from '../../../UnifiedApplicationContext'
import { SubmitApplicationButton } from './Review/SubmitApplicationButton'
import ApplicationStore from '../../../../../GeneralApplication/Application/ApplicationStore'
import {
  fetchAgreement,
  resetAgreementPromise,
} from '../../../../../GeneralApplication/Application/flow/review/requests/fetchAgreement'
import Loading from '../../../../../Loading'
import { View } from 'react-native'
import PdfViewer from '../../../../../../ui/organisms/PdfViewer'
import { VerifyEmailAlert } from '../../../../../GeneralApplication/Application/components'

export const ApplicationAgreement: FC = () => {
  const [fileUrl, setFileUrl] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)

  const { showVerifyEmail, setShowVerifyEmail } = ApplicationStore

  const store = useUnifiedApplication()

  const hasFetchedAgreement = useRef(false)

  useEffect(() => {
    if (hasFetchedAgreement.current) return

    const loadAgreement = async () => {
      setLoading(true)
      const url = await fetchAgreement()
      setFileUrl(url)
      setLoading(false)
      hasFetchedAgreement.current = true
    }
    loadAgreement()

    return () => {
      hasFetchedAgreement.current = false
      resetAgreementPromise()
    }
  }, [store.type])

  if (!(store.isSupplierApp || store.isCreditApp)) return null
  if (loading) return <Loading />

  return fileUrl ? (
    <>
      <View style={{ alignItems: 'center' }}>
        <View style={{ marginTop: 20, marginBottom: 44 }}>
          <PdfViewer
            pdfUrl={fileUrl}
            onReachedAgreementEnd={() =>
              (store.reviewStore.isAgreementRead = true)
            }
          />
        </View>
        <SubmitApplicationButton />
      </View>

      <VerifyEmailAlert close={showVerifyEmail} setClose={setShowVerifyEmail} />
    </>
  ) : (
    // eslint-disable-next-line i18next/no-literal-string
    <View>{'Error loading document.'}</View>
  )
}
