import {
  AgreementService,
  Authentication,
  AwsService,
  BankAccount,
  bluCognition,
  Company,
  createPagination,
  CustomerAccount,
  Draft,
  FinicityTransactions,
  giact,
  initializeFinicity,
  initializeRefund,
  Invoice,
  LedgerService,
  LMS,
  LoanApplication,
  LoanService,
  Operation,
  RemoteConfig,
  saveLoanPricingPackage as _saveLoanPricingPackage,
  User,
  UserRole,
} from '@linqpal/common-backend'
import admin from 'firebase-admin'
import moment from 'moment'
import controllers from '../../../controllers'
import {
  downloadBluCognitionAttachmentUrl,
  downloadFile,
} from '../../../controllers/file.controller'
import { adminRequired } from '../../../services/auth.service'
import transactional from '../../../services/transactional.service'
import mongoose from 'mongoose'
import { CompanyStatus, dictionaries, IDraftModel } from '@linqpal/models'
import { Request, Response } from 'express'
import { ControllerItem } from 'src/routes/controllerItem'
import { escapeRegExp } from 'lodash'
import { onBoardingService } from '@linqpal/common-backend/src/services/onBoarding/onBoarding.service'
import { ICreditApplicationFilter } from '@linqpal/models/src/interfaces/ICreditApplicationFilter'
import { CreditApplicationType } from '@linqpal/models/src/dictionaries/creditApplicationType'

export * from './bank'
export * from './contractor'
export * from './payments'
export * from './supplier'
export * from './loan'
export * from './finicity'
export * from './giact'
export * from './verifyUserEmail'
export * from './institutions'
export * from './inHouseCreditInvoicesList'
export * from './inHouseCreditInvoiceDetails'

export const middlewares = { pre: [adminRequired] }

export const signups: ControllerItem = {
  get: async function signups(req: Request, res: Response) {
    let search = (req.query?.search as string) || ''
    const page = parseInt(req.query?.page as string) || 1
    const limit = parseInt(req.query?.limit as string) || 10

    search = search.trim()

    // Build the main pipeline
    let pipeline = User.aggregate()
      .project({
        sub: 1,
        firebaseId: 1,
        createdAt: 1,
        login: 1,
        email: 1,
        phone: 1,
        firstName: 1,
        lastName: 1,
        isGuest: 1,
        guestInfo: 1,
      })
      .lookup({
        from: UserRole.collection.name,
        localField: 'sub',
        foreignField: 'sub',
        as: 'roles',
      })
      .addFields({
        admin: {
          $anyElementTrue: [
            {
              $filter: {
                input: '$roles',
                as: 'role',
                cond: {
                  $and: [
                    { $eq: ['$$role.role', 'Admin'] },
                    { $lte: ['$$role.company_id', ''] },
                  ],
                },
              },
            },
          ],
        },
      })
      .append({ $unwind: '$roles' })
      .lookup({
        from: Company.collection.name,
        let: { roles_company_id: '$roles.company_id' },
        pipeline: [
          {
            $match: {
              $expr: { $eq: ['$_id', { $toObjectId: '$$roles_company_id' }] },
            },
          },
        ],
        as: 'company',
      })
      .append({ $unwind: '$company' })

    if (search) {
      const searchRegex = RegExp(escapeRegExp(search))
      pipeline = pipeline.match({
        $or: [
          { login: { $regex: searchRegex, $options: 'i' } },
          { email: { $regex: searchRegex, $options: 'i' } },
          { phone: { $regex: searchRegex, $options: 'i' } },
          {
            firstName: { $regex: searchRegex, $options: 'i' },
          },
          { lastName: { $regex: searchRegex, $options: 'i' } },
          {
            'company.name': {
              $regex: searchRegex,
              $options: 'i',
            },
          },
          {
            'company.legalName': {
              $regex: searchRegex,
              $options: 'i',
            },
          },
        ],
      })
    }

    // Get total count after applying filters
    const _total = await User.aggregate(pipeline.pipeline()).count('total')
    const total = _total[0]?.total || 0

    // Continue with the main pipeline for fetching items
    let agg = pipeline.sort({ createdAt: -1 })

    // Apply pagination
    if (page > 0 && limit > 0) {
      agg = agg.skip((page - 1) * limit)
    }

    if (limit > 0) {
      agg = agg.limit(limit)
    }

    // Add remaining lookups
    agg = agg
      .lookup({
        from: Draft.collection.name,
        localField: 'roles.company_id',
        foreignField: 'company_id',
        let: { company_status: '$company.status' },
        pipeline: [
          {
            $match: {
              $expr: {
                $or: [
                  { $eq: ['$type', 'supplier_application'] },
                  {
                    $and: [
                      { $eq: ['$type', 'general_application'] },
                      { $eq: ['$$company_status', CompanyStatus.Approved] },
                    ],
                  },
                ],
              },
            },
          },
        ],
        as: 'supplierApplications',
      })
      .lookup({
        from: LoanApplication.collection.name,
        as: 'loanApplications',
        localField: 'roles.company_id',
        foreignField: 'company_id',
      })

    const items = await Promise.all(
      (
        await agg.exec()
      ).map(async (user) => {
        user.supplierApplications.map((app: IDraftModel) =>
          controllers.user.secureDocumentFields(app),
        )
        let emailVerified = false
        try {
          emailVerified =
            user.email && !user.isGuest
              ? (await admin.auth().getUser(user.firebaseId)).emailVerified
              : false
        } catch {}
        return { ...user, emailVerified }
      }),
    )
    res.send({ items, total })
  },
}

export const users: ControllerItem = {
  get: async function users(req, res) {
    let search = (req.query?.search as string) || ''
    const page = parseInt(req.query?.page as string) || 1
    const limit = parseInt(req.query?.limit as string) || 10

    let agg = User.aggregate()

    search = search.trim()
    if (search) {
      agg = agg.match({
        $or: [
          { login: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } },
          { phone: { $regex: search, $options: 'i' } },
          { firstName: { $regex: search, $options: 'i' } },
          { lastName: { $regex: search, $options: 'i' } },
        ],
      })
    }

    agg = agg.project({
      sub: 1,
      createdAt: 1,
      login: 1,
      email: 1,
      phone: 1,
      firstName: 1,
      lastName: 1,
    })

    const _total = await User.aggregate(agg.pipeline()).count('total')
    const total = _total[0]?.total || 0

    agg = agg.sort({ createdAt: -1 })

    if (page > 0 && limit > 0) {
      agg = agg.skip((page - 1) * limit)
    }

    if (limit > 0) {
      agg = agg.limit(limit)
    }

    agg = agg.lookup({
      from: UserRole.collection.name,
      localField: 'sub',
      foreignField: 'sub',
      as: 'companies',
      pipeline: [
        { $addFields: { company_id: { $toObjectId: '$company_id' } } },
        {
          $lookup: {
            from: 'companies',
            localField: 'company_id',
            foreignField: '_id',
            as: 'company',
          },
        },
        { $unwind: '$company' },
        { $replaceRoot: { newRoot: '$company' } },
      ],
    })

    const items = await agg.exec()
    res.send({ items, total })
  },
}

export const employees: ControllerItem = {
  get: async function employees(req, res) {
    const company_id = (req.query?.companyId as string) || ''

    let agg = UserRole.aggregate()

    agg
      .match({ company_id })
      .lookup({
        from: User.collection.name,
        as: 'user',
        localField: 'sub',
        foreignField: 'sub',
      })
      .unwind('$user')
      .sort({ 'user.login': 1 })

    const userRoles = await agg.exec()
    const userIds = userRoles.map(({ user }) => user._id)

    agg = User.aggregate()
    agg.match({ _id: { $in: userIds } }).lookup({
      from: 'userroles',
      localField: 'sub',
      foreignField: 'sub',
      as: 'companies',
      pipeline: [
        { $addFields: { company_id: { $toObjectId: '$company_id' } } },
        {
          $lookup: {
            from: 'companies',
            localField: 'company_id',
            foreignField: '_id',
            as: 'company',
          },
        },
        { $unwind: '$company' },
        { $replaceRoot: { newRoot: '$company' } },
      ],
    })

    const _total = await UserRole.aggregate(agg.pipeline()).count('total')
    const total = _total[0]?.total || 0

    const items = await agg.exec()

    res.send({ items, total })
  },
}

export const userCompanyChange: ControllerItem = {
  post: async function userCompanyChange(req, res) {
    const { sub, company_id } = req.body

    const userRole = await UserRole.findOne({ sub })
    if (userRole) {
      await UserRole.findOneAndUpdate({ sub }, { company_id })
    } else {
      await UserRole.create({ sub, company_id, role: 'User', status: 'Active' })
    }

    res.send({ result: 'ok' })
  },
}

export const deleteUser: ControllerItem = {
  middlewares: {
    pre: [adminRequired, ...transactional.pre],
    post: transactional.post,
  },
  post: async function deleteUser(req, res, next) {
    const { sub } = req.body
    // 1. Check transactions
    // 2. Delete drafts
    // 3. Delete bank accounts
    // 4. Delete finicity transactions
    // 5. Delete customer accounts
    // 5. Delete loan application which were not approved
    // 6. If no transactions and no approved loans delete company else mark company as disapproved
    // 7. Delete user roles
    // 8. Delete user
    const user = await User.findOne({ sub }).session(req.session)
    if (!user) return next()
    console.log('Deleting', user.login)
    const roles = await UserRole.find({
      sub: user.sub,
      company_id: mongoose.trusted({ $ne: null }),
    }).session(req.session)
    for (const r of roles) {
      const company = await Company.findById(r.company_id).session(req.session)
      let o = await Draft.deleteMany({ company_id: r.company_id }).session(
        req.session,
      )
      console.log('Delete drafts', o)
      o = await FinicityTransactions.deleteMany({
        company_id: r.company_id,
      }).session(req.session)
      console.log('Delete finicity transactions', o)
      if (company?.bankAccounts) {
        for (const b of company.bankAccounts) {
          await BankAccount.findByIdAndUpdate(b._id, {
            $set: { finicity: { accountId: null, syncState: null } },
          }).session(req.session)
        }
      }
      const invoices = await Invoice.find({ company_id: r.company_id }).session(
        req.session,
      )
      console.log('Invoices', invoices.length)
      const ops = await Operation.find({
        owner_id: mongoose.trusted({ $in: invoices.map((i) => i._id) }),
        status: mongoose.trusted({
          $nin: [
            dictionaries.OPERATION_STATUS.PLACED,
            dictionaries.OPERATION_STATUS.DECLINED,
          ],
        }),
      })
      console.log('Invoice operations', ops.length)

      let companyMarkedForDeletion = true
      let exceptCustomers: any[] = []
      if (ops.length > 0) {
        companyMarkedForDeletion = false
        exceptCustomers = ops
          .map(
            (x) =>
              invoices.find((i) => i.id === x.owner_id)?.customer_account_id,
          )
          .filter(Boolean)
      }
      o = await CustomerAccount.deleteMany({
        company_id: r.company_id,
        _id: mongoose.trusted({ $nin: exceptCustomers }),
      }).session(req.session)
      console.log('Delete customer accounts with exception', exceptCustomers, o)

      const loans = await LoanApplication.find({
        company_id: r.company_id,
      }).session(req.session)
      console.log('Loans', loans.length)
      if (loans.length > 0) {
        const lops = await Operation.find({
          owner_id: mongoose.trusted({ $in: loans.map((l) => l.id) }),
        })
        console.log('Loan operations', lops.length)
        if (lops.length > 0) {
          companyMarkedForDeletion = false
        }
      }

      if (!companyMarkedForDeletion) {
        console.log('Update company', company?.name)
      } else {
        console.log('Delete company', company?.name)
        await Company.deleteOne({ _id: r.company_id }).session(req.session)
      }
    }
    await UserRole.deleteMany({ sub: user.sub }).session(req.session)
    console.log('Delete user role')
    await user.deleteOne()
    console.log('Delete user')
    if (user.firebaseId) {
      try {
        await admin.auth().deleteUser(user.firebaseId)
        console.log('Delete user from firebase')
      } catch {}
    }

    next()
  },
}

export const toggleAdmin: ControllerItem = {
  post: async function toggleAdmin(req, res) {
    const { sub } = req.body
    const user = await User.findOne({ sub })
    if (!user) {
      res.send({ admin: false })
    }
    const roles = await UserRole.find({
      sub,
      role: 'Admin',
      company_id: null,
    })
    if (roles.length > 0) {
      for (const role of roles) {
        await role.deleteOne()
      }
    } else {
      await UserRole.create({ sub, role: 'Admin' })
    }
    res.send({ admin: roles.length === 0 })
  },
}

export const saveUserRoles: ControllerItem = {
  put: async function saveUserRoles(req, res) {
    const { firebaseId, roles } = req.body
    const idToken = getIdToken(req)

    Authentication.init()

    for (const role of roles) {
      if (role.isToggled) {
        await Authentication.grantRole(
          firebaseId,
          role.roleKey,
          'bluetape',
          idToken,
        )
      } else {
        await Authentication.revokeRole(
          firebaseId,
          role.roleKey,
          'bluetape',
          idToken,
        )
      }
    }

    res.send({ status: 'ok' })
  },
}

export const getUserRoles: ControllerItem = {
  get: async function getUserRoles(req, res) {
    const firebaseId = req.query?.firebaseId as string

    const validRoles = ['admin', 'viewer']
    const claims: any[] = []
    await admin
      .auth()
      .getUser(firebaseId)
      .then((user) => {
        if (user) {
          const roles = user?.customClaims?.roles

          validRoles.forEach((validRole) => {
            if (
              roles &&
              roles.hasOwnProperty(validRole) &&
              roles[validRole].indexOf('bluetape') !== -1
            ) {
              claims.push({
                roleKey: validRole,
                isToggled: true,
              })
            } else {
              claims.push({
                roleKey: validRole,
                isToggled: false,
              })
            }
          })

          res.send(claims)
        }
      })
  },
}

export const finicityAccounts: ControllerItem = {
  get: async (req, res) => {
    const { search = 'build' } = req.query
    const { paginationPipeline, pageSize } = createPagination(req.query)

    const pipeline: mongoose.PipelineStage[] = [
      {
        $match: {
          isManualEntry: {
            $ne: true,
          },
        },
      },
      {
        $lookup: {
          from: Company.collection.name,
          as: 'company',
          localField: '_id',
          foreignField: 'bankAccounts',
        },
      },
      {
        $lookup: {
          from: CustomerAccount.collection.name,
          as: 'customerAccount',
          localField: '_id',
          foreignField: 'bankAccounts',
        },
      },
      {
        $unwind: {
          path: '$company',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: '$customerAccount',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $addFields: {
          company_name: {
            $ifNull: [
              '$company.name',
              {
                $concat: [
                  'Customer Account: ',
                  { $ifNull: ['$customerAccount.name', ''] },
                  ' ',
                  { $ifNull: ['$customerAccount.first_name', ''] },
                  ' ',
                  { $ifNull: ['$customerAccount.last_name', ''] },
                ],
              },
            ],
          },
        },
      },
      {
        $project: {
          name: 1,
          accountName: 1,
          accountholderName: 1,
          routingNumber: 1,
          accountNumber: '$accountNumber.display',
          isManualEntry: 1,
          isPrimary: 1,
          accountType: 1,
          status: 1,
          company_id: '$company._id',
          company_name: 1,
          finicity: { $mergeObjects: ['$company.finicity', '$finicity'] },
          plaid: 1,
        },
      },
      {
        $sort: {
          createdAt: -1,
        },
      },
    ]

    if (search) {
      const searchMatchConditions = [
        {
          company_name: {
            $regex: search,
            $options: 'i',
          },
        },
        {
          accountholderName: {
            $regex: search,
            $options: 'i',
          },
        },
        {
          name: {
            $regex: search,
            $options: 'i',
          },
        },
      ]
      pipeline.push({
        $match: {
          $or: searchMatchConditions,
        },
      })
    }

    const [result] = await BankAccount.aggregate([
      ...pipeline,
      {
        $facet: {
          total: [{ $count: 'count' }],
          items: [...paginationPipeline],
        },
      },
    ])

    res.send({
      items: result.items,
      total: pageSize > 0 ? result.total[0]?.count || 0 : result.items.length,
    })
  },
}

export const importTransactions: ControllerItem = {
  get: async (req, res) => {
    console.log(req.query.companyId)
    const company = await Company.findById(req.query.companyId).populate(
      'bankAccounts',
    )
    if (company) {
      console.log(company.finicity)
      if (company.finicity) {
        const lastImport = company.finicity.lastImport || 0
        const last6Months = moment().subtract(6, 'months').unix()
        console.log(
          `finicity: lastImport ${lastImport} < last6Months ${last6Months}`,
        )
        const resp = await AwsService.sendSQSMessage(
          `finicity-transactions-aggregation`,
          JSON.stringify({ company_id: company._id.toString() }),
          'LP_FINICITY_TRANSACTIONS_HISTORY',
        )
        console.log(resp)
      } else if (
        company.bankAccounts?.some((account) => account.plaid?.account_id)
      ) {
        const resp = await AwsService.sendSQSMessage(
          `plaid-transactions-aggregation`,
          JSON.stringify({ company_id: company._id.toString() }),
          'LP_FINICITY_TRANSACTIONS_HISTORY',
        )
        console.log(resp)
      }
    }
    res.send({ result: 'ok' })
  },
}

export const deleteFinicityAccount: ControllerItem = {
  middlewares: {
    pre: [adminRequired, ...transactional.pre],
    post: transactional.post,
  },
  get: async (req, res, next) => {
    const { accountId, companyId } = req.query
    const accountRecord = await BankAccount.findById(accountId).session(
      req.session,
    )
    const company = await Company.findById(companyId).session(req.session)
    if (accountRecord && company) {
      if (accountRecord.finicity?.accountId) {
        const finicity = await initializeFinicity()
        if (company.finicity?.customerId) {
          const customerId = company.finicity.customerId
          try {
            await finicity.accounts.deleteAccount({
              customerId,
              accountId: accountRecord.finicity.accountId,
            })
          } catch (e) {
            console.log(e)
          }
        }
      }
      await BankAccount.deleteOne({ _id: accountId }).session(req.session)
      company.bankAccounts = company.bankAccounts?.filter(
        (acc) => acc._id.toString() !== accountId,
      )
      await company.save()
    }
    return next()
  },
}

export const supplierApplicationKYC: ControllerItem = {
  get: async (req, res) => {
    const { companyId } = req.query
    const company = await Company.findById(companyId)
    if (company) {
      const data = company.onBoarding || company.cbw
      return res.send({ cbw: { ...data } })
    }
    return res.send({})
  },
}

export const remoteConfig: ControllerItem = {
  async get(_req, res) {
    res.send({ config: await RemoteConfig.getConfig() })
  },
  async post(req, res) {
    const { key, value } = req.body
    await RemoteConfig.setConfig(key, value)
    return res.send({ config: await RemoteConfig.getConfig() })
  },
}

export const addKycMemo: ControllerItem = {
  async post(req, res) {
    const { companyId, comment } = req.body
    const userId = req.user!._id.toString()
    const kycMemo = await controllers.admin.addKycMemo(
      userId,
      companyId,
      comment,
    )
    res.send({ result: 'ok', id: kycMemo._id })
  },
}

export const listKycMemo: ControllerItem = {
  async get(req, res) {
    const { companyId } = req.query
    const memos = await controllers.admin.listKycMemo(companyId as string)
    res.send({ result: 'ok', memos })
  },
}

export const settings: ControllerItem = {
  post: async (req, res) => {
    const { companyId } = req.body
    const company = await Company.findOne({
      _id: companyId ? companyId : req.company!._id,
    })
    if (company) {
      Object.keys(req.body).forEach((k) => {
        // TODO: check req.body properties and add only those which is in company.settings
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        company.settings[k] = req.body[k]
      })
      await company.save()
    }
    res.send({ result: 'ok' })
  },
}

export const saveLoanPricingPackage: ControllerItem = {
  post: async (req, res, next) => {
    const { company: _company, ...rest } = req.body
    const loanPricingPackage = await _saveLoanPricingPackage(rest)

    if (!_company) return next()
    const company = await Company.findById(_company.id)
    if (!company) return next()

    const loanPricingPackageId = loanPricingPackage.name

    company.settings.loanPricingPackageId = loanPricingPackageId
    await company.save()

    res.send({ result: 'ok' })
  },
}

export const fileDownload: ControllerItem = {
  async get(req, res) {
    const { key } = req.query
    await downloadFile(key as string, req, res)
  },
}

export const getBlucognitionAttachmentUrl: ControllerItem = {
  async get(req, res) {
    const { key } = req.query
    await downloadBluCognitionAttachmentUrl(key as string, req, res)
  },
}

export const giactGauthenticate: ControllerItem = {
  async post(req, res) {
    const body = req.body
    const result = await giact.gAuthenticate(body)
    res.send({ result })
  },
}

export const triggerDataSendToBlucognition: ControllerItem = {
  async post(req, res) {
    const { loanApplicationId } = req.body
    const apiResult = await bluCognition.sendData(loanApplicationId)

    res.send({ result: apiResult ? 'ok' : 'fail' })
  },
}

export const triggerDataSendToBlucognitionForSupplier: ControllerItem = {
  async post(req, res) {
    const { applicationId } = req.body
    const apiResult = await bluCognition.sendDataForSupplier(applicationId)

    res.send({ result: apiResult ? 'ok' : 'fail' })
  },
}

export const saveCompanyCreditSettings: ControllerItem = {
  async post(req, res) {
    const { company_id, limit, purchaseType } = req.body
    const comp = await Company.findById(company_id)
    const credit: { limit?: number; purchaseType?: string } = comp?.credit ?? {}
    if (limit) {
      credit.limit = parseFloat(limit)
    }
    if (purchaseType) {
      credit.purchaseType = purchaseType
    }
    await Company.findByIdAndUpdate(company_id, {
      credit,
    })
    await AgreementService.regenerateMasterAgreement(company_id)
    res.send({ result: 'ok' })
  },
}

const getIdToken = (req: Request) => {
  const auth = req.header('authorization')
  if (!auth || !auth.startsWith('Bearer ')) {
    return ''
  }

  return auth.split(' ')[1]
}
export const updateLoanReceivableDate: ControllerItem = {
  async post(req, res) {
    const { receivableId, date, note, userId } = req.body
    await LMS.updateReceivableExpectedDate(receivableId, date, note, userId)
    res.send({ result: 'ok' })
  },
}

export const addPaymentToHistory: ControllerItem = {
  async post(req, res) {
    const { loanId, amount, note, date, userId } = req.body
    const result = await LMS.createManualPayment(
      loanId,
      amount,
      date,
      note,
      userId,
    )
    await LedgerService.handleLoanRepayment(result, loanId, null, true)
    res.send({ result: 'ok' })
  },
}

export const addLoanRefund: ControllerItem = {
  async post(req, res) {
    const { loanId, amount, note, date, userId } = req.body
    await initializeRefund(loanId, amount, note, date, userId)
    res.send({ result: 'ok' })
  },
}

export const addFee: ControllerItem = {
  async post(req, res) {
    const { loanId, type, amount, note, date, userId } = req.body
    await LMS.createFee(loanId, type, amount, note, userId, date)
    res.send({ result: 'ok' })
  },
}

export const changeLoanStatus: ControllerItem = {
  async post(req, res) {
    const { loanId, status, note, userId } = req.body
    await Promise.all([
      LMS.changeLoanStatusAdmin(loanId, status, note, userId),
      LoanService.changeLoanApplicationLmsStatus(loanId, status),
    ])

    res.send({ result: 'ok' })
  },
}

export const rescheduleLoanReceivables: ControllerItem = {
  async post(req, res) {
    const { loanId, receivables, note, userId } = req.body
    await LMS.rescheduleLoanReceivables(loanId, receivables, userId, note)
    res.send({ result: 'ok' })
  },
}

export const cancelPaymentFromHistory: ControllerItem = {
  async put(req, res) {
    const { paymentId, note, userId } = req.body
    await LMS.cancelPayment(paymentId, note, userId)
    res.send({ result: 'ok' })
  },
}

export const cancelFee: ControllerItem = {
  async patch(req, res) {
    const { loanReceivableId, type, note, userId } = req.body
    await LMS.cancelFee(loanReceivableId, type, note, userId)
    res.send({ result: 'ok' })
  },
}

export const updateFeeExpectedAmount: ControllerItem = {
  async patch(req, res) {
    const { loanReceivableId, type, expectedAmount, note, userId } = req.body
    await LMS.changeFeeExpectedAmount(
      loanReceivableId,
      type,
      expectedAmount,
      note,
      userId,
    )
    res.send({ result: 'ok' })
  },
}

export const getCompany = async (req: Request, res: Response) => {
  const { id } = req.query

  const company = await Company.findById(id)
  if (!company) throw new Error(`unable to find company ${id}`)

  res.send(company)
}

export const getCompanySettings = async (req: Request, res: Response) => {
  const { company_id } = req.query
  const company = await Company.findById(company_id)
  res.send({ settings: company?.settings, credit: company?.credit })
}

export const saveCompanySettings: ControllerItem = {
  post: async (req, res) => {
    const { company_id, settings: newSettings } = req.body

    if (!company_id) throw new Error(`company_id is required`)
    if (!newSettings) throw new Error(`settings are required`)

    const company = await Company.findById(company_id)
    if (!company) throw new Error(`unable to find company ${company_id}`)

    // more supplier settings to sync on customer will come
    const customerSettings: any = {}

    if (
      company.settings.sendFinalPaymentWhenLoanIsPaid !==
      newSettings.sendFinalPaymentWhenLoanIsPaid
    ) {
      customerSettings['settings.sendFinalPaymentWhenLoanIsPaid'] =
        newSettings.sendFinalPaymentWhenLoanIsPaid
    }

    if (Object.keys(customerSettings).length > 0) {
      await CustomerAccount.updateMany(
        { company_id },
        customerSettings,
      ).session(req.session)
    }

    // if auto-approval is disabled for supplier disable it for customers
    // but do not enable for all customers - it should be done manually

    // do not set isEnabled = false for customers without autoApproval settings,
    // otherwise customer settings won't be defaulted to supplier settings after enable
    if (
      newSettings.automatedDrawApproval?.isEnabled === false &&
      company.settings.automatedDrawApproval?.isEnabled === true
    ) {
      await CustomerAccount.updateMany(
        { company_id, 'settings.automatedDrawApproval.isEnabled': true },
        {
          'settings.automatedDrawApproval.isEnabled': false,
        },
      ).session(req.session)
    }

    await company
      .set('settings', { ...company.settings, ...newSettings })
      .save({ session: req.session })

    res.send(company?.settings || {})
  },
}

export const getCreditApplications: ControllerItem = {
  get: async (req, res) => {
    const filter = req.query as ICreditApplicationFilter

    const applications = await onBoardingService.getCreditApplications(filter)

    res.send(applications)
  },
}

export const getSupplierCustomerApplications: ControllerItem = {
  get: async (req, res) => {
    const companyId = req.query.companyId?.toString()
    const merchantId = req.query.merchantId?.toString()

    if (!companyId) throw new Error(`companyId is required`)
    if (!merchantId) throw new Error(`merchantId is required`)

    const inHouseCreditApplications =
      await onBoardingService.getCreditApplications({
        companyId,
        merchantId,
        type: CreditApplicationType.InHouseCredit,
      })

    const lineOfCreditApplications =
      await onBoardingService.getCreditApplications({
        companyId,
        type: CreditApplicationType.LineOfCredit,
      })

    res.send({
      lineOfCreditApplication: lineOfCreditApplications[0] ?? null,
      inHouseCreditApplication: inHouseCreditApplications[0] ?? null,
    })
  },
}

export const runAsInHouseCredit: ControllerItem = {
  post: async (req, res) => {
    const draftId = req.body.draftId?.toString()
    const merchantId = req.body.merchantId?.toString()

    if (!draftId) throw new Error(`draftId is required`)
    if (!merchantId) throw new Error(`merchantId is required`)

    await onBoardingService.executeCreditApplicationDE({
      draftId,
      merchantId,
      type: CreditApplicationType.InHouseCredit,
    })

    await new Promise((resolve) => setTimeout(resolve, 5000))

    res.send({ result: 'ok' })
  },
}
