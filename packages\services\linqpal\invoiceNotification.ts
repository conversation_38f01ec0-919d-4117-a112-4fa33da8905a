import {
  Company,
  connectToDatabase,
  CustomerAccount,
  Draft,
  emailService,
  getEnvironmentVariables,
  Invoice,
  InvoiceNotification,
  LoanApplication,
  Sms,
} from '@linqpal/common-backend'
import moment from 'moment-timezone'
import { NOTIFICATION_CONSTANTS as CONSTANTS } from './const'
import { parsePhoneNumber } from 'libphonenumber-js'
import {
  CompanyStatus,
  dictionaries,
  EInvoiceNotificationType,
  InvoicePaymentType,
} from '@linqpal/models'
import { PipelineStage } from 'mongoose'
import { ICompany, IInvoice } from '@linqpal/common-backend/src/models/types'
import SmsBuilder from '@linqpal/common-backend/src/helpers/SmsBuilder'
import EmailBuilder, {
  EmailMessageData,
} from '@linqpal/common-backend/src/helpers/EmailBuilder'
import {
  LOAN_APPLICATION_STATUS,
  OPERATION_TYPES,
} from '@linqpal/models/src/dictionaries'

const HOST =
  CONSTANTS.HOSTS[
    (process?.env?.LP_MODE || 'dev') as keyof typeof CONSTANTS.HOSTS
  ]

function getPastDueText(
  company: ICompany,
  invoice: Partial<IInvoice>,
  days: number,
  hasExpireDate = false,
  isSms = true,
): string | EmailMessageData {
  if (hasExpireDate) {
    if (days === 1) {
      return isSms
        ? SmsBuilder.getMessage({
            key: 'invoiceExpiredOneDayNotification',
            data: { name: company?.name },
          })
        : EmailBuilder.getSubjectAndBody({
            key: 'invoiceExpiredOneDayNotification',
            data: { name: company?.name },
          })
    }
    return ''
  }

  if (days <= 7 || days % 7 === 0) {
    return isSms
      ? SmsBuilder.getMessage({
          key: 'invoiceExpiredSevenDaysNotification',
          data: { name: company?.name, url: `${HOST}/i/r?id=${invoice._id}` },
        })
      : EmailBuilder.getSubjectAndBody({
          key: 'invoiceExpiredSevenDaysNotification',
          data: { name: company?.name, url: `${HOST}/i/r?id=${invoice._id}` },
        })
  }
  return ''
}

function getFutureDueText(
  company: ICompany,
  daysLeft: number,
  invoice: Partial<IInvoice>,
  hasExpireDate = false,
  isSms = true,
): string | EmailMessageData {
  if (daysLeft <= 3 && hasExpireDate) {
    return isSms
      ? SmsBuilder.getMessage({
          key: 'invoiceFutureLessThreeDaysNotification',
          data: {
            name: company?.name,
            url: `${HOST}/i/r?id=${invoice._id}`,
            daysLeft,
          },
        })
      : EmailBuilder.getSubjectAndBody({
          key: 'invoiceFutureLessThreeDaysNotification',
          data: {
            name: company?.name,
            url: `${HOST}/i/r?id=${invoice._id}`,
            daysLeft,
          },
        })
  }
  if (daysLeft === 0) {
    return isSms
      ? SmsBuilder.getMessage({
          key: 'invoiceTodayNotification',
          data: { name: company?.name, url: `${HOST}/i/r?id=${invoice._id}` },
        })
      : EmailBuilder.getSubjectAndBody({
          key: 'invoiceTodayNotification',
          data: { name: company?.name, url: `${HOST}/i/r?id=${invoice._id}` },
        })
  }

  if (daysLeft < 8) {
    return isSms
      ? SmsBuilder.getMessage({
          key: 'invoiceFutureLessEightDaysNotification',
          data: {
            name: company?.name,
            url: `${HOST}/i/r?id=${invoice._id}`,
            daysLeft,
          },
        })
      : EmailBuilder.getSubjectAndBody({
          key: 'invoiceFutureLessEightDaysNotification',
          data: {
            name: company?.name,
            url: `${HOST}/i/r?id=${invoice._id}`,
            daysLeft,
          },
        })
  }
  if (daysLeft % 7 === 0) {
    return isSms
      ? SmsBuilder.getMessage({
          key: 'invoiceFutureSevenDayNotification',
          data: {
            name: company?.name,
            url: `${HOST}/i/r?id=${invoice._id}`,
            dueDate: moment(invoice.invoice_due_date).format('MM/DD/YYYY'),
          },
        })
      : EmailBuilder.getSubjectAndBody({
          key: 'invoiceFutureSevenDayNotification',
          data: {
            name: company?.name,
            url: `${HOST}/i/r?id=${invoice._id}`,
            invoiceDueDate: moment(invoice.invoice_due_date).format(
              'MM/DD/YYYY',
            ),
          },
        })
  }
  return ''
}

async function addNotificationHistory(
  invoiceId: string,
  phone: string,
  sms: string,
  days: number | null,
) {
  const today = moment().format(CONSTANTS.DATE_FORMAT)

  return InvoiceNotification.create({
    invoiceId,
    date: today,
    phone,
    sms,
    days,
  })
}

export function getPipeline(): PipelineStage[] {
  const veryOld = moment().subtract(1, 'months').toDate()
  return [
    {
      $match: {
        $and: [
          { isDeleted: { $ne: true } },
          {
            status: {
              $in: [
                dictionaries.invoiceSchemaStatus.placed,
                dictionaries.invoiceStatus.pastDue,
              ],
            },
          },
          { invoice_due_date: { $gt: veryOld } },
          { total_amount: { $gt: 0 } },
        ],
      },
    },
    {
      $addFields: {
        companyId: {
          $convert: {
            input: '$company_id',
            to: 'objectId',
            onError: null,
            onNull: null,
          },
        },
      },
    },
    { $addFields: { invoiceId: { $toString: '$_id' } } },
    {
      $lookup: {
        from: Company.collection.name,
        localField: 'companyId',
        foreignField: '_id',
        as: 'company',
      },
    },
    { $addFields: { company: { $arrayElemAt: ['$company', 0] } } },
    {
      $lookup: {
        from: Draft.collection.name,
        localField: 'company_id',
        foreignField: 'company_id',
        as: 'draft',
      },
    },
    { $addFields: { draft: { $arrayElemAt: ['$draft', 0] } } },
    { $match: { 'company.status': { $eq: CompanyStatus.Approved } } },
    {
      $lookup: {
        from: LoanApplication.collection.name,
        as: 'loanApp',
        let: { invoiceId: { $toString: '$_id' } },
        pipeline: [
          {
            $addFields: {
              ids: {
                $cond: {
                  if: {
                    $eq: [{ $type: '$invoiceDetails.invoiceId' }, 'array'],
                  },
                  then: '$invoiceDetails.invoiceId',
                  else: ['$invoiceDetails.invoiceId'],
                },
              },
            },
          },
          { $match: { $expr: { $in: ['$$invoiceId', '$ids'] } } },
          { $sort: { createdAt: -1 } },
          { $limit: 1 },
        ],
      },
    },
    { $unwind: { path: '$loanApp', preserveNullAndEmptyArrays: true } },
    {
      // TODO: VK: Introduce new loan app's PENDING_DISBURSEMENT status for ATC version #2
      // exclude ATC invoices in pending disbursement status
      $match: {
        $or: [
          {
            'loanApp.metadata.repayment.autoTradeCreditEnabled': { $ne: true },
          },
          { 'loanApp.lms_id': { $exists: false } },
          { 'loanApp.lms_id': null },
          { 'loanApp.status': { $ne: LOAN_APPLICATION_STATUS.PROCESSING } },
        ],
      },
    },
    {
      $lookup: {
        from: 'operations',
        localField: 'invoiceId',
        foreignField: 'owner_id',
        pipeline: [
          {
            $match: {
              type: OPERATION_TYPES.INVOICE.PAYMENT,
            },
          },
          { $sort: { createdAt: -1 } },
        ],
        as: 'operations',
      },
    },
    { $addFields: { operation: { $first: '$operations' } } },
    {
      $match: {
        $and: [
          {
            'operation.status': { $ne: dictionaries.OPERATION_STATUS.SUCCESS },
          },
          {
            $or: [
              // For non-factoring invoices
              {
                $and: [
                  {
                    'paymentDetails.paymentType': {
                      $ne: InvoicePaymentType.FACTORING,
                    },
                  },
                  {
                    'operation.status': {
                      $ne: dictionaries.OPERATION_STATUS.PROCESSING,
                    },
                  },
                ],
              },
              // For factoring invoices
              {
                $and: [
                  {
                    'paymentDetails.paymentType': InvoicePaymentType.FACTORING,
                  },
                  {
                    $expr: {
                      $gt: [
                        {
                          $round: [
                            {
                              $subtract: [
                                {
                                  $add: [
                                    {
                                      $ifNull: [
                                        '$operation.amount',
                                        '$total_amount',
                                      ],
                                    },
                                    {
                                      $ifNull: ['$paymentDetails.fees', 0],
                                    },
                                  ],
                                },
                                {
                                  $add: [
                                    {
                                      $ifNull: ['$operation.paidAmount', 0],
                                    },
                                    {
                                      $ifNull: [
                                        '$operation.processingAmount',
                                        0,
                                      ],
                                    },
                                  ],
                                },
                              ],
                            },
                            2,
                          ],
                        },
                        0,
                      ],
                    },
                  },
                ],
              },
            ],
          },
        ],
      },
    },
    { $addFields: { operationStatus: '$operation.status' } },
    {
      $project: {
        _id: 1,
        invoice_date: 1,
        status: 1,
        draft: 1,
        total_amount: 1,
        company: 1,
        company_id: 1,
        invoice_number: 1,
        customer_account_id: 1,
        material_subtotal: 1,
        tax_amount: 1,
        invoice_due_date: 1,
        expiration_date: 1,
        invoice_document: 1,
        operationStatus: 1,
        invoiceNotificationType: 1,
        paymentDetails: 1,
        operation: 1,
      },
    },
    { $match: { status: { $nin: ['PROCESSING', 'SUCCESS'] } } },
  ]
}

export async function sendNotification() {
  await getEnvironmentVariables()
  await connectToDatabase()

  const todayString = moment().format(CONSTANTS.DATE_FORMAT)

  const invoiceNotifications = await InvoiceNotification.find(
    { date: todayString },
    'invoiceId',
  )
  if (invoiceNotifications.length > 0) {
    return 'ok'
  }

  const pipline = getPipeline()

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  const invoices = await Invoice.aggregate(pipline)

  for (const invoice of invoices) {
    const today = moment().tz('America/Chicago')
    const invoiceDueDate = moment(invoice.invoice_due_date).tz(
      'America/Chicago',
    )
    const expireDate = invoice.expiration_date
      ? moment(invoice.expiration_date).tz('America/Chicago')
      : null

    console.log(
      'today in CST',
      today.format('YYYY-MM-DD HH:mm'),
      'invoiceDueDate',
      invoiceDueDate.format('YYYY-MM-DD HH:mm'),
      'expiration_date',
      expireDate?.format('YYYY-MM-DD HH:mm'),
    )

    const dt = expireDate ? expireDate : invoiceDueDate

    const diff = dt.diff(today, 'days')
    let sms = ''
    let emailMessage = { subject: '', body: '' }

    if (invoice.operationStatus === dictionaries.OPERATION_STATUS.FAIL) {
      sms = SmsBuilder.getMessage({
        key: 'invoiceStatusFailNotification',
        data: {
          name: invoice.company?.name || 'customer',
          url: `${HOST}/i/r?id=${invoice._id}`,
        },
      })
      emailMessage = EmailBuilder.getSubjectAndBody({
        key: 'invoiceStatusFailNotification',
        data: {
          name: invoice.company?.name || 'customer',
          url: `${HOST}/i/r?id=${invoice._id}`,
        },
      })
    } else if (diff < 0) {
      sms = getPastDueText(
        invoice.company,
        invoice,
        Math.abs(diff),
        !!expireDate,
      ) as string
      emailMessage = getPastDueText(
        invoice.company,
        invoice,
        Math.abs(diff),
        !!expireDate,
        false,
      ) as EmailMessageData
    } else if (diff >= 0 && diff < 4) {
      sms = getFutureDueText(
        invoice.company,
        diff,
        invoice,
        !!expireDate,
      ) as string
      emailMessage = getFutureDueText(
        invoice.company,
        diff,
        invoice,
        !!expireDate,
        false,
      ) as EmailMessageData
    }

    if (sms && invoice.customer_account_id) {
      const customerAccount = await CustomerAccount.findOne({
        _id: invoice.customer_account_id,
      })
      if (customerAccount && (customerAccount.email || customerAccount.phone)) {
        await addNotificationHistory(
          invoice._id,
          customerAccount.phone || customerAccount.email,
          sms,
          diff,
        )
        const notificationType =
          invoice.invoiceNotificationType || EInvoiceNotificationType.BOTH

        if (
          [
            EInvoiceNotificationType.BOTH,
            EInvoiceNotificationType.EMAIL,
          ].includes(notificationType) &&
          customerAccount.email
        ) {
          try {
            await emailService.send({
              to: customerAccount.email,
              subject: emailMessage?.subject,
              html: `<p>${emailMessage?.body}</p>`,
            })
          } catch (e) {
            console.log('notification email send error', e)
          }
        }
        if (
          [
            EInvoiceNotificationType.BOTH,
            EInvoiceNotificationType.SMS,
          ].includes(notificationType) &&
          customerAccount.phone
        ) {
          if (process.env.LP_MODE !== 'dev') {
            try {
              const phoneNumber = parsePhoneNumber(
                customerAccount.phone,
                'US',
              ).number.toString()

              await Sms.send(phoneNumber, sms)
            } catch (e) {
              console.log('notification sms send error', e)
            }
          }
        }
      }
    }
  }
  return 'ok'
}

export async function sendNotificationForInvoices(invoiceIds: string[]) {
  await getEnvironmentVariables()
  await connectToDatabase()

  for (const invoiceId of invoiceIds) {
    const todayString = moment().format(CONSTANTS.DATE_FORMAT)
    const existingNotification = await InvoiceNotification.findOne({
      invoiceId: invoiceId,
      date: todayString,
    })

    if (existingNotification) {
      console.log(
        `Notification for invoice ${invoiceId} already exists for today.`,
      )
      return 'ok'
    }

    const invoice = await Invoice.findById(invoiceId)

    if (!invoice) {
      console.error(`Invoice with ID ${invoiceId} not found.`)
      continue
    }

    const company = await Company.findById(invoice.company_id)

    if (!company) {
      console.error(`Company for invoice ${invoiceId} not found.`)
      continue
    }

    const today = moment().tz('America/Chicago')
    const invoiceDueDate = moment(invoice.invoice_due_date).tz(
      'America/Chicago',
    )
    const expireDate = invoice.expiration_date
      ? moment(invoice.expiration_date).tz('America/Chicago')
      : null

    console.log(
      'today in CST',
      today.format('YYYY-MM-DD HH:mm'),
      'invoiceDueDate',
      invoiceDueDate.format('YYYY-MM-DD HH:mm'),
      'expiration_date',
      expireDate?.format('YYYY-MM-DD HH:mm'),
    )

    const dt = expireDate ? expireDate : invoiceDueDate

    const diff = dt.diff(today, 'days')
    let sms = ''
    let emailMessage = { subject: '', body: '' }

    if (diff < 0) {
      sms = getPastDueText(
        company,
        invoice,
        Math.abs(diff),
        !!expireDate,
      ) as string
      emailMessage = getPastDueText(
        company,
        invoice,
        Math.abs(diff),
        !!expireDate,
        false,
      ) as EmailMessageData
    } else if (diff >= 0 && diff < 4) {
      sms = getFutureDueText(company, diff, invoice, !!expireDate) as string
      emailMessage = getFutureDueText(
        company,
        diff,
        invoice,
        !!expireDate,
        false,
      ) as EmailMessageData
    }

    if (sms && invoice.customer_account_id) {
      const customerAccount = await CustomerAccount.findOne({
        _id: invoice.customer_account_id,
      })
      if (customerAccount && (customerAccount.email || customerAccount.phone)) {
        await addNotificationHistory(
          invoice.id,
          customerAccount.phone || customerAccount.email,
          sms,
          diff,
        )
        const notificationType =
          invoice.invoiceNotificationType || EInvoiceNotificationType.BOTH

        if (
          [
            EInvoiceNotificationType.BOTH,
            EInvoiceNotificationType.EMAIL,
          ].includes(notificationType) &&
          customerAccount.email
        ) {
          try {
            await emailService.send({
              to: customerAccount.email,
              subject: emailMessage?.subject,
              html: `<p>${emailMessage?.body}</p>`,
            })
            console.log(
              `Email sent to ${customerAccount.email} for invoice ${invoiceId}`,
            )
          } catch (e) {
            console.log('notification email send error', e)
          }
        }
        if (
          [
            EInvoiceNotificationType.BOTH,
            EInvoiceNotificationType.SMS,
          ].includes(notificationType) &&
          customerAccount.phone
        ) {
          if (process.env.LP_MODE !== 'dev') {
            try {
              const phoneNumber = parsePhoneNumber(
                customerAccount.phone,
                'US',
              ).number.toString()

              await Sms.send(phoneNumber, sms)
              console.log(`SMS sent to ${phoneNumber} for invoice ${invoiceId}`)
            } catch (e) {
              console.log('notification sms send error', e)
            }
          }
        }
      }
    }
  }
  return 'ok'
}
