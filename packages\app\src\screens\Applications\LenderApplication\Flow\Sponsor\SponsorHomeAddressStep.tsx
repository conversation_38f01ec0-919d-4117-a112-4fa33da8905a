import React, { FC } from 'react'
import { StyleSheet, View } from 'react-native'
import { BtGoogleAutocomplete, BtInput_v1 } from '@linqpal/components/src/ui'
import { observer } from 'mobx-react-lite'
import { useTranslation } from 'react-i18next'
import { runInAction } from 'mobx'
import { useLenderApplication } from '../../LenderApplicationContext'
import { ILenderApplicationEditor } from '../getLenderApplicationEditor'
import { IAddress } from '@linqpal/models/src/applications/lender/ILenderApplicationDraft'
import { Validators } from '../../../Helpers/Validators'
import { BtNumberInput_v1 } from '@linqpal/components/src/ui/BtNumberInput_v1'
import { editorStyles } from '../editorStyles'

const SponsorHomeAddressEditor: FC = observer(() => {
  const { t } = useTranslation('application')
  const store = useLenderApplication()

  const setValue = (key: keyof IAddress, value: string) => {
    runInAction(() => {
      if (!store.draft.data.sponsor.homeAddress) {
        store.draft.data.sponsor.homeAddress = {}
      }

      store.draft.data.sponsor.homeAddress[key] = value
    })
  }

  const onAddressSelect = (value: IAddress) => {
    runInAction(() => {
      if (!store.draft.data.sponsor.homeAddress) {
        store.draft.data.sponsor.homeAddress = {}
      }

      store.draft.data.sponsor.homeAddress.street = value.street
      store.draft.data.sponsor.homeAddress.state = value.state
      store.draft.data.sponsor.homeAddress.city = value.city
      store.draft.data.sponsor.homeAddress.zip = value.zip
    })
  }

  return (
    <View style={editorStyles.formContainer}>
      <View style={editorStyles.singleColumnRow}>
        <BtGoogleAutocomplete
          autoFocus
          style={styles.addressLine}
          label={t('LenderApplication.Flow.Sponsor.HomeAddress.Search')}
          testID="LenderApplication.Sponsor.StreetAddress"
          onSelect={onAddressSelect}
        />
      </View>

      <View style={editorStyles.row}>
        <View style={editorStyles.columnLeft}>
          <BtInput_v1
            required
            disabled
            value={store.draft.data.sponsor.homeAddress?.city || ''}
            label={t('LenderApplication.Flow.Sponsor.HomeAddress.City')}
            testID="LenderApplication.Sponsor.City"
            validate={Validators.required}
          />
        </View>
        <View style={editorStyles.columnRight}>
          <BtInput_v1
            required
            disabled
            value={store.draft.data.sponsor.homeAddress?.state || ''}
            label={t('LenderApplication.Flow.Sponsor.HomeAddress.State')}
            testID="LenderApplication.Sponsor.State"
            validate={Validators.required}
          />
        </View>
      </View>

      <View style={editorStyles.singleColumnRow}>
        <BtInput_v1
          required
          disabled
          value={store.draft.data.sponsor.homeAddress?.street || ''}
          label={t('LenderApplication.Flow.Sponsor.HomeAddress.Street')}
          testID="LenderApplication.Sponsor.Street"
          validate={Validators.required}
        />
      </View>

      <View style={editorStyles.row}>
        <View style={editorStyles.columnLeft}>
          <BtInput_v1
            value={store.draft.data.sponsor.homeAddress?.apartment || ''}
            label={t('LenderApplication.Flow.Sponsor.HomeAddress.Apartment')}
            testID="LenderApplication.Sponsor.Apartment"
            onChangeText={(val: string) => setValue('apartment', val)}
          />
        </View>
        <View style={editorStyles.columnRight}>
          <BtNumberInput_v1
            required
            disabled
            value={store.draft.data.sponsor.homeAddress?.zip || ''}
            format={'#####'}
            label={t('LenderApplication.Flow.Sponsor.HomeAddress.Zip')}
            testID="LenderApplication.Sponsor.Zip"
            validate={Validators.zip}
          />
        </View>
      </View>
    </View>
  )
})

const styles = StyleSheet.create({
  addressLine: {
    borderWidth: 2,
    borderRadius: 4,
    borderColor: '#99ADBA',
  },
})

export const SponsorHomeAddressStep: ILenderApplicationEditor = {
  options: {
    title: 'LenderApplication.Flow.Sponsor.HomeAddress.Title',
  },
  component: SponsorHomeAddressEditor,
}
