{"name": "@linqpal/common-backend", "version": "1.0.0", "license": "MIT", "private": true, "types": "index", "scripts": {"e2e": "cross-env NODE_ENV=test mocha --exit --timeout 30000 --require test/setup.js e2e", "test": "cross-env NODE_ENV=test mocha --exit --timeout 30000 --recursive --require test/setup.js test"}, "dependencies": {"@aws-sdk/client-eventbridge": "3.236.0", "@aws-sdk/client-kms": "3.236.0", "@aws-sdk/client-lambda": "3.236.0", "@aws-sdk/client-s3": "3.236.0", "@aws-sdk/client-secrets-manager": "3.237.0", "@aws-sdk/client-sfn": "3.236.0", "@aws-sdk/client-sns": "3.236.0", "@aws-sdk/client-sqs": "3.236.0", "@aws-sdk/s3-request-presigner": "3.236.0", "@azure/core-auth": "1.4.0", "@azure/core-util": "1.6.0", "@azure/service-bus": "7.5.0", "@elastic/ecs-pino-format": "1.3.0", "@linqpal/models": "workspace:*", "@sendgrid/helpers": "7.7.0", "@sendgrid/mail": "7.7.0", "@slack/web-api": "6.7.2", "axios": "0.21.1", "convertapi": "1.11.2", "crypto-js": "4.0.0", "deepdash": "5.3.9", "docxtemplater": "3.28.0", "experian-node": "0.0.1", "generate-schema": "2.6.0", "jsrsasign": "10.1.13", "jwt-decode": "4.0.0", "libphonenumber-js": "1.10.61", "lodash": "4.17.20", "mathjs": "9.4.4", "mobx": "6.3.6", "mobx-state-tree": "5.0.4", "mocha": "^8.1.3", "moment": "^2.29.4", "moment-business-days": "1.2.0", "moment-timezone": "^0.5.40", "mongoose": "8.16.4", "numbro": "2.3.5", "pdf-lib": "^1.17.1", "pdfmake": "^0.2.10", "pino": "9.4.0", "pino-abstract-transport": "^1.2.0", "pino-pretty": "^11.2.1", "pizzip": "3.1.1", "plaid": "16.0.0", "query-string": "6.14.1", "sinon": "9.0.3", "ts-mocha": "8.0.0", "twilio": "3.83.1", "uuid": "8.3.2", "xlsx": "0.16.9"}, "devDependencies": {"@types/chai": "4.3.0", "@types/crypto-js": "4.1.1", "@types/dotenv": "8.2.0", "@types/jsrsasign": "10.5.4", "@types/mocha": "9.0.0", "@types/node": "17.0.23", "@types/pdfmake": "^0", "@types/sinon": "10.0.6", "@types/uuid": "8.3.4", "aws-sdk-client-mock": "2.0.1", "chai": "4.2.0", "chai-as-promised": "7.1.1", "mongodb-memory-server": "8.11.5", "nock": "^13.0.11", "ts-mocha": "^8.0.0", "ts-node": "10.4.0", "typescript": "^5.4.5"}, "resolutions": {"@azure/core-auth": "1.4.0", "@azure/core-util": "1.6.0"}, "mocha": {"extension": ["ts", "js"]}}