import {
  Company,
  getLoanPricingPackage,
  getPlan,
  invoicesService,
  LedgerService,
  LMS,
  Logger,
} from '@linqpal/common-backend'
import moment from 'moment-timezone'
import { exceptions, IEncrypted } from '@linqpal/models'
import {
  ICompany,
  ILoanApplication,
} from '@linqpal/common-backend/src/models/types'
import { getInvoice, IInvoiceData } from './index'
import * as math from 'mathjs'
import { LOAN_APPLICATION_TYPE } from '@linqpal/models/src/dictionaries/loanApplicationTypes'
import { QuoteService } from '@linqpal/common-backend/src/services/quote/quote.service'
import { ClientSession } from 'mongoose'
import { QuoteNotifications } from '@linqpal/common-backend/src/services/quote/quoteNotifications.service'
import { compatibilityService } from '@linqpal/common-backend/src/services/compatibility/compatibility.service'
import { LoanApplicationService } from '@linqpal/common-backend/src/services/loanApplication.service'
import { FundingSources } from '@linqpal/models/src/dictionaries/fundingSources'

const log = new Logger({
  module: 'LoanPro',
  subModule: 'loanCreation',
})

export const loanCreation = async (
  loanApplication: ILoanApplication,
  session: ClientSession | null,
) => {
  const { invoiceDetails } = loanApplication
  const invoices = await getInvoice(loanApplication)
  if (!invoices) throw new exceptions.LogicalError('invoice/not-found')

  const payeeId = invoices[0].company_id
  const payerCompany = await Company.findById<ICompany>(
    loanApplication.company_id,
  )
  if (!payerCompany) throw new exceptions.LogicalError('Company not found')

  const loanPlan = await getPlan(invoiceDetails.paymentPlan)

  if (!loanPlan) {
    throw new exceptions.LogicalError('Loan plan is not selected')
  }

  const lmsTemplateId = loanPlan.lmsTemplateId

  const payeeCompany = await Company.findById(payeeId)

  const loanPack = await getLoanPricingPackage(
    payeeCompany?.settings?.loanPricingPackageId,
  )

  if (!loanPack) {
    throw new exceptions.LogicalError('Supplier package is not selected')
  }

  // For old applications fees were deducted from advance payment
  // Now fees are deducted from the final payment. Use this flag to use different final payment logic for old and new apps
  loanApplication.metadata!.deductFeeFromFinalPayment = true

  const contractDate = moment()

  const invoiceData: IInvoiceData = {}

  let totalAmount = 0
  let totalAdvanceAmount = 0
  let totalDiscount = 0

  for (const invoice of invoices) {
    const payment = LoanApplicationService.calculatePayments(
      loanApplication,
      invoice.total_amount,
      loanPack.metadata,
    )

    log.info({ payment }, `payment for invoice ${invoice.id}`)

    totalAmount += invoice.total_amount
    totalAdvanceAmount += payment.advance.amount
    totalDiscount += payment.advance.discount
  }

  totalAmount = math.round(totalAmount, 2)

  loanApplication.approvedAmount = totalAmount
  loanApplication.usedAmount = totalAmount

  if (loanApplication.type === LOAN_APPLICATION_TYPE.QUOTE) {
    await QuoteService.releaseHeldAmount(invoices[0])
  }

  let firstPaymentDate: string | undefined

  if (!loanApplication.metadata) {
    loanApplication.metadata = {}
  }
  loanApplication.metadata.loanPackage = {
    ...loanPack.metadata.toObject(),
    name: loanPack.title,
  }

  if (!loanApplication.lms_id) {
    const einHash = (loanApplication.draft?.businessInfo_ein as IEncrypted)
      ?.hash

    if (!einHash) {
      throw new exceptions.LogicalError('Ein was not found')
    }
    const customerId = invoices[0].customer_account_id

    const drawApprovalDetails =
      await compatibilityService.getCreateLoanDrawApprovalDetails(
        loanApplication,
      )

    const noSupplierDetails = invoices[0]?.supplierInvitationDetails?.name
      ? {
          businessName: invoices[0].supplierInvitationDetails.name,
        }
      : null

    log.info(`Found noSupplierDetails in invoices: ${noSupplierDetails}.`)

    log.info(
      { loanApplication },
      `Creating loan for loan application ${loanApplication.id}`,
    )

    const {
      id: loan_id,
      loanReceivables,
      fundingSource,
      amount,
      fee,
    } = await LMS.createLoan(
      loanApplication.company_id,
      drawApprovalDetails.companyName,
      lmsTemplateId,
      loanApplication.approvedAmount,
      einHash,
      LMS.getLoanOrigin(loanApplication, noSupplierDetails),
      drawApprovalDetails.debtInvestor ?? FundingSources.Arcadia,
      drawApprovalDetails.projectId,
      drawApprovalDetails.merchantId,
      drawApprovalDetails.merchantName,
      drawApprovalDetails.drawApprovalId,
      drawApprovalDetails.loanPayables,
      drawApprovalDetails.downPaymentPercentage,
      drawApprovalDetails.downPaymentAmount,
      drawApprovalDetails.downPaymentExpireAt,
    )

    firstPaymentDate = loanReceivables[0]?.expectedDate ?? ''

    loanApplication.lms_id = loan_id
    loanApplication.issueDate = contractDate.toDate()

    await invoicesService.addInvoiceWatermarks(
      invoices,
      loanReceivables,
      fundingSource,
    )

    await LedgerService.handleLoanIssue({
      loanId: loan_id,
      customerId: customerId,
      amount: amount,
      fee: fee,
    })

    if (loanApplication.type === LOAN_APPLICATION_TYPE.QUOTE) {
      await QuoteNotifications.quoteDrawCreated(loanApplication, invoices[0])
    }
  }

  return {
    ...invoiceData,
    advanceAmount: totalAdvanceAmount,
    discount: totalDiscount,
    firstPaymentDate,
  }
}
