import { StyleSheet, TouchableOpacity, View } from 'react-native'
import { useTranslation } from 'react-i18next'
import { BtPlainText } from '@linqpal/components/src/ui'
import React from 'react'
import { useUnifiedApplication } from '../UnifiedApplicationContext'
import { CompanyUtils } from '@linqpal/models/src/helpers/companyUtils'
import { ApplicationType } from '@linqpal/models/src/dictionaries/applicationType'
import { useResponsive } from '../../../../utils/hooks'
import RootStore from '../../../../store/RootStore'
import ApplicationStore from '../../../GeneralApplication/Application/ApplicationStore'
import { Spacer } from '../../../../ui/atoms'
import { IconCloseSquare } from '../../../../assets/icons'

interface IHeaderProps {
  title?: string
  onClose: () => void
}

interface ICloseButtonProps {
  disabled?: boolean
  onPress: () => void
}

export const WizardHeader = ({ onClose }: IHeaderProps) => {
  const { t } = useTranslation('application')
  const { sm } = useResponsive()

  const store = useUnifiedApplication()

  let title = ''

  switch (store.type) {
    case ApplicationType.Supplier:
      title = t('GetPaidApplication')
      break
    case ApplicationType.Credit:
      title = t('CreditRequest')
      break
    case ApplicationType.InHouseCredit:
      title = t('InHouseCreditApplication', {
        supplierName: CompanyUtils.getCompanyName(
          RootStore.userStore.suppliers?.find(
            (s) => s.id === ApplicationStore.supplierId,
          ),
        ),
      })
      break
    default:
      title = ''
      break
  }

  return sm ? (
    <HeaderDesktop title={title} onClose={onClose} />
  ) : (
    <HeaderMobile title={title} onClose={onClose} />
  )
}

export const HeaderDesktop = ({ onClose, title }: IHeaderProps) => {
  const { t } = useTranslation('application')

  return (
    <View style={[styles.headerWrapper, styles.headerWrapperDesktop]}>
      <CloseButton
        onPress={onClose}
        disabled={ApplicationStore.buttonLoading}
      />

      <BtPlainText style={[styles.title, styles.titleDesktop]}>
        {title || t('SetUpAccount')}
      </BtPlainText>
      <Spacer width={50} />
    </View>
  )
}

export const HeaderMobile = ({ title, onClose }: IHeaderProps) => {
  const { t } = useTranslation('application')

  const store = useUnifiedApplication()

  return (
    <View style={[styles.headerWrapper, styles.headerWrapperMobile]}>
      <CloseButton
        onPress={onClose}
        disabled={ApplicationStore.buttonLoading}
      />

      <BtPlainText style={[styles.title, styles.titleMobile]}>
        {title || t('SetUpAccount')}
      </BtPlainText>

      {store.stepOptions.canSkip ? (
        <BtPlainText style={styles.skipButton} onPress={() => store.skipStep()}>
          {t('Skip')}
        </BtPlainText>
      ) : (
        <Spacer width={50} />
      )}
    </View>
  )
}

const CloseButton = ({ onPress, disabled }: ICloseButtonProps) => {
  const { sm } = useResponsive()

  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled}
      style={[
        styles.closeButton,
        sm ? styles.closeButtonDesktop : styles.closeButtonMobile,
      ]}
    >
      <IconCloseSquare width={12} height={12} />
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  closeButton: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 40,
    height: 40,
  },
  closeButtonDesktop: {
    borderColor: '#E6EBEE',
    borderRadius: 6,
    borderWidth: 1,
  },
  closeButtonMobile: {
    borderRadius: 8,
    backgroundColor: '#F5F7F8',
  },
  headerWrapper: {
    width: '100%',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    borderBottomColor: '#EBEEEF',
    borderBottomWidth: 1,
  },
  headerWrapperDesktop: {
    height: 72,
    paddingHorizontal: 20,
  },
  headerWrapperMobile: {
    height: 54,
    paddingHorizontal: 10,
  },
  title: {
    flex: 1,
    textAlign: 'center',
  },
  titleDesktop: {
    fontSize: 20,
    fontWeight: '600',
  },
  titleMobile: {
    fontSize: 16,
    fontWeight: '500',
  },
  skipButton: {
    color: '#335C75',
    fontWeight: '500',
    marginRight: 10,
  },
})
