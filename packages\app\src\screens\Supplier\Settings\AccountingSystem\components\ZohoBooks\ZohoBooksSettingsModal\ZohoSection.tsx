import React, { FC, useState } from 'react'
import {
  StyleProp,
  StyleSheet,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native'
import { Text } from 'react-native-paper'
import { ScrollHorizontalContainer } from '../../../../../../../ui/atoms/ScrollHorizontalContainer'
import { RadioButton } from '@linqpal/common-frontend/src/ui'
import { BtCheckBox, BtNumberInput, BtText } from '@linqpal/components/src/ui'

export const TextSection: FC<{
  title: string
  description: string
  onCopy?: () => void
}> = ({ title, description, onCopy }) => {
  const [copyButtonText, setCopyButtonText] = useState('Copy')

  const handleCopy = () => {
    if (onCopy) {
      onCopy()
    }

    setCopyButtonText('Copied')

    setTimeout(() => {
      setCopyButtonText('Copy')
    }, 1500)
  }
  return (
    <View style={styles.section}>
      <Text style={styles.title}>{title}:</Text>
      <View style={styles.descriptionWrapper}>
        <View style={{ width: 250 }}>
          <ScrollHorizontalContainer
            value={description ?? '-'}
            textStyle={styles.description}
          />
        </View>

        {onCopy && (
          <TouchableOpacity onPress={handleCopy} style={styles.copyButton}>
            <Text style={styles.copyButtonText}>{copyButtonText}</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  )
}

interface SelectSectionProps {
  title: string
  value: string
  onValueChange: (value: string) => void
  options: {
    label: string
    value: string
  }[]
}

export const SelectOptionSection: FC<SelectSectionProps> = ({
  title,
  value,
  onValueChange,
  options,
}) => {
  return (
    <View style={[styles.section]}>
      <BtText style={styles.title}>{title}:</BtText>
      <View style={[styles.descriptionWrapper, { alignItems: 'center' }]}>
        {options.map((option) => (
          <View key={option.value} style={styles.radioButtonContainer}>
            <RadioButton
              checked={value === option.value}
              label={option.label}
              onPress={() => onValueChange(option.value)} // Update value on selection
            />
          </View>
        ))}
      </View>
    </View>
  )
}

interface IRadioButtonSectionProps {
  title: string
  value: boolean
  onValueChange: (value: boolean) => void
}

export const RadioButtonSection: FC<IRadioButtonSectionProps> = ({
  title,
  value,
  onValueChange,
}) => {
  return (
    <View style={[styles.descriptionWrapper, { alignItems: 'center' }]}>
      <TouchableOpacity
        style={styles.checkboxWrapper}
        onPress={() => onValueChange(!value)}
        activeOpacity={0.7}
      >
        <BtCheckBox
          style={styles.checkbox}
          checked={value}
          onPress={() => onValueChange(!value)}
        />
        <BtText style={styles.checkboxLabel}>{title}</BtText>
      </TouchableOpacity>
    </View>
  )
}

interface INumberInputSectionProps {
  title: string
  value: string
  onValueChange: (value: string) => void
  required?: boolean
  containerStyle?: StyleProp<ViewStyle>
}

export const InputNumberSection: FC<INumberInputSectionProps> = ({
  title,
  value,
  onValueChange,
  required = false,
  containerStyle,
}) => {
  return (
    <View style={[styles.descriptionWrapper, containerStyle]}>
      <BtNumberInput
        value={value}
        onChangeText={(text) => onValueChange(text)}
        label={title}
        testID={'zoho-input-number'}
        format={'###'}
        style={{ width: 250 }}
        required={required}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  section: {
    marginBottom: 30,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    textAlign: 'center',
    fontFamily: 'Inter',
    fontSize: 16,
    fontWeight: '600',
    lineHeight: 26,
    marginTop: 10,
  },
  descriptionWrapper: {
    width: 330,
    height: 40,
    justifyContent: 'flex-start',
    display: 'flex',
    flexDirection: 'row',
  },
  description: {
    fontFamily: 'Inter',
    fontSize: 16,
    fontWeight: '400',
    lineHeight: 26,
    marginTop: 10,
  },
  copyContainer: {
    justifyContent: 'center',
    flexDirection: 'row',
    alignItems: 'center',
  },
  copyButton: {
    marginHorizontal: 10,
    width: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  copyButtonText: {
    color: '#00A0F3',
    fontSize: 16,
  },
  radioButtonContainer: {
    paddingTop: 8,
    marginRight: 30,
    alignItems: 'center',
  },
  checkboxWrapper: {
    flexDirection: 'row',
    marginBottom: 30,
    alignItems: 'center',
  },
  checkbox: { width: 20, height: 20, marginRight: 11 },
  checkboxLabel: {
    textAlign: 'center',
    fontFamily: 'Inter',
    fontSize: 14,
    fontWeight: '600',
    lineHeight: 26,
  },
})
