import React, { FC } from 'react'
import { useTranslation } from 'react-i18next'
import { BtDateInput } from '@linqpal/components/src/ui'
import { observer } from 'mobx-react-lite'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { runInAction } from 'mobx'
import { UnifiedApplicationValidator } from '@linqpal/models'
import { IUnifiedApplicationEditor } from '../getUnifiedApplicationEditor'

const BusinessStartDateEditor: FC = () => {
  const { t } = useTranslation('application')

  const store = useUnifiedApplication()

  const handleValidation = (value: string) =>
    value.length !== 7 ||
    UnifiedApplicationValidator.validateBusinessStartDate(value)
      ? ''
      : (t('ValidationErrors.InvalidDate') as string)

  const handleChange = (value: string) => {
    runInAction(() => (store.draft.data.businessInfo.startDate = value))
  }

  return (
    <BtDateInput
      value={store.draft.data.businessInfo.startDate || ''}
      //eslint-disable-next-line i18next/no-literal-string
      format="MM/YYYY"
      size="large"
      validate={handleValidation}
      onChangeText={handleChange}
      label={t('Business.StartLabel') as string}
      testID="UnifiedApplication.BusinessInfo.StartDate"
    />
  )
}

export const BusinessStartDateStep: IUnifiedApplicationEditor = {
  options: {
    title: 'Business.Start',
  },
  component: observer(BusinessStartDateEditor),
}
