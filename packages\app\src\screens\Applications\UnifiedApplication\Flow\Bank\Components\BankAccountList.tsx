import React, { FC, useRef, useState } from 'react'
import { ActivityIndicator } from 'react-native-paper'
import { TouchableOpacity, View } from 'react-native'
import { useResponsive } from '@linqpal/components/src/hooks'
import { BtRadioButton, BtText } from '@linqpal/components/src/ui'
import { IUIBankAccount } from '@linqpal/models'
import { useTranslation } from 'react-i18next'
import { BankAccountType } from './BankAccountType'
import { Icon } from '@ui-kitten/components'
import { composeStyle } from '@linqpal/common-frontend/src/helpers'
import { useUnifiedApplication } from '../../../UnifiedApplicationContext'
import { FinicityConnectionError } from '../../../../../Contractor/MoreTab/Wallet/FinicityConnectionError'
import {
  PlaidConnectButton,
  PlaidConnectButtonRef,
} from '../../../../../../ui/add-payment-method-components/PlaidConnectButton'

interface Props {
  bankAccounts: IUIBankAccount[]
  selectedBankAccountId: string
  onSelect?: (bankAccount: IUIBankAccount) => void
  onDelete?: (id: string) => void
}

export const BankAccountList: FC<Props> = ({
  bankAccounts,
  selectedBankAccountId,
  onSelect,
  onDelete,
}) => {
  const { t } = useTranslation('application')
  const { sm } = useResponsive()
  const [showDisconnected, setShowDisconnected] = useState(false)
  const ref = useRef<PlaidConnectButtonRef>(null)
  const [selectedId, setSelectedId] = useState('')

  const store = useUnifiedApplication()

  const handleSelectBankAccount = (selectedAccount: IUIBankAccount) => {
    if (!onSelect) return
    onSelect(selectedAccount)
  }

  const onCloseConnectionErrorModal = () => setShowDisconnected(false)

  const plaidConnection = (bankId, routingNumber?: string) =>
    ref.current?.createToken(bankId, routingNumber)

  const onPress = (bankAccount) => {
    setSelectedId(bankAccount._id)
    if (!bankAccount.isReconnectionRequired || !store.isCreditApp)
      handleSelectBankAccount(bankAccount)
    else if (bankAccount.isFinicityConnectedBank) setShowDisconnected(true)
    else if (bankAccount.isExpiredPlaidBank) plaidConnection(bankAccount._id)
    else if (bankAccount.isDisconnectedPlaidBank)
      plaidConnection('', bankAccount.routingNumber)
  }

  if (!bankAccounts || bankAccounts.length === 0) {
    return null
  }

  return (
    <View
      style={{
        flex: 1,
        flexWrap: 'wrap',
        flexDirection: sm ? 'row' : 'column',
      }}
    >
      {bankAccounts.map((bankAccount) => {
        return (
          <View
            style={{ position: 'relative', marginBottom: 20 }}
            key={bankAccount.id}
          >
            <BankAccountItem
              bankAccount={bankAccount}
              selectedBankAccountId={selectedBankAccountId}
              onPress={onPress}
              onDelete={onDelete}
              showDisconnectedBadge={store.isCreditApp}
            />
            <FinicityConnectionError
              visible={showDisconnected && selectedId === bankAccount._id}
              onClose={onCloseConnectionErrorModal}
              finicityBankId={bankAccount._id}
            />
            <PlaidConnectButton ref={ref} />
            {selectedBankAccountId === bankAccount.id && (
              <BtText size={12} color="#668598">
                {t('Bank.SelectedAsPrimaryCaption')}
              </BtText>
            )}
          </View>
        )
      })}
    </View>
  )
}

export const BankAccountItem: React.FC<{
  bankAccount: IUIBankAccount
  selectedBankAccountId: string
  onPress: (arg: IUIBankAccount) => void
  onDelete?: (arg: string) => void
  loadingBankId?: string
  showDisconnectedBadge?: boolean
}> = ({
  bankAccount,
  selectedBankAccountId,
  onPress,
  onDelete,
  loadingBankId,
  showDisconnectedBadge = false,
}) => {
  const { t } = useTranslation('application')
  const { sm } = useResponsive()
  return (
    <TouchableOpacity
      key={bankAccount.id}
      onPress={() => onPress(bankAccount)}
      testID={'bank-account-item'}
      style={composeStyle(
        {
          flexDirection: 'row',
          alignItems: 'flex-start',
          borderRadius: 8,
          borderColor: '#CCD6DD',
          borderWidth: 1,
          marginRight: sm ? 10 : 0,
          height: 85,
          paddingHorizontal: 20,
          paddingVertical: 15,
          backgroundColor: loadingBankId ? 'rgba(224, 220, 220, 0.5)' : 'white',
        },
        sm ? { width: 216 } : { minWidth: 216 },
      )}
      disabled={!!loadingBankId}
    >
      {loadingBankId === bankAccount._id ? (
        <ActivityIndicator size={18} style={{ marginRight: 8 }} />
      ) : (
        <BtRadioButton
          containerStyle={{ paddingVertical: 0 }}
          checked={selectedBankAccountId === bankAccount._id}
          onPress={() => onPress(bankAccount)}
          labelStyle={{ marginLeft: 15 }}
        />
      )}
      <View style={{ flex: 1 }}>
        <BtText
          style={{
            fontSize: 16,
            color: '#335C75',
            fontWeight: '500',
            overflow: 'hidden',
          }}
          numberOfLines={1}
          title={bankAccount.name}
        >
          {bankAccount.name.split('/')[0]}
        </BtText>
        <BtText
          style={{
            fontWeight: '500',
            fontSize: 12,
            color: '#668598',
            marginTop: 5,
          }}
          numberOfLines={1}
        >
          {bankAccount.accountType === BankAccountType.Savings
            ? t('Bank.SavingAccount')
            : t('Bank.CheckingAccount')}
          ...
          {bankAccount.accountNumber.substring(
            bankAccount.accountNumber.length - 4,
          )}
        </BtText>
        {bankAccount.isManualEntry && onDelete && (
          <div
            style={{
              display: 'inline-flex',
              flexDirection: 'row',
              alignItems: 'center',
              marginTop: 5,
            }}
            onClick={() => onDelete(bankAccount.id)}
          >
            <Icon
              style={{ height: 15, marginRight: 5 }}
              fill="black"
              name="trash-2-outline"
            />
            <BtText color="#003353" numberOfLines={1} weight={'600'} size={12}>
              {t('Bank.DeleteManualButton')}
            </BtText>
          </div>
        )}
      </View>
      {!bankAccount.isManualEntry && showDisconnectedBadge && (
        <View
          style={{
            position: 'absolute',
            backgroundColor: bankAccount.isReconnectionRequired
              ? '#DB2424'
              : '#33B3F5',
            paddingVertical: 3,
            paddingHorizontal: 10,
            borderRadius: 5,
            top: -15,
            right: -3,
          }}
        >
          <BtText size={12} style={{ color: 'white' }}>
            {bankAccount.isReconnectionRequired
              ? t('Bank.Disconnected')
              : t('Bank.ConnectedViaOnlineBanking')}
          </BtText>
        </View>
      )}
    </TouchableOpacity>
  )
}
