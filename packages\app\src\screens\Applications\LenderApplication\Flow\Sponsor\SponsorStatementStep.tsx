import React, { FC } from 'react'
import { StyleSheet, View } from 'react-native'
import { BtPlainText, BtRadioGroup } from '@linqpal/components/src/ui'
import { observer } from 'mobx-react'
import { useTranslation } from 'react-i18next'
import { useLenderApplication } from '../../LenderApplicationContext'
import { StatementQuestions } from '@linqpal/models/src/applications/lender/ILenderApplicationDraft'
import { runInAction } from 'mobx'
import { ILenderApplicationEditor } from '../getLenderApplicationEditor'
import { colors } from '@linqpal/components/src/theme'
import { Spacer } from '../../../../../ui/atoms'
import { editorStyles } from '../editorStyles'

const SponsorStatementEditor: FC = () => {
  const { t } = useTranslation('application')
  const store = useLenderApplication()

  const handleAnswerChange = (statementId: string, option: boolean) => {
    runInAction(() => {
      const statement = store.draft.data.sponsor.statements?.find(
        (s) => s.statementId === statementId,
      )

      if (statement) {
        statement.answer = option
      } else {
        if (!store.draft.data.sponsor.statements) {
          store.draft.data.sponsor.statements = []
        }

        store.draft.data.sponsor.statements.push({
          statementId,
          answer: option,
        })
      }
    })
  }

  const getAnswer = (statementId: string) => {
    const statement = store.draft.data.sponsor.statements?.find(
      (s) => s.statementId === statementId,
    )
    return statement?.answer
  }

  const questions = Object.values(StatementQuestions)

  return (
    <View style={styles.container}>
      {questions.map((question, index) => {
        const answer = getAnswer(question)

        return (
          <View
            key={question}
            style={
              index === questions.length - 1
                ? styles.statementContainerLast
                : styles.statementContainer
            }
          >
            <BtPlainText style={styles.statementText}>
              {t(
                `LenderApplication.Flow.Sponsor.Statements.Questions.${question}`,
              )}
            </BtPlainText>
            <BtRadioGroup
              value={answer}
              // prettier-ignore
              options={[
                {
                  label: t('LenderApplication.Flow.Sponsor.Statements.Radio.Yes'),
                  value: true,
                },
                {
                  label: t('LenderApplication.Flow.Sponsor.Statements.Radio.No'),
                  value: false,
                },
              ]}
              onChange={(opt: boolean) => handleAnswerChange(question, opt)}
              groupStyle={editorStyles.radioGroupHorizontal}
              labelStyle={editorStyles.radioLabel}
              testID={`LenderApplication.Sponsor.Statements.${question}`}
            />
            {answer === true && (
              <BtPlainText style={styles.explanationWarning}>
                {t(
                  'LenderApplication.Flow.Sponsor.Statements.ExplanationWarning',
                )}
              </BtPlainText>
            )}
            <Spacer height={26} />
          </View>
        )
      })}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  statementContainer: {
    marginBottom: 32,
    borderBottomWidth: 1,
    borderBottomColor: '#DEE5EB',
  },
  statementContainerLast: {
    marginBottom: -32,
    borderBottomWidth: 0,
  },
  statementText: {
    fontSize: 16,
    fontWeight: '600',
    lineHeight: 24,
    marginBottom: 12,
    color: '#333',
  },
  explanationWarning: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 24,
    color: colors.accentText,
    marginTop: 6,
  },
})

export const SponsorStatementStep: ILenderApplicationEditor = {
  options: {
    title: 'LenderApplication.Flow.Sponsor.Statements.Title',
    canSkip: false,
  },
  component: observer(SponsorStatementEditor),
}
