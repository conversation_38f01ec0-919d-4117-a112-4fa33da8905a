import { Steps as $ } from './LenderApplicationSteps'
import { ILenderApplicationDraft } from './ILenderApplicationDraft'
import { Flow } from '../FlowController'
import { ILenderApplicationOptions } from './ILenderApplicationOptions'

export class LenderApplicationFlow {
  definition: Flow<ILenderApplicationDraft, ILenderApplicationOptions> = [
    /****************
     * Sponsor
     ****************/
    $.sponsor.loanOfficer,
    $.sponsor.personalInformation,
    $.sponsor.homeAddress,
    $.sponsor.maritalStatus,
    $.sponsor.statements,
    $.sponsor.citizenship,

    /****************
     * Business Entity
     ****************/
    $.businessEntity.entityInformation,
    {
      path: 'businessEntity.gotoReview',
      selector: () => $.review.review,
    },
    $.businessEntity.address,
    $.businessEntity.isCreatedForProject,
    $.businessEntity.dateIncorporated,
    $.businessEntity.representative,
    $.businessEntity.generalContractorName,
    $.businessEntity.bankAccountId,

    /****************
     * Current Project
     ****************/
    $.currentProject.hasMultipleProducts,
    $.currentProject.loanType,
    $.currentProject.mainAddress,
    $.currentProject.products,
    $.currentProject.loanPurpose,
    $.currentProject.loanTerm,
    $.currentProject.willCompletePermits,
    $.currentProject.originalPurchasePrice,
    $.currentProject.originalPurchaseDate,
    $.currentProject.payOffAmount,
    $.currentProject.subordinateDebtType,
    $.currentProject.subordinateDebtBalance,
    $.currentProject.financialDetails,

    /****************
     * Previous Projects
     ****************/
    $.previousProjects.projects,

    /****************
     * Review
     ****************/
    $.review.review,
  ]
}
