import React, { useCallback, useMemo, useState } from 'react'
import { StyleSheet, TouchableOpacity, View } from 'react-native'
import { useTranslation } from 'react-i18next'
import { GUEST_MOBILE_FLOWS } from '../../../../InvoiceDetails/utils/flows'
import ContactInformation from '../../../components/ContactInformation'
import Wrapper from '../../../../../Contractor/Wrapper'
import Button from '../../../components/Button'
import { makeGuestCardPayment } from '../../../requests/makeGuestCardPayment'
import { useResponsive } from '@linqpal/components/src/hooks'
import { PaymentMethod } from '../../../components/PaymentMethodsSection'
import SecurityNotice from '../../../../../../ui/molecules/SecurityNotice'
import { AddNewCreditCardFlow } from '../../../../InvoiceDetails/flows/AddNewCreditCardFlow'
import { BillingAddress } from '../../../components/BillingAdress'
import { currencyMask } from '../../../../../../utils/helpers/masking'
import { AdditionalInvoiceFields } from '../../../components/InvoicePanel'
import { PayTermsAndConditionsForIntegration } from '../../../../../Auth/TermsAndConditions'
import PaymentErrorModal from '../../../components/PaymentErrorModal'
import { TopBarGuestMobile } from '../../../components/TobBarGuestMobile'
import { BackIcon, LoginIcon } from '../../../../../../assets/icons'
import { IPayAsGuestMobileProps, IPaymentMethodData } from '../types'
import { getGuestPaymentMethod } from '../../../requests/getGuestPaymentMethod'
import { InputFieldNames } from '../../../components/ContactInformation/types'
import { InvoicePanelMobile } from '../../../components/InvoicePanelMobile'
import { formatPhoneNumberToInternational } from '../../../helpers/formatPhoneNumber'
import { PayWithCreditCardFlow_PROCESSING } from '../../../../InvoiceDetails/components/PayWithCreditCardFlow_PROCESSING'
import RootStore from '../../../../../../store'
import { PoweredByBluetape } from '../../../../../../ui/white-label-components/PoweredByBluetape'

const PayAsGuestMobile = ({
  invoice,
  paymentMethodData,
  setPaymentMethodData,
  onPaymentSuccess,
  contactDetails,
  setContactDetails,
  customer,
  onSignUp,
  onUserExists,
}: IPayAsGuestMobileProps) => {
  const { branding } = RootStore
  const { t } = useTranslation('global')
  const [isContactValid, setIsContactValid] = useState(false)
  const [phoneNumber, setPhoneNumber] = useState('')
  const [paymentLoading, setPaymentLoading] = useState(false)
  const [paymentError, setPaymentError] = useState<boolean>(false)
  const [paymentSuccess, setPaymentSuccess] = useState<boolean>(false)
  const [currentFlow, setCurrentFlow] = useState<GUEST_MOBILE_FLOWS>(
    GUEST_MOBILE_FLOWS.CONTACT_INFO,
  )

  const { mobileWidth } = useResponsive()

  const handleContactValidation = useCallback(
    (isValid, validPhone, formValues) => {
      setContactDetails(formValues)
      setIsContactValid(isValid)
      setPhoneNumber(validPhone)
    },
    [setContactDetails],
  )

  const handleContinue = () => {
    if (isContactValid) {
      if (!paymentMethodData) {
        setCurrentFlow(GUEST_MOBILE_FLOWS.ADD_NEW_PAYMENT_METHOD)
      } else {
        setCurrentFlow(GUEST_MOBILE_FLOWS.PAY_INVOICE)
      }
    }
  }

  const onAddCardPress = useCallback(() => {
    setCurrentFlow(GUEST_MOBILE_FLOWS.ADD_NEW_CREDIT_CARD)
  }, [])

  const handlePaymentMethodAdded = useCallback(
    (paymentMethodDetails: IPaymentMethodData) => {
      setPaymentMethodData(paymentMethodDetails)
      if (paymentMethodDetails) {
        setCurrentFlow(GUEST_MOBILE_FLOWS.PAY_INVOICE)
      } else setCurrentFlow(GUEST_MOBILE_FLOWS.ADD_NEW_PAYMENT_METHOD)
    },
    [setPaymentMethodData],
  )

  const onRemovePaymentMethod = useCallback(() => {
    setPaymentMethodData(null)
    setCurrentFlow(GUEST_MOBILE_FLOWS.ADD_NEW_PAYMENT_METHOD)
  }, [setPaymentMethodData])

  const handleBackPress = useCallback(() => {
    setCurrentFlow(GUEST_MOBILE_FLOWS.CONTACT_INFO)
  }, [])

  const onModalClose = useCallback(() => {
    if (paymentMethodData) {
      setCurrentFlow(GUEST_MOBILE_FLOWS.PAY_INVOICE)
    } else setCurrentFlow(GUEST_MOBILE_FLOWS.ADD_NEW_PAYMENT_METHOD)
  }, [paymentMethodData])

  const paymentMethodSubtitle = useMemo(() => {
    const accNum = paymentMethodData?.card?.last4 || ''
    return paymentMethodData?.card?.type === 'Debit'
      ? t('PaymentMethodsListing.debit-card', { accNum })
      : t('PaymentMethodsListing.credit-card', { accNum })
  }, [paymentMethodData, t])

  const handlePayment = useCallback(async () => {
    if (!contactDetails || !paymentMethodData?.bankAccount?.id) {
      console.error(
        'Payment cannot be processed: Missing contact details or payment method',
      )
      return
    }
    setPaymentLoading(true)
    const response = await makeGuestCardPayment({
      user: {
        ...contactDetails,
        phone: formatPhoneNumberToInternational(contactDetails.phone),
      },
      company: {
        name: contactDetails.businessName ?? '',
        isBusiness: !!contactDetails.isBusiness,
      },
      payment: paymentMethodData.payment,
      bankAccountId: paymentMethodData.bankAccount.id,
      invoiceId: invoice._id,
    })
    setPaymentLoading(false)

    if (response?.result === 'ok') {
      console.log('Payment Successful')

      localStorage.setItem('contactDetails', JSON.stringify(contactDetails))
      localStorage.setItem(
        'paymentMethodData',
        JSON.stringify(paymentMethodData),
      )
      setPaymentSuccess(true)
    } else {
      if (response?.code === 'auth/user-already-exists') {
        console.error('Payment Failed')
        onUserExists({
          type: 'email',
          login: contactDetails.email,
          isNewUser: false,
        })
      } else {
        setPaymentError(true)
      }
    }
  }, [contactDetails, paymentMethodData, invoice, onUserExists])

  const footerButtons = useMemo(() => {
    if (currentFlow === GUEST_MOBILE_FLOWS.CONTACT_INFO) {
      return (
        <View style={styles.continueContainer}>
          <Button
            onPress={handleContinue}
            disabled={!isContactValid}
            label={t('integrations.buttons.continue')}
          />
        </View>
      )
    } else if (currentFlow === GUEST_MOBILE_FLOWS.PAY_INVOICE) {
      return (
        <View
          style={[
            styles.continueContainer,
            { height: 127, paddingVertical: 12, marginBottom: 6 },
          ]}
        >
          <PayTermsAndConditionsForIntegration />
          <Button
            onPress={handlePayment}
            disabled={!isContactValid || !paymentMethodData || paymentLoading}
            label={t('integrations.buttons.pay-invoice')}
          />
        </View>
      )
    }
    return null
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentFlow, isContactValid, t])

  const buttonLeft = useMemo(
    () =>
      currentFlow === GUEST_MOBILE_FLOWS.CONTACT_INFO ? (
        <></>
      ) : (
        <TouchableOpacity onPress={handleBackPress}>
          <BackIcon width={40} height={40} />
        </TouchableOpacity>
      ),
    [currentFlow, handleBackPress],
  )

  const additionalFields = useMemo(
    () => [
      ...(isContactValid && paymentMethodData
        ? [
            {
              label: t('integrations.invoice.invoice-amount'),
              value: currencyMask(invoice.total_amount),
            },
            {
              label: t('integrations.invoice.service-fee'),
              value: currencyMask(paymentMethodData.payment?.fee),
            },
            {
              label: t('integrations.invoice.total'),
              value: currencyMask(
                Number(invoice.total_amount) +
                  Number(paymentMethodData.payment?.fee),
              ),
              labelStyle: styles.totalText,
              valueStyle: styles.totalText,
            },
          ]
        : []),
    ],
    [isContactValid, paymentMethodData, invoice, t],
  )

  return (
    <Wrapper
      containerStyle={{
        alignSelf: 'center',
      }}
      toolbar={
        <TopBarGuestMobile
          buttonRight={
            <TouchableOpacity onPress={onSignUp}>
              <LoginIcon width={40} height={40} />
            </TouchableOpacity>
            /*<TouchableOpacity
              onPress={() => Linking.openURL('https://google.com')}
            >
              <MobileAttachment width={40} height={40} />
            </TouchableOpacity>*/
          }
          buttonLeft={buttonLeft}
        />
      }
      contentContainerStyle={{ height: '100%' }}
      footer={
        <>
          {footerButtons}
          {!!branding && (
            <View
              style={[
                styles.poweredByContainer,
                !footerButtons && styles.poweredByContainerBorders,
              ]}
            >
              <PoweredByBluetape />
            </View>
          )}
        </>
      }
      styleWidth={'100%'}
    >
      <View
        style={[
          styles.container,
          { width: mobileWidth > 576 ? '100%' : mobileWidth - 40 },
        ]}
      >
        <InvoicePanelMobile invoice={invoice} />

        {currentFlow === GUEST_MOBILE_FLOWS.CONTACT_INFO && (
          <View key="contact-info" style={styles.section}>
            <ContactInformation
              onValidationChange={handleContactValidation}
              hideHeaderTitle={true}
              initialValues={{
                [InputFieldNames.FIRST_NAME]:
                  customer?.firstName ?? contactDetails?.firstName,
                [InputFieldNames.LAST_NAME]:
                  customer?.lastName ?? contactDetails?.lastName,
                [InputFieldNames.EMAIL]:
                  customer?.email ?? contactDetails?.email,
                [InputFieldNames.PHONE]: (
                  customer?.phone ??
                  contactDetails?.phone ??
                  ''
                )
                  .replace(/\D/g, '')
                  .slice(-10),
                [InputFieldNames.BUSINESS_NAME]:
                  customer?.businessName ?? contactDetails?.businessName,
                [InputFieldNames.IS_BUSINESS]: isContactValid
                  ? contactDetails?.isBusiness
                  : true,
              }}
            />
          </View>
        )}
        {currentFlow === GUEST_MOBILE_FLOWS.ADD_NEW_PAYMENT_METHOD && (
          <View key="add-credit-card" style={styles.section}>
            <PaymentMethod
              paymentMethod={null}
              onAddCardPress={onAddCardPress}
              onRemovePaymentMethod={onRemovePaymentMethod}
              sectionTitleStyle={styles.paymentMethodTitle}
              phoneNumber={phoneNumber}
            />
            <SecurityNotice />
          </View>
        )}
        {currentFlow === GUEST_MOBILE_FLOWS.ADD_NEW_CREDIT_CARD && (
          <AddNewCreditCardFlow
            onClose={onModalClose}
            onSuccess={handlePaymentMethodAdded}
            customRoute={getGuestPaymentMethod}
            invoiceId={invoice._id}
            phoneNumber={phoneNumber}
            isGuest={true}
          />
        )}
        {currentFlow === GUEST_MOBILE_FLOWS.PAY_INVOICE && (
          <>
            <AdditionalInvoiceFields
              additionalFields={additionalFields}
              rowStyle={styles.row}
            />
            <View key="pay-invoice" style={styles.section}>
              <PaymentMethod
                paymentMethod={paymentMethodData}
                onAddCardPress={onAddCardPress}
                onRemovePaymentMethod={onRemovePaymentMethod}
                sectionTitleStyle={styles.paymentMethodTitle}
                phoneNumber={phoneNumber}
              />
              {paymentMethodData?.bankAccount?.billingAddress && (
                <BillingAddress
                  billingAddress={paymentMethodData.bankAccount.billingAddress}
                />
              )}
              <SecurityNotice />
            </View>
          </>
        )}
        {paymentError && (
          <PaymentErrorModal
            onClose={() => {
              setPaymentError(false)
            }}
            visible={true}
            isMobile
          />
        )}
        {paymentSuccess && (
          <PayWithCreditCardFlow_PROCESSING
            invoiceTotalAmount={paymentMethodData?.payment?.totalAmount ?? 0}
            invoiceCompanyName={invoice?.company?.name ?? ''}
            paymentMethodName={paymentMethodData?.bankAccount?.name ?? ''}
            subtitle={paymentMethodSubtitle}
            onClose={onPaymentSuccess}
          />
        )}
      </View>
    </Wrapper>
  )
}

const styles = StyleSheet.create({
  section: {
    marginTop: 8,
    marginBottom: 20,
  },
  container: {
    display: 'flex',
    justifyContent: 'center',
    marginTop: 16,
    maxWidth: '100%',
    width: 350,
  },
  continueContainer: {
    borderTopColor: '#E6EBEE',
    borderTopWidth: 1,
    height: 80,
    justifyContent: 'center',
    width: '100%',
    paddingHorizontal: 20,
    gap: 10,
  },
  paymentMethodTitle: {
    fontSize: 16,
  },
  totalText: {
    fontWeight: '700',
    fontSize: 14,
    lineHeight: 21,
  },
  row: {
    marginTop: 0,
  },
  poweredByContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'flex-end',
    marginTop: -5,
    marginBottom: 18,
    marginRight: 18,
  },
  poweredByContainerBorders: {
    borderTopWidth: 1,
    paddingTop: 10,
    borderTopColor: '#E6EBEE',
  },
})

export default PayAsGuestMobile
