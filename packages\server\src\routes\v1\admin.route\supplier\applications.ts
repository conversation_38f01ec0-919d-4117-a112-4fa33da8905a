import {
  AccountingSettings,
  BankAccount,
  Company,
  CustomerAccount,
  Draft,
  getCardPricingPackage,
  getLoanPricingPackage,
  getLoanPricingPackages,
  getPaymentPlans,
  User,
  UserRole,
} from '@linqpal/common-backend'
import { CompanyStatus, exceptions } from '@linqpal/models'
import { currentZDate } from '@linqpal/models/src/helpers/date'
import mongoose, { PipelineStage } from 'mongoose'
import multer from 'multer'
import controllers from '../../../../controllers'
import { listFiles, uploadFile } from '../../../../controllers/file.controller'
import { adminRequired } from '../../../../services/auth.service'
import { Request, Response } from 'express'
import { IBankAccount } from '@linqpal/common-backend/src/models/types'
import { escapeRegExp } from 'lodash'
import { FactoringService } from '@linqpal/common-backend/src/services/factoring/factoring.service'
import { ICreateCredit } from '@linqpal/models/src/types/routes'
import { ApplicationDraftType } from '@linqpal/models/src/helpers/draft/models/ApplicationDraftType'

export default {
  get: async (req: Request, res: Response) => {
    const { status } = req.query
    const page = parseInt((req.query?.page as string) || '0')
    const limit = parseInt((req.query?.limit as string) || '0')

    const search = (req.query?.search as string) || ''
    const searchRegex = RegExp(escapeRegExp(search))

    const currentPage = (req.query?.currentPage as string) || ''

    let matchingSupplierIds: string[] = []
    if (search.length >= 3 && currentPage === 'suppliers') {
      const matchingCompaniesPipeline = [
        {
          $match: {
            $or: [
              { name: { $regex: searchRegex, $options: 'i' } },
              { legalName: { $regex: searchRegex, $options: 'i' } },
            ],
          },
        },
        {
          $lookup: {
            from: UserRole.collection.name,
            as: 'userRole',
            let: { companyId: { $toString: '$_id' } },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $eq: ['$company_id', '$$companyId'],
                  },
                },
              },
            ],
          },
        },
        { $unwind: '$userRole' },
        {
          $lookup: {
            from: User.collection.name,
            as: 'user',
            localField: 'userRole.sub',
            foreignField: 'sub',
          },
        },
        { $unwind: '$user' },
        {
          $lookup: {
            from: CustomerAccount.collection.name,
            as: 'customerAccount',
            let: { userEmail: '$user.email' },
            pipeline: [
              {
                $match: {
                  $expr: { $eq: ['$email', '$$userEmail'] },
                },
              },
            ],
          },
        },
        { $unwind: '$customerAccount' },
        {
          $group: {
            _id: '$customerAccount.company_id',
          },
        },
      ]
      const matchingCompanies = await Company.aggregate(
        matchingCompaniesPipeline,
      )
      matchingSupplierIds = matchingCompanies.map((comp) => comp._id.toString())
    }

    const pipeline: PipelineStage[] = [
      { $addFields: { id: { $toString: '$_id' } } },
      {
        $match: {
          status:
            status === 'rejected'
              ? CompanyStatus.Rejected
              : status === 'active'
              ? CompanyStatus.Approved
              : status === 'review'
              ? { $in: [CompanyStatus.Validated, CompanyStatus.Applied] }
              : {
                  $nin: [
                    CompanyStatus.Approved,
                    CompanyStatus.Validated,
                    CompanyStatus.Applied,
                    CompanyStatus.Rejected,
                  ],
                },
        },
      },
      // apps yet to be processed in new BO should be hidden in old BO, approved ones should stay visible
      ...(currentPage !== 'suppliers' && status !== 'active'
        ? [
            {
              $match: {
                newDECreditApplicationExecutionArn: { $exists: false },
              },
            },
          ]
        : []),
      {
        $lookup: {
          from: Draft.collection.name,
          as: 'document',
          localField: 'id',
          foreignField: 'company_id',
          pipeline: [
            {
              $match: {
                $or: [
                  { type: ApplicationDraftType.GeneralApplication },
                  { type: ApplicationDraftType.SupplierApplication },
                ],
              },
            },
            // ensure general_application is used if available, otherwise supplier_application
            {
              $addFields: {
                priority: {
                  $cond: {
                    if: {
                      $eq: ['$type', ApplicationDraftType.GeneralApplication],
                    },
                    then: 1,
                    else: 2,
                  },
                },
              },
            },
            { $sort: { priority: 1 } },
            { $limit: 1 },
          ],
        },
      },
      { $unwind: '$document' },
      {
        $addFields: {
          authorized: {
            // to support legacy supplier applications schema
            $filter: {
              input: '$document.data.ownership.items',
              as: 'item',
              cond: { $eq: ['$$item.content.authorized', true] },
            },
          },
          ownerFirstName: getDraftField('businessOwner', 'firstName'),
          ownerLastName: getDraftField('businessOwner', 'lastName'),
          ownerEmail: getDraftField('businessOwner', 'email'),
        },
      },
      { $unwind: { path: '$authorized', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'users',
          as: 'user',
          localField: 'document.sub',
          foreignField: 'sub',
        },
      },
      { $unwind: { path: '$user', preserveNullAndEmptyArrays: true } },
      { $addFields: { document: { id: { $toString: '$document._id' } } } },
      {
        $lookup: {
          from: AccountingSettings.collection.name,
          as: 'accountingSettings',
          localField: 'id',
          foreignField: 'company_id',
        },
      },
      {
        $addFields: {
          bankId: {
            $convert: {
              input: '$draft.bank_details.id',
              to: 'objectId',
              onError: null,
            },
          },
        },
      },
      {
        $lookup: {
          from: BankAccount.collection.name,
          as: 'bankSelectedInApp',
          localField: 'bankId',
          foreignField: '_id',
        },
      },
      {
        $unwind: {
          path: '$bankSelectedInApp',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          document: '$document',
          updatedAt: '$updatedAt',
          business: { $ifNull: ['$legalName', '$name'] },
          dba: '$name',
          status: { $ifNull: ['$status', 'incomplete'] },
          draft: 1,
          cardPackageKey: { $ifNull: ['$settings.cardPricingPackageId', null] },
          loanPackageKey: { $ifNull: ['$settings.loanPricingPackageId', null] },
          loanPlans: '$settings.loanPlans',
          name: [
            {
              $ifNull: [
                '$ownerFirstName',
                {
                  $ifNull: ['$authorized.content.firstName', '$user.firstName'],
                },
              ],
            },
            {
              $ifNull: [
                '$ownerLastName',
                { $ifNull: ['$authorized.content.lastName', '$user.lastName'] },
              ],
            },
          ],
          email: {
            $ifNull: [
              '$ownerEmail',
              { $ifNull: ['$authorized.content.email', '$user.email'] },
            ],
          },
          login: '$user.login',
          phone: { $ifNull: ['$authorized.content.phone', '$user.phone'] },
          bankAccounts: 1,
          settings: 1,
          accountingSettings: { $first: '$accountingSettings' },
          bankSelectedInApp: 1,
          customerAccounts: 1,
        },
      },
      { $sort: { 'document.submitDate': -1, updatedAt: -1 } },
      {
        $addFields: {
          companyIdString: {
            $convert: {
              input: '$_id',
              to: 'string',
              onError: null,
            },
          },
        },
      },
    ]
    if (search) {
      pipeline.push({
        $match: {
          $or: [
            { business: { $regex: searchRegex, $options: 'i' } },
            { dba: { $regex: searchRegex, $options: 'i' } },
            { login: { $regex: searchRegex, $options: 'i' } },
            { email: { $regex: searchRegex, $options: 'i' } },
            { 'name.0': { $regex: searchRegex, $options: 'i' } },
            { 'name.1': { $regex: searchRegex, $options: 'i' } },
            { companyIdString: search },
            ...(matchingSupplierIds.length > 0
              ? [
                  {
                    _id: {
                      $in: mongoose.trusted(
                        matchingSupplierIds.map(
                          (id) => new mongoose.Types.ObjectId(id),
                        ),
                      ),
                    },
                  },
                ]
              : []),
          ],
        },
      })
    }
    const total =
      (
        await Company.aggregate([
          ...pipeline,
          {
            $count: 'count',
          },
        ])
      )[0]?.count || 0

    if (page > 0 && limit > 0) {
      pipeline.push({
        $skip: (page - 1) * limit,
      })
    }
    if (limit > 0) {
      pipeline.push({
        $limit: limit,
      })
    }
    pipeline.push({
      $lookup: {
        from: 'bankaccounts',
        as: 'bankAccounts',
        let: { accounts_id: '$bankAccounts' },
        pipeline: [
          {
            $match: {
              $expr: {
                $gte: [{ $indexOfArray: ['$$accounts_id', '$_id'] }, 0],
              },
            },
          },
          {
            $project: {
              name: 1,
              accountholderName: 1,
              finicity: 1,
              routingNumber: 1,
              accountNumber: 1,
              isManualEntry: 1,
              paymentMethodType: 1,
              isPrimary: 1,
              status: 1,
              accountType: 1,
              accountName: 1,
            },
          },
        ],
      },
    })
    let items = await Company.aggregate(pipeline)
    items = items.map((company) => ({
      ...company,
      document: controllers.user.secureDocumentFields(company.document),
      finicityAccountAdded: company.bankAccounts.some(
        (d: IBankAccount) => !!d.finicity?.accountId || !!d.plaid,
      ),
      accountHolderNameMatchesOwner: company.bankAccounts.some(
        (d: IBankAccount) => !!d.finicity?.accountId || !!d.plaid,
      )
        ? checkFinicityNameMatch(company.bankAccounts, company.document)
        : 'Finicity / Plaid account not added',
    }))

    const uniqueLoanPackageKeys = [
      ...new Set(items.map((item) => item.loanPackageKey).filter(Boolean)),
    ]

    const loanPackagesMap = new Map(
      await Promise.all(
        uniqueLoanPackageKeys.map(async (key) => {
          const pack = await getLoanPricingPackage(key)
          return [key, pack] as const
        }),
      ),
    )

    items.forEach((item) => {
      if (item.loanPackageKey) {
        item.loanPricingPackage = loanPackagesMap.get(item.loanPackageKey)
      }
    })

    res.send({ items, total })
  },
}

export const checkFinicityNameMatch = (
  bankAccounts: IBankAccount[],
  draft: any,
) => {
  const owners =
    // pre-unified app
    draft.data?.ownership?.items ??
    // unified app with co-owners
    draft.data?.coOwnerInfo?.items ??
    []

  const businessOwner = draft.data?.businessOwner?.items
  owners.push({
    content: {
      firstName: businessOwner?.find(
        (item: any) => item.identifier === 'firstName',
      )?.content,
      lastName: businessOwner?.find(
        (item: any) => item.identifier === 'lastName',
      )?.content,
    },
  })

  // pre-co-owners unified app co-owner
  const coOwner = businessOwner?.find(
    (item: any) => item.identifier === 'ownerName',
  )?.content

  if (coOwner) {
    owners.push({
      content: {
        firstName: coOwner?.firstname,
        lastName: coOwner?.lastname,
      },
    })
  }

  return bankAccounts.some((account) =>
    owners
      .filter((item: any) => item.content?.firstName && item.content?.lastName)
      .map((item: any) => `${item.content.firstName} ${item.content.lastName}`)
      .some(
        (ownerName: any) =>
          ownerName.toLowerCase() === account.accountholderName?.toLowerCase(),
      ),
  )
}

export const kycValidate = {
  post: async (req: Request, res: Response) => {
    const company = await Company.findOne({ _id: req.body.id })
    if (company) {
      company.status = CompanyStatus.Validated
      await company.save()
    }
    res.send({ result: 'ok' })
  },
}

export const sendBack = {
  post: async (req: Request, res: Response) => {
    const company = await Company.findOne({ _id: req.body.id })
    if (company) {
      company.status = CompanyStatus.New
      await company.save()
    }
    res.send({ result: 'ok' })
  },
}

export const reject = {
  post: async (req: Request, res: Response) => {
    const company = await Company.findOne({ _id: req.body.id })
    if (company) {
      company.settings.approveRead = false
      company.status = CompanyStatus.Rejected
      await company.save()
    }
    res.send({ result: 'ok' })
  },
}

export const approve = {
  post: async (req: Request, res: Response) => {
    const company = await Company.findOne({ _id: req.body.id })
    if (company) {
      // pricing package is mandatory to approve the application
      const pricingPackageRequired =
        await controllers.company.pricingPackageRequired(company)
      if (pricingPackageRequired) {
        throw new exceptions.LogicalError('Pricing package is not selected')
      }

      const draft = await Draft.findOne({
        company_id: company._id,
        type: 'general_application',
      })
      const business = draft?.data?.get('businessInfo')?.get('businessName')
      if (business) {
        company.name = business.dba || business.legalName
        company.legalName = business.legalName
      }

      company.settings.acceptAchPayment = true
      company.status = CompanyStatus.Approved

      await company.save()
    }
    await BankAccount.updateMany(
      {
        _id: mongoose.trusted({
          $in: company?.bankAccounts,
        }),
      },
      {
        $set: {
          status: 'verified',
        },
      },
    )

    /**
     * Get owner info and add conversion for Supplier.
     */
    const [
      {
        user: [userObj],
      },
    ] = await Company.aggregate([
      { $addFields: { id: { $toString: '$_id' } } },
      {
        $match: {
          id: req.body.id,
        },
      },
      {
        $lookup: {
          from: UserRole.collection.name,
          as: 'roles',
          localField: 'id',
          foreignField: 'company_id',
        },
      },
      {
        $match: {
          'roles.role': 'Owner',
        },
      },
      {
        $lookup: {
          from: User.collection.name,
          as: 'user',
          localField: 'roles.sub',
          foreignField: 'sub',
        },
      },
    ]).session(req.session)

    const user = await User.findOne({ sub: userObj.sub }).session(req.session)
    if (!user) {
      throw new Error('User not found')
    }

    await controllers.user.conversion({
      user,
      session: req.session,
      conversionObj: {
        supplierApplicationApproved: currentZDate(),
      },
    })

    res.send({ result: 'ok' })
  },
}

export const edit = {
  post: async (req: Request, res: Response) => {
    const { id, ...document } = req.body
    if (
      !['supplier_application', 'general_application'].includes(document.type)
    ) {
      throw new exceptions.LogicalError(
        `Invalid document type. Expected "supplier_application", but got "${document.type}"`,
      )
    }
    const company = await Company.findOne({ _id: document.company_id })
    if (company?.status !== 'applied') {
      throw new exceptions.LogicalError('You cannot edit draft at this stage')
    }
    const doc = await Draft.findOne({
      $or: [{ type: 'supplier_application' }, { type: 'general_application' }],
      company_id: document.company_id,
    })
    if (!doc) throw new exceptions.LogicalError('Document not found')
    const val = controllers.user.validations[document.type]
    if (document.data && val) {
      await Promise.all(
        Object.keys(document.data).map(async (group) => {
          const items = document.data[group]?.items || []
          items.length > 0 &&
            (await Promise.all(
              items.map(async (item: any) => {
                await controllers.user.encryptValidate(
                  item.content,
                  item.identifier,
                  val,
                  (doc.data[group as keyof typeof doc.data] as any)?.items ||
                    [],
                )
              }),
            ))
        }),
      )
    }
    doc.data = document.data
    await doc.save()
    res.send({ result: 'ok' })
  },
}

export const upload = {
  middlewares: { pre: [adminRequired, multer().any()] },
  post: async (req: Request, res: Response) => {
    console.log(req.query)
    // TODO fix this hack
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    req.company = { _id: req.query.companyId }
    await uploadFile(req, res, 'supplier_applications')
  },
}
export const uploads = {
  get: async (req: Request, res: Response) => {
    console.log(req.query)
    const items = await listFiles(
      'supplier_applications',
      req.query.companyId as string,
    )
    res.send({ items })
  },
}

export const cardPricingPackage = {
  get: async (req: Request, res: Response) => {
    const pricingPackage = await getCardPricingPackage({ onlyActive: true })
    res.send({ result: 'ok', pricingPackage })
  },
}

export const loanPricingPackage = {
  get: async (req: Request, res: Response) => {
    let { individual, status } = req.query
    status = status && String(status)
    const individualQueryValue =
      individual === 'true' ? true : individual === 'false' ? false : undefined
    const packages = await getLoanPricingPackages({
      status,
      individual: individualQueryValue,
    })
    const loanPricingPackages = await Promise.all(
      packages.map(async (p) => {
        if (p.individual) {
          const company = await Company.findById(p.name)
          return {
            ...p.toObject({ virtuals: true }),
            company: company?.toObject({ virtuals: true }) || null,
          }
        }
        return p
      }),
    )
    const total = loanPricingPackages.length
    res.send({ result: 'ok', loanPricingPackages, total })
  },
}

export const loanPaymentPlan = {
  get: async (req: Request, res: Response) => {
    const plans = await getPaymentPlans()
    res.send({ result: 'ok', data: plans })
  },
}

export const isArAdvanceAvailable = {
  get: async (req: Request, res: Response) => {
    const companyId = req.query.id as string
    if (!companyId) {
      throw new exceptions.LogicalError('Company id was not provided.')
    }
    const creditInfo = await FactoringService.getArAdvanceCreditInfo(companyId)

    res.send({
      result: 'ok',
      isArAdvanceAvailable: !!creditInfo?.limit,
      creditLimit: creditInfo?.limit || 0,
    })
  },
}

export const getArAdvanceCreditInfo = {
  get: async (req: Request, res: Response) => {
    const companyId = req.query.id as string

    if (!companyId) throw new Error('companyId is required')

    const creditInfo = await FactoringService.getArAdvanceCreditInfo(companyId)

    res.send({
      result: 'ok',
      arAdvanceCredit: creditInfo,
    })
  },
}

export const createArAdvanceCompanyCredit = {
  post: async (req: Request, res: Response) => {
    const createCreditDto: ICreateCredit = req.body
    const userId = req.user!.id

    const arAdvanceCredit = await FactoringService.createArAdvanceCompanyCredit(
      userId,
      createCreditDto,
    )

    res.send({
      result: 'ok',
      arAdvanceCredit,
    })
  },
}

export function getDraftField(section: string, field: string) {
  return {
    $getField: {
      field: 'content',
      input: {
        $first: {
          $filter: {
            input: `$document.data.${section}.items`,
            as: 'item',
            cond: {
              $eq: ['$$item.identifier', field],
            },
          },
        },
      },
    },
  }
}
