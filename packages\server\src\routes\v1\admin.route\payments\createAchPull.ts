import { cbw, Operation } from '@linqpal/common-backend'
import {
  IBankAccount,
  ICompany,
} from '@linqpal/common-backend/src/models/types'
import {
  OPERATION_STATUS,
  OPERATION_TYPES,
} from '@linqpal/models/src/dictionaries'
import moment from 'moment'
import { adminRequired } from '../../../../services/auth.service'
import transactional from '../../../../services/transactional.service'
import { NextFunction, Request, Response } from 'express'

interface ReqBody {
  company: ICompany
  bankAccount: IBankAccount
  amount: number
  reason?: string | null
}

// noinspection JSUnusedGlobalSymbols
export const createAchPull = {
  middlewares: {
    pre: [adminRequired, ...transactional.pre],
    post: transactional.post,
  },
  async post(
    req: Request<unknown, unknown, ReqBody>,
    res: Response,
    next: NextFunction,
  ) {
    const customer = await cbw.getRecipient(
      req.body.company._id.toString(),
      req.body.bankAccount._id?.toString() || null,
    )

    const amount = req.body.amount

    const [operation] = await Operation.create(
      [
        {
          amount,
          date: moment().toDate(),
          type: OPERATION_TYPES.ACH.PULL,
          status: OPERATION_STATUS.PLACED,
          metadata: { payer_id: req.body.company._id },
        },
      ],
      { session: req.session },
    )

    await cbw.achPull(
      operation,
      {
        amount,
        fee: 0.0,
        currency: 'USD',
        transactionDateTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        reason: req.body.reason || 'ACH PULL',
        ...customer,
      },
      req.session,
    )

    return next()
  },
}
