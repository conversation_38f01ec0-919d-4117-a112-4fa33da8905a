import { dictionaries, exceptions, Item } from '@linqpal/models'
import { Draft, Invoice, Operation } from '../../models'
import { cancelInvoice } from './cancel'
import {
  findBuilderAccount,
  findBuilderByAccountId,
} from './findBuilderAccount'
import { checkIfCreditAppProcessing } from './checkIfCreditAppProcessing'
import {
  firstNotifications,
  getPaymentNotificationReceivers,
  getSms,
  sendInvoiceNotifications,
  sendInvoicePaymentNotification,
} from './notifications'
import { saveInvoice } from './save'
import { startDecisionEngine } from './startDecision'
import { addProjectToInvoices } from './addProjectToInvoices'
import { v4 } from 'uuid'
import { AttachmentData } from '@sendgrid/helpers/classes/attachment'
import { IInvoice } from '../../models/types'
import AwsService from '../aws.service'
import { Logger } from '../logger/logger.service'
import { linkInvoiceToCustomer } from './linkInvoiceToCustomer'
import {
  checkSupplierExists,
  extractNoSupplierDetails,
} from './extractNoSupplierDetails'
import { handleNoSupplierFlow } from './handleNoSupplierFlow'
import mongoose from 'mongoose'
import { invoiceSchemaStatus } from '@linqpal/models/src/dictionaries'
import { IReceivable, LmsFundingSource } from '../lms.service'
import { toCurrency } from '@linqpal/models/src/helpers/formatter'
import { invoiceService } from '../invoice.dotnet.service'
import { Slack } from '../slack.service'
import { customErrorToString } from '../../helpers/SnsEventBuilder'
import moment from 'moment-timezone'

const logger = new Logger({
  module: 'bluetape.services',
  subModule: 'invoicesService',
})

async function saveOwnerData(company_id: string, user: any) {
  if (!company_id) {
    throw new exceptions.LogicalError('company not found')
  }
  logger.info(`getting draft for company ${company_id}`)

  const document = await Draft.findOne({
    type: 'general_application',
    company_id: company_id,
  })

  logger.info(
    `got draft ${document?._id} of ${document?.type} type for company ${company_id}`,
  )

  fillOwnerData(document, user)
  logger.info(
    `ownerData of draft ${document?._id} of ${document?.type} type for company ${company_id} was updated`,
  )

  if (document) {
    document.version = (document.version || 0) + 1
  }

  await document?.save()
  return
}

function fillOwnerData(document: any, user: any) {
  const items = ['firstName', 'lastName', 'email', 'phone']

  const businessOwnerGroup = document?.data?.get('businessOwner')

  if (!businessOwnerGroup) {
    document?.data?.set('businessOwner', {
      group: 'businessOwner',
      title: 'BusinessOwner',
      items: [],
    })
  }

  items.forEach((i) => {
    const exists = document?.data
      ?.get('businessOwner')
      ?.items.find((item: any) => item.identifier === i)

    if (!exists) {
      document?.data
        ?.get('businessOwner')
        ?.items.push(
          Item.create({ identifier: i, filled: true, content: user[i] ?? '' }),
        )
    }
  })

  // generate ownerId to match LoC approverId and simplify future migration
  const ownerId = document?.data
    ?.get('businessOwner')
    ?.items.find((item: any) => item.identifier === 'id')

  if (!ownerId) {
    document?.data
      ?.get('businessOwner')
      ?.items.push(
        Item.create({ identifier: 'id', filled: true, content: v4() }),
      )
  }
}

async function getAttachment(
  invoice: IInvoice,
): Promise<AttachmentData | undefined> {
  let attachment: AttachmentData | undefined = undefined

  if (invoice.invoice_document) {
    const fileUrl = await AwsService.getPreSignedUrl({
      key: invoice.invoice_document,
      method: 'get',
    })

    const content = await AwsService.getS3FileByUrl(fileUrl)

    attachment = {
      content,
      filename: invoice.invoice_document,
      type: 'application/pdf',
      disposition: 'attachment',
    }
  }

  return attachment
}

async function checkInvoiceIsPaid(
  invoiceId: string,
): Promise<{ isPaid: boolean; status: string | null }> {
  const operation = await Operation.findOne({
    owner_id: invoiceId,
    status: mongoose.trusted({
      $ne: dictionaries.OPERATION_STATUS.DECLINED,
    }),
  })

  if (
    operation &&
    [
      dictionaries.OPERATION_STATUS.SUCCESS,
      dictionaries.OPERATION_STATUS.PROCESSING,
    ].includes(operation.status)
  ) {
    return { isPaid: true, status: operation.status }
  }

  return { isPaid: false, status: null }
}

async function checkInvoiceIsCanceled(
  invoiceId: string,
): Promise<{ isCanceled: boolean }> {
  const invoice = await Invoice.findById(invoiceId)

  return {
    isCanceled: !!invoice && invoice.status === invoiceSchemaStatus.cancelled,
  }
}

async function addInvoiceWatermarks(
  invoices: IInvoice[],
  loanReceivables: IReceivable[],
  fundingSource: LmsFundingSource,
) {
  if (fundingSource !== LmsFundingSource.Raistone) {
    logger.info(`skip watermarking for ${fundingSource} funding source`)
    return
  }

  const invoiceIds = invoices.map((i) => i.id)
  const receivableIds = loanReceivables.map((r) => r.id)

  logger.info({ invoiceIds, receivableIds }, 'adding watermarks to invoices')

  for (const invoice of invoices) {
    if (
      !invoice.invoice_document ||
      !invoice.invoice_document.toLowerCase().endsWith('.pdf')
    ) {
      logger.info(`skipping watermark for ${invoice.id}: no PDF document found`)
      continue
    }

    try {
      const watermarkLines = ['Updated BlueTape Due Date:']

      // Group receivables by expectedDate using Map
      const groupedReceivables = new Map<string, number>()

      for (const receivable of loanReceivables) {
        const dueDate = receivable?.expectedDate || ''
        const amount = receivable?.expectedAmount ?? 0

        const totalAmount = groupedReceivables.get(dueDate) || 0
        groupedReceivables.set(dueDate, totalAmount + amount)
      }

      // Add watermark lines for each unique due date
      for (const [dueDate, amount] of groupedReceivables) {
        const formattedDate = dueDate
          ? moment.utc(dueDate).format('MM/DD/YYYY')
          : ''

        watermarkLines.push(
          `Payment due date: ${formattedDate} Amount Due: ${toCurrency(
            amount,
          )}`,
        )
      }

      logger.info(
        { invoiceId: invoice.id, watermarkLines },
        'adding watermark to invoice',
      )

      await invoiceService.addWatermark(invoice.id, watermarkLines)
    } catch (e: any) {
      logger.error({ e, invoiceId: invoice.id }, 'failed invoice watermarking')

      await Slack.notifyError(
        'loanCreation',
        `Invoice ${invoice.id} watermarking failed`,
        customErrorToString(e),
      )
    }
  }

  logger.info('finish adding watermarks')
}

export const invoicesService = {
  findBuilderAccount,
  firstNotifications,
  getSms,
  saveInvoice,
  startDecisionEngine,
  sendInvoiceNotifications,
  sendInvoicePaymentNotification,
  cancelInvoice,
  findBuilderByAccountId,
  saveOwnerData,
  fillOwnerData,
  getAttachment,
  checkIfCreditAppProcessing,
  getPaymentNotificationReceivers,
  addProjectToInvoices,
  linkInvoiceToCustomer,
  extractNoSupplierDetails,
  handleNoSupplierFlow,
  checkSupplierExists,
  checkInvoiceIsPaid,
  checkInvoiceIsCanceled,
  addInvoiceWatermarks,
}
