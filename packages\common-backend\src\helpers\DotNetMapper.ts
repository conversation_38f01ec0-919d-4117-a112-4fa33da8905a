import { CriticalError } from '@linqpal/models/src/types/exceptions'
import { EPaymentType } from '../services/payment/types'
import { EInvoiceType } from '@linqpal/models'
import { PayableType } from '../services/onBoarding/types'

export class DotNetMapper {
  static invoiceTypeToEPaymentType(type: EInvoiceType) {
    switch (type) {
      case EInvoiceType.INVOICE:
        return EPaymentType.invoice
      case EInvoiceType.QUOTE:
        return EPaymentType.quote
      case EInvoiceType.SALE_ORDER:
        return EPaymentType.sales_order
      default:
        throw new CriticalError(
          'Unknown invoice type for mapping to EPaymentType enum',
          { type },
        )
    }
  }

  static invoiceTypeToPayableType(type: EInvoiceType): PayableType {
    switch (type) {
      case EInvoiceType.INVOICE:
        return PayableType.Invoice
      case EInvoiceType.QUOTE:
        return PayableType.Quote
      case EInvoiceType.SALE_ORDER:
        return PayableType.SalesOrder
      default:
        throw new Error(`unable to map ${type} to PayableType`)
    }
  }
}
