import { createLoan } from './fixtures/bluetape_data'
import {
  BankAccount,
  crypt,
  initCbwApiRequester,
  makeLoanPayment,
  Operation,
  Transaction,
} from '@linqpal/common-backend'
import { dictionaries } from '@linqpal/models'
import chai from 'chai'
import {
  beforeEachMockEncryption,
  beforeEachMockSecretsManager,
} from './helper'
import * as sinon from 'sinon'
import mongoose, { ClientSession } from 'mongoose'
import moment from 'moment'
import { finalPayment } from '../loanpro/finalPayment'
import lambda from 'lambda-tester'
import nock from 'nock'
import {
  AdvancePaymentDay,
  PaymentDelayCode,
} from '@linqpal/models/src/dictionaries/factoring'
import { PricingProduct } from '@linqpal/models/src/dictionaries/pricingProduct'

chai.should()

describe('Loan', () => {
  beforeEachMockEncryption()
  beforeEachMockSecretsManager()

  describe('Repayment', () => {
    const tabapayBaseUrl = 'https://api.sandbox.tabapay.net:10443'
    process.env.LP_TABAPAY_CLIENT_ID = 'test-client-id'
    process.env.LP_TABAPAY_BEARER_TOKEN = 'test-token'
    process.env.LP_TABAPAY_SETTLEMENT_ACCOUNT_ID = 'test-settlement-account-id'
    process.env.LP_TABAPAY_MID_NO_CONVENIENCE_FEE = 'test-mid-0001'
    process.env.LP_TABAPAY_MID_WITH_CONVENIENCE_FEE = 'test-mid-0002'
    process.env.AZ_PAYMENT_SERVICE_QUEUE_CONNECTION_STRING =
      'Endpoint=sb://service-bus-namespace-payment-service-dev.servicebus.windows.net/;SharedAccessKeyName=payment-bus-queue-connection-auth-rule;SharedAccessKey=bdH5pRhWdbo/Kkdp0qkZQOTwM7pxDh8n3+ASbAgpYz4=;EntityPath=paymentrequestqueue-dev'
    process.env.AZ_PAYMENT_SERVICE_QUEUE_NAME = 'paymentrequestqueue-dev'

    function mockTabapayTransaction() {
      nock(tabapayBaseUrl)
        .post((uri: any) => uri.includes('transactions'))
        .reply(200, {
          transactionID: 'tabapay-transaction-id',
          status: 'COMPLETED',
        })
    }

    let cbwMock: sinon.SinonStub, session: ClientSession
    beforeEach(async () => {
      const cbwApi = initCbwApiRequester()
      cbwMock = sinon.stub(cbwApi, 'post').callsFake((path, { payload }) => {
        switch (payload.transactionType) {
          case 'TRANSACTION_STATUS':
            return Promise.resolve({
              transactionStatus: 'PROCESSED',
            })
          default:
            return Promise.resolve({
              transactionNumber: '123',
              transactionAmountCents: parseInt(
                payload.transactionAmount.amount,
              ),
              api: {
                reference: '123',
                dateTime: '',
                originalReference: payload.reference,
              },
              statusCode: '000',
              statusDescription: 'SUCCESS',
            })
        }
      })
      process.env.LP_CBW_FBO_IDENTIFICATION = '*********12'
      session = await mongoose.startSession()
    })
    afterEach(async () => {
      cbwMock.restore()
      session.endSession()
    })
    it('should make final invoice payment', async () => {
      const { app } = await createLoan(true, true, null, true)
      const { app: app2 } = await createLoan(true, true, null, true)
      app2.issueDate = moment().subtract(5, 'days').toDate()
      app.issueDate = moment().subtract(30, 'days').toDate()
      app2.status = app.status = 'approved'
      app2.metadata = app.metadata = {
        paymentPlan: {
          id: '123',
          type: 'regular',
          days: 30,
          name: '',
          lmsTemplateId: '',
          fee: 0,
          frequency: 'single',
          term: 1,
          firstPaymentDelayDays: 0,
          paymentDelayCode: PaymentDelayCode.TD30,
          product: PricingProduct.LineOfCredit,
        },
        loanPackage: {
          finalPayment: 20,
          merchant: 5,
          advanceRate: 80,
          maxAmountReceived: 0,
          merchantRebate: 0,
          merchantFeeAfterRebate: 0,
          maxAmountReceivedAfterRebate: 0,
          CustomerFees6090: '',
          CustomerFees30: 0,
          advancePaymentDay: AdvancePaymentDay.TD1,
          finalPaymentDay: PaymentDelayCode.TD30,
        },
      }
      await app.save()
      await app2.save()
      await lambda(finalPayment).expectResult((v) => {
        v.items.should.eq(1)
      })
      const appDisb = await Operation.findOne({
        owner_id: app.id,
        type: 'loan_final_issue',
        status: 'SUCCESS',
      })
      appDisb!.amount.should.eq(570)
      const invFP1 = await Operation.findOne({
        owner_id: app!.invoiceDetails!.invoiceId![0],
      })
      const invFP2 = await Operation.findOne({
        owner_id: app!.invoiceDetails!.invoiceId![1],
      })
      const amount = invFP1!.amount + invFP2!.amount
      amount.should.eq(appDisb!.amount)

      await lambda(finalPayment).expectResult((v) => {
        v.items.should.eq(0)
      })
    })
    it('should pull repayment from card, for regulated debit card fee should be zero', async () => {
      mockTabapayTransaction()
      const { app, builderCompany } = await createLoan()
      const account = await BankAccount.create({
        accountNumber: {
          cipher: await crypt.encrypt('*********'),
          display: '111',
        },
        paymentMethodType: 'card',
        accountType: 'checking',
        name: 'card',
        routingNumber: '********',
        cardMetadata: {
          network: 'Visa',
          type: 'Debit',
          isRegulated: true,
        },
      })
      builderCompany!.bankAccounts!.push(account)
      await builderCompany!.save()
      await makeLoanPayment(
        {
          loanApplication: app,
          amount: 100,
          paymentDate: moment().format('YYYY/MM/DD'),
          bankAccount: account,
        },
        session,
      )
      const op = await Operation.findOne({
        owner_id: app.id,
        type: dictionaries.OPERATION_TYPES.LOAN.REPAYMENT,
      })
      op!.status.should.eq(dictionaries.OPERATION_STATUS.PROCESSING)
      const tr = await Transaction.findOne({ operation_id: op!.id })
      chai.expect(tr?.amount).to.be.equal(100)
      chai.expect(tr?.fee).to.be.equal(0)
      console.log(tr)
    })
    it('should pull repayment from card and 3.5% fee should be applied', async () => {
      mockTabapayTransaction()
      const { app, builderCompany } = await createLoan()
      const account = await BankAccount.create({
        accountNumber: {
          cipher: await crypt.encrypt('*********'),
          display: '111',
        },
        paymentMethodType: 'card',
        accountType: 'checking',
        name: 'card',
        routingNumber: '********',
        cardMetadata: {
          network: 'Visa',
          type: 'Debit',
          isRegulated: false,
        },
      })
      builderCompany!.bankAccounts!.push(account)
      await builderCompany!.save()
      await makeLoanPayment(
        {
          loanApplication: app,
          amount: 17,
          paymentDate: moment().format('YYYY/MM/DD'),
          bankAccount: account,
        },
        session,
      )
      const op = await Operation.findOne({
        owner_id: app.id,
        type: dictionaries.OPERATION_TYPES.LOAN.REPAYMENT,
      })
      op!.status.should.eq(dictionaries.OPERATION_STATUS.PROCESSING)
      const tr = await Transaction.findOne({ operation_id: op!.id })
      chai.expect(tr?.amount).to.be.equal(17)
      chai.expect(tr?.fee).to.be.equal(0.6) // 0.595 should be rounded to 0.6
      console.log(tr)
    })
  })
})
