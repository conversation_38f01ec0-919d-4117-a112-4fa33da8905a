export const TradeCreditStatus = {
  NotApplied: 'Not Applied',
  IncompleteApplication: 'Incomplete Application',
  GoodStanding: 'Good Standing',
  Approved: 'Approved',
  PendingReview: 'Pending Review',
  BankDisconnected: 'Bank Disconnected',
  BankDataMissing: 'Bank Data Missing',
  AccountOnHold: 'Account On Hold',
  PastDue: 'Past Due',
  PastDue10: 'Past due by less than 10 days',
  PastDue60: 'Past Due',
  Denied: 'Denied',
  Delinquent: 'Delinquent',
  AccountInReview: 'Account In Review',
} as const

export type TradeCreditStatusType =
  typeof TradeCreditStatus[keyof typeof TradeCreditStatus]
