import React, { FC } from 'react'
import { View } from 'react-native'
import {
  BtEncryptedAndSecuredBadge,
  BtInputBase as BtInput,
  BtNumberInput,
  BtRadioButton,
  BtText,
} from '@linqpal/components/src/ui'
import { KeyboardType } from '@linqpal/models/src/dictionaries'
import { useTranslation } from 'react-i18next'
import { BankAccountType } from './BankAccountType'
import { CreateBankAccountModel } from './BankAccountFormModel'
import { useResponsive } from '@linqpal/components/src/hooks'
import { Spacer } from '../../../../../../ui/atoms'

interface Props {
  review: boolean
  model: CreateBankAccountModel
  onChange?: (newValue: CreateBankAccountModel) => void
}

export const BankAccountForm: FC<Props> = ({ model, review, onChange }) => {
  const { t } = useTranslation('application')
  const { sm } = useResponsive()

  const validateBankName = () => (model.name ? '' : t('Bank.BankNameInvalid'))

  const validateAccountHolderName = () =>
    model.accountholderName ? '' : t('Bank.HolderNameInvalid')

  const validateAccountNumber = () =>
    model.accountNumber.length >= 6 ? '' : t('Bank.AccountNumberInvalid')

  const validateRoutingNumber = () =>
    model.routingNumber.length === 9 ? '' : t('Bank.RoutingNumberInvalid')

  const handleChange =
    (key: keyof CreateBankAccountModel) => (newValue: string) => {
      if (!onChange) return

      onChange({ ...model, [key]: newValue })
    }

  const handleRadioChange = (newValue: BankAccountType) => () => {
    if (!onChange) return

    onChange({ ...model, accountType: newValue })
  }

  return (
    <View style={{ minWidth: sm ? 500 : undefined }}>
      {sm ? (
        <>
          <Spacer height={review ? 24 : 16} />
          <BtEncryptedAndSecuredBadge />
        </>
      ) : (
        <BtText
          style={{
            fontSize: 20,
            fontWeight: 700,
          }}
        >
          {t('Bank.EnterBankDetailsManually')}
        </BtText>
      )}
      <Spacer height={16} />
      <BtInput
        value={model.name}
        maxLength={70}
        testID="BankName"
        disabled={review}
        validate={validateBankName}
        label={t('Bank.BankName')}
        onChangeText={handleChange('name')}
      />
      <Spacer height={review ? 24 : 16} />
      <BtInput
        value={model?.accountholderName}
        label={t('Bank.HolderName')}
        disabled={review}
        validate={validateAccountHolderName}
        testID="BankHolderName"
        maxLength={70}
        onChangeText={handleChange('accountholderName')}
      />
      <Spacer height={review ? 24 : 16} />
      <BtNumberInput
        value={model.accountNumber}
        label={t('Bank.AccountNumber')}
        disabled={review}
        validate={validateAccountNumber}
        format="################"
        testID="BankAccountNumber"
        keyboardType={KeyboardType.NumberPad}
        onChangeText={handleChange('accountNumber')}
      />
      <Spacer height={review ? 24 : 16} />
      <BtNumberInput
        value={model.routingNumber}
        label={t('Bank.RoutingNumber')}
        disabled={review}
        validate={validateRoutingNumber}
        format="#########"
        testID="BankRoutingNumber"
        keyboardType={KeyboardType.NumberPad}
        onChangeText={handleChange('routingNumber')}
      />
      <Spacer height={review ? 24 : 16} />
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
        }}
      >
        <BtRadioButton
          label={t('Bank.BusinessChecking')}
          checked={model.accountType === BankAccountType.Checking}
          disabled={review}
          onPress={handleRadioChange(BankAccountType.Checking)}
        />
        <Spacer width={30} />
        <BtRadioButton
          label={t('Bank.BusinessSaving')}
          checked={model.accountType === BankAccountType.Savings}
          disabled={review}
          onPress={handleRadioChange(BankAccountType.Savings)}
        />
      </View>
    </View>
  )
}
