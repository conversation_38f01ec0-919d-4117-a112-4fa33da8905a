import { authRequired } from '../../services/auth.service'
import {
  BankAccount,
  Company,
  crypt,
  initializeFinicity,
  institutionsService,
} from '@linqpal/common-backend'
import md5 from 'crypto-js/md5'
import _ from 'lodash'
import transactionMiddlewares from '../../services/transactional.service'
import crypto from 'crypto'
import { IBankAccount } from '@linqpal/common-backend/src/models/types'
import { ControllerItem } from '../controllerItem'
import { exceptions } from '@linqpal/models'

export const middlewares = { pre: [authRequired()] }

export const createConnectLink: ControllerItem = {
  middlewares: {
    pre: [...middlewares.pre, ...transactionMiddlewares.pre],
    post: [...transactionMiddlewares.post],
  },
  post: async (req, res, next) => {
    const finicity = await initializeFinicity()
    const company = await Company.findOne({ _id: req.company!.id })
      .session(req.session)
      .exec()
    let customerId = company?.finicity?.customerId || ''
    if (company && !customerId) {
      customerId = await finicity.customers.addCustomer(req.company!.id)
      await company
        .updateOne(
          { $set: { 'finicity.customerId': customerId } },
          { session: req.session },
        )
        .exec()
    }
    const link = await finicity.connect.generateLink(customerId)
    res.locals.result = { link }
    next()
  },
}

export const syncAccounts: ControllerItem = {
  middlewares: {
    pre: [...middlewares.pre, ...transactionMiddlewares.pre],
    post: [...transactionMiddlewares.post],
  },
  get: async (req, res, next) => {
    const company = await Company.findOne({ _id: req.company!._id })
      .populate('bankAccounts')
      .session(req.session)
    const existingPrimary = company?.bankAccounts?.find(
      (bank) => bank.isPrimary,
    )
    const finicity = await initializeFinicity()
    if (company) {
      if (company.finicity?.customerId) {
        const customerId = company.finicity.customerId
        const accounts = await finicity.accounts.getAccounts(customerId)
        await Promise.all(
          accounts.map(async (account, index) => {
            const institution = await finicity.institutions.getInstitution(
              account.institutionId,
            )
            let accountNo = account.number
            let routingNo = ''
            try {
              const { accountNumber, routingNumber } =
                await finicity.accounts.getACHDetails(customerId, account.id)
              accountNo = accountNumber
              routingNo = routingNumber
            } catch (e) {
              console.log(e)
              if (process.env.LP_MODE !== 'prod') {
                accountNo = '****************'
                routingNo = '*********'
              }
            }

            const isInstitutionBlocked =
              await institutionsService.checkIfIsBlocked(
                institution.name,
                routingNo,
              )
            if (isInstitutionBlocked) {
              throw new exceptions.DeniedError()
            }

            const { name } = await finicity.accounts.getAccountOwner(
              customerId,
              account.id,
              3000,
            )

            const bankObject: Partial<IBankAccount> = {
              name: institution.name,
              accountholderName: name,
              finicity: {
                accountId: account.id,
                syncState: account.aggregationStatusCode,
              },
              routingNumber: routingNo,
              accountNumber: accountNo,
              isManualEntry: false,
              paymentMethodType: 'bank',
              isPrimary: !existingPrimary && index === 0,
              status: 'verified',
              accountType: account.type.toLowerCase(),
              accountName: account.name,
            }
            if (_.isString(bankObject.accountNumber)) {
              const cipher = await crypt.encrypt(bankObject.accountNumber)
              bankObject.accountNumber = {
                cipher,
                hash: md5(bankObject.accountNumber).toString(),
                display: new Array(7).join('*') + account.accountNumberDisplay,
              }
            }
            const existing = await BankAccount.findOne({
              'finicity.accountId': account.id,
            }).session(req.session)
            if (existing) {
              await existing.updateOne(bankObject)
            } else {
              const [newBankAccount] = await BankAccount.create([bankObject], {
                session: req.session,
              })
              if (!company.bankAccounts) {
                company.bankAccounts = []
              }
              company.bankAccounts.push(newBankAccount)
            }
            return bankObject
          }),
        )
      }

      await company.save()
    }
    next()
  },
}

export const removeAccount: ControllerItem = {
  middlewares: {
    pre: [...middlewares.pre, ...transactionMiddlewares.pre],
    post: [...transactionMiddlewares.post],
  },
  get: async (req, res, next) => {
    const { accountId } = req.query
    const company = await Company.findOne({ _id: req.company!._id }).session(
      req.session,
    )
    const accountRecord = await BankAccount.findById(accountId)
    if (accountRecord) {
      if (accountRecord.finicity?.accountId) {
        const finicity = await initializeFinicity()
        if (company?.finicity?.customerId) {
          const customerId = company.finicity.customerId
          try {
            await finicity.accounts.deleteAccount({
              customerId,
              accountId: accountRecord.finicity.accountId,
            })
          } catch (e) {
            console.log(e)
          }
        }
      }
      await BankAccount.deleteOne({ _id: accountId }).session(req.session)
      if (company) {
        company.bankAccounts = company.bankAccounts?.filter(
          (acc) => acc._id.toString() !== accountId,
        )
        await company.save()
      }
    }
    next()
  },
}

export const processWebhook: ControllerItem = {
  middlewares: {
    pre: [...transactionMiddlewares.pre],
    post: [...transactionMiddlewares.post],
  },
  post: async (req, _res, next) => {
    const body = req.body
    console.log('Webhook payload', req.body)
    const signature = crypto
      .createHmac('sha256', process.env.LP_FINICITY_PARTNER_SECRET || '')
      .update(JSON.stringify(body))
      .digest('hex')

    if (req.get('x-finicity-signature') !== signature) {
      throw new Error('Spoofing detected, rejecting webhook')
    }

    const { eventType, customerId } = req.body
    console.log('Current event type - ', eventType)
    console.log('Current customer id - ', customerId)
    if (eventType === 'done') {
      const finicity = await initializeFinicity()
      const accounts = await finicity.accounts.getAccounts(customerId)
      await Promise.all(
        accounts.map(async (account) => {
          try {
            const response = await finicity.accounts.syncHistoricTransactions(
              customerId,
              account.id,
              3000,
            )
            console.log(
              'Making an api call to load historic transaction for account',
              account,
            )
            console.log(
              'Response from api call to load historic transaction for account',
              response,
            )
            console.log(response)
          } catch (e) {
            console.log(e)
          }
        }),
      )
    } else if (eventType === 'added') {
      const { accounts } = req.body.payload
      try {
        if (accounts) {
          await Promise.all(
            accounts.map(async (account: { id: string }) => {
              const bankAccount = await BankAccount.findOne({
                finicity: { accountId: account.id },
              })
              if (!bankAccount) return
              if (!bankAccount.accountholderName) {
                const finicity = await initializeFinicity()
                const { name } = await finicity.accounts.getAccountOwner(
                  customerId,
                  account.id,
                )
                console.log('Got Account Owner Name', account.id, name)
                if (name) {
                  await bankAccount.updateOne({ accountholderName: name })
                }
              }
            }),
          )
        }
      } catch (e) {
        console.log(e)
      }
    } else {
      console.log('Webhook process skipping for event', eventType)
    }
    console.log('Webhook processed')
    next()
  },
}
