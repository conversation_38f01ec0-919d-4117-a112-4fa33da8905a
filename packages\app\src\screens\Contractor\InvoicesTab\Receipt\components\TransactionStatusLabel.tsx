import React, { FC } from 'react'
import {
  IStatusUIPara<PERSON>,
  StatusLabel,
} from '../../../../../ui/molecules/StatusLabel/StatusLabel'
import {
  IconDrawCancelled,
  IconDrawPaidOff,
  IconTransactionInProgress,
} from '../../../../../assets/icons'
import { css } from 'styled-components'
import {
  TRANSACTION_STATUS,
  transactionStatusBadge,
} from '@linqpal/models/src/dictionaries'

interface ITransactionStatusLabelProps {
  status: any
}

export const TransactionStatusLabel: FC<ITransactionStatusLabelProps> = ({
  status,
}) => {
  return (
    <StatusLabel
      status={status}
      uiOptionsProvider={getTransactionDocumentStatusUIOptions}
    />
  )
}

export function getTransactionDocumentStatusUIOptions(
  status: string,
): IStatusUIParams {
  const label = transactionStatusBadge[status]?.label || status

  switch (status) {
    case TRANSACTION_STATUS.SUCCESS:
      return {
        label,
        icon: <IconDrawPaidOff />,
        style: css`
          color: #0ec06b;
          font-weight: 600;
          font-size: 12px;
          line-height: 20px;
        `,
        gap: 4,
      }
    case TRANSACTION_STATUS.PENDING:
    case TRANSACTION_STATUS.PROCESSING:
    case TRANSACTION_STATUS.PLACED:
    case TRANSACTION_STATUS.SCHEDULED:
      return {
        label,
        icon: <IconTransactionInProgress />,
        style: css`
          color: #ff7926;
          font-weight: 600;
          font-size: 12px;
          line-height: 20px;
        `,
        gap: 4,
      }
    case TRANSACTION_STATUS.ERROR:
    case TRANSACTION_STATUS.CANCELED:
      return {
        label,
        icon: <IconDrawCancelled />,
        style: css`
          color: #ec002a;
          font-weight: 600;
          font-size: 12px;
          line-height: 20px;
        `,
        gap: 4,
      }
    default:
      return { label: status }
  }
}
