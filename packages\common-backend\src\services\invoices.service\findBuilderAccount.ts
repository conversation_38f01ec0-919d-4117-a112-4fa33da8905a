import { Company, CustomerAccount, User, UserRole } from '../../models'
import { RoleStatus } from '@linqpal/models'
import { escapeRegExp } from 'lodash'
import { ClientSession } from 'mongoose'

export async function findBuilderAccount(
  login: string,
): Promise<{ _id: string }[]> {
  return Company.aggregate([
    { $addFields: { id: { $toString: '$_id' } } },
    {
      $lookup: {
        from: UserRole.collection.name,
        as: 'roles',
        localField: 'id',
        foreignField: 'company_id',
      },
    },
    {
      $lookup: {
        from: User.collection.name,
        as: 'user',
        localField: 'roles.sub',
        foreignField: 'sub',
      },
    },
    { $unwind: '$user' },
    { $match: { 'roles.role': 'Owner' } },
    { $match: { 'user.login': login } },
  ])
}

export async function findBuilderByAccountId(
  accountId: string | null,
  session: ClientSession | null = null,
) {
  if (!accountId) return null

  const customerAccount = await CustomerAccount.findById(accountId).session(
    session,
  )

  if (!customerAccount) return null

  const escapedEmail = escapeRegExp(customerAccount.email)

  const companies_id = await User.aggregate([
    {
      $match: {
        $or: [
          { login: { $regex: new RegExp(`^${escapedEmail}$`, 'i') } },
          { email: { $regex: new RegExp(`^${escapedEmail}$`, 'i') } },
        ],
      },
    },
    {
      $lookup: {
        from: UserRole.collection.name,
        as: 'role',
        let: { sub: '$sub' },
        pipeline: [
          {
            $match: {
              $expr: {
                $eq: ['$sub', '$$sub'],
              },
            },
          },
          {
            $addFields: {
              priority: {
                $cond: {
                  if: { $eq: ['$status', RoleStatus.Active] },
                  then: 1,
                  else: 2,
                },
              },
            },
          },
          {
            $sort: { priority: 1 },
          },
          {
            $limit: 1,
          },
        ],
      },
    },
    { $unwind: '$role' },
    { $group: { _id: '$role.company_id' } },
  ]).session(session)

  if (companies_id.length !== 1) return null

  const company = await Company.findById(companies_id[0]._id).session(session)
  return company ? company : null
}
