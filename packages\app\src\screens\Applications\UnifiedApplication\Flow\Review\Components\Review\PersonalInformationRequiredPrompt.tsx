import { useTranslation } from 'react-i18next'
import { useUnifiedApplication } from '../../../../UnifiedApplicationContext'

import { StyleSheet, View } from 'react-native'
import { commonColors } from '@linqpal/common-frontend/src/theme'
import { BtButton, BtText } from '@linqpal/components/src/ui'
import React from 'react'
import ApplicationStore, {
  EditMode,
} from '../../../../../../GeneralApplication/Application/ApplicationStore'
import {
  Groups,
  Steps,
} from '@linqpal/models/src/applications/unified/UnifiedApplicationSteps'
import { PeopleIconBlue } from '../../../../../../../assets/icons'

export const PersonalInformationRequiredPrompt = () => {
  const { t } = useTranslation('application')

  const store = useUnifiedApplication()

  // TODO: VK: Unified: implement in new version
  // draft.users[currentUser] => isOwner and isInvited
  if (ApplicationStore.isInvitedOwner) return null

  const personalInfoSteps = store
    .getFlowSteps()
    .filter((step) =>
      [
        Steps.businessOwner.address,
        Steps.businessOwner.birthdate,
        Steps.businessOwner.ssn,
      ].includes(step),
    )

  // no these steps for IHC
  if (!personalInfoSteps.length) return null

  const isPersonalInfoFilled = personalInfoSteps.every((step) =>
    store.validateStep(step),
  )

  if (isPersonalInfoFilled) return null

  const handlePress = () => {
    // TODO: VK: Unified: re-implement edit modes for new store
    ApplicationStore.setEditMode(EditMode.SKIP_FILLED_STRICT)
    store.editGroup(Groups.businessOwner, false)
  }

  return (
    <View style={styles.wrapper}>
      <View style={styles.header}>
        <PeopleIconBlue style={{ height: 18, width: 18, marginTop: 4 }} />
        <BtText style={styles.title}>
          {t('Review.PersonalInformationPrompt')}
        </BtText>
      </View>

      <View>
        <BtButton size={'small'} style={styles.button} onPress={handlePress}>
          {t('Review.CompleteApplication')}
        </BtButton>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  wrapper: {
    backgroundColor: commonColors.backgroundColor,
    borderRadius: 12,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
  },
  title: {
    fontSize: 16,
    marginLeft: 12,
    marginBottom: 12,
  },
  button: {
    marginLeft: 30,
    maxWidth: 217,
  },
})
