import crypt from '../services/crypt.service'
import isObject<PERSON>ike from 'lodash/isObjectLike'
import md5 from 'crypto-js/md5'
import { Document, Query, Schema } from 'mongoose'
import { IEncrypted } from '@linqpal/models'

export function wireCryptValidator<T extends Document>(
  schema: Schema<any>,
  column: string,
) {
  schema.pre('validate', preValidate<T>(column))
  schema.pre(['updateOne'], preUpdateOne<T>(column))
}

export function preValidate<T extends Document>(column: string) {
  return async function (this: T, next: () => void) {
    ;(this as any)[column] = (await encryptValidate(this as any, column)) as any
    next()
  }
}

export function preUpdateOne<T>(column: string) {
  return async function (this: Query<T, T>, next: () => void) {
    const data = this.getUpdate() as any
    if (data) {
      data[column] = (await encryptValidate(data, column)) as any
      this.setUpdate(data)
    }

    next()
  }
}

export async function encryptValidate(
  data: Record<string, any>,
  column: string,
): Promise<IEncrypted> {
  const value = data[column]
  if (!value || isObjectLike(value)) return value
  const cipher = await crypt.encrypt(value)
  return {
    cipher,
    hash: md5(value).toString(),
    display: `${new Array(value.length - 4).join('*')}${value.slice(-4)}`,
  }
}
