import React, { FC } from 'react'
import { useTranslation } from 'react-i18next'
import { StyleSheet, View } from 'react-native'
import { BtPlainText } from '@linqpal/components/src/ui'
import { observer } from 'mobx-react'
import { useResponsive } from '../../../../utils/hooks'
import { useLenderApplication } from '../LenderApplicationContext'

export const WizardStepTitle: FC = observer(() => {
  const { sm } = useResponsive()
  const { t } = useTranslation('application')

  const store = useLenderApplication()

  const title = store.stepOptions?.title
  if (!title) return null

  return (
    <View>
      <BtPlainText
        style={[styles.title, sm ? styles.titleDesktop : styles.titleMobile]}
      >
        {t(title as any)}
      </BtPlainText>
    </View>
  )
})

const styles = StyleSheet.create({
  title: {
    fontWeight: '600',
    marginBottom: 10,
  },
  titleDesktop: {
    fontSize: 24,
    lineHeight: 36,
  },
  titleMobile: {
    fontSize: 18,
    lineHeight: 26,
  },
})
