import { useTranslation } from 'react-i18next'
import React from 'react'
import { commonStyles } from './commonStyles'
import { ApplicationType } from '@linqpal/models/src/dictionaries/applicationType'
import { StyleSheet, View } from 'react-native'
import { BtLink, BtText } from '@linqpal/components/src/ui'
import { getGroupTitle } from '../../../../../../GeneralApplication/Application/groupTitles'
import { useUnifiedApplication } from '../../../../UnifiedApplicationContext'
import { Groups } from '@linqpal/models/src/applications/unified/UnifiedApplicationSteps'
import { IconGrayCheck } from '../../../../../../../assets/icons'

export const IncompleteGroupLine = ({ group }) => {
  const { t } = useTranslation('application')

  const store = useUnifiedApplication()

  const showIncompleteSectionHint = !(
    [Groups.finance, Groups.bank].includes(group) &&
    store.type === ApplicationType.InHouseCredit
  )

  const handleEdit = () => {
    if (!store.isSubmitting) {
      store.editGroup(group, false)
    }
  }

  return (
    <View
      style={[
        commonStyles.groupLineWrapper,
        showIncompleteSectionHint && styles.wrapperWithHint,
      ]}
    >
      <IconGrayCheck style={{ width: 16, height: 12, marginTop: 7 }} />

      <View style={styles.textWrapper}>
        <BtText style={[commonStyles.groupLineText, { marginLeft: 0 }]}>
          {t(getGroupTitle(group) as any)}
        </BtText>

        {showIncompleteSectionHint && (
          <BtText style={styles.hint}>
            {t('Review.IncompleteSectionText')}
          </BtText>
        )}
      </View>

      <BtLink
        title={t('Review.Edit')}
        onPress={handleEdit}
        disabled={store.isSubmitting}
        textStyle={commonStyles.editGroupLink}
        testID={`UnifiedApplication.Review.Edit.Incomplete.${group}`}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  wrapperWithHint: {
    height: 96,
    paddingTop: 23,
    alignItems: 'flex-start',
  },
  textWrapper: {
    flex: 1,
    marginLeft: 15,
  },
  hint: {
    marginTop: 9,
    color: '#FE8134',
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 16,
  },
})
