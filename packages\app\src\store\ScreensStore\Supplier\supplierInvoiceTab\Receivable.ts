import { types } from 'mobx-state-tree'
import { ILoanPaymentPlanModel, Invoice } from '@linqpal/models'

export const Receivable = types.compose(
  Invoice,
  types.model({
    inHouseCreditTerm: types.maybe(types.frozen<ILoanPaymentPlanModel>()),
    totalPaidAmount: types.optional(types.number, 0),
    totalProcessingAmount: types.optional(types.number, 0),
    totalRemainingAmount: types.optional(types.number, 0),
    lateFee: types.optional(types.number, 0),
    customerFee: types.optional(types.number, 0),
  }),
)
