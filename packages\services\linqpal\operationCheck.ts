import {
  AchOutPayload,
  AchTransactionTypes,
  AwsService,
  cbw,
  Company,
  connectToDatabase,
  emailService,
  FactoringService,
  getEnvironmentVariables,
  Invoice,
  LedgerService,
  LMS,
  LoanApplication,
  Logger,
  Operation,
  Settings,
  Sms,
  Transaction,
} from '@linqpal/common-backend'
import {
  ICompany,
  ILoanApplication,
  IOperation,
  ITransaction,
} from '@linqpal/common-backend/src/models/types'
import { dictionaries, exceptions, InvoicePaymentType } from '@linqpal/models'
import 'moment-business-days'
import moment from 'moment-timezone'
import mongoose from 'mongoose'
import {
  OPERATION_STATUS,
  OPERATION_TYPES,
  SettingKeys,
} from '@linqpal/models/src/dictionaries'
import EmailNotifications from '@linqpal/common-backend/src/helpers/EmailNotifications'
import { PaymentStatus } from '@linqpal/common-backend/src/services/lms.service'

const logger = new Logger({ module: 'LoanPro', subModule: 'operationCheck' })

export async function operationCheck() {
  await getEnvironmentVariables()
  await connectToDatabase()
  const log = logger.startTransaction()

  log.info('Start operation check process. Getting operations from database')

  const pullResults: {
    _id: string
    totalOutAmount: number
    operations: string[]
    outs: ITransaction[]
    cardPullTransactions: ITransaction[]
    lastPullTransactionType?: string
  }[] = await Operation.aggregate([
    {
      $match: {
        updatedAt: {
          $gte: moment().startOf('day').businessSubtract(20, 'days').toDate(),
        },
        status: { $in: [OPERATION_STATUS.PROCESSING] },
        'metadata.pullResult': { $exists: true },
        $or: [
          {
            type: {
              $in: [
                OPERATION_TYPES.LOAN.REPAYMENT,
                OPERATION_TYPES.INVOICE.PAYMENT,
              ],
            },
          },
          { paymentRequestId: { $exists: false } },
        ],
      },
    },
    { $addFields: { id: { $toString: '$_id' } } },
    {
      $lookup: {
        from: 'transactions',
        as: 'pull',
        localField: 'id',
        foreignField: 'operation_id',
        pipeline: [
          {
            $match: {
              'metadata.transactionType': {
                $in: ['PULL', 'INTERNAL'],
              },
              status: {
                $in: [dictionaries.TRANSACTION_STATUS.SUCCESS],
              },
            },
          },
          { $sort: { createdAt: -1 } },
        ],
      },
    },
    {
      $addFields: {
        lastPullTransactionType: {
          $arrayElemAt: ['$pull.metadata.transactionType', 0],
        },
      },
    },
    {
      $lookup: {
        from: 'transactions',
        as: 'out',
        localField: 'id',
        foreignField: 'operation_id',
        pipeline: [
          {
            $match: {
              'metadata.transactionType': 'OUT',
              status: { $ne: dictionaries.TRANSACTION_STATUS.CANCELED },
              paymentTransactionId: { $exists: false },
            },
          },
        ],
      },
    },
    { $unwind: '$pull' },
    { $unwind: { path: '$out', preserveNullAndEmptyArrays: true } },
    {
      $group: {
        _id: '$pull.operation_id', // changed from pull.metadata.transactionNumber (IHC partial payments)
        date: { $last: '$date' }, // changed from $first to $last (IHC partial payments)
        totalOutAmount: { $sum: '$out.amount' },
        operations: { $push: '$id' },
        outs: { $push: '$out' },
        lastPullTransactionType: { $first: '$lastPullTransactionType' },
        cardPullTransactions: {
          $push: {
            $cond: [
              { $eq: ['$pull.payment_method', 'card'] },
              '$pull',
              '$$REMOVE',
            ],
          },
        },
      },
    },
    { $sort: { date: 1 } },
  ])

  log.info({ count: pullResults.length }, 'Got operations from database')

  for (const p of pullResults) {
    const session = await mongoose.startSession()

    let operation: IOperation | null = null

    try {
      session.startTransaction()

      operation = await Operation.findById(p.operations[0]).session(session)

      if (!operation?.owner_id) {
        logger.warn({ operation: operation }, 'operation ownerId is missing')
        continue
      }

      if (!(operation && operation.metadata?.pullResult)) continue

      log.info({ o: operation }, 'operationCheck: Start processing operation')

      const pulled = moment(operation.metadata.pullResult)
      if (!pulled.isValid()) continue

      const company = operation.metadata.payee_id
        ? await Company.findById(operation.metadata.payee_id).session(session)
        : null

      const invoice =
        operation.type !== OPERATION_TYPES.LOAN.REPAYMENT
          ? await Invoice.findById(operation.owner_id).populate('customer')
          : null

      const is_delayed =
        p.lastPullTransactionType !== 'INTERNAL' &&
        (operation.type !== OPERATION_TYPES.INVOICE.PAYMENT ||
          !company?.settings?.achDelayDisabled)

      if (
        operation.metadata.payment_method === 'card' ||
        p.cardPullTransactions?.length
      ) {
        const isECommerceInvoice = !!invoice?.connector
        const isGuestPayer = invoice?.customer?.isGuest

        let delayDays = 1

        if (operation.type === OPERATION_TYPES.INVOICE.PAYMENT) {
          delayDays = isGuestPayer ? 3 : isECommerceInvoice ? 2 : 1
        }

        const expectedDate = pulled.endOf('day').businessAdd(delayDays, 'days')

        if (moment().endOf('day') < expectedDate) {
          log.info(
            {
              operationId: operation._id,
              operationType: operation.type,
              expectedDate,
              isECommerceInvoice,
              isGuestPayer,
            },
            `Delaying card payment processing for ${delayDays} days since ${pulled.format()}`,
          )

          continue
        }
      } else if (is_delayed) {
        const period = process.env.LP_MODE === 'prod' ? 2 : 0
        log.info(
          {
            operationId: operation._id,
            pulledDate: pulled.endOf('day').businessAdd(period, 'days'),
          },
          'operationCheck: Operation is payment and delayed. Start checking if it is time to process it',
        )
        if (
          moment().endOf('day') <
          pulled.endOf('day').businessAdd(period, 'days')
        ) {
          log.info(
            { operationId: operation._id },
            'operationCheck: Operation is payment and delayed. It is not time to process it yet. Will skip it',
          )
          continue
        }
      }

      log.info(
        { operationId: operation._id },
        'operationCheck: Operation is ready to be processed',
      )

      if (p.outs.length > 0) {
        // exclude invoice payments coming from the payment service
        if (
          operation.paymentRequestId &&
          operation.type === OPERATION_TYPES.INVOICE.PAYMENT
        ) {
          continue
        }

        const pending = p.outs.filter(
          (v) => v.status === dictionaries.TRANSACTION_STATUS.PENDING,
        )

        if (pending.length === 0) continue

        const totalOutAmount = pending.reduce((prev, v) => prev + v.amount, 0)

        const res = await Promise.all(
          pending.map((v) => Transaction.findById(v._id).session(session)),
        )

        const transactions: ITransaction[] = []

        for (const r of res) {
          if (r) transactions.push(r)
        }

        const t = transactions[0]
        const recipient = await cbw.getRecipient(
          t.payee_id,
          t.metadata?.accountId,
        )
        const nextPayload = cbw.toPayload(
          {
            id: `${t.__v}-${t.id}`,
            ...recipient,
            amount: totalOutAmount,
            currency: t.currency,
            reason: t.reason,
          },
          t.metadata.transactionType! as AchTransactionTypes,
          operation.metadata.payment_method ===
            dictionaries.PAYMENT_METHODS.CARD
            ? process.env.LP_TABAPAY_ACH_IDENTIFICATION
            : process.env.LP_CBW_ACH_IDENTIFICATION,
        )
        const achOutPayload = new AchOutPayload(nextPayload)
        await achOutPayload.validate()
        try {
          await cbw.processTransaction(
            transactions,
            achOutPayload.toObject(),
            session,
          )
          await session.commitTransaction()
        } catch (e) {
          await session.abortTransaction()
          session.startTransaction()
          log.error(
            { operationId: operation._id, transactionId: t.id, error: e },
            'operationCheck: Error while processing transaction',
          )
          for (const item of transactions) {
            if ((e as { code?: string }).code === 'A107') {
              item.status = dictionaries.TRANSACTION_STATUS.PROCESSING
            } else {
              item.__v += 1
              item.metadata.error = e
              item.status = dictionaries.TRANSACTION_STATUS.PENDING
            }
            item.markModified('metadata')
            await item.save()
          }
        }

        if (invoice?.connector?.document_id) {
          await AwsService.sendSQSMessage(
            'netsuite-connector',
            JSON.stringify({
              id: invoice.id,
              operationType: 'InvoicePaid',
              status: 'final',
              amount: t.amount,
              fee: t.fee || 0,
              paymentMethod: operation.metadata.payment_method,
            }),
            'NETSUITE',
          )
        }
      } else {
        switch (operation.type) {
          case OPERATION_TYPES.LOAN.REPAYMENT:
            await approveLoanPayment(operation)
            await checkLoan(operation)
            await Operation.updateOne(
              { _id: operation._id },
              { $set: { status: dictionaries.OPERATION_STATUS.SUCCESS } },
            )
            break
          case OPERATION_TYPES.INVOICE.PAYMENT:
            // IHC invoice_payment operation turned to SUCCESS by the payment service during a final payment creation
            await approveLoanIhcCardPayment(operation, p.cardPullTransactions)
            await disburseInvoicePayment(operation.owner_id, company, log)
            break
        }
      }
      await session.commitTransaction()
    } catch (e: any) {
      log.error({ error: e, pullResult: p }, 'error while processing operation')

      await sendOperationErrorEmail(p, operation, e)
      await session.abortTransaction()
    } finally {
      if (session.inTransaction()) {
        await session.abortTransaction()
      }
    }
    await session.endSession()
  }
}

async function disburseInvoicePayment(
  invoiceId: string,
  supplier: ICompany | null,
  log: Logger,
) {
  log.info({ invoiceId, supplier }, 'disbursing invoice payment')

  // prettier-ignore
  if (!supplier) throw new Error('supplier is required to disburse invoice payment')

  const invoice = await Invoice.findById(invoiceId)
  if (!invoice) throw new Error(`Invoice ${invoiceId} is not found`)

  if (invoice?.paymentDetails?.paymentType === InvoicePaymentType.FACTORING) {
    const draw = (
      await LMS.findLoans({
        payableId: invoice.id,
        detailed: true,
      })
    )?.[0]

    if (draw?.loanDetails?.isFullyPaid) {
      await FactoringService.disburseFinalPayment(invoice)
    } else {
      log.info(
        { loanId: draw.id },
        'Draw is not fully paid, skipping IHC invoice final disbursement',
      )
    }
  }
}

async function approveLoanPayment(operation: IOperation) {
  const log = logger.startTransaction()
  log.info({ operation }, 'Start approving loan payment process')
  const loan = await LoanApplication.findById(operation.owner_id)
  if (!loan) throw new exceptions.LogicalError('Loan not found')

  log.info(
    {
      loanId: loan._id,
      paymentDate: operation.metadata.paymentDate,
      lmsPaymentId: operation.metadata.lms_paymentId,
    },
    'Got loan from database',
  )

  if (operation.metadata.paymentDate && operation.metadata.lms_paymentId) {
    const paymentInfo = await LMS.approvePayment(
      operation.metadata.lms_paymentId,
    )
    log.info(
      { loanId: loan._id, lmsPaymentId: operation.metadata.lms_paymentId },
      'Payment approved',
    )
    await LedgerService.handleLoanRepayment(paymentInfo, loan.lms_id, operation)
  }
}

async function approveLoanIhcCardPayment(
  operation: IOperation,
  cardPullTransactions: ITransaction[],
) {
  const log = logger.startTransaction()
  log.info(
    { operation, cardPullTransactions },
    'Start approving IHC card payment process',
  )

  if (!cardPullTransactions?.length) {
    log.info(
      { operationId: operation._id },
      'Operation has no card payments, skipping',
    )
    return
  }

  if (!operation.metadata.pullResult) {
    log.info(
      { operationId: operation._id },
      'Missing required metadata, skipping',
    )
    return
  }

  const draw = (
    await LMS.findLoans({
      payableId: operation.owner_id,
      detailed: true,
    })
  )?.[0]

  if (!draw) {
    log.error({ owner_id: operation.owner_id }, 'Draw not found in LMS')
    return
  }

  log.info(
    {
      drawId: draw.id,
      drawStatus: draw.status,
      payableId: operation.owner_id,
    },
    'LMS findLoans response',
  )

  if (!draw.payments || draw.payments.length === 0) {
    log.info({ drawId: draw.id }, 'Draw has no payments, skipping')
    return
  }

  for (const pullTransaction of cardPullTransactions) {
    if (!pullTransaction?.metadata?.lms_paymentId) {
      log.info(
        { transactionId: pullTransaction._id },
        'Transaction has no lms_paymentId, skipping',
      )
      continue
    }

    const currentPayment = draw.payments.find(
      (payment) => payment.id === pullTransaction.metadata.lms_paymentId,
    )

    if (!currentPayment) {
      log.info(
        {
          drawId: draw.id,
          lmsPaymentId: pullTransaction.metadata.lms_paymentId,
        },
        'Payment not found in payments array',
      )
      continue
    }

    if (currentPayment.status !== PaymentStatus.Processing) {
      log.info(
        {
          loanId: draw.id,
          lmsPaymentId: pullTransaction.metadata.lms_paymentId,
        },
        'Payment not in processing status, skipping approval',
      )
      continue
    }

    const paymentInfo = await LMS.approvePayment(
      pullTransaction.metadata.lms_paymentId,
    )

    log.info(
      { loanId: draw.id, lmsPaymentId: pullTransaction.metadata.lms_paymentId },
      'IHC Payment approved and amounts updated',
    )

    await LedgerService.handleLoanRepayment(paymentInfo, draw.id, operation)
  }
}

export async function checkLoan(op: IOperation) {
  const log = logger.startTransaction()
  log.info({ operation: op.id }, 'Start checking loan process')
  const application = await LoanApplication.findOne({ _id: op.owner_id })
  if (application) {
    const fullyPaid = await LMS.checkIsFullyPaid(application.lms_id)
    log.info(
      { loanId: application.lms_id, fullypPaid: fullyPaid },
      'Checked is application loan fully paid',
    )
    if (!fullyPaid) {
      log.info({ loanId: application.lms_id }, 'Loan is not fully paid')
      await successfulRepaymentHandler(application)
    }
    await LMS.syncLoanApplicationFields(application)
  }
}

async function successfulRepaymentHandler(application: ILoanApplication) {
  const log = logger.startTransaction()
  log.info({ app_id: application._id }, 'Start successful repayment handler')
  if (!application?.lms_id)
    throw new exceptions.LogicalError(
      `Provided application(with id ${
        application?._id || 'undefined'
      }) does not have lms id`,
    )

  const loanLMS = await LMS.getLoanInfo(application.lms_id)

  const nextDueDate = moment(
    loanLMS?.loanReceivables.find((i: any) => i.expectedAmount !== i.paidAmount)
      ?.expectedDate,
  )

  const invoice = await Invoice.findById(application.invoiceDetails?.invoiceId)
  if (!invoice) {
    log.info(
      { invoiceId: application.invoiceDetails?.invoiceId },
      'Invoice not found',
    )
    return
  }

  let businessName: string
  if (invoice.company_id) {
    const company = await Company.findById(invoice?.company_id)
    businessName = company!.name
  } else {
    businessName = invoice.supplierInvitationDetails.name!
  }

  const message = Sms.getMessage({
    key: 'successfulRepayment',
    data: {
      nextDueDate: moment(nextDueDate).format('MM/DD/YY'),
      BusinessName: businessName,
    },
  })

  try {
    log.info(
      {
        app_id: application._id,
        companyId: application?.company_id,
        message: message,
      },
      'Sending sms to user',
    )

    await Sms.smsUser(application?.company_id, message)
  } catch (e) {
    const error = e as Error

    log.info(
      {
        app_id: application._id,
        companyId: application?.company_id,
        message: error.message,
      },
      'Failed to send sms notification',
    )
  }

  log.info(
    {
      app_id: application._id,
      companyId: application?.company_id,
    },
    'Successful repayment handler',
  )
}

async function sendOperationErrorEmail(
  pullResult: { _id: string },
  operation: IOperation | null,
  error: any,
) {
  const emails = await Settings.findOne({
    key: SettingKeys.OperationCheckErrorEmails,
  })

  if (emails) {
    const recipients = emails.value.split(',')

    const message = EmailNotifications.operationCheckError({
      transactionId: pullResult._id,
      operationId: operation?._id?.toString() ?? '',
      error: `${error.message}\n
              ${error.name}\n
              ${error.stack}\n
              cause: ${JSON.stringify(error.cause)}\n
              data: ${JSON.stringify(error.metadata)}`,
    })

    for (const recipient of recipients) {
      await emailService.sendTo(recipient, message)
    }
  }
}
