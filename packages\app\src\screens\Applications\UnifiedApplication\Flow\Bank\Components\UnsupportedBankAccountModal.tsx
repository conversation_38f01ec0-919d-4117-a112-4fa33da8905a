import React, { <PERSON> } from 'react'
import { useResponsive } from '@linqpal/components/src/hooks'
import { BtAlert, BtText } from '@linqpal/components/src/ui'
import { useTranslation } from 'react-i18next'

interface Props {
  isVisible: boolean
  message?: string
  onClose: () => void
}

export const UnsupportedBankAccountModal: FC<Props> = ({
  isVisible,
  message,
  onClose,
}) => {
  const { t } = useTranslation('application')
  const { sm } = useResponsive()

  return (
    <BtAlert
      type="warning"
      visible={isVisible}
      width={sm ? 500 : 330}
      buttons={[
        {
          testID: 'BankWarningClose',
          label: t('Close'),
          onPress: onClose,
        },
      ]}
      onClose={onClose}
    >
      <BtText
        style={{ fontSize: 18, color: '#335C75', textAlign: 'center' }}
        testID="BankWarningMessage"
      >
        {message}
      </BtText>
    </BtAlert>
  )
}
