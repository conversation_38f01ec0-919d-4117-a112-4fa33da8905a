import React, { FC, useEffect, useMemo, useState } from 'react'
import { observer } from 'mobx-react'

import { View } from 'react-native'
import { WizardHeader } from './WizardHeader'
import { WizardGroupTitle } from './WizardGroupTitle'
import { useUnifiedApplication } from '../UnifiedApplicationContext'
import { WizardStepTitle } from './WizardStepTitle'
import { WizardStepDescription } from './WizardStepDescription'
import { WizardDesktopButtons } from './WizardDesktopButtons'
import { WizardBackButton } from './WIzardBackButton'
import { WizardMobileFooter } from './WizardMobileFooter'
import { WizardScrollArea } from '../../Components/WizardScrollArea'
import { WizardFooter } from './WizardFooter'
import { UnifiedApplicationReviewStep } from '../Store/UnifiedApplicationReviewStore'
import { ConsentStatement } from '../Flow/Review/Components/Review/ConsentStatement'
import { useResponsive } from '../../../../utils/hooks'
import ApplicationStore from '../../../GeneralApplication/Application/ApplicationStore'
import { CloseApplicationAlert } from '../../../GeneralApplication/Application/components'
import { Spacer } from '../../../../ui/atoms'
import {
  getUnifiedApplicationEditor,
  IUnifiedApplicationEditor,
} from '../Flow/getUnifiedApplicationEditor'

export const WizardStepEditor: FC = observer(() => {
  const [displayCloseAlert, setDisplayCloseAlert] = useState(false)
  const { sm } = useResponsive()

  const onCloseApplication = () => {
    setDisplayCloseAlert(false)

    ApplicationStore.setCurrentCoOwnerIndex(-1)
  }

  /// ^^^-- old version
  /// vvv-- new version
  const store = useUnifiedApplication()

  const editor: IUnifiedApplicationEditor = useMemo(() => {
    return store.currentStep
      ? getUnifiedApplicationEditor(store.currentStep)
      : { options: {}, component: () => <></> }
  }, [store.currentStep])

  useEffect(() => {
    store.setStepOptions(editor.options)
  }, [store, editor.options])

  console.log('wizard step', {
    currentGroup: store.currentGroup,
    currentStep: store.currentStep,
    isValid: store.isCurrentStepValid,
    editor,
    draft: store.draft,
  })

  const isPreview =
    store.isInReview &&
    store.reviewStore.currentStep === UnifiedApplicationReviewStep.PREVIEW

  return (
    <View style={{ flex: 1 }}>
      <WizardHeader onClose={() => setDisplayCloseAlert(true)} />
      <WizardGroupTitle />

      <WizardScrollArea
        currentStep={store.currentStep}
        wrapperStyle={{
          paddingVertical: isPreview ? 0 : 20,
        }}
      >
        {sm && store.canGoBack && <WizardBackButton />}

        <Spacer height={sm ? 20 : 10} />

        <WizardStepTitle />
        <WizardStepDescription />

        <Spacer height={24} />

        <editor.component />

        {sm && <WizardDesktopButtons />}
      </WizardScrollArea>

      <WizardFooter>
        {!sm ? <WizardMobileFooter /> : isPreview && <ConsentStatement />}
      </WizardFooter>

      {displayCloseAlert && (
        <CloseApplicationAlert
          onCloseAlert={() => setDisplayCloseAlert(false)}
          onCloseApplication={onCloseApplication}
        />
      )}
    </View>
  )
})
