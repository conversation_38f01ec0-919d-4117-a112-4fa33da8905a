import React from 'react'
import { observer } from 'mobx-react-lite'
import { useTranslation } from 'react-i18next'
import { BtCurrencyInput } from '@linqpal/components/src/ui'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { runInAction } from 'mobx'
import { IUnifiedApplicationEditor } from '../getUnifiedApplicationEditor'

const FinanceCreditLimitEditor = observer(() => {
  const { t } = useTranslation('application')
  const store = useUnifiedApplication()

  const handleChange = (value: string) => {
    runInAction(() => {
      const cleaned = value?.trim().replace(/[^0-9.]/g, '')
      const numeric = parseFloat(cleaned)

      store.draft.data.finance.creditLimit = !isNaN(numeric)
        ? numeric
        : undefined
    })
  }

  const value = store.draft.data.finance.creditLimit
    ? store.draft.data.finance.creditLimit.toString()
    : ''

  return (
    <BtCurrencyInput
      value={value}
      label={t('Finance.CreditLabel')}
      size="large"
      onChangeText={handleChange}
      testID="UnifiedApplication.Finance.CreditLimit"
    />
  )
})

export const FinanceCreditLimitStep: IUnifiedApplicationEditor = {
  options: {
    title: 'Finance.Credit',
  },
  component: FinanceCreditLimitEditor,
}
