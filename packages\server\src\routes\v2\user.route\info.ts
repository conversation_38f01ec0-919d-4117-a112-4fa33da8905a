import {
  BankAccount,
  CustomerAccount,
  getBranding,
  LoanApplication,
  Project,
  User,
  UserRole,
} from '@linqpal/common-backend'
import admin from 'firebase-admin'
import { APPROVED_STATUSES } from '@linqpal/models/src/dictionaries'
import { ICompany, IUser } from '@linqpal/common-backend/src/models/types'
import {
  IInfoCompany,
  IInfoResponse,
  IInfoSettings,
} from '@linqpal/models/src/routes2/user/types'
import { TCompoundRoute } from '@linqpal/models/src/routes2/types'
import controllers from '../../../controllers'
import mongoose, { PipelineStage } from 'mongoose'
import { Request } from 'express'
import { LogicalError } from '@linqpal/models/src/types/exceptions'
import { CustomerRepository } from '@linqpal/common-backend/src/repositories/customer.repository'
import { onBoardingService } from '@linqpal/common-backend/src/services/onBoarding/onBoarding.service'
import { CreditApplicationType } from '@linqpal/models/src/dictionaries/creditApplicationType'

const getInfo = async (_: any, req: Request) => {
  const company = req.company
  const user = req.user

  if (!user) throw new LogicalError('auth/user-not-found')
  if (!company) throw new LogicalError('business-not-found')

  const companyStatus = company.status || 'new'
  const canUploadInvoice = company.settings?.canUploadInvoice || false
  const onboarding = company.settings.onboardingType
    ? company.settings.onboardingType.length > 0
    : false
  const pricingPackageRequired =
    controllers.company.pricingPackageRequired(company)

  const {
    cardPricingPackageId,
    loanPricingPackageId,
    onboardingType,
    approveRead,
    supplierCanPay,
    achDiscount,
    canPostTransactions,
    canEditAuthorization,
  } = company.settings

  const settings: IInfoSettings = {
    approveRead,
    cardPricingPackageId,
    loanPricingPackageId,
    onboardingType,
    status: companyStatus,
    onboarding,
    canUploadInvoice,
    pricingPackageRequired,
    supplierCanPay,
    canPostTransactions,
    canEditAuthorization,
    achDiscount,
  }

  // approveRead is set to true when Get Paid application is started and just indicates that application is already started.
  // In old supplier application it could be set to false when application editor was closed, this prevents normal flow in unified application.
  // Now approveRead has to be removed, CompanyStatus.New has to be used instead.
  // For backward compatibility with Get Paid applications created before Unified Application introduction
  // consider combination of CompanyStatus.NotStarted and any defined approveRead as CompanyStatus.New
  if (settings.approveRead === false) settings.approveRead = true

  const [
    bankAccounts,
    suppliers,
    inHouseCreditApplications,
    projects,
    brandingResponse,
    roles,
    approvedApps,
  ] = await Promise.all([
    getBankAccounts(company),
    CustomerRepository.getCustomersSuppliers(company._id.toString()),
    onBoardingService.getCreditApplications({
      companyId: company.id,
      type: CreditApplicationType.InHouseCredit,
    }),
    Project.find({ company_id: company._id }),
    getBranding({ url: req.headers.origin || '' }),
    UserRole.find({
      sub: user.sub,
      company_id: mongoose.trusted({ $ne: null }),
    }),
    LoanApplication.find({
      company_id: company?.id,
      status: mongoose.trusted({ $in: APPROVED_STATUSES }),
    }),
  ])

  const isCustomerOfSupplier = suppliers.length > 0
  const isOnlyCustomerOfBrandedSupplier =
    brandingResponse?.branding &&
    suppliers.length === 1 &&
    suppliers[0].id === brandingResponse.branding.company_id

  const _company: IInfoCompany = {
    ...company.toObject(),
    id: company._id.toString(),
    isCustomerOfSupplier,
  }

  settings.walletConnected = !!bankAccounts.length
  switch (company.status) {
    case 'approved': {
      const businessCategories = company.draft?.businessInfo_category || []
      _company.businessCategory = businessCategories

      const fUser = await admin.auth().getUser(req.user!.firebaseId)
      settings.status = fUser.emailVerified ? companyStatus : 'not_verified'
      break
    }
    default: {
      const fUser = await admin.auth().getUser(req.user!.firebaseId)
      settings.status = fUser.emailVerified ? companyStatus : 'not_verified'
      await CustomerAccount.updateMany(
        { phone: user.login, invited: true },
        { invited: false },
      )
      break
    }
  }

  settings.previouslyApproved =
    !!company?.credit?.limit || !!approvedApps.length

  const brandingInfo = brandingResponse?.branding && {
    isOnlyCustomerOfBrandedSupplier,
  }

  return {
    user: user,
    roles,
    company: _company,
    settings,
    brandingInfo,
    projectAdded: !!projects.length,
    suppliers,
    inHouseCreditApplications,
  } as IInfoResponse
}

const getBankAccounts = async (company: ICompany) => {
  const bankAccountsPipeline: PipelineStage[] = [
    {
      $match: {
        _id: {
          $in: company.bankAccounts,
        },
        isDeactivated: {
          $ne: true,
        },
      },
    },
    {
      $match: {
        $or: [
          {
            paymentMethodType: 'card',
          },
          {
            $and: [
              {
                paymentMethodType: 'bank',
              },
              {
                accountType: {
                  $in: ['savings', 'checking'],
                },
              },
            ],
          },
        ],
      },
    },
  ]

  const bankAccounts = await BankAccount.aggregate(bankAccountsPipeline)
  return bankAccounts
}

export const info: TCompoundRoute<IUser, IInfoResponse> = {
  get: getInfo,
  post: async (data: IUser, req) => {
    const user = await User.findOne({ sub: { $eq: req.user.sub } })
    if (!user) return getInfo(data, req)
    const {
      name = user.name,
      lastName = user.lastName,
      firstName = user.firstName,
      phone = user.phone,
      email = user.email,
      addresses = user.addresses,
    } = data
    await user.updateOne({
      name,
      lastName,
      firstName,
      phone,
      email,
      addresses,
    })
    const userInfo = await getInfo(data, req)
    return {
      ...userInfo,
      user: await User.findOne({ sub: { $eq: req.user.sub } }),
    } as IInfoResponse
  },
}
