import { StyleSheet, Text, View } from 'react-native'
import { BtCheckBox, BtText } from '@linqpal/components/src/ui'
import { Trans, useTranslation } from 'react-i18next'
import { commonColors } from '@linqpal/common-frontend/src/theme'
import React from 'react'
import { observer } from 'mobx-react'
import { urls } from '@linqpal/models/src/dictionaries'
import { useUnifiedApplication } from '../../../../UnifiedApplicationContext'
import { ApplicationType } from '@linqpal/models/src/dictionaries/applicationType'
import { GoToAgreementButton } from './GoToAgreementButton'
import { SubmitApplicationButton } from './SubmitApplicationButton'
import { useResponsive } from '../../../../../../../utils/hooks'
import ApplicationStore from '../../../../../../GeneralApplication/Application/ApplicationStore'
import { UnifiedApplicationReviewStep } from '../../../../Store/UnifiedApplicationReviewStore'
import { Col, Row } from '../../../../../../../ui/atoms/Grid'

export const ConsentStatement = observer(() => {
  const { sm } = useResponsive()

  const { hasSubmissionRights } = ApplicationStore

  const store = useUnifiedApplication()

  const isPreview =
    store.isInReview &&
    store.reviewStore.currentStep === UnifiedApplicationReviewStep.PREVIEW

  const isGetPaidApp = store.type === ApplicationType.Supplier
  const isCreditApp = store.type === ApplicationType.Credit

  return (
    <View
      style={[styles.container, !sm || isPreview ? styles.containerMobile : {}]}
    >
      {isPreview && hasSubmissionRights ? (
        <Row>
          <Col xs={sm ? 8 : 12}>
            <ConsentStatementText />
          </Col>

          {sm && (
            <Col xs={3}>
              {isGetPaidApp || isCreditApp ? (
                <GoToAgreementButton />
              ) : (
                <SubmitApplicationButton />
              )}
            </Col>
          )}
        </Row>
      ) : (
        <ConsentStatementText />
      )}
    </View>
  )
})

const ConsentStatementText = observer(() => {
  const store = useUnifiedApplication()

  const Statement = () => {
    switch (store.type) {
      case ApplicationType.Credit:
        return <CreditApplicationStatement />
      case ApplicationType.InHouseCredit:
        return <InHouseCreditStatement />
      case ApplicationType.Supplier:
        return <SupplierApplicationStatement />
      default:
        return <></>
    }
  }

  // TODO: VK: Unified: Refactor ApplicationStore.hasSubmissionRights

  return (
    <View style={{ flexDirection: 'row' }}>
      {ApplicationStore.hasSubmissionRights ? (
        <BtCheckBox
          style={styles.acceptAgreementsCheckbox}
          checked={store.reviewStore.isAgreementAccepted}
          onPress={(val: boolean) =>
            (store.reviewStore.isAgreementAccepted = val)
          }
        />
      ) : null}
      <Statement />
    </View>
  )
})

const CreditApplicationStatement = observer(() => {
  const { t } = useTranslation('application')
  const { onClickLink, previewMasterAgreement } = ApplicationStore

  const onClickPgaLink = () =>
    ApplicationStore.previewPersonalGuarantorAgreement()

  return (
    <BtText style={styles.creditAgreementText}>
      <Trans
        t={t}
        i18nKey="TermsAndConditionsMessage"
        components={{
          terms: (
            <Text style={styles.link} onPress={() => onClickLink(urls.TC)} />
          ),
          privacy: (
            <Text style={styles.link} onPress={() => onClickLink(urls.PP)} />
          ),
          PGA: (
            <Text
              style={[styles.link, styles.underline]}
              onPress={onClickPgaLink}
            />
          ),
          TCA: (
            <Text
              style={[styles.link, styles.underline]}
              onPress={previewMasterAgreement}
            />
          ),
        }}
      />
    </BtText>
  )
})

const InHouseCreditStatement = observer(() => {
  const { t } = useTranslation('application')

  return (
    <BtText style={styles.agreementText}>
      <Trans
        t={t}
        i18nKey={'InHouseCreditConsentStatement'}
        components={{
          terms: (
            <Text
              style={styles.link}
              onPress={() => ApplicationStore.onClickLink(urls.TC)}
            />
          ),
          privacy: (
            <Text
              style={styles.link}
              onPress={() => ApplicationStore.onClickLink(urls.PP)}
            />
          ),
        }}
      />
    </BtText>
  )
})

const SupplierApplicationStatement = observer(() => {
  const { t } = useTranslation('application')
  const {
    previewInvoicePurchaseAgreement,
    previewSellerAgreement,
    previewGuarantySecuredAgreement,
  } = ApplicationStore

  return (
    <BtText style={styles.agreementText}>
      <Trans
        t={t}
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        i18nKey="BySubmittingYourApplication"
        components={{
          BCA: (
            <Text
              style={{
                color: commonColors.accentText,
                textDecorationLine: 'underline',
              }}
              onPress={previewSellerAgreement}
            />
          ),
          GSA: (
            <Text
              style={{
                color: commonColors.accentText,
                textDecorationLine: 'underline',
              }}
              onPress={previewGuarantySecuredAgreement}
            />
          ),
          IPA: (
            <Text
              style={{
                color: commonColors.accentText,
                textDecorationLine: 'underline',
              }}
              onPress={previewInvoicePurchaseAgreement}
            />
          ),
        }}
      />
    </BtText>
  )
})

const styles = StyleSheet.create({
  container: {
    width: '100%',
    padding: 20,
    alignItems: 'center',
    alignSelf: 'baseline',
    marginBottom: 12,
    backgroundColor: '#FFF',
  },
  containerMobile: {
    marginBottom: 0,
    backgroundColor: '#F5F7F8',
  },
  agreementText: {
    maxWidth: 615,
    textAlign: 'justify',
    color: '#668598',
    lineHeight: 21,
  },
  creditAgreementText: {
    maxWidth: 720,
    textAlign: 'center',
    color: '#668598',
    fontSize: 12,
    fontWeight: '400',
    lineHeight: 14,
  },
  link: {
    color: '#00A0F3',
  },
  underline: {
    textDecorationLine: 'underline',
  },
  acceptAgreementsCheckbox: {
    width: 20,
    height: 20,
    marginTop: 2,
    marginRight: 10,
  },
})
