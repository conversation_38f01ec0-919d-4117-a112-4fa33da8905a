import {
  Company,
  createPagination,
  CustomerAccount,
  Invoice,
  LMS,
  LoanApplication,
  Operation,
  User,
  UserRole,
} from '@linqpal/common-backend'
import {
  invoiceStatus,
  IPayablesVendors,
  LOAN_APPLICATION_STATUS,
  OPERATION_STATUS,
  OPERATION_TYPES,
  PayablesVendorsSort,
  PayablesVendorsSortColumn,
  PayablesVendorsSortColumnType,
  PROCESSING_STATUSES,
} from '@linqpal/models/src/dictionaries'
import {
  DBSortingOrder,
  DBSortingOrderType,
  PaginatedResult,
  SortingOrder,
} from '@linqpal/models/src/dictionaries/global'
import { Request } from 'express'
import { PipelineStage, Types } from 'mongoose'
import {
  CompanyStatus,
  EInvoiceType,
  exceptions,
  InvoicePaymentType,
} from '@linqpal/models'
import moment from 'moment'
import { CREDIT_STATUS } from '@linqpal/models/src/dictionaries/creditApplicationTypes'
import { CreditApplicationType } from '@linqpal/models/src/dictionaries/creditApplicationType'

export async function getVendorsList(
  req: Request,
): Promise<PaginatedResult<IPayablesVendors>> {
  const cstNow = moment().tz('America/Chicago').toDate()
  const builderId = req.company!._id.toString()

  const search = req.query?.search?.toString() ?? ''

  const { paginationPipeline, pageSize } = createPagination(req.query)
  const paginationArr =
    req.query?.pageSize && req.query?.page ? paginationPipeline : []

  const sortColumn = req.query?.sortColumn
    ? req.query.sortColumn.toString()
    : PayablesVendorsSort.CREATED_AT

  const sortDirection =
    req.query.sortDirection ||
    (sortColumn === PayablesVendorsSort.CREATED_AT
      ? SortingOrder.DESC
      : SortingOrder.ASC)

  const userRoles = await UserRole.aggregate([
    { $match: { company_id: builderId } },
    {
      $lookup: {
        from: User.collection.name,
        as: 'user',
        localField: 'sub',
        foreignField: 'sub',
      },
    },
    { $unwind: '$user' },
  ])
  const matchConditions = userRoles
    .map((userRole) => userRole.user.email || userRole.user.login)
    .filter(Boolean)
    .map(String)

  const customerAccounts = await CustomerAccount.aggregate([
    {
      $match: {
        $or: [
          { email: { $in: matchConditions } },
          { phone: { $in: matchConditions } },
        ],
      },
    },
  ])

  const customerAccountIds = customerAccounts.map((ca) => ca._id.toString())

  const customerAccountMap: { [key: string]: boolean } = {}
  customerAccounts.forEach((account) => {
    customerAccountMap[account._id.toString()] =
      account.settings?.inHouseCredit?.isEnabled || false
  })

  const pipeline: PipelineStage[] = [
    {
      $match: {
        $and: [
          {
            $or: [
              { customer_account_id: { $in: customerAccountIds } },
              { payer_id: builderId },
            ],
          },
          {
            isDeleted: {
              $ne: true,
            },
          },
          {
            $or: [
              { status: { $ne: invoiceStatus.draft } },
              {
                $and: [
                  { status: { $eq: invoiceStatus.draft } },
                  {
                    $or: [
                      { company_id: null },
                      { company_id: { $exists: false } },
                      { company_id: '' },
                    ],
                  },
                ],
              },
            ],
          },
          {
            $or: [
              { type: { $ne: EInvoiceType.QUOTE } },
              {
                type: EInvoiceType.QUOTE,
                $or: [
                  {
                    'paymentDetails.paymentType': {
                      $ne: InvoicePaymentType.FACTORING,
                    },
                  },
                  { paymentDetails: null },
                  { paymentDetails: { $exists: false } },
                  { 'paymentDetails.paymentType': null },
                  { 'paymentDetails.paymentType': { $exists: false } },
                ],
              },
            ],
          },
        ],
      },
    },
    {
      $lookup: {
        from: User.collection.name,
        let: {
          email: '$supplierInvitationDetails.email',
          company_id: '$company_id',
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $ne: ['$$email', ''] },
                  { $ne: ['$$email', null] },
                  { $eq: ['$email', '$$email'] },
                  { $eq: [{ $ifNull: ['$$company_id', ''] }, ''] }, // Only perform lookup if company_id is null/empty
                ],
              },
            },
          },
          {
            $lookup: {
              from: UserRole.collection.name,
              localField: 'sub',
              foreignField: 'sub',
              as: 'userRoleDetails',
            },
          },
          {
            $unwind: {
              path: '$userRoleDetails',
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $lookup: {
              from: Company.collection.name,
              let: {
                company_id: {
                  $convert: {
                    input: '$userRoleDetails.company_id',
                    to: 'objectId',
                    onError: null,
                    onNull: null,
                  },
                },
              },
              pipeline: [
                { $match: { $expr: { $eq: ['$_id', '$$company_id'] } } },
                { $match: { status: CompanyStatus.Approved } },
              ],
              as: 'companyDetails',
            },
          },
          {
            $project: {
              company_id: {
                $cond: {
                  if: { $gt: [{ $size: '$companyDetails' }, 0] },
                  then: '$userRoleDetails.company_id',
                  else: null,
                },
              },
            },
          },
        ],
        as: 'userDetails',
      },
    },
    {
      $unwind: {
        path: '$userDetails',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $addFields: {
        supplier_id: {
          $cond: {
            if: { $eq: [{ $ifNull: ['$company_id', ''] }, ''] },
            then: {
              $cond: {
                if: {
                  $or: [
                    {
                      $eq: [
                        { $ifNull: ['$supplierInvitationDetails.email', ''] },
                        '',
                      ],
                    },
                    { $eq: [{ $ifNull: ['$userDetails', null] }, null] },
                    {
                      $eq: [
                        { $ifNull: ['$userDetails.company_id', null] },
                        null,
                      ],
                    },
                    { $eq: [{ $ifNull: ['$userDetails.company_id', ''] }, ''] },
                  ],
                },
                then: {
                  $concat: [
                    {
                      $ifNull: [
                        { $trim: { input: '$supplierInvitationDetails.name' } },
                        '',
                      ],
                    },
                    '_',
                    {
                      $ifNull: [
                        {
                          $trim: { input: '$supplierInvitationDetails.email' },
                        },
                        '',
                      ],
                    },
                  ],
                },
                else: {
                  $convert: {
                    input: '$userDetails.company_id',
                    to: 'objectId',
                    onError: null,
                  },
                },
              },
            },
            else: {
              $convert: { input: '$company_id', to: 'objectId', onError: null }, // If company_id exists, convert it
            },
          },
        },
      },
    },
    {
      $lookup: {
        from: Company.collection.name,
        localField: 'supplier_id',
        foreignField: '_id',
        as: 'tempCompany',
        pipeline: [
          {
            $project: {
              draft: 0,
              credit: 0,
              finicity: 0,
              address: 0,
              bankAccounts: 0,
            },
          },
        ],
      },
    },
    {
      $addFields: {
        company: {
          $cond: {
            if: { $gt: [{ $size: '$tempCompany' }, 0] },
            then: { $arrayElemAt: ['$tempCompany', 0] },
            else: {
              name: '$supplierInvitationDetails.name',
              phone: '$supplierInvitationDetails.phone',
              email: '$supplierInvitationDetails.email',
              isInvited: true,
            },
          },
        },
      },
    },
  ]

  if (search) {
    pipeline.push({
      $match: {
        'company.name': { $regex: search, $options: 'i' },
      },
    })
  }

  const additionalStages: PipelineStage[] = [
    {
      $lookup: {
        from: Operation.collection.name,
        let: { invoice_id: { $toString: '$_id' } },
        pipeline: [
          { $match: { $expr: { $eq: ['$owner_id', '$$invoice_id'] } } },
          {
            $match: {
              $expr: {
                $or: [
                  {
                    $and: [
                      {
                        $in: [
                          '$status',
                          [
                            OPERATION_STATUS.PLACED,
                            OPERATION_STATUS.PROCESSING,
                            OPERATION_STATUS.SUCCESS,
                            OPERATION_STATUS.FAIL,
                          ],
                        ],
                      },
                      { $eq: ['$type', OPERATION_TYPES.INVOICE.PAYMENT] },
                    ],
                  },
                  {
                    $and: [
                      { $eq: ['$status', OPERATION_STATUS.SUCCESS] },
                      { $eq: ['$type', OPERATION_TYPES.INVOICE.REFUND] },
                    ],
                  },
                ],
              },
            },
          },
          { $limit: 1 },
        ],
        as: 'operation',
      },
    },
    {
      $lookup: {
        from: LoanApplication.collection.name,
        let: { invoice_id: { $toString: '$_id' } },
        pipeline: [
          {
            $addFields: {
              ids: {
                $cond: {
                  if: {
                    $eq: [{ $type: '$invoiceDetails.invoiceId' }, 'array'],
                  },
                  then: '$invoiceDetails.invoiceId',
                  else: ['$invoiceDetails.invoiceId'],
                },
              },
            },
          },
          { $match: { $expr: { $in: ['$$invoice_id', '$ids'] } } },
          { $sort: { createdAt: -1 } },
          { $limit: 1 },
          { $project: { status: 1, creditApplicationId: 1 } },
        ],
        as: 'loanApp',
      },
    },
    { $unwind: { path: '$loanApp', preserveNullAndEmptyArrays: true } },

    {
      $addFields: {
        operation: { $arrayElemAt: ['$operation', 0] },
        creditApplicationId: {
          $ifNull: ['$loanApp.creditApplicationId', null],
        },
      },
    },
    {
      $addFields: {
        totalPaidAmount: {
          $cond: [
            { $ifNull: ['$operation.paidAmount', false] },
            '$operation.paidAmount',
            {
              $cond: [
                { $eq: ['$operation.status', OPERATION_STATUS.SUCCESS] },
                '$operation.amount',
                0,
              ],
            },
          ],
        },
      },
    },
    {
      $addFields: {
        totalProcessingAmount: {
          $cond: [
            { $ifNull: ['$operation.processingAmount', false] },
            '$operation.processingAmount',
            {
              $cond: [
                {
                  $and: [
                    { $eq: ['$operation.status', OPERATION_STATUS.PROCESSING] },
                    { $eq: ['$totalPaidAmount', 0] },
                  ],
                },
                '$operation.amount',
                0,
              ],
            },
          ],
        },
      },
    },
    {
      $addFields: {
        totalRemainingAmount: {
          $round: [
            {
              $subtract: [
                {
                  $round: [
                    {
                      $add: [
                        { $ifNull: ['$operation.amount', '$total_amount'] },
                        { $ifNull: ['$lateFee', 0] },
                        { $ifNull: ['$paymentDetails.customerFee', 0] },
                      ],
                    },
                    2,
                  ],
                },
                {
                  $round: [
                    {
                      $add: [
                        { $ifNull: ['$totalPaidAmount', 0] },
                        { $ifNull: ['$totalProcessingAmount', 0] },
                      ],
                    },
                    2,
                  ],
                },
              ],
            },
            2,
          ],
        },
      },
    },

    {
      $addFields: {
        isActiveInvoice: {
          $and: [
            {
              $or: [
                {
                  $not: {
                    $in: [
                      '$status',
                      [
                        invoiceStatus.dismissed,
                        invoiceStatus.cancelled,
                        invoiceStatus.draft,
                      ],
                    ],
                  },
                },
                {
                  $and: [
                    { $eq: ['$status', invoiceStatus.draft] },
                    { $gt: ['$supplierInvitationDetails.name', ''] },
                    { approved: false },
                  ],
                },
              ],
            },
            {
              $or: [
                { $eq: [{ $ifNull: ['$loanApp', null] }, null] },
                {
                  $not: {
                    $in: [
                      '$loanApp.status',
                      [
                        ...PROCESSING_STATUSES,
                        LOAN_APPLICATION_STATUS.APPROVED,
                      ],
                    ],
                  },
                },
              ],
            },
            {
              $or: [
                { $eq: [{ $ifNull: ['$operation', null] }, null] },
                { $eq: [{ $ifNull: ['$operation.status', null] }, null] },
                {
                  $and: [
                    { $ne: ['$operation.status', OPERATION_STATUS.SUCCESS] },
                    { $gt: ['$totalRemainingAmount', 0] },
                  ],
                },
              ],
            },
            {
              $or: [
                {
                  $and: [
                    { $ne: [{ $ifNull: ['$expiration_date', null] }, null] },
                    { $lte: [cstNow, '$expiration_date'] },
                  ],
                },
                { $eq: [{ $ifNull: ['$expiration_date', null] }, null] },
              ],
            },
            /*{
              $ne: ['$type', EInvoiceType.QUOTE],
            },*/
            {
              $eq: ['$type', EInvoiceType.INVOICE],
            },
          ],
        },
      },
    },
    {
      $addFields: {
        isPastDue: {
          $cond: [
            {
              $and: [
                { $eq: ['$isActiveInvoice', true] },
                {
                  $or: [
                    { $eq: [{ $ifNull: ['$operation', null] }, null] },
                    { $eq: ['$operation.status', OPERATION_STATUS.PLACED] },
                  ],
                },
                { $gt: [cstNow, '$invoice_due_date'] },
              ],
            },
            true,
            false,
          ],
        },
      },
    },
    ...(sortColumn === PayablesVendorsSort.CREATED_AT
      ? [
          {
            $sort: { createdAt: -1 },
          } as PipelineStage,
        ]
      : []),
    {
      $group: {
        _id: '$supplier_id',
        supplierName: { $first: '$company.name' },
        supplierEmail: { $first: '$company.email' },
        customerAccountIds: { $addToSet: '$customer_account_id' },
        supplierPhone: { $first: '$company.phone' },
        supplierIsInvited: {
          $first: {
            $ifNull: ['$company.isInvited', false],
          },
        },
        creditApplicationId: { $first: '$creditApplicationId' },
        arAdvanceIsEnabled: {
          $first: {
            $ifNull: ['$company.settings.arAdvance.isEnabled', false],
          },
        },
        totalDueInvoices: {
          $sum: { $cond: [{ $eq: ['$isActiveInvoice', true] }, 1, 0] },
        },
        totalDueSum: {
          $sum: {
            $cond: [
              { $eq: ['$isActiveInvoice', true] },
              { $round: ['$totalRemainingAmount', 2] },
              0,
            ],
          },
        },
        totalPastDueInvoices: {
          $sum: { $cond: [{ $eq: ['$isPastDue', true] }, 1, 0] },
        },
        totalPastDueSum: {
          $sum: {
            $cond: [{ $eq: ['$isPastDue', true] }, '$totalRemainingAmount', 0],
          },
        },
        latestInvoiceCreatedAt: { $first: '$createdAt' },
      },
    },
    {
      $project: {
        _id: 0,
        id: '$_id',
        supplierName: 1,
        supplierPhone: 1,
        supplierEmail: 1,
        supplierIsInvited: 1,
        customerAccountIds: 1,
        arAdvanceIsEnabled: 1,
        totalDueInvoices: 1,
        totalDueSum: 1,
        creditApplicationId: 1,
        totalPastDueInvoices: 1,
        totalPastDueSum: 1,
        latestInvoiceCreatedAt: 1,
      },
    },
  ]

  pipeline.push(...additionalStages)

  pipeline.push({
    $sort: {
      [PayablesVendorsSortColumn[sortColumn as PayablesVendorsSortColumnType]]:
        DBSortingOrder[sortDirection as DBSortingOrderType],
    },
  })

  pipeline.push({
    $facet: {
      totalCount: [
        {
          $count: 'count',
        },
      ],
      paginatedResults: paginationArr,
    },
  })

  const [invoiceAggregation] = await Invoice.aggregate(pipeline)

  if (!invoiceAggregation?.paginatedResults?.length) {
    return {
      items: [],
      count: 0,
    }
  }

  invoiceAggregation.paginatedResults = invoiceAggregation.paginatedResults.map(
    (item: IPayablesVendors) => {
      const inHouseCreditIsEnabled = item.customerAccountIds.some(
        (accountId) => customerAccountMap[accountId],
      )

      return {
        ...item,
        inHouseCreditIsEnabled,
      }
    },
  )

  const responseData: { items: IPayablesVendors[]; count: number } = {
    items: invoiceAggregation.paginatedResults,
    count:
      pageSize > 0
        ? invoiceAggregation.totalCount[0].count
        : invoiceAggregation.paginatedResults.length,
  }

  return responseData
}

export async function isFactoringEnabled(req: Request) {
  const supplierCompanyId = req.query?.supplierId as string
  const customerAccountId = req.query?.customerAccountId

  if (!(supplierCompanyId || customerAccountId)) {
    throw new exceptions.LogicalError(`Vendor or customer id was not provided.`)
  }

  let query = {}

  if (customerAccountId) {
    Object.assign(query, {
      _id: new Types.ObjectId(customerAccountId as string),
    })
  } else {
    const builderId = req.company!._id.toString()
    const userRoles = await UserRole.aggregate([
      { $match: { company_id: builderId } },
      {
        $lookup: {
          from: User.collection.name,
          as: 'user',
          localField: 'sub',
          foreignField: 'sub',
        },
      },
      { $unwind: '$user' },
    ])

    const matchConditions = userRoles
      .map((userRole) => userRole.user.email || userRole.user.login)
      .filter(Boolean)
      .map(String)

    Object.assign(query, {
      $and: [
        {
          $or: [
            { email: { $in: matchConditions } },
            { phone: { $in: matchConditions } },
          ],
        },
        { company_id: supplierCompanyId },
      ],
    })
    console.log(matchConditions)
  }

  const result = await CustomerAccount.aggregate([
    {
      $match: query,
    },
    {
      $addFields: {
        supplier_id: {
          $convert: {
            input: '$company_id',
            to: 'objectId',
            onError: null,
          },
        },
      },
    },
    {
      $lookup: {
        from: Company.collection.name,
        localField: 'supplier_id',
        foreignField: '_id',
        as: 'companyInfo',
      },
    },
    {
      $unwind: {
        path: '$companyInfo',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $addFields: {
        arAdvanceIsEnabled: {
          $ifNull: ['$companyInfo.settings.arAdvance.isEnabled', false],
        },
        inHouseCreditIsEnabled: {
          $ifNull: ['$settings.inHouseCredit.isEnabled', false],
        },
      },
    },
    {
      $group: {
        _id: null,
        arAdvanceIsEnabled: { $first: '$arAdvanceIsEnabled' },
        inHouseCreditIsEnabled: { $max: '$inHouseCreditIsEnabled' },
      },
    },
    {
      $project: {
        _id: 0,
        arAdvanceIsEnabled: 1,
        inHouseCreditIsEnabled: 1,
      },
    },
  ]).exec()

  const customerAccount = result.length > 0 ? result[0] : null

  if (!customerAccount) {
    return {
      arAdvanceIsEnabled: false,
      inHouseCreditIsEnabled: false,
    }
  }

  return {
    arAdvanceIsEnabled: customerAccount.arAdvanceIsEnabled,
    inHouseCreditIsEnabled: customerAccount.inHouseCreditIsEnabled,
  }
}

export async function getInHouseCreditInfo(req: Request) {
  const supplierId = req.query?.supplierId as string
  const builderId = req.company!._id.toString()

  if (!supplierId)
    throw new exceptions.LogicalError(`Vendor id was not provided.`)

  const credits = await LMS.getCreditCompanyInfo(builderId)

  if (!credits?.length) {
    return {
      totalCreditLimit: 0,
      totalAvailableCredit: 0,
      totalOutstandingCredit: 0,
    }
  }

  const inHouseCredit = credits.find(
    (credit) =>
      credit.product === CreditApplicationType.InHouseCredit &&
      credit.merchantId === supplierId &&
      [CREDIT_STATUS.ACTIVE, CREDIT_STATUS.PAST_DUE].includes(credit.status),
  )

  if (!inHouseCredit) {
    return {
      totalCreditLimit: 0,
      totalAvailableCredit: 0,
      totalOutstandingCredit: 0,
    }
  }

  return {
    totalCreditLimit: inHouseCredit.creditLimit,
    totalAvailableCredit: inHouseCredit.creditDetails.availableCredit,
    totalOutstandingCredit: inHouseCredit.creditDetails.outstandingCredit,
  }
}
