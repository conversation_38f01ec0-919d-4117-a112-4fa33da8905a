import { User, UserRole } from '../models'
import { exceptions } from '@linqpal/models'
import SmsBuilder from '../helpers/SmsBuilder'
import { Logger } from './logger/logger.service'

const logger = new Logger({
  module: 'BlueTape',
  subModule: 'smsService',
})

const isDev = process.env.LP_MODE !== 'prod'

export async function send(phone: string | null | undefined, message: string) {
  if (!phone) {
    logger.warn(`no phone number to send message ${message}`)
    return
  }

  try {
    const TWILIO = {
      accountSid: isDev
        ? '**********************************'
        : process.env.LP_TWILIO_ACCOUNT_SID,
      authToken: isDev
        ? '1f4ffe1040c7e76676992bef3cc4a3cf'
        : process.env.LP_TWILIO_AUTH_TOKEN,
      phoneNumber: isDev ? '+***********' : process.env.LP_TWILIO_PHONE_NUMBER,
    }
    const client = require('twilio')(TWILIO.accountSid, TWILIO.authToken)
    const msg = {
      body: message,
      from: TWILIO.phoneNumber,
      to: phone,
    }

    logger.info(`sending Twilio message: ${JSON.stringify(msg)}`)
    return client.messages.create(msg)
  } catch (e: any) {
    logger.error(e, 'sending Twilio message failed')
    throw e
  }
}

async function smsUser(companyId: string, message: string) {
  const role = await UserRole.findOne({ company_id: companyId })
  if (!role) throw new exceptions.LogicalError('error/role-does-not-exist')

  const user = await User.findOne({ sub: role.sub })
  if (!user) throw new exceptions.LogicalError('error/user-does-not-exist')

  if (user.phone) {
    logger.info(`sending SMS to user ${user.id}: ${message}`)
    await send(user.phone, message)
  } else {
    logger.warn(`no phone for user ${user.id}`)
  }
}

export default {
  send,
  TEMPLATES: {
    INVITE: 'd-8a61befcf92141868f72803ea4ae629a',
  },
  smsUser,
  getMessage: SmsBuilder.getMessage,
}
