import { RouteBase } from '../types'
import {
  IGetLenderApplicationResponse,
  IGetUnifiedApplicationRequest,
  IGetUnifiedApplicationResponse,
  IPostLenderApplicationRequest,
  IPostLenderApplicationResponse,
} from './types'

export class Application extends RouteBase {
  domain = 'application'

  getUnifiedApplicationDraft(request: IGetUnifiedApplicationRequest) {
    return this.buildGetRoute<IGetUnifiedApplicationResponse>(
      this.getUnifiedApplicationDraft,
      request,
    )
  }

  getLenderApplicationDraft() {
    return this.buildGetRoute<IGetLenderApplicationResponse>(
      this.getLenderApplicationDraft,
    )
  }

  postLenderApplicationDraft(request: IPostLenderApplicationRequest) {
    return this.buildPostRoute<IPostLenderApplicationResponse>(
      this.postLenderApplicationDraft.name,
      request,
    )
  }
}

export default new Application()
