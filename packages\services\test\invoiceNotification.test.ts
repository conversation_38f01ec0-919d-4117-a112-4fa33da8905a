import { sendNotification } from '../linqpal/invoiceNotification'
import { beforeEachMockSecretsManager } from './helper'
import {
  Company,
  CustomerAccount,
  Draft,
  emailService,
  Invoice,
  InvoiceNotification,
  Operation,
  Sms,
} from '@linqpal/common-backend'
import moment from 'moment'
import chai from 'chai'
import sinon from 'sinon'
import { ClientResponse } from '@sendgrid/mail'
import {
  CompanyStatus,
  dictionaries,
  EInvoiceNotificationType,
} from '@linqpal/models'

async function createCompany() {
  return Company.create({
    name: 'Supplier Inc.',
    type: 'supplier',
    status: CompanyStatus.Approved,
  })
}

async function createOperation(
  invoiceId: string,
  status: string,
  amount = 1000,
) {
  return Operation.create({
    owner_id: invoiceId,
    amount: amount,
    metadata: {},
    status,
    type: 'invoice_payment',
  })
}

async function createCustomerAccount(companyId: string) {
  await CustomerAccount.create({
    company_id: companyId.toString(),
    phone: '+***********',
    name: 'Builder Inc',
    first_name: 'Some',
    last_name: 'One',
    email: '<EMAIL>',
    address: 'address',
  })
  return CustomerAccount.create({
    company_id: companyId.toString(),
    phone: '+***********',
    name: 'Builder Inc',
    first_name: 'Some',
    last_name: 'One',
    email: '<EMAIL>',
    address: 'address',
  })
}

async function createInvoice(
  companyId: string,
  customerAccountId: string,
  date: string,
  expirationDate: string | null,
  invoiceNotificationType = EInvoiceNotificationType.BOTH,
) {
  const invoiceNumber = Math.random().toString(36).slice(-5)
  const amount = Math.floor(Math.random() * (10000 - 1000 + 1) + 1000)

  return Invoice.create({
    address: '',
    addressType: 'Pickup',
    company_id: companyId.toString(),
    customer_account_id: customerAccountId.toString(),
    expiration_date: expirationDate ? expirationDate : null,
    invoice_date: date,
    invoice_document: null,
    invoice_due_date: date,
    invoice_number: invoiceNumber,
    isDeleted: false,
    material_description: 'desc',
    note: '',
    status: 'PLACED',
    total_amount: amount,
    invoiceNotificationType,
  })
}

async function createApplication(companyId: string, state = 'New York') {
  return Draft.create({
    filled: [],
    company_id: companyId.toString(),
    type: 'supplier_application',
    data: {
      registered: {
        group: 'registered',
        items: [
          {
            identifier: 'details',
            title: 'Business Address',
            content: {
              state: state,
            },
          },
        ],
      },
    },
  })
}

chai.should()

describe('Invoice builder notification', () => {
  let stubSmsSend: sinon.SinonStub,
    clock: sinon.SinonFakeTimers,
    timeZoneOffset: () => number,
    stubEmailSend: sinon.SinonStub
  beforeEachMockSecretsManager()
  beforeEach(async () => {
    stubSmsSend = sinon.stub(Sms, 'send').callsFake(async () => {})
    stubEmailSend = sinon
      .stub(emailService, 'send')
      .callsFake(() => Promise.resolve([{} as ClientResponse, {}]))
    timeZoneOffset = Date.prototype.getTimezoneOffset
    // eslint-disable-next-line no-extend-native
    Date.prototype.getTimezoneOffset = function () {
      return 300 // -5 GMT Chicago
    }
    clock = sinon.useFakeTimers()
  })
  afterEach(() => {
    stubSmsSend.restore()
    stubEmailSend.restore()
    clock.restore()
    // eslint-disable-next-line no-extend-native
    Date.prototype.getTimezoneOffset = timeZoneOffset
  })

  it('40 days before DUE DATE', async () => {
    const company = await createCompany()
    const customerAccount = await createCustomerAccount(company.id)
    await createApplication(company.id)

    const today = moment(`2021-10-10 00:00`)
    const mockedToday = today.valueOf()
    sinon.useFakeTimers(mockedToday)

    const dueDate = today.add(40, 'days').format('MM/DD/YYYY')
    const invoice = await createInvoice(
      company.id,
      customerAccount.id,
      dueDate,
      null,
    )

    await sendNotification()

    const invoiceNotification = await InvoiceNotification.find({
      invoiceId: invoice._id,
      phone: customerAccount.phone,
    })
    invoiceNotification.should.have.lengthOf(0)
    // invoiceNotification[0].days.should.equal(40)
  })

  it('30 days before DUE DATE', async () => {
    const company = await createCompany()
    const customerAccount = await createCustomerAccount(company.id)
    await createApplication(company.id)

    const today = moment(`2021-12-10`)
    const mockedToday = today.valueOf()
    sinon.useFakeTimers(mockedToday)

    const dueDate = today.add(30, 'days').format('MM/DD/YYYY')
    const invoice = await createInvoice(
      company.id,
      customerAccount.id,
      dueDate,
      null,
    )
    await sendNotification()
    // check whether notification is not sent two times in same day
    await sendNotification()

    const invoiceNotification = await InvoiceNotification.find({
      invoiceId: invoice._id,
      phone: customerAccount.phone,
    })
    invoiceNotification.should.have.lengthOf(0)
    // invoiceNotification[0].days.should.equal(30)
  })

  it('3 days before DUE DATE', async () => {
    const company = await createCompany()
    const customerAccount = await createCustomerAccount(company.id)
    await createApplication(company.id)

    const today = moment('2021-12-10')
    const mockedToday = today.valueOf()
    sinon.useFakeTimers(mockedToday)

    const dueDate = today.add(3, 'days').format('MM/DD/YYYY')
    const invoice = await createInvoice(
      company.id,
      customerAccount.id,
      dueDate,
      null,
      EInvoiceNotificationType.EMAIL,
    )
    await sendNotification()
    const invoiceNotification = (await InvoiceNotification.find({
      invoiceId: invoice._id,
    })) as any[]
    invoiceNotification.should.have.lengthOf(1)
    invoiceNotification[0].days.should.equal(3)
  })

  it('DUE DATE is TODAY', async () => {
    const company = await createCompany()
    const customerAccount = await createCustomerAccount(company.id)
    await createApplication(company.id)

    const today = moment('2021-12-10')
    const mockedToday = today.valueOf()
    sinon.useFakeTimers(mockedToday)

    const dueDate = today.format('MM/DD/YYYY')
    const invoice = await createInvoice(
      company.id,
      customerAccount.id,
      dueDate,
      null,
    )
    await sendNotification()
    const invoiceNotification = (await InvoiceNotification.find({
      invoiceId: invoice._id,
    })) as any[]
    invoiceNotification.should.have.lengthOf(1)
    invoiceNotification[0].days.should.equal(0)
  })

  it('DUE DATE is TODAY, send only email reminder when notification type is set to email', async () => {
    const company = await createCompany()
    const customerAccount = await createCustomerAccount(company.id)
    await createApplication(company.id)

    const today = moment('2021-12-10')
    const mockedToday = today.valueOf()
    sinon.useFakeTimers(mockedToday)

    const dueDate = today.format('MM/DD/YYYY')
    const invoice = await createInvoice(
      company.id,
      customerAccount.id,
      dueDate,
      null,
      EInvoiceNotificationType.EMAIL,
    )
    await sendNotification()
    stubSmsSend.called.should.eq(false)
    stubEmailSend.called.should.eq(true)
    const invoiceNotification = (await InvoiceNotification.find({
      invoiceId: invoice._id,
    })) as any[]
    invoiceNotification.should.have.lengthOf(1)
    invoiceNotification[0].days.should.equal(0)
  })

  it('DUE DATE is TODAY, send only sms reminder when notification type is set to sms', async () => {
    const company = await createCompany()
    const customerAccount = await createCustomerAccount(company.id)
    await createApplication(company.id)

    const today = moment('2021-12-10')
    const mockedToday = today.valueOf()
    sinon.useFakeTimers(mockedToday)

    const dueDate = today.format('MM/DD/YYYY')
    const invoice = await createInvoice(
      company.id,
      customerAccount.id,
      dueDate,
      null,
      EInvoiceNotificationType.SMS,
    )
    await sendNotification()
    stubSmsSend.called.should.eq(true)
    stubEmailSend.called.should.eq(false)
    const invoiceNotification = (await InvoiceNotification.find({
      invoiceId: invoice._id,
    })) as any[]
    invoiceNotification.should.have.lengthOf(1)
    invoiceNotification[0].days.should.equal(0)
  })

  it('DUE DATE is TODAY, send both sms and email reminders when notification type is set to both', async () => {
    const company = await createCompany()
    const customerAccount = await createCustomerAccount(company.id)
    await createApplication(company.id)

    const today = moment('2021-12-10')
    const mockedToday = today.valueOf()
    sinon.useFakeTimers(mockedToday)

    const dueDate = today.format('MM/DD/YYYY')
    const invoice = await createInvoice(
      company.id,
      customerAccount.id,
      dueDate,
      null,
      EInvoiceNotificationType.BOTH,
    )
    await sendNotification()
    stubSmsSend.called.should.eq(true)
    stubEmailSend.called.should.eq(true)
    const invoiceNotification = (await InvoiceNotification.find({
      invoiceId: invoice._id,
    })) as any[]
    invoiceNotification.should.have.lengthOf(1)
    invoiceNotification[0].days.should.equal(0)
  })

  it('1 Day Past Due', async () => {
    const company = await createCompany()
    const customerAccount = await createCustomerAccount(company.id)
    await createApplication(company.id)

    const today = moment(`2021-12-23`)
    const mockedToday = today.valueOf()
    sinon.useFakeTimers(mockedToday)

    const dueDate = today.subtract(1, 'days').format('MM/DD/YYYY')
    const invoice = await createInvoice(
      company.id,
      customerAccount.id,
      dueDate,
      null,
    )

    await sendNotification()

    const invoiceNotification = (await InvoiceNotification.find({
      invoiceId: invoice._id,
    })) as any[]
    invoiceNotification.should.have.lengthOf(1)
    invoiceNotification[0].days.should.equal(-1)
  })

  it('Invoice payment failed notification', async () => {
    const company = await createCompany()
    const customerAccount = await createCustomerAccount(company.id)
    await createApplication(company.id)

    const today = moment(`2021-12-23`)
    const mockedToday = today.valueOf()
    sinon.useFakeTimers(mockedToday)

    const dueDate = today.subtract(1, 'days').format('MM/DD/YYYY')
    const invoice = await createInvoice(
      company.id,
      customerAccount.id,
      dueDate,
      null,
    )
    await createOperation(invoice.id, dictionaries.OPERATION_STATUS.FAIL, 1000)

    await sendNotification()

    const invoiceNotification = (await InvoiceNotification.find({
      invoiceId: invoice._id,
    })) as any[]
    invoiceNotification.should.have.lengthOf(1)
    invoiceNotification[0].days.should.equal(-1)
    invoiceNotification[0].sms.should.contain('has failed')
  })

  it("Don't send notification for paid invoices", async () => {
    const company = await createCompany()
    const customerAccount = await createCustomerAccount(company.id)
    await createApplication(company.id)

    const today = moment(`2021-12-23`)
    const mockedToday = today.valueOf()
    sinon.useFakeTimers(mockedToday)

    const dueDate = today.subtract(1, 'days').format('MM/DD/YYYY')
    const invoice = await createInvoice(
      company.id,
      customerAccount.id,
      dueDate,
      null,
    )
    await createOperation(
      invoice.id,
      dictionaries.OPERATION_STATUS.SUCCESS,
      1000,
    )

    await sendNotification()

    const invoiceNotification = (await InvoiceNotification.find({
      invoiceId: invoice._id,
    })) as any[]
    invoiceNotification.should.have.lengthOf(0)
  })

  it('5 Days Past Due', async () => {
    const company = await createCompany()
    const customerAccount = await createCustomerAccount(company.id)
    await createApplication(company.id)

    const today = moment(`2021-12-23`)
    const mockedToday = today.valueOf()
    sinon.useFakeTimers(mockedToday)

    const dueDate = today.subtract(5, 'days').format('MM/DD/YYYY')
    const invoice = await createInvoice(
      company.id,
      customerAccount.id,
      dueDate,
      null,
    )

    await sendNotification()

    const invoiceNotification = (await InvoiceNotification.find({
      invoiceId: invoice._id,
    })) as any[]
    invoiceNotification.should.have.lengthOf(1)
    invoiceNotification[0].days.should.equal(-5)
  })

  it('8 Days Past Due', async () => {
    const company = await createCompany()
    const customerAccount = await createCustomerAccount(company.id)
    await createApplication(company.id)

    const today = moment(`2021-12-23`)
    const mockedToday = today.valueOf()
    sinon.useFakeTimers(mockedToday)

    const dueDate = today.subtract(8, 'days').format('MM/DD/YYYY')
    const invoice = await createInvoice(
      company.id,
      customerAccount.id,
      dueDate,
      null,
    )

    await sendNotification()

    const invoiceNotification = (await InvoiceNotification.find({
      invoiceId: invoice._id,
    })) as any[]
    invoiceNotification.should.have.lengthOf(0)
  })

  it('14 Days Past Due', async () => {
    const company = await createCompany()
    const customerAccount = await createCustomerAccount(company.id)
    await createApplication(company.id)

    const today = moment(`2021-12-23`)
    const mockedToday = today.valueOf()
    sinon.useFakeTimers(mockedToday)

    const dueDate = today.subtract(14, 'days').format('MM/DD/YYYY')
    const invoice = await createInvoice(
      company.id,
      customerAccount.id,
      dueDate,
      null,
    )

    await sendNotification()

    const invoiceNotification = (await InvoiceNotification.find({
      invoiceId: invoice._id,
    })) as any[]
    invoiceNotification.should.have.lengthOf(1)
    invoiceNotification[0].days.should.equal(-14)
  })

  it('15 Days Past Due', async () => {
    const company = await createCompany()
    const customerAccount = await createCustomerAccount(company.id)
    await createApplication(company.id)

    const today = moment(`2021-12-23`)
    const mockedToday = today.valueOf()
    sinon.useFakeTimers(mockedToday)

    const dueDate = today.subtract(15, 'days').format('MM/DD/YYYY')
    const invoice = await createInvoice(
      company.id,
      customerAccount.id,
      dueDate,
      null,
    )

    await sendNotification()

    const invoiceNotification = (await InvoiceNotification.find({
      invoiceId: invoice._id,
    })) as any[]
    invoiceNotification.should.have.lengthOf(0)
  })

  it('Expires in 3 days', async () => {
    const company = await createCompany()
    const customerAccount = await createCustomerAccount(company.id)
    await createApplication(company.id)

    const today = moment(`2021-12-23`)
    const mockedToday = today.valueOf()
    sinon.useFakeTimers(mockedToday)

    const dueDate = today.add(3, 'days').format('MM/DD/YYYY')
    const invoice = await createInvoice(
      company.id,
      customerAccount.id,
      dueDate,
      dueDate,
    )

    await sendNotification()

    const invoiceNotification = (await InvoiceNotification.find({
      invoiceId: invoice._id,
    })) as any[]
    invoiceNotification.should.have.lengthOf(1)
    invoiceNotification[0].days.should.equal(3)
    invoiceNotification[0].sms.should.contains('Expires in 3 days')
  })

  it('Expire Date is Tomorrow', async () => {
    const company = await createCompany()
    const customerAccount = await createCustomerAccount(company.id)
    await createApplication(company.id)

    const today = moment(`2021-12-23`)
    const mockedToday = today.valueOf()
    sinon.useFakeTimers(mockedToday)

    const dueDate = today.add(1, 'days').format('MM/DD/YYYY')
    const invoice = await createInvoice(
      company.id,
      customerAccount.id,
      dueDate,
      dueDate,
    )

    await sendNotification()

    const invoiceNotification = (await InvoiceNotification.find({
      invoiceId: invoice._id,
    })) as any[]
    invoiceNotification.should.have.lengthOf(1)
    invoiceNotification[0].days.should.equal(1)
    invoiceNotification[0].sms.should.contains('Expires in 1 days')
  })

  it('Expire Date is Today', async () => {
    const company = await createCompany()
    const customerAccount = await createCustomerAccount(company.id)
    await createApplication(company.id)

    const today = moment(`2021-12-23`)
    const mockedToday = today.valueOf()
    sinon.useFakeTimers(mockedToday)

    const dueDate = today.format('MM/DD/YYYY')
    const invoice = await createInvoice(
      company.id,
      customerAccount.id,
      dueDate,
      dueDate,
    )

    await sendNotification()

    const invoiceNotification = (await InvoiceNotification.find({
      invoiceId: invoice._id,
    })) as any[]
    invoiceNotification.should.have.lengthOf(1)
    invoiceNotification[0].days.should.equal(0)
    invoiceNotification[0].sms.should.contains('Expires Today')
  })

  it('Expire date was yesterday', async () => {
    const company = await createCompany()
    const customerAccount = await createCustomerAccount(company.id)
    await createApplication(company.id)

    const today = moment(`2021-12-23`)
    const mockedToday = today.valueOf()
    sinon.useFakeTimers(mockedToday)

    const dueDate = today.subtract(1, 'days').format('MM/DD/YYYY')
    const invoice = await createInvoice(
      company.id,
      customerAccount.id,
      dueDate,
      dueDate,
    )

    await sendNotification()

    const invoiceNotification = (await InvoiceNotification.find({
      invoiceId: invoice._id,
    })) as any[]
    invoiceNotification.should.have.lengthOf(1)
    invoiceNotification[0].days.should.equal(-1)
    invoiceNotification[0].sms.should.contains('has Expired')
  })

  it('Expire date was 5 days ago', async () => {
    const company = await createCompany()
    const customerAccount = await createCustomerAccount(company.id)
    await createApplication(company.id)

    const today = moment(`2021-12-23`)
    const mockedToday = today.valueOf()
    sinon.useFakeTimers(mockedToday)

    const dueDate = today.subtract(5, 'days').format('MM/DD/YYYY')
    const invoice = await createInvoice(
      company.id,
      customerAccount.id,
      dueDate,
      dueDate,
    )

    await sendNotification()

    const invoiceNotification = (await InvoiceNotification.find({
      invoiceId: invoice._id,
    })) as any[]
    invoiceNotification.should.have.lengthOf(0)
  })

  it('Invoice has no customer account', async () => {
    const company = await createCompany()
    await createApplication(company.id)

    const today = moment('2021-12-10')
    const mockedToday = today.valueOf()
    sinon.useFakeTimers(mockedToday)

    const dueDate = today.add(3, 'days').format('MM/DD/YYYY')
    const invoice = await createInvoice(company.id, '', dueDate, null)
    await sendNotification()
    const invoiceNotification = (await InvoiceNotification.find({
      invoiceId: invoice._id,
    })) as any[]
    invoiceNotification.should.have.lengthOf(0)
  })
})
