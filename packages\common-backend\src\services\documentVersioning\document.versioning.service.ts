﻿import axios, { Method } from 'axios'
import { Logger } from '../logger/logger.service'
import { IS3FileDetailsResponse } from './IS3FileDetailsResponse'
import { IDocumentTemplateResponse } from './IDocumentTemplateResponse'
import { IDocumentResponse } from './IDocumentResponse'
import { AgreementType, IAgreementFileDetails } from '../agreement/types'
import { IDocumentMetadataResponse } from './IDocumentMetadataResponse'
import {
  CriticalError,
  SystemError,
} from '@linqpal/models/src/types/exceptions'
import { AgreementService, DocumentVersioningService } from '../../../index'
import { IDocumentVersionsApprovals } from './IDocumentVersionApprovals'
import { IApproverPayload } from './IApproverPayload'
import awsService from '../aws.service'
import { IDocumentApprovalInfo } from './IDocumentApprovalInfo'
import { RequestContext } from '../../helpers/RequestContext'
import { ApiHeaders } from '../../helpers/ApiRequester'
import { IDownloadable } from '@linqpal/models/src/types/approvals'

const log = new Logger({
  module: 'DocumentVersioning',
  subModule: 'documentVersioning.service',
})

export async function getFileS3InfoByIdAndTemplateType(
  referenceId: string,
  templateType: AgreementType,
): Promise<IS3FileDetailsResponse> {
  return requester.get(
    `documents/new/${referenceId}/by-template-type?code=${templateType}`,
  )
}

export async function getNewDocumentOptions(
  companyId: string,
  referenceId: string | null | undefined,
  templateType: AgreementType,
): Promise<IS3FileDetailsResponse> {
  const url = referenceId
    ? `documents/new/${templateType}/company/${companyId}/reference/${referenceId}`
    : `documents/new/${templateType}/company/${companyId}`

  return requester.get(url)
}

export async function getDraftFileS3InfoByReferenceIdAndTemplateId(
  referenceId: string,
  templateId: string,
): Promise<IS3FileDetailsResponse> {
  return requester.get(
    `documents/new/${referenceId}/by-template-id?templateId=${templateId}`,
  )
}

export async function getLatestDocumentByReferenceIdAndType(
  referenceId: string,
  templateType: AgreementType,
): Promise<IDocumentResponse> {
  const requestUrl = `documents/reference/${referenceId}/type/${templateType}/latest`
  return requester.get(requestUrl)
}

export async function getDocumentsByCompanyIdAndType(
  companyId: string,
  templateType: AgreementType[],
): Promise<IDocumentResponse[]> {
  const typesQuery = templateType.map((type) => `types=${type}`).join('&')
  const requestUrl = `documents/company/${companyId}/all?${typesQuery}`

  return requester.get(requestUrl)
}

export async function getDocumentMetadata(
  companyId: string,
  type: AgreementType,
): Promise<IDocumentMetadataResponse> {
  return requester.get(`documents/company/${companyId}/type/${type}/metadata`)
}

export async function getLatestTemplateByType(
  templateType: AgreementType,
): Promise<IDocumentTemplateResponse> {
  return requester.get(`documents/templates?code=${templateType.toString()}`)
}

export async function getTemplateById(
  id: string,
): Promise<IDocumentTemplateResponse> {
  return requester.get(`documents/templates/${id}`)
}

export async function getDownloadLink(id: string): Promise<IDownloadable> {
  const s3Url = await requester.get(`documents/${id}/path`)
  return AgreementService.getAgreementFile(s3Url)
}

export async function createDocument(
  loanApplicationId: string,
  templateId: string,
  s3Url: string,
): Promise<string> {
  const data = {
    documentTemplateId: templateId,
    s3Url: s3Url,
  }

  log.info({ data }, 'creating DVS document')

  const response = await requester.post(
    `documents/loan-application/${loanApplicationId}`,
    data,
    undefined,
  )

  return response.id
}

export async function saveDocument(
  companyId: string,
  referenceId: string,
  templateId: string,
  s3Url: string,
): Promise<string> {
  const data = {
    documentTemplateId: templateId,
    s3Url: s3Url,
  }

  const response = await requester.post(
    `documents/company/${companyId}/reference/${referenceId}`,
    data,
    undefined,
  )

  return response.id
}

export function validateJwt(jwt: string) {
  return requester.get<IDocumentApprovalInfo>(`documents/validate-jwt`, { jwt })
}

export function approveDocument(token: string, ipAddress: string) {
  return requester.post<IDocumentApprovalInfo>(
    `documents/approvals/authorize`,
    { token, ipAddress },
  )
}

export async function createApproval(
  documentId: string,
  approver: IApproverPayload,
  alreadyApproved = false,
): Promise<string> {
  try {
    log.info(
      `sending approval request for document ${documentId}: ${JSON.stringify(
        approver,
      )}`,
    )

    const approvalJwt = await requester.post(
      `documents/${documentId}/approvals?alreadyApproved=${alreadyApproved}`,
      approver,
      undefined,
      approver.userId,
    )

    log.info(
      `received approval token for ${approver.firstName} ${approver.lastName}, documentId ${documentId}`,
    )

    return approvalJwt
  } catch (error: any) {
    // let user continue with loan application submission if previous submission failed in the middle
    if (
      ['active_approval_already_exist', 'approval_already_accepted'].includes(
        error.code,
      )
    ) {
      return ''
    }
    throw error
  }
}

export async function getApprovalsForInitialVersion(
  companyId: string,
  templateType: AgreementType,
): Promise<IDocumentVersionsApprovals | null> {
  const documents = await getDocumentsByCompanyIdAndType(companyId, [
    templateType,
  ])

  if (documents) {
    // When invited user submits loan request, we don't have prequal application
    // with master agreement template id to get specific document version
    // so use heuristics to get the first approvable document.
    // Would be better to specific document id in company.credit and avoid saving company in document service.
    const approvableVersion = documents
      .sort((doc1, doc2) => doc1.createdAt.valueOf() - doc2.createdAt.valueOf())
      .find((doc) => doc.documentApprovals?.length > 0)

    if (approvableVersion) {
      const approvals = approvableVersion?.documentApprovals?.filter(
        (approval) => approval.isApproved,
      )

      return {
        documentId: approvableVersion?.id,
        approvals: approvals ?? [],
      }
    }
  }

  return null
}

export async function getEmailAttachment(agreement: IAgreementFileDetails) {
  if (!agreement.documentId) {
    // prettier-ignore
    throw new CriticalError('Cannot create an attachment for agreement', { agreement })
  }

  const link =
    agreement.url && agreement.fileName
      ? agreement
      : await getDownloadLink(agreement.documentId)

  const content =
    agreement.content ?? (await awsService.getS3FileByUrl(link.url))

  return {
    filename: link.fileName,
    type: 'application/pdf',
    disposition: 'attachment',
    content,
  }
}

export async function getLatestDocument(
  companyId: string,
  referenceId: string | null,
  type: AgreementType,
): Promise<IDocumentResponse | null> {
  const documents =
    await DocumentVersioningService.getDocumentsByCompanyIdAndType(companyId, [
      type,
    ])

  const latestDocument = documents
    .filter(
      (doc) => !referenceId || doc.loanApplicationId === referenceId, // loanApplicationId is actually referenceId
    )
    .sort((doc1, doc2) => doc2.createdAt.valueOf() - doc1.createdAt.valueOf())

  return latestDocument.length ? latestDocument[0] : null
}

async function request<T = any>(
  method: Method,
  url: string,
  data: any,
  params: any,
  baseUrl?: string,
  userId?: string,
) {
  const apiKey = process.env.NET_COMPANIES_APIKEY

  const headers = {
    'Content-Type': 'application/json',
    Authorization: apiKey ? `Bearer ${apiKey}` : '',
    'X-User-Id': userId ?? '',
    [ApiHeaders.CorrelationId]: RequestContext.current?.requestId ?? '',
  }

  if (!data) {
    data = {}
  }

  const companyServiceUrl = process.env.NET_COMPANY_SERVICE_API_URL

  const base_url = baseUrl || companyServiceUrl
  if (!params) {
    params = {}
  }
  const fullUrl = `${base_url}${url}`
  log.debug(
    { method, url: fullUrl, data, params },
    'DocumentVersioning request',
  )
  return axios
    .request<T>({
      method,
      url: fullUrl,
      data: method !== 'get' ? data : null,
      params,
      headers,
    })
    .then((response) => {
      log.debug(
        { method, url: fullUrl, data, params, response },
        'DocumentVersioning response',
      )
      return response.data
    })
    .catch((error) => {
      log.error(
        { method, url: fullUrl, data, params, err: error },
        'DocumentVersioning request error',
      )
      const err = Array.isArray(error) ? error[0] : error
      throw new SystemError(err?.code, `${err?.reason}`)
    })
}

axios.interceptors.response.use(
  async (response) => {
    return response
  },
  async (error) => {
    const { response, config } = error
    // Retry Later Error
    if (response.status === 504 || response.status === 502) {
      config.params.retry = config.params.retry ? config.params.retry + 1 : 1
      await new Promise((resolve) => setTimeout(resolve, 1000))
      if (config.params.retry < 5) {
        return axios.request(config)
      }
    }
    return Promise.reject(response.data)
  },
)

const requester = {
  get: <T = any>(
    url: string,
    params?: any,
    baseUrl?: string,
    userId?: string,
  ) => {
    return request<T>('get', url, null, params, baseUrl, userId)
  },
  post: <T = any>(
    url: string,
    data?: any,
    baseUrl?: string,
    userId?: string,
  ) => {
    return request<T>('post', url, data, null, baseUrl, userId)
  },
  put: <T = any>(
    url: string,
    data?: any,
    baseUrl?: string,
    userId?: string,
  ) => {
    return request<T>('put', url, data, null, baseUrl, userId)
  },
  patch: <T = any>(
    url: string,
    data?: any,
    baseUrl?: string,
    userId?: string,
  ) => {
    return request<T>('patch', url, data, null, baseUrl, userId)
  },
  delete: <T = any>(
    url: string,
    params?: any,
    baseUrl?: string,
    userId?: string,
  ) => {
    return request<T>('delete', url, null, params, baseUrl, userId)
  },
}
