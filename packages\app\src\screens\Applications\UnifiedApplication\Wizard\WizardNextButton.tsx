import { observer } from 'mobx-react'
import { useTranslation } from 'react-i18next'
import React from 'react'
import { useResponsive } from '../../../../utils/hooks'
import { useUnifiedApplication } from '../UnifiedApplicationContext'
import { Steps } from '@linqpal/models/src/applications/unified/UnifiedApplicationSteps'
import { BtButton } from '@linqpal/components/src/ui'
import RootStore from '../../../../store/RootStore'

export const WizardNextButton = observer(() => {
  const { t } = useTranslation('application')
  const { sm } = useResponsive()

  const store = useUnifiedApplication()

  const handleNext = () => {
    if (store.stepOptions.onMoveNext) {
      store.stepOptions.onMoveNext(store)
    }

    store.moveNext()
  }

  const isInvitation =
    store.currentStep === Steps.businessOwner.authorizedDetails

  let width: number | string

  if (sm) {
    width = isInvitation ? 238 : 150
  } else {
    width = store.canGoBack ? (isInvitation ? 174 : 85) : '100%'
  }

  const label = isInvitation
    ? sm
      ? t('InviteAndContinue')
      : t('SendInvite')
    : t('Next')

  return (
    <BtButton
      onPress={handleNext}
      style={{ width }}
      disabled={RootStore.isBusy || !store.isCurrentStepValid}
      testID="UnifiedApplication.Wizard.NextButton"
    >
      {label}
    </BtButton>
  )
})
