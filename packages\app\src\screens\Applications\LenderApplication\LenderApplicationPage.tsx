import { observer } from 'mobx-react-lite'
import React, { FC, useEffect } from 'react'
import { useLenderApplication } from './LenderApplicationContext'
import { WizardStepEditor } from './Wizard/WizardStepEditor'
import { LenderApplicationWelcomePage } from './LenderApplicationWelcomePage'
import { Spinner } from '@ui-kitten/components'
import { StyleSheet, View } from 'react-native'

export const LenderApplicationPage: FC = observer(() => {
  const store = useLenderApplication()

  useEffect(() => {
    store.loadDraft().catch((e) => console.error(e))
  }, [store])

  return store.isDraftLoading ? (
    <View style={styles.spinnerWrapper}>
      <Spinner />
    </View>
  ) : store.showWelcomePage ? (
    <LenderApplicationWelcomePage />
  ) : (
    <WizardStepEditor />
  )
})

const styles = StyleSheet.create({
  spinnerWrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
})
