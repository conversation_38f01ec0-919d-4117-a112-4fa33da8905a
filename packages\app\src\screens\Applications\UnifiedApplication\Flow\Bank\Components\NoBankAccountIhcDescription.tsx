import React from 'react'
import { StyleSheet, View } from 'react-native'
import { BtText } from '@linqpal/components/src/ui'
import { useTranslation } from 'react-i18next'
import { useResponsive } from '../../../../../../utils/hooks'
import { CheckBlueOutlined } from '../../../../../../assets/icons'

export const NoBankAccountIhcDescription: React.FC = () => {
  const { sm } = useResponsive()
  const { t } = useTranslation('application')

  const benefits = [
    'Bank.IHC.NotSelected.Benefits.0',
    'Bank.IHC.NotSelected.Benefits.1',
    'Bank.IHC.NotSelected.Benefits.2',
    'Bank.IHC.NotSelected.Benefits.3',
  ]

  return (
    <View style={sm ? styles.container : styles.containerMobile}>
      <BtText
        style={
          sm
            ? styles.description
            : [styles.description, styles.descriptionMobile]
        }
      >
        {t('Bank.IHC.NotSelected.Description')}
      </BtText>

      <View style={styles.benefitsContainer}>
        {benefits.map((benefitKey, index) => (
          <View key={index} style={styles.benefitRow}>
            <CheckBlueOutlined width={20} height={20} />
            <BtText style={[styles.description, styles.benefitsText]}>
              {t(benefitKey as any)}
            </BtText>
          </View>
        ))}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  containerMobile: {
    marginBottom: 24,
  },
  container: {
    marginBottom: 24,
  },
  descriptionMobile: {
    fontSize: 14,
    lineHeight: 20,
  },
  description: {
    fontFamily: 'Inter, sans-serif',
    fontSize: 16,
    fontWeight: '500',
    color: '#001929',
    lineHeight: 28,
    letterSpacing: 0,
  },
  benefitsContainer: {
    marginTop: 8,
  },
  benefitRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 16,
    gap: 10,
  },
  benefitsText: {
    fontSize: 14,
    lineHeight: 20,
  },
})
