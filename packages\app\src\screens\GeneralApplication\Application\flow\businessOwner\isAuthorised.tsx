import React, { useMemo } from 'react'
import { View } from 'react-native'
import { BtRadioGroup } from '@linqpal/components/src/ui'
import { observer } from 'mobx-react'
import { useEditorModel } from '../../useEditorModel'
import { useTranslation } from 'react-i18next'
import { BtInputLabel } from '@linqpal/components/src/ui/BtInputLabel'
import { Spacer } from '../../../../../ui/atoms'

function validate(value) {
  return !!value
}

function IsAuthorised(props) {
  const { t } = useTranslation('application')
  const { doc, group, item, onValueUpdate, decidingQuestion, review } = props
  const model = useEditorModel(doc, group, item, onValueUpdate, '', validate)

  const radioButtons = useMemo(() => {
    const options = [
      { label: t('Owner.AuthorizedLabelYes'), value: 'Yes' },
      { label: t('Owner.AuthorizedLabelNo'), value: 'No' },
    ]
    return (
      <BtRadioGroup
        disabled={decidingQuestion}
        model={model}
        name="value"
        options={options}
        radioStyle={{ marginRight: 32 }}
        groupStyle={{ flexDirection: 'row' }}
        testID="businessOwner_isAuthorized"
      />
    )
  }, [decidingQuestion, model, t])

  return review ? (
    <>
      <Spacer height={24} />
      <BtInputLabel>{t('Finance.AuthorizedReviewTitle')}</BtInputLabel>
      <View>{radioButtons}</View>
    </>
  ) : (
    radioButtons
  )
}

export default {
  title: 'Finance.Authorized',
  component: observer(IsAuthorised),
  decidingQuestion: true,
}
