import { observer } from 'mobx-react'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { ILoanPaymentPlanModel, routes, routes2 } from '@linqpal/models'
import { IDownPaymentDetails } from '@linqpal/models/src/types/routes'
import { BtButton } from '@linqpal/components/src/ui'
import ApplicationStore from '../../../../GeneralApplication/Application/ApplicationStore'
import { useStore } from '../../../../../store'
import RootStore from '../../../../../store/RootStore'

interface IProps {
  invoiceIds: string[]
  paymentPlan: ILoanPaymentPlanModel
  downPayment: IDownPaymentDetails | null
  navigation: any
  onSuccess: () => void
  isDirectTerms?: boolean
}

export const InvoiceCreditPaymentButton: React.FC<IProps> = observer(
  ({
    invoiceIds,
    paymentPlan,
    downPayment,
    navigation,
    onSuccess,
    isDirectTerms,
  }) => {
    const { t } = useTranslation('global')

    const { submitCreditApplication, payAndSubmitApplication } =
      ApplicationStore
    const { userStore } = useStore()

    const createLoanApplication = () => {
      // TODO: VK: move to backend
      routes2.user
        .loanApplication({
          invoiceId: invoiceIds,
          paymentPlan: paymentPlan.id,
          cardId: undefined,
        })
        .then((res) => {
          routes.contractor.callDecisionEngine(res.id, downPayment).then(() => {
            if (isDirectTerms) {
              routes.invoices
                .inviteSupplierSchedulePay(
                  invoiceIds,
                  userStore?.user?.sub,
                  'credit',
                )
                .then(onSuccess)
            } else {
              onSuccess()
            }
          })
        })
        .catch(console.log)
    }

    const handlePress = () => {
      if (payAndSubmitApplication) {
        submitCreditApplication(
          userStore.document?.loanApplicationId || '',
          downPayment,
          navigation,
        )
      } else {
        createLoanApplication()
      }
    }

    return (
      <BtButton
        style={{ width: 200 }}
        disabled={RootStore.isBusy}
        loading={RootStore.isBusy}
        onPress={handlePress}
      >
        {t('payInvoice.agree-and-pay')}
      </BtButton>
    )
  },
)
