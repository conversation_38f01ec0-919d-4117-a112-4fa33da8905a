import { PipelineStage, Types } from 'mongoose'
import {
  BankAccount,
  Company,
  CustomerAccount,
  FactoringService,
  getPlan,
  LMS,
  LoanApplication,
  Settings,
  User,
  UserRole,
} from '@linqpal/common-backend'
import { getCustomerCompanyPipeline } from '@linqpal/common-backend/src/services/customer.service'
import Middlewares from './middlewares'
import { ControllerItem } from 'src/routes/controllerItem'
import { ErpSystems } from '@linqpal/models/src/dictionaries/ErpSystems'
import TradeCreditService from '@linqpal/common-backend/src/services/tradeCredit/tradeCredit.service'
import moment from 'moment'
import {
  IHouseCreditInfoModel,
  SupplierAccount,
  SupplierAccountHouseCreditInfo,
} from '@linqpal/models'
import { getPrimaryAccountQuery } from '@linqpal/common-backend/src/services/customer/customers-of-supplier.service'
import {
  getCreditApplicationsQuery,
  getLoansQuery,
} from '@linqpal/common-backend/src/services/customer/customer-status.service'
import { escapeRegExp } from 'lodash'
import tran from '../../../services/transactional.service'
import { CustomerAccountType } from '@linqpal/models/src/dictionaries/customerAccountType'

interface CustomersOfSupplierFilters {
  status?: any
  isConnectorParent?: any
  invoice_import_enabled?: boolean | null
  type?: string
}

const getHouseCreditInfo = (
  customerType: CustomerAccountType = CustomerAccountType.TradeCredit,
  hci: IHouseCreditInfoModel | null | undefined,
): SupplierAccountHouseCreditInfo | undefined => {
  if (!hci) {
    return undefined
  }

  if (customerType === CustomerAccountType.IHC) {
    return {
      account_open_date: hci.account_open_date?.toString() || null,
      ar_forward_terms: hci.ar_forward_terms?.toString() || null,
      ar_forward_available_credit:
        hci.ar_forward_available_credit?.toString() || null,
      processing_payments: hci.processing_payments?.toString() || null,
      current_balance: hci.current_balance?.toString() || null,
      max_credit_limit: hci.max_credit_limit?.toString() || null,
      payment_history: hci.payment_history?.toString() || null,
      avg_days_to_pay: hci.avg_days_to_pay?.toString() || null,
      open_a_r: hci.open_a_r?.toString() || null,
      highest_credit: hci.highest_credit?.toString() || null,
      billing_past_due_1: hci.billing_past_due_1?.toString() || null,
      billing_past_due_2: hci.billing_past_due_2?.toString() || null,
      billing_past_due_3: hci.billing_past_due_3?.toString() || null,
      billing_past_due_more: null,
      total_past_due: hci.total_past_due?.toString() || null,
      average_invoices: hci.average_invoices?.toString() || null,
      rejected_ach_count: null,
      ein_ssn: hci.ein_ssn,
    }
  }

  return {
    account_open_date: hci.account_open_date?.toString() || null,
    ar_forward_terms: null,
    ar_forward_available_credit: null,
    processing_payments: null,
    max_credit_limit: hci.max_credit_limit?.toString() || null,
    payment_history: hci.payment_history?.toString() || null,
    avg_days_to_pay: hci.avg_days_to_pay?.toString() || null,
    open_a_r: hci.open_a_r?.toString() || null,
    current_balance: hci.current_balance?.toString() || null,
    highest_credit: hci.highest_credit?.toString() || null,
    billing_past_due_1: hci.billing_past_due_1?.toString() || null,
    billing_past_due_2: hci.billing_past_due_2?.toString() || null,
    billing_past_due_3: hci.billing_past_due_3?.toString() || null,
    billing_past_due_more: hci.billing_past_due_more?.toString() || null,
    total_past_due: hci.total_past_due?.toString() || null,
    average_invoices: hci.average_invoices?.toString() || null,
    rejected_ach_count: hci.rejected_ach_count?.toString() || null,
    ein_ssn: hci.ein_ssn,
  }
}

export const customerLoans = {
  middlewares: {
    pre: [...Middlewares.pre],
  },
  get: async (req, res) => {
    const customerAccountId = req.query.customerAccountId as string
    const [customer] = await CustomerAccount.aggregate([
      {
        $match: {
          _id: new Types.ObjectId(customerAccountId),
          company_id: req.company!.id,
        },
      },
      ...getCustomerCompanyPipeline,
    ])
    const customer_company_id = customer?.customer?._id

    if (!customer_company_id) {
      res.status(404).send({ customerAccountId })
      return
    }

    const loanApplications = await LoanApplication.aggregate([
      {
        $addFields: { invoiceId: { $toObjectId: '$invoiceDetails.invoiceId' } },
      },
      { $match: { company_id: customer_company_id.toString() } },
      {
        $lookup: {
          from: 'invoices',
          localField: 'invoiceId',
          foreignField: '_id',
          as: 'invoice',
        },
      },
      { $unwind: '$invoice' },
    ])

    const promises = loanApplications.map(async (loanApplication) => {
      const {
        invoice,
        invoiceDetails,
        approvedAmount,
        company_id,
        createdAt,
        invoiceId,
        status,
        _id,
        lms_id,
      } = loanApplication
      const plan =
        loanApplication.metadata?.paymentPlan ||
        (await getPlan(invoiceDetails.paymentPlan))
      const loanData: { [key: string]: any } = {
        invoice,
        invoiceDetails,
        approvedAmount,
        company_id,
        createdAt,
        invoiceId,
        status,
        _id,
        option: plan,
        lms_id,
      }

      if (loanApplication.lms_id) {
        try {
          loanData.loanLMS = await LMS.getLoanInfo(loanApplication.lms_id)
        } catch (e) {}
      }

      return loanData
    })

    res.send({ result: 'ok', loans: await Promise.all(promises) })
  },
} as ControllerItem

export const allCustomersOfSupplier = {
  middlewares: {
    pre: [...Middlewares.pre, ...tran.pre],
  },
  get: async (req, res) => {
    const page = parseInt((req.query?.page as string) || '1')
    const pageSize = parseInt((req.query?.pageSize as string) || '25')
    const type = req.query?.type || CustomerAccountType.TradeCredit
    const paginationPipeline: PipelineStage.FacetPipelineStage[] = []

    if (page !== -1) paginationPipeline.push({ $skip: (page - 1) * pageSize })
    if (pageSize !== -1) paginationPipeline.push({ $limit: pageSize })

    if (type === CustomerAccountType.TradeCredit) {
      paginationPipeline.push(getLoansQuery as PipelineStage.FacetPipelineStage) // Getting the loans after pagination (required to calculate TradeCreditStatus) to fix size exceeded error
    } else {
      paginationPipeline.push(
        getCreditApplicationsQuery as PipelineStage.FacetPipelineStage,
      ) // Getting the credit Applications after pagination (required to calculate TradeCreditStatus) to fix size exceeded error
    }

    paginationPipeline.push(
      ...(getPrimaryAccountQuery as PipelineStage.FacetPipelineStage[]),
    )

    const pipeline: PipelineStage[] = [
      {
        $match: {
          company_id: req.company!.id,
          isDeleted: { $in: [false, null] },
          parent_id: { $in: ['', null] },
        },
      },
      {
        $set: {
          email: { $ifNull: ['$guestInfo.email', { $ifNull: ['$email', ''] }] },
          phone: { $ifNull: ['$guestInfo.phone', { $ifNull: ['$phone', ''] }] },
        },
      },
      {
        $lookup: {
          from: User.collection.name,
          let: { email: '$email', phone: '$phone' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $cond: {
                    if: { $ne: ['$$email', ''] },
                    then: {
                      $eq: ['$email', '$$email'],
                    },
                    else: {
                      $or: [
                        { $eq: ['$login', '$$email'] },
                        { $eq: ['$login', '$$phone'] },
                      ],
                    },
                  },
                },
              },
            },
            {
              $project: {
                sub: 1,
              },
            },
          ],
          as: 'users',
        },
      },
      { $unwind: { path: '$users', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: UserRole.collection.name,
          let: { sub: '$users.sub' },
          pipeline: [
            { $match: { $expr: { $eq: ['$sub', '$$sub'] } } },
            { $project: { company_id: 1 } },
          ],
          as: 'roles',
        },
      },
      { $unwind: { path: '$roles', preserveNullAndEmptyArrays: true } },

      {
        $addFields: {
          customer_id: { $toObjectId: '$roles.company_id' },
        },
      },
      {
        $lookup: {
          from: Company.collection.name,
          as: 'customer',
          localField: 'customer_id',
          foreignField: '_id',
          pipeline: [
            {
              $project: {
                name: 1,
                bankAccounts: 1,
                'credit.limit': 1,
              },
            },
          ],
        },
      },
      { $unwind: { path: '$customer', preserveNullAndEmptyArrays: true } },
      {
        $unset: ['users', 'roles', 'credit_info'],
      },
      {
        $addFields: {
          invoice_import_enabled: {
            $cond: {
              if: {
                $and: [
                  { $eq: ['$contact_source', ErpSystems.QuickBooks] },
                  { $ifNull: ['$connector', false] },
                ],
              },
              then: {
                $cond: {
                  if: {
                    $eq: [
                      '$connector.quickBooksSettings.invoice_import_enabled',
                      false,
                    ],
                  },
                  then: false,
                  else: true, // QB import is enabled by default for old records without this setting
                },
              },
              else: null,
            },
          },
        },
      },
      {
        $project: {
          salesRepUserInfo: 0,
        },
      },
    ]

    if (
      req.query.status ||
      req.query.isConnectorParent ||
      req.query.isImportEnabled ||
      req.query.type
    ) {
      const queryMatch: CustomersOfSupplierFilters = {}
      if (req.query.status) {
        queryMatch.status = req.query.status
      }

      if (req.query.isConnectorParent) {
        if (req.query.isConnectorParent === 'false') {
          queryMatch.isConnectorParent = { $in: [false, null] }
        }
      }

      if (req.query.isImportEnabled) {
        const isImportEnabled =
          req.query.isImportEnabled === 'none'
            ? null
            : req.query.isImportEnabled === 'true'

        queryMatch.invoice_import_enabled = isImportEnabled
      }

      if (req.query.type) {
        queryMatch.type = req.query.type.toString()
      }

      pipeline.push({
        $match: queryMatch,
      })
    }

    const { search = '', sortDirection = 'asc' } = req.query
    const sortColumn = req.query?.sortColumn as string

    if (search) {
      pipeline.push({
        $addFields: {
          full_name: {
            $concat: ['$first_name', ' ', '$last_name'],
          },
          id: {
            $toString: '$_id',
          },
        },
      })

      pipeline.push({
        $match: {
          $or: [
            'name',
            'phone',
            'first_name',
            'last_name',
            'full_name',
            'display_name',
            'email',
            'customer.name',
          ].map((field) => ({
            [field]: {
              $regex: escapeRegExp(search as string),
              $options: 'i',
            },
          })),
        },
      })
    }

    let _sortColumn = sortColumn ? sortColumn : 'createdAt'

    switch (_sortColumn.toLowerCase()) {
      case 'business':
        pipeline.push({
          $addFields: {
            lower_name: { $toLower: '$name' },
          },
        })
        _sortColumn = 'lower_name'
        break
      case 'contact':
        pipeline.push({
          $addFields: {
            contact: {
              $concat: [
                { $ifNull: ['$first_name', ''] },
                ' ',
                { $ifNull: ['$last_name', ''] },
              ],
            },
          },
        })
        pipeline.push({
          $addFields: {
            lower_contact: {
              $cond: {
                if: {
                  $and: [
                    { $ifNull: ['$contact', false] },
                    { $ne: [{ $trim: { input: '$contact' } }, ''] },
                  ],
                },
                then: { $toLower: '$contact' },
                else: {
                  $cond: {
                    if: {
                      $and: [
                        { $ifNull: ['$display_name', false] },
                        { $ne: [{ $trim: { input: '$display_name' } }, ''] },
                      ],
                    },
                    then: { $toLower: '$display_name' },
                    else: { $toLower: '$name' },
                  },
                },
              },
            },
          },
        })
        _sortColumn = 'lower_contact'
        break
      case 'contact info':
        _sortColumn = 'phone'
        break
      case 'status':
        _sortColumn = 'status'
        break
      case 'lastpurchasedate':
        pipeline.push({
          $addFields: {
            last_purchase_date_time: { $toDate: '$last_purchase_date' },
          },
        })
        _sortColumn = 'last_purchase_date_time'
        break
      case 'quickBooks import':
        _sortColumn = 'invoice_import_enabled'
        break
    }

    pipeline.push({
      $sort: {
        [_sortColumn]: sortDirection === 'desc' ? -1 : 1,
      },
    })

    const [result, tradeCreditPeriod] = await Promise.all([
      CustomerAccount.aggregate([
        ...pipeline,
        {
          $facet: {
            total: [{ $count: 'count' }],
            items: [...paginationPipeline],
          },
        },
      ]),
      Settings.findOne({ key: 'trade_credit_period' }),
    ])
    const { items, total } = result[0]

    const finalItems = items.map((account: any) => {
      let status, validTill
      let customerCreditLimit = 0
      if (type === CustomerAccountType.TradeCredit) {
        const { status: tradeCreditStatus, validTill: tradeCreditValidTIll } =
          TradeCreditService.calculateStatus(
            account.loans,
            account.primaryAccount,
            tradeCreditPeriod?.value || 90,
          )
        status = tradeCreditStatus
        validTill = tradeCreditValidTIll ?? null
        customerCreditLimit = account.customer?.credit?.limit
      } else {
        const { status: ihcStatus } = FactoringService.calculateStatus(
          account.creditApplications?.length
            ? account.creditApplications[0]
            : null,
          account.settings,
        )
        status = ihcStatus
        validTill = null
        customerCreditLimit =
          account.settings?.inHouseCredit?.limit ??
          account.creditApplications[0]?.approvedCreditLimit ??
          0
      }

      return {
        id: account._id,
        ...account,
        credit_status: status,
        credit_status_valid_till: validTill,
        customer_credit_limit: customerCreditLimit,
        name: account.customer?.name || account.name, //If Company (customer) has business name set that, else show Customer Account name (name).
        contacts: [],
        dateAdded: moment(account.createdAt).format('MM/DD/YYYY'),
        customer: account.customer
          ? {
              id: account.customer._id,
              ...account.customer,
            }
          : null,
        house_credit_info: getHouseCreditInfo(
          account.type,
          account.house_credit_info || {},
        ),
        loans: undefined,
        creditApplications: undefined,
        primaryAccount: undefined,
        bankAccounts: undefined,
      } as SupplierAccount
    })

    res.send({
      items: finalItems,
      totalCount: total[0]?.count ?? 0,
    })
  },
} as ControllerItem

export const customerDetails = {
  middlewares: {
    pre: [...Middlewares.pre],
  },
  get: async (req, res) => {
    const customerAccountId = req.query.id as string

    const pipeline: PipelineStage[] = [
      {
        $match: {
          _id: new Types.ObjectId(customerAccountId),
          company_id: req.company!.id,
        },
      },
      {
        $addFields: {
          email: { $ifNull: ['$email', ''] },
          phone: { $ifNull: ['$phone', ''] },
        },
      },
      {
        $lookup: {
          from: User.collection.name,
          let: { email: '$email', phone: '$phone' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $cond: {
                    if: { $ne: ['$$email', ''] },
                    then: {
                      $eq: ['$email', '$$email'],
                    },
                    else: {
                      $or: [
                        { $eq: ['$login', '$$email'] },
                        { $eq: ['$login', '$$phone'] },
                      ],
                    },
                  },
                },
              },
            },
          ],
          as: 'users',
        },
      },
      { $unwind: { path: '$users', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: UserRole.collection.name,
          let: { sub: '$users.sub' },
          pipeline: [{ $match: { $expr: { $eq: ['$sub', '$$sub'] } } }],
          as: 'roles',
        },
      },
      { $unwind: { path: '$roles', preserveNullAndEmptyArrays: true } },

      {
        $addFields: {
          customer_id: { $toObjectId: '$roles.company_id' },
        },
      },
      {
        $lookup: {
          from: Company.collection.name,
          as: 'customer',
          localField: 'customer_id',
          foreignField: '_id',
        },
      },
      { $unwind: { path: '$customer', preserveNullAndEmptyArrays: true } },
      {
        $unset: ['users', 'roles', 'customer.onBoarding'],
      },
      /**
       * If Company (customer) has business name set that, else show Customer Account name (name).
       */
      {
        $set: {
          name: {
            $cond: {
              if: {
                $ne: [{ $ifNull: ['$customer.name', ''] }, ''],
              },
              then: '$customer.name',
              else: '$name',
            },
          },
        },
      },
      /**
       * If House Credit Info is null, set {}
       */
      {
        $set: {
          house_credit_info: {
            $ifNull: ['$house_credit_info', {}],
          },
        },
      },
      //
      {
        $lookup: {
          from: BankAccount.collection.name,
          as: 'primaryAccount',
          let: {
            ids: { $ifNull: ['$customer.bankAccounts', []] },
          },
          pipeline: [
            {
              $match: {
                $and: [
                  {
                    $expr: {
                      $in: ['$_id', '$$ids'],
                    },
                  },
                  {
                    $expr: {
                      $in: ['$status', ['verified', 'manualverified']],
                    },
                  },
                  {
                    isDeactivated: { $in: [false, null] },
                  },
                  {
                    $or: [
                      { isPrimaryForCredit: true },
                      {
                        $and: [
                          {
                            $or: [
                              { isPrimaryForCredit: null },
                              { isPrimaryForCredit: { $exists: false } },
                            ],
                          },
                          { isPrimary: true },
                        ],
                      },
                    ],
                  },
                ],
              },
            },
          ],
        },
      },
      { $set: { primaryAccount: { $last: '$primaryAccount' } } },
      {
        $addFields: {
          invoice_import_enabled: {
            $cond: {
              if: {
                $and: [
                  { $eq: ['$contact_source', ErpSystems.QuickBooks] },
                  { $ifNull: ['$connector', false] },
                ],
              },
              then: {
                $cond: {
                  if: {
                    $eq: [
                      '$connector.quickBooksSettings.invoice_import_enabled',
                      false,
                    ],
                  },
                  then: false,
                  else: true, // QB import is enabled by default for old records without this setting
                },
              },
              else: null,
            },
          },
        },
      },
    ]

    const result = await CustomerAccount.aggregate([...pipeline])
    const account = result[0]

    const response = {
      id: account._id,
      ...account,
      bankAccounts: undefined,
      name: account.name,
      contacts: [],
      dateAdded: moment(account.createdAt).format('MM/DD/YYYY'),
      customer: account.customer
        ? {
            id: account.customer._id,
            ...account.customer,
          }
        : null,
      house_credit_info: getHouseCreditInfo(
        account.type,
        account.house_credit_info || {},
      ),
      loans: undefined,
      primaryAccount: undefined,
    } as SupplierAccount

    res.send(response)
  },
} as ControllerItem
