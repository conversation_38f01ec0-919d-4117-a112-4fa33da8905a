import { OwnerTypes } from '../../dictionaries/UnifiedApplication'
import { IUIBankAccount } from '../../mst'

export enum ApplicationUserRole {
  None = 'None',
  Authorized = 'Authorized',
  Owner = 'Owner',
}

export interface IUnifiedApplicationDraft {
  initialStep: string
  currentStep: string
  data: IUnifiedApplicationData
  users: IApplicationUser[]

  // need this to correctly display progress for optional fields,
  // optional field should be valid and visited to count in progress
  visitedSteps: string[]
}

export interface IUnifiedApplicationData {
  businessInfo: {
    email?: string
    category?: string
    businessName?: {
      legalName?: string
      dba?: string
    }
    trade?: string[]
    businessPhone?: string
    businessAddress?: IApplicationAddress
    startDate?: string
    type?: IApplicationBusinessType
    ein?: string
  }
  finance: {
    revenue?: number
    debt?: number
    creditLimit?: number
    arAdvanceLimit?: number
  }
  coOwners: ICoOwner[]
  primaryBankAccount?: IUIBankAccount
}

export interface IApplicationUser {
  id?: string
  firstName?: string
  lastName?: string
  email?: string
  phone?: string
  address?: IApplicationAddress
  birthdate?: string
  ssn?: string
  ownershipPercentage?: number
  role?: ApplicationUserRole
}

export interface IApplicationBusinessType {
  selectedType?: string
  other?: string
}

export interface IApplicationAddress {
  address?: string
  city?: string
  state?: string
  zip?: string
}

export interface ICoOwner {
  // common
  id: string
  type: typeof OwnerTypes.INDIVIDUAL | typeof OwnerTypes.ENTITY
  percentOwned: number
  address: string
  city: string
  state: string
  zip: string
  phone: string
  email: string
  // individual owner
  firstName: string
  lastName: string
  birthday: string
  ssn: string
  // entity
  entityName: string
  ein: string
}
