import { Invitation, User, UserRole } from '@linqpal/common-backend'
import { IInvitation } from '@linqpal/common-backend/src/models/types'
import { exceptions, validators } from '@linqpal/models'
import { parsePhoneNumber } from 'libphonenumber-js'
import { ControllerItem } from 'src/routes/controllerItem'
import controllers from '../../../controllers'

function checkData(item: string | null | undefined, name: string) {
  if (!item || item.length < 1) {
    throw new exceptions.LogicalError('Please enter ' + name)
  }
}

function checkLogin(isContractor: boolean, login: string) {
  if (isContractor) {
    try {
      const phone = parsePhoneNumber(login, 'US').number
      return [phone, '']
    } catch (e) {
      return [login, 'Invalid phone number']
    }
  } else {
    if (!validators.validateEmail(login)) {
      return [login, 'Invalid email']
    }
    return [login, '']
  }
}

async function updateUser(data: any, company_id: string) {
  const { firstName, lastName, phone, role, roleSettings, _id } = data
  if (!_id) return 'continue'
  const user = await User.findById(_id)
  if (user) {
    await user.updateOne({ firstName, lastName, phone })
    await UserRole.findOneAndUpdate(
      { sub: user.sub, company_id: company_id },
      { role: role || 'User', settings: roleSettings },
    )
    return 'updated'
  } else {
    return 'Invalid user identificator'
  }
}

async function checkUserExistsInCompany(data: any, company_id: string) {
  const { login } = data
  const firebaseUser = await controllers.user.getFirebaseUser(login)
  if (!firebaseUser) {
    return 'continue'
  }
  const user = await User.findOne({ firebaseId: firebaseUser.uid })

  if (!user) {
    return 'continue'
  }

  const userRoles = await UserRole.findOne({
    company_id,
    sub: user.sub,
  })
  if (!userRoles) {
    return 'continue'
  }

  const invitation = await Invitation.findOne({ sub: user.sub })
  if (invitation) {
    return 'has invited'
  }

  return 'exists'
}

export default {
  post: async (req, res) => {
    const { firstName, lastName, login, role = 'User' } = req.body

    checkData(firstName, 'First Name')
    checkData(lastName, 'Last Name')
    checkData(login, 'Login')
    checkData(role, 'Role')

    const currentCompany = req.company!

    const isContractor = currentCompany.type === 'contractor'

    const [, loginCheckError] = checkLogin(isContractor, login)

    if (loginCheckError) {
      res.status(400).send({ message: loginCheckError })
      return
    }

    const companyId = currentCompany._id.toString()

    let result = await updateUser(req.body, companyId)
    if (result !== 'continue') {
      if (result === 'updated') {
        res.send({ result: 'ok', action: 'update' })
        return
      } else {
        res.status(400).send({ message: result })
        return
      }
    }

    result = await checkUserExistsInCompany(req.body, companyId)
    if (result !== 'continue') {
      res.status(400).send({ message: 'User already ' + result })
      return
    }

    let invitation: IInvitation | { invitation: IInvitation; action: string }
    if (isContractor) {
      invitation = await controllers.invitation.contractorInviteUser(req)
    } else {
      invitation = await controllers.invitation.supplierInviteUser(req)
    }

    res.send({ result: 'ok', invitation, action: 'invite' })
  },
} as ControllerItem
