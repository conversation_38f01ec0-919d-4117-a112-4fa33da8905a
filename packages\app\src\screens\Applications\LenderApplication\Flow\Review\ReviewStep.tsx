import React, { FC } from 'react'
import { StyleSheet, View } from 'react-native'
import { useLenderApplication } from '../../LenderApplicationContext'
import { BtLink, BtPlainText } from '@linqpal/components/src/ui'
import { Trans, useTranslation } from 'react-i18next'
import { ILenderApplicationEditor } from '../getLenderApplicationEditor'
import { commonColors } from '@linqpal/common-frontend/src/theme'
import RootStore from '../../../../../store/RootStore'

export const Review: FC = () => {
  const { t } = useTranslation('application')

  const store = useLenderApplication()
  const groups = store.getFlowGroups()

  // there are different designs for review as final step
  // and review as the first step after reopen
  // use same step, display different title / content

  return (
    <View>
      {groups.map((group) => {
        const steps = store.getGroupSteps(group)

        const totalCount = steps.length
        const filledCount = steps.filter((step) =>
          store.validateStep(step),
        ).length

        return (
          <View
            key={group}
            style={{ flexDirection: 'row', borderColor: 'red', borderWidth: 1 }}
          >
            <View>
              <BtPlainText>{group}</BtPlainText>
              <BtPlainText>
                {totalCount === filledCount ? (
                  <Trans
                    t={t}
                    i18nKey="LenderApplication.Flow.Review.FilledGroupDescription"
                    values={{
                      total: totalCount,
                    }}
                  />
                ) : (
                  <Trans
                    t={t}
                    i18nKey="LenderApplication.Flow.Review.IncompleteGroupDescription"
                    values={{
                      filled: filledCount,
                      total: totalCount,
                    }}
                  />
                )}
              </BtPlainText>
            </View>
            <View style={{ marginLeft: 'auto' }}>
              <BtLink
                title={t('LenderApplication.Flow.Review.EditSectionLink')}
                textStyle={styles.linkText}
                onPress={() => store.editGroup(group, false)}
                disabled={RootStore.isBusy}
              />
            </View>
          </View>
        )
      })}
    </View>
  )
}

const styles = StyleSheet.create({
  linkText: {
    fontFamily: 'Inter, sans-serif',
    fontSize: 14,
    fontWeight: '700',
    lineHeight: 22,
    color: commonColors.accentText,
  },
})

export const ReviewStep: ILenderApplicationEditor = {
  options: {
    title: 'LenderApplication.Flow.Review.Title',
    showNavigationButtons: false,
  },
  component: Review,
}
