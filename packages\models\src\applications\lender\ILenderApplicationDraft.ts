export interface ILenderApplicationDraft {
  id?: string
  initialStep: string
  currentStep: string
  data: ILenderApplicationData

  // need this to correctly display progress for optional fields,
  // optional field should be valid and visited to count in progress
  visitedSteps: string[]
}

export interface ILenderApplicationData {
  sponsor: {
    loanOfficer?: string // temporary type, should be complex
    personalInfo?: ISponsorPersonalInfo
    homeAddress?: IAddress
    maritalStatus?: MaritalStatus
    statements?: IStatement[]
    // this can be replaced with single enum, object is better if we need to collect more data on citizenship
    citizenship?: {
      isUsCitizen?: boolean
      legalResidencyStatus?: LegalResidencyStatus
    }
  }
  businessEntity: {
    name?: string
    type?: BusinessEntityType
    ein?: string // !!! encrypt on save
    address?: IAddress
    isCreatedForProject?: boolean
    dateIncorporated?: Date
    representative?: {
      firstName?: string
      lastName?: string
      phone?: string
      email?: string
    }
    isGeneralContractor?: boolean // same step with generalContractorName
    generalContractorName?: string //? can multiple users edit this information?
    bankAccountId?: string // paymentMethodId would be better name
  }
  currentProject: {
    hasMultipleProducts?: boolean // save just to restore on UI, better remove this step at all
    loanType?:
      | 'New Construction'
      | 'Counstruction Completion'
      | 'Rehab'
      | 'Bridge' // enum or arbitrary strings?
    mainAddress?: IAddress
    products?: IProduct[]
    loanPurpose?: 'Purchase' | 'Refinance' // enum
    loanTerm?: string // payment plan id? some enum?
    willCompletePermits?: boolean
    originalPurchasePrice?: number
    originalPurchaseDate?: Date
    payOffAmount?: number | null // or extra hasPayOffAmount: boolean flag to show radiobuttons
    subordinateDebtType?: 'None' | 'Seller Carry' | 'Entity Ownership'
    subordinateDebtBalance?: number
    financialDetails?: {
      projectValue?: number
      perUnitValue?: number
      totalFinishedValue?: number
      perUnitFinishedValue?: number
    }
  }
  previousProjects: IProject[]
}

export interface ISponsorPersonalInfo {
  fullName?: string
  email?: string
  phone?: string
  birthdate?: string
  ssn?: string // !!! encrypt on save
}

export interface IAddress {
  city?: string
  state?: string
  street?: string
  apartment?: string
  zip?: string
}

interface IStatement {
  statementId?: string
  answer?: boolean
}

/* or

interface IStatement1 {
  statementId: string,
  answer: {
    value: any
    // since extra info can be requested for specific answers we could store extensible object in answer
  },
}

*/

interface IProduct {
  address?: IAddress
  type?: string
  numberOfUnits?: number
  budget?: number
}

interface IProject {
  entityTitle?: string
  numberOfHomesCompleted?: number
  numberOfUnits?: number
  address?: IAddress
  acquisitionPrice?: number
  dispositionPrice?: number
  type?: 'enum'
  structure?: 'enum'
  loanType?: 'enum'
}

export enum MaritalStatus {
  Single = 'Single',
  Married = 'Married',
}

export enum StatementQuestions {
  RecentForeclosureOrRelief = 'RecentForeclosureOrRelief',
  RecentBankruptcy = 'RecentBankruptcy',
  CurrentFederalDefault = 'CurrentFederalDefault',
  FelonyConviction = 'FelonyConviction',
  PendingLitigation = 'PendingLitigation',
  OutstandingJudgments = 'OutstandingJudgments',
  LawsuitDefendant = 'LawsuitDefendant',
  PortfolioLitigation = 'PortfolioLitigation',
}

export enum LegalResidencyStatus {
  PermanentResident = 'PermanentResident',
  Visa = 'Visa',
  ForeignResident = 'ForeignResident',
}

export enum BusinessEntityType {
  LLC = 'LLC',
  CCorp = 'CCorp',
  SCorp = 'SCorp',
}
