{"contact-email": "<EMAIL>", "contact-phone": "************", "SignUp": "Sign Up", "form": {"businessEmail": "Business email address"}, "quote": "Quote", "invoice": "Invoice", "sales_order": "Sales Order", "statement": "Statement", "the_legal_agreement_content": "The legal agreement content", "add_delivery_adress": "Add delivery address", "payTerms&Cond": "By continuing, you agree to the <2>Customer Agreement</2>, <5>Terms & Conditions</5>, and <8>Privacy Policy</8>", "credit": "Credit", "consoleHome": "Console Home", "go_back_and_save_draft": "Go back and save draft", "invoicesToPay": "Invoices To Pay", "noInvoices": "No Invoices", "noItems": "No search results were found", "projects": "Projects", "noProjects": "No Projects", "uploadInvoiceToPayWithBTCredit": "Upload invoice to pay with BT credit", "toApplyBTCredit": "To apply for BlueTape credit", "chooseInvoice": "Choose an Invoice", "toRequestCredit": "To request for credit", "90deg": "90deg", "previewInvoice": "Preview Invoice", "processingApplication": "Processing Application", "payables": "Payables", "loanApproved": {"congratulationMessage": " Congratulations. Your invoice to your supplier has been paid. Your draw has started with BlueTape.\n\nDraw Details: ${{amount}}\nSupplier: {{- supplier}}\n\nClick to see your draw details and your NEW repayment schedule.", "autoApprovedLoan": "Your invoice has been paid to your supplier and a draw has been started.\n\nDraw Amount: $ {{amount}}\nSupplier: {{- supplier}}\nInvoice: {{invoiceNumber}}\n\nClick to see your draw details, draw schedule, and invoice.", "loanHasApproved": "Draw has started!"}, "autoPayAlert": {"required": {"title": "To begin processing your invoices, you’re required to set up Auto Pay by adding a valid bank account or credit/debit card.", "message": "Setting up Auto Pay takes less than a minute and ensures smooth payment processing from day one.", "buttonText": "Set up Payment Method"}, "recommended": {"title": "To begin processing your invoices, set up Auto Pay by adding a valid bank account or credit/debit card.", "message": "Setting up Auto Pay takes less than a minute and ensures smooth payment processing from day one.", "buttonText": "Set up Payment Method"}}, "autoPayWarning": {"recommended": {"title": "Enable Auto Pay for Seamless Invoice Payments", "message": "Activate automatic payments to ensure your incoming invoices are paid on time, hassle-free.", "buttonText": "Turn ON Auto Pay"}, "required": {"title": "Some of your invoices require Auto Pay to proceed.", "message": "To avoid missed payments and potential late fees, please enable automatic payments. You’ll receive an email notification before each scheduled payment.", "buttonText": "Set up Payment Method"}, "enabled": {"title": "Auto Pay is", "on": "ON", "message": "Default Repayment account for all invoices is", "bankAccountTitle": "Bank Account {{number}}", "cardAccountTitle": "{{bankName}} {{cardNetwork}} {{cardType}} Card {{number}}", "buttonTextPayables": "Manage in Accounts", "buttonTextAccount": "Manage Auto Pay"}}, "autoPayIHCFlow": {"addOrSelectPaymentMethod": {"title": "Please add or select a payment method to enable Auto Pay", "subtitle": "You will be able to change the payment method anytime in the Accounts section.", "bankAccountTitle": "Bank Account ... {{number}}", "cardAccountTitle": "{{cardType}} Card ... {{number}}", "buttonText": "Turn ON Auto-Pay", "addNewPaymentMethod": "Add a new payment method"}, "addNewMethod": {"title": "Please add a new payment method to enable Auto Pay", "subtitle": "You will be able to update the payment method anytime soon in the Accounts section."}, "updatePaymentMethod": {"title": "Update Auto Pay Payment Method", "subtitle": "To change your Auto Pay payment method, please add or select a new payment method. This change will apply to all your invoices, including any ongoing repayments.", "save": "Save", "turnOff": "Turn off Auto Pay"}, "turnOff": {"title": "Are you sure you want to turn off Auto Pay?", "subtitle": "Disabling Auto Pay may result in missed payments and additional late fees. We recommend keeping Auto Pay enabled to avoid any disruptions.", "keepAutoPayEnabledButton": "Keep Auto Pay Enabled", "keepAutoPayButton": "Keep Auto Pay", "turnOffButton": "Turn off Auto Pay"}, "success": {"enabled": {"title": "Auto Pay Setup is completed", "subtitle": "Your payment method has been successfully added for Auto Pay. All future payments will be made automatically using the selected <bold>{{paymentMethod}}.</bold>", "additionalInfo": "You can manage your Auto Pay settings anytime in the Accounts section."}, "updated": {"title": "Auto Pay Payment Method Updated", "subtitle": "Your Auto Pay payment method has been successfully updated. All future payments will be made automatically using the selected <bold>{{paymentMethod}}.</bold>", "additionalInfo": "You can manage your Auto Pay settings at any time in the Accounts section."}, "disabled": {"title": "Auto Pay Disabled", "subtitle": "Auto Pay has been turned off. Please make sure to keep track of your payment due dates to avoid late fees.", "additionalInfo": "You can re-enable Auto Pay anytime using a bank account or card."}, "bankAccountTitle": "Bank Account ending in {{accountNumber}}", "cardTitle": "{{typeOfCard}} Card ending in {{accountNumber}}", "doneButton": "Done"}, "paymentMethodLabel": "Please select a payment method", "addNewPaymentMethod": "Add new payment method", "processingFeeNote": "A {{processingFee}}% card processing fee will be added to your invoice payment."}, "forFasterCheckout": "For faster checkout", "linkBankManually": "Link your bank manually", "takes2BusinessDays": "Takes 2-3 business days", "sales": "Sales", "colors": {"white": "White"}, "back": "Back", "home": {"sentPaymentRequest": "{{- company}} sent you {{type}} of {{amount}} that is due {{due}}", "newQuote": "{{- supplier}} has sent you quote for {{amount}}", "authorizedQuote": "{{quoteNumber}} was authorized for {{amount}}"}, "account": {"labels": {"cell-phone-number": "Cell Phone Number", "first-name": "First Name", "last-name": "Last Name", "email-address": "Email Address"}, "edit-billing-contact": "Edit Billing Contacts", "add-billing-contact": "Add Billing Contacts", "billing-contact-details": "Billing Contact Details", "add-new-person": "Add New Person"}, "payInvoice": {"swipe-to-agree": "Swipe to agree", "request-to-pay": "Request to pay", "swipe-to-pay": "Swipe to pay", "agree-and-pay": "Agree & Pay", "pay-invoice": "Pay Invoice", "invoice-amount": "Invoice Amount", "total-amount": "Total Amount", "total-amount-with-down-payment": "Total Repayment Amount Including Down Payment", "late-interest-charge": "Late Interest Charge", "down-payment-card-fee": "Card Fee for Down Payment", "loan-fee": "Draw Fees", "select-autodebit-account": "All future repayments will be debited from the payment method below", "payment-method": "Payment Method", "payment-terms-conditions-text": "By clicking \"Agree & Pay\", you agree to the Borrower Terms and Conditions <1>found here.</1>", "payment_will_proceed": "Payment will proceed", "To": "To", "servicing_fee": "Servicing Fee", "discount": "Discount"}, "payIhcInvoice": {"makePayment": "Make a Payment", "invoiceData": {"dueDate": "Invoice Due Date", "outstandingAmount": "Outstanding Invoice Amount", "lateInterestCharge": "Late Interest Charge"}, "paymentMethod": {"label": "Payment Method", "addNewPaymentMethod": "Add new payment method"}, "selectOptionToPay": {"title": "Select an option to pay", "payRemainingAmount": "Pay Remaining Amount", "payPartialAmount": "Pay Partial Amount"}, "processingFee": "Processing Fee", "totalAmountToPay": "Total Amount to pay", "agree-and-pay": "Pay {{amount}}"}, "emptyResult": {"header": "No matches found", "text": "Please select other filter options"}, "skip": "<PERSON><PERSON>", "hi": "Hi", "yes": "Yes", "no": "No", "okay": "Okay", "on": "On", "off": "Off", "chatLabel": "Need Help?", "logOut": "Log Out", "close": "Close", "continue-session": "Continue Session", "delete": "Delete", "add": "Add", "cancel": "Cancel", "select": "Select", "select-options": "Select Options", "are-you-sure-to-logout": "Are you sure you want to log out?", "are-you-sure-to": "Are you sure you want to?", "this_will_not_save": " This will not be saved, and you will not be able to pay it at a later time.", "system-failure": "System failure", "yesCancel": "Yes, cancel", "goBack": "Go back", "salesInfoModal": {"view-on-computer": "Please view on computer", "view-on-computer-message": "The Sales section is available on", "computer": "computer", "for-now": "for now"}, "save": "Save", "BtCredit": "BlueTape Credit", "invoiceAlert": {"contact-supplier": "Please contact the supplier.", "cancelled": "This invoice has been cancelled!", "dismissed": "This invoice has been dismissed!", "expired": "This invoice has expired!", "paid": "This invoice has been paid!", "call-supplier": "Call supplier", "no-phone": "Company didn't provided a phone number", "choose-payment": "Choose payment", "choose-different-payment-title": "Choose a different payment method", "choose-different-payment-message": "Your vendor does not accept debit or credit cards. Choose a different payment option."}, "invoiceStatusTooltip": {"invoice-not-sent": "Invoice is created but has not been sent to customer", "invoice-expired-add-new-one": "Invoice is expired. Add and send a new invoice", "invoice-sent": "Invoice is sent to customer", "customer-dismissed-invoice": "Customer has dismissed invoice, no payment will be paid on this invoice", "past-due-seen-status": "Past Due (Seen)"}, "payWithCreditButton": {"app-processing": "Application Processing", "min-amount-applicable": "Minimum amount applicable is $5,000.00", "not-eligible": "Invoice is not eligible for BNPL", "does-not-affect-credit-score": "Credit request does NOT affect credit score", "no-fees": "No fees & payments until day 30"}, "Pay": "Pay", "PayNow": "Pay Now", "Processing": "Processing", "application_status": "Get paid", "GetPaid": "Get Paid", "ConnectYourAccounts": "Connect your accounts", "PleaseVerifyYourMail": "Please verify your e-mail", "WeHaveSentEmailTo": "We have sent you an email to", "ClickHere": "Click here", "toResendEmailVerification": "to resend email verification", "EmailIsVerified": "Email is verified", "Connect": "Connect", "ConnectYourBank": "Connect your bank", "ConnectionsAreEncrypted": "Connections are encrypted", "LinkYourCard": "Link your card", "CardDetailsAreTokenized": "Card details are tokenized", "Get": "Get", "Confirmation": "Confirmation", "Prequalified": "Prequalified", "BNPL306090Days": "Buy Now Pay Later in 30, 60, 90 days", "ForBTBuyNoPayLater": "For BlueTape Buy Now, Pay Later", "RequestingCreditDoesNOTAffectCreditScore": "Credit request does NOT affect credit score", "Requirements": "Requirements:", "StartYourGetPaidApplicationNow": "Start Your Get Paid Application Now", "NoCreditCheckApplying": "No credit check. Applying doesn’t affect your credit score.", "Start": "Start", "VerificationOfEmail": "Verification of email", "PleaseVerifyYourEmailBefore": "Please verify your email before submitting your application", "ThankYouVerifyingYourEmail": "Thank you for verifying your email", "ViewOnComputer": "View on computer for full access", "Welcome": "Welcome", "ProvideBusinessDetails": "Provide business details", "WeNeedYourTaxIDSSN": "We need your Tax ID & SSN to verify your business and run a background check", "NoCreditCheck": "No credit check", "WeWillNotRunYourCredit": "We will not run your credit. Your credit will not be affected", "AddCustomer": "Add customer", "AddInvoice": "Add invoice", "StartAddingYourCustomer": "Click here to navigate to Customer tab, from there you will be able to add your customer.", "OnceYouAddedYourCustomers": "Click here to navigate to the Invoice tab, from there you will be able to add invoices, quotes, etc manually for your customers. ", "YourApplicationIsPendingApproval": "Your application is pending approval", "PendingApproval": "Pending approval", "ThankYouForSubmittingYourPartnershipApplication": "Thank you for submitting your partnership application.", "SomeoneFromOurTeamWillContact": "Someone from our team will contact you soon.", "YourApplicationApproved": "Your application has been approved!", "AddCustomersSendInvoices": "Add customers and send invoices", "TermsConditions": "Terms & Conditions", "connectBankOrCard": "Connect your bank or card for faster payments", "requiredActions": "Required Actions", "see-more": "See More", "submit": "Submit", "upload": "Upload", "upload-voided-check": "<0>Upload a</0> <1>voided check</1>", "pay-with-btc": "<0>Pay with</0> <2>BlueTape Credit</2>", "export-data-tooltip": "Data in the table will be generated as a file based on the selected filter settings.\n To change export settings, change table filter options.", "export-button-text": "Export Data", "doYouHaveInvoiceModal": {"title": "Do you have an invoice?", "yes-i-have": "Yes, I have", "upload-your-invoice": "Upload your invoice to get started", "no-i-dont": "No, I dont", "get-prequalified-now": "Get prequalified now to purchase later"}, "DateRange": {"last-n-days": "Last {{days}} Days", "next-n-days": "Next {{days}} Days", "last-n-months": "Last {{months}} Months", "next-n-months": "Next {{months}} Months", "last-n-years": "Last {{years}} Years", "next-n-years": "Next {{years}} Years", "last-month": "Last 1 Month", "next-month": "Next 1 Month", "last-year": "Last 1 Year", "next-year": "Next 1 Year", "all-dates": "All Dates", "custom-dates": "Custom Dates..."}, "BluetapeCreditCarousel": {"get-prequalified": "<simple>Get <styled>Prequalified</styled></simple>", "check-back-later-for-update": "Check back later for update", "click-to-connect-bank": "Click to connect bank account", "connect-bank-now": "Connect your bank account now", "unable-to-approve": "Unable To Approve Application", "unable-to-offer-credit": "Unable to Offer Credit at this time", "you-are-prequalified-for": "You are pre-qualified for", "application-processing": "<simple>Application Is <styled>Processing</styled></simple>", "continue-request": "<simple>Continue <styled>Credit Request</styled></simple>", "click-here-for-details": "<simple><styled>Click here</styled> for more details</simple>", "we-will-notify-you": "We'll notify you when you can apply again", "thank-you-for-applying": "Thank you for applying", "application-declined": "Your application has been declined", "up-to": "<simple>Up to <styled>$ {{amount}}</styled></simple>", "small": "small", "bluetape-credit-is-ready": "BlueTape Credit is ready to use", "pay-with-btc": "Pay With BlueTape Credit", "pay-invoices-faster": "Pay SO or invoices easier and faster", "update-request": "<simple>Update <styled>Credit Request</styled></simple>", "avoid-fees-and-delays": "To avoid application fee and delays", "continue-credit-request": "Continue Credit Request", "your-info-prefilled": "Your business information has been pre-filled. Take a few minutes to review and submit your credit request."}, "AddPaymentCarousel": {"add-bank": "Add Bank", "or": "or", "card": "Card"}, "InvoiceDetails": {"title": "Details", "view-invoice-attachment": "View Invoice", "view-quote-attachment": "View Quote", "add-new-payment-method": "Add new payment method", "pick-up": "Pick-up", "quote-number": "Quote {{quote<PERSON><PERSON><PERSON>}}", "deliver-to": "Deliver to:", "deliver-tooltip": "<bold>Not sure about this address?</bold>\nOpen the attached invoice for more details or contact us if you have more <NAME_EMAIL>.", "attention-to": "Attention to:", "choose-method": "Choose Payment Method", "connected-accounts": "Connected Accounts", "pay-now": "Pay Now", "view-payment-methods": "View Payment Methods", "or": "or", "tell-us-why-you-want-to-cancel": "Tell us why you want to cancel", "i-dont-want-to-pay": "I don't want to pay", "what-is-bt": "What is BlueTape?", "amount_due": "Amount Due", "no_invoices_yet": "No invoices yet!", "pending_invoices": "Pending invoices", "action_required": "Action required", "due_date": "Due date:", "past_due": "Past Due", "transactions": "Transactions", "call_the_supplier": "Call the supplier", "close": "Close", "Invoices": "Invoices", "Invoice": "Invoice", "SelectAtLeastOneInvoice": "Please select at least one Invoice/SO to continue.", "ChoosePayment": "Choose payment", "InvoiceEligibleForACHdiscount": "This invoice is eligible for ACH Discount.", "SomeEligibleForACHdiscount": "Some invoices are eligible for ACH Discount.", "EligibleForACHdiscount": "Invoices are eligible for ACH Discount."}, "PaymentProcessingAlert": {"title": "Processing payment...", "message": "Your payment request is being processed"}, "UploadedInvoicePaymentSuccess": {"title": "Payment request is processing!", "message": "Thank you for using BlueTape to pay for your invoice. Your request for payment has been sent to your supplier. In order for us to process your payment, your supplier will need to onboard within 7 days of this request.\n\nWe have reached out to your supplier to onboard and accept your payment. We will notify when you if this invoice is accepted."}, "DismissAlert": {"title": "Are you sure you want to dismiss the invoice?", "message": "This invoice will no longer be active, and you will not be able to pay it at a later time."}, "AddedToProjectAlert": {"title": "Added to Project", "for-uploaded-invoice": "<simple>Your invoice was successfully added to</simple> <styled>{{projectName}}</styled>", "for-received-invoice": "<simple>Your invoice from</simple> <styled>{{companyName}}</styled> <simple>was successfully added to</simple> <styled>{{projectName}}</styled>"}, "InvoiceSidebarFooter": {"editDraft": "Edit Draft", "reject": "Reject", "approve": "Approve", "approve&send": "Approve & Send", "deleteDraft": "Delete Draft", "saveDraft": "Save Draft", "draft": "Draft", "edit": "Edit", "cancel": "Cancel", "send": "Send", "resend": "Resend", "delete": "Delete", "cannotEditExternalInvoice": "Invoices synced from {{source}} cannot be edited"}, "QuoteSidebarFooter": {"cannotEditExternalQuote": "Quotes synced from {{source}} cannot be edited"}, "AccountSidebarFooter": {"EditCustomer": "Edit Customer", "ExportInvoices": "Export Invoices", "AddInvoice": "Add Invoice", "ResendInvite": "Resend Invite", "SendInvite": "Send invite"}, "Menu": {"Home": "Home", "TradeCredit": "Trade Credit", "Draws": "Draws", "AccountOverview": "Account Overview", "arAdvanceInvoices": "AR Advance Invoices", "Invoices": "Invoices", "Payables": "Payables", "Receivables": "Receivables", "ReferFriends": "Refer Friends", "Sales": "Sales", "ARAdvance": "AR Advance", "Customers": "Customers", "SalesTransactions": "Transactions", "CreditTransactions": "Transactions", "Settlements": "Settlements", "Statements": "Statements", "Settings": "Settings", "Pay": "Pay", "Notifications": "Notifications", "Projects": "Projects", "Accounts": "Accounts", "Profile": "Profile", "More": "<PERSON><PERSON>"}, "NotificationsModal": {"notifications": "Notifications", "heading": "No notification yet", "sub_heading": "stay tuned! Notification about your activity will show up here."}, "CreditTab": {"make_a_payment": "Make a payment", "makePaymentTo": "BlueTape Inc", "pay": "Pay", "make_one_time_payment": "Make One time payment", "amountError": "The amount entered is greater than the amount due", "amount": "Amount", "to": "To", "payment_method": "Payment method", "loan_schedule": "Loan <PERSON>", "details": "Details", "total_paid": "Total Paid", "remaining": "Remaining", "payments": "Payment(s)", "payment": "Payment", "LoanPayment": "Loan Payment", "supplier": "Supplier", "dueOn": "Due on", "loan-payment-made-to-supplier": "Loan payment is made to {{- supplierCompany}}", "loan-repayment-made-to-supplier": "Loan repayment is made to {{- supplierCompany}}", "total-daily-penalty-interest": "Total daily penalty ", "interest": "interest", "avoid-penalty-interest": "Your account is past due. Please pay the past due amount now to avoid accruing penalty interest.", "why-penalty-is-charged-title": "Why am I being charged a penalty interest?", "why-penalty-is-charged-message": "If you're past due on two different installment payments or late by 7 days or more on one installment payment, you will start accruing a periodic penalty interest charge on the outstanding balance, as detailed in your account agreement. This penalty interest accrual will end once you've paid the past due principal amounts as well as all late fees and penalty interest charges.", "including-fee-and-interest": "Including Fees and Interest*"}, "AutoTradeCredit": {"badge": "Automated Trade Credit", "DrawApprovedAlert": {"title": "A new invoice has been approved as a draw!", "multipleDrawsTitle": "{{drawsCount}} draws have been approved!", "contactInfoDescription": "If you have any concerns about this invoice or would like to extend your payment term schedule, please contact us here:", "supplier": "Supplier", "invoiceAmount": "Invoice Amount", "invoiceNumber": "Invoice Number", "drawAmount": "Draw Amount", "terms": "Terms", "numberOfPayments": "Number of Payments", "drawFee": "Draw Fee", "drawDetails": "Go to Draw Details", "missingDraw": "Unable to load draw information"}, "ScheduleWarning": {"title": "A new draw was processed through automated trade credit.", "warning": "If you have any concerns about this invoice or would like to extend payment term schedule, please contact us here:"}}, "LoanDetails": {"total_invoice": "Total Invoice Purchase", "total_installment": "Total Installment", "invoice_amount": "Invoice Amount", "total_draw_amount": "Total Draw Amount", "draw_id": "Draw ID", "draw_status": "Draw Status", "payment_method": "Payment method", "view_invoice": "View invoice #", "loan_agreement": "Loan Agreement", "virtualCardDetails": "Virtual Card Details", "viewCardNumber": "View Card Number", "rescheduling-fee": "Extension Fee"}, "Account": {"first-name": "First Name", "last-name": "Last Name", "full_name": "Full Name", "display-name": "Display Name", "email": "Email Address", "click-here-to-download-LoC": "Click here to view and download your line of credit agreement", "click-here-to-download-PGA": "Click here to view and download your personal guarantor agreement", "address_placeholder": "street address, unit #, city, state, zip code ", "save": "Save", "to_deactivate_account": "To deactivate account, contact <NAME_EMAIL>", "edit": "Edit", "edit-account": "Edit Account", "edit-customer": "Edit Customer", "customer-number": "customer number", "customer-number-label": "Customer number", "date-added": "Date added", "contact-name": "Contact name", "cell-phone-number": "Cell phone number", "business-name": "Business Name", "business-email": "Business email", "business-phone-number": "Business phone number", "business-address": "Business address", "business-information": "Business Information", "bluetape-credit": "BlueTape Credit", "in-house-credit-details": "In-House Credit Details", "credit-status": "Credit Status", "ihc-credit-status": "In-House Credit Status", "recourse-type": "Recourse Type", "non-recourse": "Non-Recourse", "recourse-percentage": "Recourse Percentage", "ihc-credit-terms": "In-House Credit Terms ", "ihc-credit-limit": "In-House Credit Limit", "ihc-credit-balance": "In-House Credit Balance", "ihc-credit-available": "In-House Credit Available", "ihc-credit-available-tooltip": "May reflect Approved Quotes for Invoices that are not submitted yet", "ihc-processing-payments": "Processing Payment(s)", "credit-limit-used": "Credit Limit Used", "total-invoices-due": "Total Invoices Due", "maximum-credit-amount": "Maximum Credit Amount", "available-credit": "Available Credit", "held-amount": "Quote <PERSON> Amount", "outstanding-amount": "Outstanding Amount", "processing-amount": "Processing Amount", "past-due-amount": "Past Due Amount", "past-due-interval-30": "1-30 days", "past-due-interval-60": "31-60 days", "past-due-interval-90": "61-90 days", "past-due-interval-120": "91-120 days", "past-due-interval-120-plus": "120+ days", "billing-contact": "Billing Contact", "notes": "Notes", "notes-will-be-here": "Notes will be here", "house-credit-info": "House Credit Info", "in-house-credit-info": "In House Credit Info", "account-open-date": "Account Open Date", "ar-forward-terms": "AR Forward Terms", "ar-forward-credit-limit": "AR Forward Credit Limit", "ar-forward-credit-balance": "AR Forward Credit Balance", "ar-forward-available-credit": "AR Forward Available Credit", "processing-payments": "Processing Payment(s)", "date-placeholder": "MM/YYYY", "maximum-credit-limit": "Maximum Credit Limit", "payment-history": "Payment History", "average-days-to-pay": "Average Days To Pay", "open-a-r": "Open A/R", "current-balance": "Current Balance", "highest-credit": "Highest Credit", "billing-past-due-1": "1 Billing Past Due", "billing-past-due-2": "2 Billing Past Due", "billing-past-due-3": "3 Billing Past Due", "ihc-billing-past-due-1": "Less than 30 Days Past Due", "ihc-billing-past-due-2": "More than 60 Days Past Due", "ihc-billing-past-due-3": "More than 90 Days Past Due", "billing-past-due-more": "More than 3 Billing Past Due", "total-past-due": "Total Past Due", "average-invoices": "Average Invoices", "number-of-rejected-ach": "Number of Rejected ACH & Check in Past 24 months", "ein-ssn": "EIN/SSN", "invalid-phone-number": "Invalid Phone Number", "sales-rep": "Sales Rep", "add-sales-rep": "Add Sales Representative", "choose-a-sales-rep": "Choose a sales representative from the list below", "if-sales-rep-is-not-listed": "If the sales representative is not listed below, first add them as a user under setting.", "edit-sales-rep": "Edit Sales Representative ", "trade-credit-status": {"not-applied": "Not Applied", "good-standing": "Good Standing", "approved": "Trade Credit approved until {{validTill}}", "approved-hint": "After, customers must provide bank details or connect bank to continue use.", "incomplete-application": "Incomplete Application", "pending": "Application Pending Review", "account-in-review": "Account in Review", "account-in-review-hint": "The customer’s account is under review. Please continue submitting invoices as usual, processing may be delayed, and BlueTape will contact the customer if needed.", "account-in-review-decision-rule-hint": "Aging amount details are provided below. Please continue submitting invoices as usual, processing may be delayed, and BlueTape will contact the customer if needed.", "past-due-10": "Past due by less than 10 days", "past-due-10-hint": "Aging amount details are provided below. Please continue submitting invoices as usual, processing may be delayed, and BlueTape will contact the customer if needed.", "past-due-60": "Past Due", "past-due-60-hint": "Past due account details are below. Please continue submitting invoices as usual, processing may be delayed, and BlueTape will contact the customer if needed.", "denied": "Denied until {{validTill}}", "delinquent": "Delinquent", "credit-denied": "Denied"}, "in-house-credit-status": {"not-applied": "Not Applied", "good-standing": "Good Standing", "past-due": "Past Due", "past-due-hint": "Invoice(s) past due by less than 10 days", "incomplete-application": "Application Incomplete", "pending": "Application In Review", "pending-hint": "Application is under review by BlueTape", "closed": "Closed", "rejected": "Rejected", "cancelled": "Cancelled", "in-collection": "Purchaser Account on Hold", "in-collection-hint": "For more details contact us.", "on-hold": "Purchaser Account on Hold", "on-hold-hint": "For more details contact us", "inactive": "Account Set Up Required", "inactive-hint": "Purchaser has an account but is not enabled, contact BlueTape to set up In House Account"}}, "TabAccount": {"hi": "Hi", "admin": "Admin", "pageTitle": "Customers", "tradeCreditTab": "Trade Credit", "arAdvanceTab": "AR Advance", "exportData": "Export Data", "searchPlaceholder": "Business name, contact, cell phone...", "columns": {"status": "Status", "business": "Business", "contact": "Contact", "phone": "Phone Number", "tradeCreditStatus": "Trade Credit Status", "inHouseCreditStatus": "In-House Credit Status", "lastPurchaseDate": "Last Purchase Date", "quickBooksImport": "QuickBooks Import"}, "turnOnQuickBooksImport": "Turn On QuickBooks Import", "turnOffQuickBooksImport": "Turn Off QuickBooks Import", "deleteCustomer": "Delete Customer", "deleteConfirmationModal": {"text": "{{accountName}} account will be permanently delete. All data associated with the customer remains the same.", "header": "Are you sure you want to delete the customer?"}, "empty-customers-table-title": "You don’t have any Customers yet", "click-to-create-new": "Click on the button below to add new Customer"}, "TabTransactions": {"apply_dates": "Apply Dates", "select_dates": "Select Dates", "to": "To", "from": "From"}, "trade-credit": {"document-name-transactions": "Trade Credit Transactions", "tab": "Trade Credit", "advance-payment": "Advance", "final-payment": "Final", "term-days": " days", "search-placeholder": "Search for business name, amount or invoice number", "number-of-payments": "Num. of Settled Payments", "total-settled-amount": "Total Settled Amount", "advance-settled-amount": "Adv. Payment Settled", "final-settled-amount": "Final Payment Settled", "outstanding-final-payment": "Outstanding Final Payment", "number-of-payments-tooltip": "Number of payments that have been settled for selected time frame.", "total-settled-amount-tooltip": "Advanced payments plus final payments settled into supplier bank for selected time frame.", "advance-settled-amount-tooltip": "Advance payment amount deposited into supplier bank for selected time frame.", "final-settled-amount-tooltip": "Final payment amount deposited into supplier bank for selected time frame.", "outstanding-final-payment-tooltip": "Outstanding final payment amount waiting to be deposited into supplier bank account."}, "TabSettlements": {"grouped_invoices_label": "{{invoices_count}} invoices", "overview": "Overview", "overview-description": "Data is shown based on selected filters.", "emptyResult": {"header": "You don’t have any payments yet.", "text": "All future payments will be displayed here"}, "columns": {"business": "Business", "invoice-number": "Invoice #", "invoice-amount": "Invoice Amount", "ach-discount": "ACH Discount", "merchant-fee": "Merchant Fee", "supplier-fee": "Supplier Fee", "terms": "Terms", "term": "Term", "transaction-date": "Transaction Date", "release-date": "Release Date", "settled-amount": "Settled Amount", "type": "Type", "payment-type": "Payment Type", "status": "Status"}, "trade-credit": {"document-name-transactions": "Trade Credit Transactions", "tab": "Trade Credit", "advance-payment": "Advance", "final-payment": "Final", "reserve-amount": "Reserve Amount", "ar-advance-payment": "Advance Payment", "term-days": " days", "search-placeholder": "Search for business name, amount or invoice number", "number-of-settled-payments": "Number of Settled Payments", "total-settled-amount": "Total Settled Amount", "advance-settled-amount": "Adv. Payment Settled", "final-settled-amount": "Final Payment Settled", "outstanding-final-payment": "Outstanding Final Payment", "reserve-amount-release": "Reserve Amount Release", "reserve-amount-outstanding": "Reserve Amount Outstanding", "number-of-payments-tooltip": "Number of payments that have been settled for selected time frame.", "total-settled-amount-tooltip": "Advanced payments plus final payments settled into supplier bank for selected time frame.", "advance-settled-amount-tooltip": "Advance payment amount deposited into supplier bank for selected time frame.", "final-settled-amount-tooltip": "Final payment amount deposited into supplier bank for selected time frame.", "outstanding-final-payment-tooltip": "Outstanding final payment amount waiting to be deposited into supplier bank account.", "reserve-amount-release-tooltip": "Reserve amount deposited into supplier bank for selected time frame.", "reserve-amount-outstanding-tooltip": "Outstanding reserve amount waiting to be deposited into supplier bank account."}, "ar-advance": {"tab": "AR Advance"}, "ach": {"tab": "ACH & Card", "search-placeholder": "Search for business name, amount, or payment methods", "number-of-payments": "Number of Payments", "total-invoice-amount": "Total Invoice Amount", "settled-amount": "Settled Amount", "amount-in-progress": "Amount in Progress", "number-of-payments-tooltip": "Number of payments that have been settled for selected time frame.", "total-invoice-amount-tooltip": "Total invoice amount for selected time frame.", "settled-amount-tooltip": "Amount deposited into supplier bank account after discounts and fees for selected time frame.", "amount-in-progress-tooltip": "Invoice amount, after discounts and fees, that is waiting to be settled for selected time frame."}, "Sidebar": {"settlement-details": "Settlement Details", "business-name": "Business Name", "invoice-number": "Invoice #", "terms": "Terms", "settled-amount": "Settled Amount", "status": "Status", "transaction-date": "Transaction Date", "release-date": "Release Date", "amount": "Amount", "total-fee": "Total Fee", "merchant-fee": "Merchant Fee", "supplier-fee": "Supplier Fee", "ach-discount": "ACH Discount", "invoices-list-title": "Multiple Invoice Payment", "advance-payment": "Advance Payment", "invoice-details": "Invoice Details", "invoice-amount": "Invoice Amount", "final-payment": "Final Payment"}}, "TabInvoice": {"lets_get": "Let’s get you started with a BlueTape account!", "first_name": "First Name", "last_name": "Last Name", "email_address": "Email Address", "sign_in_to_bluetape": "Sign in to your BlueTape account!", "cell_phone_number": "Cell Phone Number", "continue": "Continue", "enter_code": "Enter code", "enter_six_digit": "Enter the six (6) digit code that was sent to:", "next": "Next", "resend_code": "Resend the code", "pay_invoice": "Pay invoice", "invoice_amount": "Invoice amount:", "total_amount": "Total amount", "LateInterestCharge": "Late Interest Charge", "InvoiceAmount": "Invoice Amount", "payment_will_proceed": "Payment will proceed", "swipe_to_pay": "Swipe to pay", "pick_payment_plan": "Pick a payment plan", "bluetape_credit": "BlueTape Credit", "zero_payment": "Zero payment for the first 30 days", "select_one_of_three": "Select one of these 3 options", "one_time_payment": "One time payment of", "five_payments": "5 Weekly payments of $1,020", "nine_payments": "9 Weekly payments of $578", "auto_pay": "Auto pay", "checking": "Checking", "auto_pay_on_jan": "Auto pay on Jan 24", "by_swipe_and_pay": "by swipe and pay you agreed to the", "terms_and_conditions": "terms and conditions", "days": "days", "first_due_date": "First due date", "jan_24": "Jan 24", "interest": "Interest", "total": "Total", "payment_processing": "payment is processing!", "it_may_take": "It may take a few minutes.Please don’t refresh or close the tab", "payment_didnt_go": "Your payment didn’t go through!", "please_start": "Please start over or contact your bank", "go_back_to_invoice": "Go back to invoice", "payment_success": "Your payment has been successfully processed", "thank_you": "Thank you!", "please_wait_until": "Please wait while we process your payment!", "this_will_take": "This may take a few moments. Please do not refresh or close the tab.", "thank_you_it_will_take": "Thank you for your payment. It’ll take 2-3 business days to process.", "done": "Done", "insufficient_funds": "Insufficient Funds", "your_account": "Your account does not have sufficient funds.", "choose_another_payment_method": "Choose Another Payment Method", "go_back_and_try_again": "Go back and try again", "call_the_supplier": "Call the supplier", "close": "Close", "choose_payment_method": " Choose payment method", "amount_due": "Amount Due", "invoice_number": "Invoice number:", "view_invoice": "View the invoice", "deliver_to": "Deliver to:", "pick_up": "Pick-up", "bt_credit": "BlueTape Credit", "coming_soon": "Coming soon", "dismiss": "<PERSON><PERSON><PERSON>", "due_date": "Due date:", "by_continuing_you_agree": "By continuing, you agree to the", "contract": "Contract", "terms_&_conditions": ", Terms & Conditions", "and": "and", "privacy_policy": "Privacy Policy", "allCompanyInvoices": "All Invoices/SO from {{- companyName}}({{count}})", "allPayables": "All Invoices/SO", "PayTotal": "Pay Total", "TotalAmount": "Total Amount", "Pay": "Pay", "DueOn": "Due on", "NewInvoice": "New Invoice", "InvoiceStatus": "Invoice Status", "RemainingAmount": "Remaining Amount"}, "TabPayments": {"link_a_bank_account": "Link a Bank account", "account_type": "Account Type", "name_on_account": "Name on account", "choose_bank": "Choose bank", "routing_number": "Routing Number", "account_number": "Account Number", "be_sure_to_double_check_account": "Be sure to double-check your account number. Banks may not flag errors until you transfer money.", "agree_and_link": "Agree and link", "payment_methods": "Payment Methods", "your_info_is_safe": "Your information is safe and secure", "refer_a_supplier_and_earn": "Refer a supplier and earn $1,000", "bank_account": "Bank Account", "credit_or_debit_card": "Credit/Debit Card", "link_your_card_manualy": "Link your card manually", "debit_or_credit": "Debit or Credit card", "welcome_to_bluetape": "Welcome to BlueTape", "for_faster_checkout": "For Faster Checkout", "bt_credit_will_launch_soon": "BlueTape Credit will launch soon.", "a_new_way": "A new way of paying for building materials over time will be available to you shortly.", "we_will_notify": "We will notify you when you can apply. Thank you for your patience.", "error": "Error:", "invite_collegue": "Invite a colleague", "invite_description": "Invite colleagues to help complete the credit application or to help with payment management.", "authorized": "Authorized?", "explain": "Explain why we ask this question...", "invite": "Invite"}, "Projects": {"projects": "Projects", "invoices": "Invoices", "details": "Details", "no_invoices": "No invoices yet", "no_projects": "No Project added yet", "sub_heading": "Tap the plus button below to add a card project to tag your invoices.", "added": "Added", "confirm_action": "Confirm Action", "confirm_message": "All the details of this project will be deleted. Please confirm you want to delete this project from your account.", "total_invoice_value": "Total Invoice Value", "date_created": "Date Created", "type": "Type of Project", "address": "Address", "project_information": "Project Information", "your_invoice_from": "Your invoice from ", "was_added": "was successfully added to ", "your_project": "Your project ", "been_added": "has been added to your project list you can now add invoice to it. ", "view_project": "View Project", "add_new_item": "Add new item", "add_new_project": "Add new project", "ok_thanks": "Okay, Thanks", "create_first_project": "Create your first project", "create_first_project_subtitle": "You do not have any projects yet. Сreate \n a project and add details.", "project_created": "The project was created", "project_created_subtitle": "<styled>Your project <h2>{{name}}</h2> was created succesfully.\n You can add more details on the Projects Detail \n page.</styled>", "view_invoice_and_add_project": "View invoices and add new projects", "are_you_sure_to_delete": "Are you sure you want to \n delete the project?", "the": "The", "project_delete_subtitle": "project and all information \n about it will be permanently deleted without \n the possibility of recovery", "delete_project": "Delete the project", "go_back": "Go back", "project_was_deleted": "The project was deleted", "was_permanently_deleted": "was permanently deleted.", "what_is_the_name": "What is the name of the project?*", "enter_the_customer": "Enter the customer/vendor name", "enter_customer_name": "Enter the customer name", "what_is_the_address": "What is the address?*", "what_is_the_value": "What is the value of your contract for this project?", "add_note": "Add note", "enter_name": "Enter name of project", "enter_or_search": "Enter or search customer name, business name, email, cell phone", "enter_an_address": "Enter an address", "enter_amount": "Enter amount", "enter_description": "Enter a description", "create_project": "Create project", "save": "Save", "select_contact": "Select Contact", "add_new_customer": "Add new customer", "okay": "Okay", "BackToProjects": "Back to Projects", "ViewDetail": "View Detail", "OutstandingReceivables": "Outstanding Receivables", "Income": "Income", "Expense": "Expense", "Customer": "Customer", "InvoicesNSalesOrders": "Invoices & Sales Orders", "AddNewInvSo": "Add Invoice/SO", "AddInvSOToProject": "Add Invoices/SO to project", "cancel": "Cancel", "selectAddToProject": "Select and add Invoice(s) related to this project", "nameOfTheProject": "Name of the project", "customer": "Customer", "valueOfContract": "Value of contract", "notesWillBeHere": "Notes will be here", "edit": "Edit details", "RemoveInvoiceSo": "Remove Invoice/SO", "AreYouSureRemoveInvoice": "Are you sure you want to remove this invoice?", "RemoveInvoiceText": "The invoice will be removed from under this project. You can add it again later.", "AddRemoveSearchInputTable": "Search invoice or PO number, business, date, amount", "paymentDetails": "Payment Details", "invoiceDetails": "Invoice Details", "invoiceNumber": "Invoice number", "businessName": "Business Name", "phoneNumber": "Cell Phone Number", "deliverType": "Delivery Type", "deliverTo": "Deliver to", "paidOn": "Paid on", "status": "Status", "paymentMethod": "Payment method", "project": "Project", "paidAmount": "<PERSON><PERSON>", "processingAmount": "Processing Amount", "remainingAmount": "Remaining Amount", "lateFee": "Late Interest Charge", "invoiceAmount": "Invoice Amount", "totalAmount": "Total Amount", "ProjectTabNoFound": "You do not have any invoice or sales orders for this project.", "AddInvoiceSO": "Add invoices or SO", "NoReceivablesYet": "No receivables yet", "NoIncomeYet": "No income yet", "NoPayablesYet": "No payables yet", "NoExpensesYet": "No expenses yet", "Expenses": "Expenses", "InvOrSONo": "Invoice / SO No.", "projectDetails": "Project Details", "editTitle": "Edit {{name}} project", "searchInvoice": "Search invoice or SO number, business, date, amount", "searchPropertyAddress": "Search property address", "left": "left", "InvoiceRemoved": "The Invoice/SO is removed", "wasPermanentRemoved": "was permanently removed.", "createdAt": "createdAt", "date": "Date", "SearchInvoiceSOInputText": "Search invoice or SO number, business, date, amount", "SearchInvoicePOInputPlaceholder": "Search invoice or SO number, business, date, amount", "PickUp": "Pick up", "TryAgainInvSo": "Try again or add new invoice/SO", "ProjectName": "Project Name", "LastActivityDate": "Last Activity Date", "TotalInvoiceSO": "Total Receivables Paid", "AllStatuses": "All statuses", "AllDates": "All dates", "Last30days": "Last 30 days", "Last60days": "Last 60 days", "Last90days": "Last 90 days", "TryAgainProject": "Try again or add new project", "more-details": "More Details", "create-project": "Create project", "supplier": "Supplier"}, "ProjectSidebar": {"address": "Address", "notice": "Notice of Commencement", "value-of-contract": "Value of Contract", "first-day": "First day on the job", "last-day": "Expected last day on the job", "role": "Role", "contractorDetails": "Prime / General Contractor Details", "businessName": "Business Name", "firstName": "First Name", "lastName": "Last Name", "business-phone": "Business Phone Number", "phone": "Phone Number", "email": "Email", "business-address": "Business Address", "home-address": "Home Address", "project-type": "Project Type", "private-type": "Type of Private Project", "built-for": "Project is being built for", "description-of-public": "Description of public project", "project-bond": "Project Bond", "project-contract": "Project Contract", "bond-exists": "Bond exists", "subcontractor-should-carry-bond": "Required to carry a bond (As the subcontractor)", "owner-type": "Type"}, "Referrals": {"referrals": "Referrals", "earn": "<PERSON><PERSON><PERSON>", "give": "Give", "for_limited_time": "For a limited time only!", "share_your_referral": "Share your referral code", "suplier_vendor_signs_up": "Supplier/vendor signs up with your code", "suplier_vendor_makes_transaction": "Supplier/vendor makes their first transaction of $5K or more", "copy_and_share_your_link": "Copy and share your unique link", "share_with_friends": "Share with your friends", "or_share_your_unique": "Or share your unique referral code", "terms": "Terms & conditions", "apply_for_more_info": " apply - visit BlueTape.com for more info", "both_you_and_supplier": "Both you and your supplier get ", "when_they_signup": "when they sign up with your code and they reach $3,000 in transactions.", "earned": "Earned ", "total_of": "Total of", "users_invited": "users invited", "joined": "Joined"}, "Wallet": {"link_a_bank": "Link a Bank Account", "account_type": "Account Type", "checking": "Business Checking", "saving": "Business Saving", "name_an_account": "Name on account", "search_bank": "Search your bank", "routing_number": "Routing Number", "account_number": "Account Number", "set_primary_account": "Set this as my primary account", "agree": "Agree & Link Account", "confirm_action": "Confirm Action", "please_confirm_to_delete": "Please confirm you want to delete this payment method from your account", "go_back": "Go back", "yes_delete": "Yes, delete", "no_bank_conected": "No bank connected yet", "tap_the_plus": "Tap the plus button below to connect your bank for faster payments", "savings": "Savings", "no_card_linked_yet": "No card linked yet", "tap_to_link_your_card": "Tap the plus button below to link your card for faster payments", "credit_debit_card": "Credit/Debit Card", "account_alert_title": "Your financial institution is not supported at this time.", "account_alert_text": "Please go back and add another bank.", "account_alert_button": "Go back ", "manually-added-bank": "Manually Added Bank:", "online-connected": "Connected via Online Banking", "disconnected": "Disconnected - <PERSON><PERSON> to try again", "upload_bank_statements": "Upload bank statements", "upload_bank_statements_extensions": "(pdf, jpg, png, zip, eps. not more than 2MB)", "connection-error-text": "The connection to this bank account cannot be established. Please link another bank account.", "connection-error": "Connection Error", "header": "BlueTape Wallet", "bank-delete-footer": "Contact BlueTape to Remove Account", "card-delete-footer": "<PERSON>wipe left to delete", "no-account-name": "No account name"}, "More": {"more": "More", "log_out": "Log Out"}, "Onboaring": {"connect_to_bank": "Connect to your bank", "access_your_account": "Access your account today.", "link_bank_manualy": "Link your bank manually", "take_days": "Takes 2 - 3 business days.", "what_is_bt_credit": "What is BlueTape Credit", "once_you_have_invoice": "Once you have an invoice from a partnered supplier, use BlueTape Credit to finance your building materials with payment terms of ", "30_60_90": "30, 60, or 90 days.", "what_do_i_need": "What do I need to qualify?", "credit_request_does_not": " Credit request does NOT affect credit score ", "connect_your_bank_or_card": "Connect your bank / card", "add_wallet_later": "Add wallet later", "bluetape": "Bluetape", "credit": "Credit", "is_here": "is here", "what_is": "What is", "bluetape_credit": "BlueTape Credit?", "want_to": "Want to", "buy_now_pay_later": "buy now and pay later", "at_your_favorite_material": "at your favorite material suppliers? Ask them to join BlueTape and", "earn": "earn", "invite_suppliers": "Invite suppliers", "learn_how_to_use_bluetape": "Learn how to use BlueTape"}, "Profile": {"profile": "Profile", "edit": "Edit", "business_info": "Business information", "full_name": "Full name", "business_name": "Business Name", "email": "Email", "phone_number": "Phone Number", "edit_business_info": "Edit business information", "first_name": "First Name", "last_name": "Last Name", "phone": "Phone Number", "save": "Save", "team": "Team", "do_you_realy_want_to_delete_user": "Do you really want to delete user?"}, "BankManagment": {"checkingAccount": "Checking Account", "routing": "Routing"}, "TransactionsView": {"details": "Transaction details", "issueRefund": "Issue a refund", "number": "Transaction number", "action": "Action", "amount": "Amount", "dateOfPay": "Date of payment", "formOfPay": "Form of payment", "invoiceNumber": "Invoice number", "invoiceDueDate": "Invoice due date", "business": "Business", "contact": "Contact", "cellPhoneNumber": "Cell Phone number", "accountInformation": "Account Information", "accountNumber": "Account Number"}, "doYouHaveInvoiceAlert": {"topTitle1": "Do you have a Sales ", "topTitle2": "Order or Invoice?", "subtitle": "I have a SO or Invoice", "upload": "Upload to get started", "bottomTitle": "No SO or Invoice? ", "bottomSubtitle": "Get prequalified now to purchase later", "getPrequalified": "Get prequalified "}, "ContactAuthorizedModal": {"contact": "Contact your authorized person\n to submit this application", "okThanks": "Okay, Thanks"}, "PayNowWithVirtualCard": {"title": "Virtual Cards", "message": "Use your one-time card to pay your uploaded SO or invoice.", "pay-now": "Pay Now", "upload-new-invoice": "Upload new SO or invoice", "ready-to-pay": "Ready to pay with Your One-Time Card", "expired": "The amount on the card has expired. Your loan has been canceled.", "see-card-details": "See Card Details"}, "CardDetails": {"details": "Card Details", "amount": "Amount Used", "cardNumber": "Card Number", "valid": "Valid thru", "cvv": "CVV"}, "VirtualCard": {"expiration": "Expiration", "amount": "$ ", "cvv": "CVV", "debit": "Debit"}, "CardExpired": {"info": "The expiration occurred because the card was not used within 3 days.", "upload-new-so-or-invoice": "Upload new SO or invoice to pay with BlueTape Credit.", "mask": "XXXX-XXXX-XXXX-XXXX"}, "HowToUseVirtualCard": {"one-time-card-ready": "Your One-Time Card is ready to pay!", "valid-for-3": "Card funds will be valid for <2>3 business days.</2>", "how-to-use-this-card": "How to use this card", "point1-title": "Contact your vendor", "point1-info": "Pay over the phone using the card details.", "point2-title": "Billing address details ", "point2-info": "<simple>During online checkout, enter the following for the billing address:\n</simple><styled>{{ businessAddress }}</styled>", "point3-title": "Your loan starts when card is used", "point3-info": "The first payment will be due 30 days from when card is used. Loan details and schedule will be provided on this page."}, "VirtualCardReadyAlert": {"title": "Your application has been approved!", "message": "Your one-time card is ready to use. Call your vendor to make your payment now.", "detail": "Detail", "vendor": "<PERSON><PERSON><PERSON>", "button-text": "Pay Now"}, "InvoiceUpload": {"title": "Add Invoice", "subtitle1": "Invoice Details", "subtitle2": "<PERSON>ter Vendor Information", "is-this-for-service-or-material": "Is this payment for service or material?", "picked-up-or-delivered": "Will this item be picked up or delivered?", "invoice-number": "Invoice number", "due": "Due - MM/DD/YYYY", "tax-amount": "Tax Amount", "service": "Service", "material": "Material", "both": "Both", "next": "Next", "add-invoice": "Add Invoice", "business-name": "Business name", "contact-name": "Contact name", "email": "Email address", "business-address": "Business address", "cell-phone": "Phone number", "amount": "Total Amount", "pick-up": "Pick up", "deliver-to": "Deliver to", "supplier-exists-modal": {"title": "Great news! Your supplier is part of BlueTape.", "message": "What does this mean for you?", "description": "Ask your supplier to send your invoice through BlueTape today! You’ll enjoy lower fees when using Trade Credit. If you need more help, contact us at <email><EMAIL></email>", "got-it-button": "Got it!", "okay-button": "Okay, Thanks"}}, "Receivables": {"invoice": {"label": "Invoice", "short-label": "Invoice", "list-title": "All Invoices", "number": "Invoice Number", "number-export": "Invoice Number", "amount": "Invoice Amount", "status": "Invoice Status", "number-validation-message": "Enter the invoice number", "date": "Date of Invoice", "new": "New Invoice", "add": "Add Invoice", "create": "Create Invoice", "add-new": "Add New Invoice", "create-new": "Create New Invoice", "edit": "Edit Invoice", "upload": "Upload Invoice", "preview": "Preview Invoice", "cancel": "Cancel Invoice", "send": "Send Invoice", "resend": "Resend Invoice", "delete": "Delete Invoice", "approve": "Approve Invoice", "reject": "Reject Invoice", "send-new": "Send new Invoice", "email": "Email Invoice", "text": "Text Invoice", "uploading": "Uploading Invoice", "invalid-date-error": "Invoice date is invalid", "send-method-title": "How do you want to send the invoice?", "filter-placeholder": "Invoice number, Business customer, Amount", "upload-description": "Upload or drag and drop the invoice", "big-file-size-error": "The invoice size is too big. (max. is 10MB). Please upload a smaller file size", "receivable-placed": "Invoice has been placed successfully", "receivable-draft-placed": "Invoice has been placed as draft successfully", "select-receivable-prompt": "Select invoice below to preview & edit each invoice details.", "no-receivables-title": "You don’t have any Invoices yet", "click-to-create-new": "Click on the button below to create new Invoice"}, "sales_order": {"label": "Sales Order", "short-label": "SO", "list-title": "All Sales Orders", "number": "Order number", "amount": "SO Amount", "status": "SO status", "number-validation-message": "Enter the SO number", "date": "Date of SO", "new": "New SO", "add": "Add Sales Order", "create": "Create Sales Order", "add-new": "Add New Sales Order", "create-new": "Create New SO", "edit": "Edit Sales Order", "upload": "Upload Sales Order", "preview": "Preview SO", "cancel": "Cancel Sales Order", "send": "Send Sales Order", "resend": "Resend Sales Order", "delete": "Delete Sales Order", "approve": "Approve Sales Order", "reject": "Reject Sales Order", "send-new": "Send new Sales Order", "email": "Email SO", "text": "Text SO", "uploading": "Uploading SO", "invalid-date-error": "Order date is invalid", "send-method-title": "How do you want to send the SO?", "filter-placeholder": "Sales Order number, Business customer, Amount", "upload-description": "Upload or drag and drop the sales order", "big-file-size-error": "The SO size is too big. (max. is 10MB). Please upload a smaller file size", "receivable-placed": "SO has been placed successfully", "receivable-draft-placed": "SO has been placed as draft successfully", "select-receivable-prompt": "Select SO below to preview & edit each SO details.", "no-receivables-title": "You don’t have any Sales Orders yet", "click-to-create-new": "Click on the button below to create new Sales Order"}, "quote": {"label": "Quote", "short-label": "Quote", "list-title": "All Quotes", "number": "Quote number", "amount": "Quote Amount", "status": "Quote status", "number-validation-message": "Enter the quote number", "date": "Date of Quote", "new": "New Quote", "add": "Add Quote", "create": "Create Quote", "add-new": "Add New Quote", "create-new": "Create New Quote", "edit": "Edit Quote", "upload": "Upload Quote", "preview": "Preview Quote", "cancel": "<PERSON><PERSON> Quote", "send": "Send Quote", "resend": "<PERSON>send Quote", "delete": "Delete Quote", "approve": "Approve Quote", "reject": "Reject Quote", "send-new": "Send new Quote", "email": "<PERSON><PERSON>uote", "text": "Text Quote", "uploading": "Uploading Quote", "invalid-date-error": "Quote date is invalid", "send-method-title": "How do you want to send the quote?", "filter-placeholder": "Quote number, Business customer, Amount", "upload-description": "Upload or drag and drop the quote", "big-file-size-error": "The quote size is too big. (max. is 10MB). Please upload a smaller file size", "receivable-placed": "Quote has been placed successfully", "receivable-draft-placed": "Quote has been placed as draft successfully", "select-receivable-prompt": "Select quote below to preview & edit each quote details.", "no-receivables-title": "You don’t have any Quotes yet", "click-to-create-new": "Click on the button below to create new Quote"}, "receivables": "Receivables", "invoices": "Invoices", "ar-advance-invoices": "AR Advance", "sales-orders": "Sales Orders", "quotes": "Quotes", "action-required": "Action Required", "total-amount": "Total Amount", "search-customer-placeholder": "Search by contact name, business name, phone number, and Email...", "select-contact": "Select Contact", "tax-exempted": "Tax Exempted", "sync-to-quickbooks": "Sync to Quickbooks", "subtotal": "Subtotal", "note": "Note", "description-of-material": "Description of the material", "due-date": "Due Date", "expire-on": "Expire on", "expiration-date-tooltip": "Add an expiration date if you want the invoice to expire.", "customer-details": "Customer details", "address-type-title": "Please choose one of the following options (required<requiredStyle>*</requiredStyle>):", "deliver-to-label": "Deliver to", "delivery-address-label": "Delivery Address", "pick-up-label": "Pick up", "service-label": "Service", "collect-payment": "Collect Payment", "export-data": "Export Data", "business": "Business", "pay-now-tooltip": "The invoice was moved to the <bold>Receivables tab</bold>. <br>To view details please locate this invoice in Receivables tab. ", "invoice-status": "Invoice Status", "customer-fee-and-interest": "Customer Fee & Interest", "amount-type": "Amount Type", "paid-amount": "<PERSON><PERSON>", "processing-amount": "Processing Amount", "remaining-amount": "Remaining Amount", "invoice-amount": "Invoice Amount", "contact": "Contact", "payers-info-recipients": "Recipient(s)", "receivable-status": "Status", "authorization-deadline": "Authorization Expiration Date", "amount": "Amount", "all-statuses": "All statuses", "due-on": "Due on {{date}}", "expires-on": "Expires on {{date}}", "invalid-date-error": "This date is no longer valid.", "enter-valid-date-prompt": "Please enter valid date", "post-transaction": {"button": "Post Transaction", "title": "A new Invoice will be created based on Quote Details", "factoring-title": "A new transaction will be created based on this request", "description": "The transaction will be posted, and funds will be withdraw from customer’s line of credit account, and they will be notified immediately. Please ensure the details are correct before processing.", "factoring-description": "A transaction will be created. The advanced payment will be disbursed, and customer will receive an invoice with a due date, and a generic invoice attachment. Please ensure all details are correct before proceeding.", "go-back": "Go Back", "continue": "Continue", "success": "Transaction has been posted", "failure": "Failed to post transaction"}, "edit-authorization": {"header": "Back to Details", "edit-amount-hint": "To edit, a new quote will need to be sent", "expiration-date-title": "Please enter the new expiration date in the field below", "expiration-date-description": "Please note that new date should be no more than 120 days from the original approval date", "original-approval-date": "Original Approval Date", "new-expiration-date": "New Expiration Date", "max-expiration-date": "Max. Expiration Date", "edit-expiration-date": "Edit Expiration Date", "save-expiration-date": "Save New Date", "expiration-date-validation-message": "The new expiration date should be set between {{minExpirationDate}} and {{maxExpirationDate}}", "expiration-date-update-success": "Expiration date has been updated", "expiration-date-update-failure": "Failed to update the expiration date"}, "ar-advance": {"status-column": "AR Advance Status", "summary-limit": "AR Advance Limit", "summary-balance": "AR Advance Balance", "summary-available": "AR Advance Available", "summary-total-authorized": "Total Quotes Authorized", "in-house-trade-credit-term": "In-House Trade Credit Term"}}, "BatchInvoice": {"Upload": "Upload", "FillingInformation": "Filling in Information", "FileSizeBig": "File size too big", "UploadInvoice": "Upload {{type}}", "FileSizeLimit10MB": "Only PDF (max of 10MB each)", "FilesWithError": "Files uploaded with error", "EditDetails": "Edit Details", "SaveDraft": "Save draft", "SendNContinue": "Send & Continue", "EditInvoice": "Edit {{type}}", "InvoiceNumber": "{{type}} number", "DateOFInvoice": "Date of {{type}}"}, "InvoiceNotes": {"notes": "Notes", "add_new_note": "Add New Note", "write_something": "Write something here...", "save": "Save Note", "here": "Notes will be here", "delete": "Delete", "are_u_sure": "Are you sure you want to", "delete_note": "delete the note?", "no_go_back": "No, go back", "yes_delete": "Yes, delete", "PreviewInvoice": "Preview Invoice", "UploadingInvoice": "Uploading Invoice", "FillingInformation": "Filling in Information"}, "InvoiceListing": {"filters": "Filters", "no-invoices": "No invoices yet", "no-invoices-message": "You do not have any invoice yet. Tap on the button below to add an invoice to send to your supplier."}, "InvoiceReceipt": {"payment_details": "Payment Details", "add_to_project": "Add to Project", "payment_method": "Payment method", "paid_on": "Paid on", "project": "Project", "AmountPaid": "Amount <PERSON>", "InvoiceAmount": "Invoice Amount", "LateInterestCharge": "Late Interest Charge", "Discount": "Discount", "status": "Status", "collected-by-supplier": "Collected by <PERSON><PERSON><PERSON>", "payment-collected-by-supplier": "Your Invoice has been collected by supplier!", "login": "Log In", "signup": "Sign Up"}, "PaymentHistoryInvoice": {"totalAmountDue": "Total Amount Due", "details": "Details", "viewMoreDetails": "View More Details", "paymentHistory": "Payment History", "paymentsSchedule": "Payments Schedule", "paymentScheduleSummary": "Payment Schedule Summary", "noPaymentSchedule": "No payment schedule available", "payment": "Payment", "partialPayment": "Partial Payment", "externalPayment": "External Payment", "manualPaymentByBluetape": "Manual Payment by Bluetape", "manualPayment": "Manual Payment", "choosePaymentMethod": "Choose payment method", "paymentSchedule": {"paid": "Payment Received", "processing": "Payment Processing", "due": "Payment Due", "rescheduled": "Payment Rescheduled", "principalPayment": "Principal", "customerFee": "Customer <PERSON>e"}, "invoiceDetailsModal": {"title": "Invoice Details", "supplierPhoneNumber": "Supplier Phone Number", "invoiceAmount": "Invoice Amount", "paidAmount": "<PERSON><PERSON>", "processingAmount": "Processing Amount", "customerFee": "Customer <PERSON>e", "lateFee": "Late Interest Charge", "processingFee": "Processing Fee", "totalAmountDue": "Total Amount Due", "originalTotalAmount": "Original Total Amount", "remainingAmount": "Remaining Amount"}, "makePayment": "Make a payment", "autoPayIHCAlert": {"title": "AutoPay:", "on": "ON", "button": "Manage AutoPay"}}, "BTModal": {"alertTitle": "What is BlueTape", "title": "BlueTape is a payment and financing tool for the construction industry, offering a “Buy Now, Pay Later” solution for building materials.\n\n BlueTape allows you to directly finance building materials with payment terms of 30, 60, or 90 days", "subtitle": "Requesting credit does NOT affect credit score", "request_credit": "Request Credit"}, "CreditHomePage": {"header-title": "BlueTape", "app-update": "Application Update", "whats-next": "Whats next", "ways-to-use-credit": "Ways to use your credit", "benefits-of-applying-for-credit": "Benefits of applying for credit", "rejected-message": "At this time we are unable to approve your application. We will follow up with additional information on the reason for the decision. You may contact <NAME_EMAIL>", "processing-title": "Why is my application processing?", "processing-message": "Your application is processing due to the underwriting process of the applicable credit policy. We are reviewing to make sure all the data is correct. We will update your status soon, check back later."}, "LoanListing": {"header": "Trade Credit Account Overview", "down-payment-title": "DOWN PAYMENT %", "down-payment-hint": "A down payment must be paid for each invoice.", "download-wire-instructions": "Download Wire Instructions", "security-deposit-title": "SECURITY DEPOSIT AMOUNT", "security-deposit-hint": "A one-time security deposit is required. This must be paid in full prior to invoice approval.", "security-deposit-paid": "Paid", "active-draws": "Active Draws", "previous-draws": "Previous Draws", "total-paid": "Total Paid", "loan-past-due": "Your loan payment is past due.", "pay-now": "Pay now", "pay-within-grace-days": "Pay within {{graceDays}} business days to avoid late fee.", "pay-within-1-day": "Pay within 1 business day to avoid late fee.", "trade-credit-limit": "Trade Credit Limit", "current-balance": "Current Balance", "current-balance-tooltip": "Payments in progress will not reflect the balance.", "available-credit": "Available Credit", "available-credit-tooltip": "Payments in progress will not reflect the balance.", "past-due-amount": "Past Due Amount", "good-standing": "Good Standing", "past-due": "Past Due", "processing-amount": "Processing Amount", "amount-on-hold": "Amount on Hold", "held-amounts-title": "Quote amount held from available credit", "quote-expires-on": "Expires on {{deadline}}", "quote-limit-overrun": "10% Hold of Trade Credit Limit", "total-hold-amount": "Total Hold Amount"}, "CreditApplication": {"submit-success-messages": {"with-invoice-previously-approved": {"title": "Draw request is processing", "message": "Thank you for submitting your draw request. Your request is currently under review by our underwriting team.\n\nIf additional information is required, we will contact you."}}, "ChoosePaymentPlan": "Choose Payment Plan", "LoadingPlans": "Loading plans..", "Continue": "Continue", "PleaseEnterValue": "Please enter value"}, "TimeoutHandler": {"continue-session": "Continue Session", "session-expired": "Your session has expired. Continue to login.", "session-about-to-expire": "Your session is about to expire!", "you-will-be-logged": "You will be logged out in 2 minutes.", "login": "<PERSON><PERSON>"}, "LoanSchedule": {"total-paid": "<simple>Total Paid ({{transactions}})</simple>", "remaining": "<simple>Remaining ({{transactions}})</simple>", "payments": "Payment(s)", "includes-late-fees": "Total remaining balance includes late fees.", "includes-penalty": "Total remaining balance includes penalty interest.", "includes-late-fee-and-penalty": "Total remaining balance includes late fees and or penalty interest.", "processing-caption": "*{{amount}} is processing as of {{partialAmountCoveredOn}}", "paid-off-caption": "*{{amount}} is paid off as of {{partialAmountCoveredOn}}"}, "ViewLoan": {"loan-schedule": "Loan <PERSON>", "details": "Details", "make-one-time-pay": "Make One time payment", "amount": "Amount", "pay": "Pay", "to": "To", "payment-method": "Payment method", "late-fee": "Late Fee", "Paid": "Paid", "DueNext": "Due Next", "DueNow": "Due Now", "Processing": "Processing", "failed": "Failed", "PastDue": "Past Due", "processing-info": "Payment is processing may take up to 5 business days to reflect payment plan"}, "MakeLoanPayment": {"title": "Make a payment", "subtitle": "for {{company}} Invoice", "other-amount": "Other Amount", "pay": "Pay", "add-new-payment-method": "Add new payment method", "amount": "Amount", "pay-title": "Pay", "payment-method": "Payment method", "total-amount": "Total Amount", "to": "To", "payment-to": "BlueTape Inc", "servicing-fee": "Servicing fee", "success-title": "Payment is processing!", "success-message": "Thank you for paying your loan. We will update your loan details.", "including-penalty-interest": "<simple>Including Penalty Interest of <bold>{{amount}}</bold></simple>"}, "AddCard": {"go-back": "Go back", "add-new-card": "Add a new card", "link-a-card": "Link a Card", "type-customer-billing-address": "Type Customer Billing Address", "invalid-card": "Invalid card", "invalid-card-msg": "Card verification failed. Please check your card information or try a different card.", "invalid-card-for-pulls-msg": "This card cannot be used for payments. Please use a different card.", "invalid-card-number": "Invalid card", "invalid-card-number-msg": "The card number you entered is invalid. Please check your information and try again.", "invalid-address": "Invalid Address", "invalid-address-msg": "Billing address does not match. Please enter correct address and try again.", "missing-address-line1": "Invalid Address", "missing-address-line1-msg": "Street address (Line 1) is required. Please enter your street address.", "invalid-zipcode": "Invalid Address", "invalid-zipcode-msg": "The ZIP code you entered is invalid. Please check your information and try again.", "address-verification-error": "Address Verification Error", "address-verification-error-msg": "Unable to verify your billing address. Please check your information or try again later.", "invalid-security-code": "Invalid security code", "invalid-security-code-msg": "The security code you entered is invalid", "network-error": "Network Error", "network-error-msg": "There was network error while adding this card. Please try again later.", "card-declined": "Card Declined", "card-declined-msg": "Your card was declined by your bank. Please contact your bank or try another card.", "expired-card": "Expired Card", "expired-card-msg": "This card has expired. Please use a card with a valid expiration date.", "stolen-card-msg": "This card has been reported lost or stolen. Please contact your bank for assistance.", "insufficient-funds": "Insufficient Funds", "insufficient-funds-msg": "There are insufficient funds available on this card. Please use another payment method.", "limit-exceeded": "Limit Exceeded", "limit-exceeded-msg": "This transaction exceeds the card's limit. Please contact your bank or try another payment method.", "transaction-not-permitted": "Transaction Not Permitted", "transaction-not-permitted-msg": "This type of transaction is not permitted with this card. Please try another payment method.", "duplicate-transaction": "Duplicate Transaction", "duplicate-transaction-msg": "This appears to be a duplicate transaction. Please try again later.", "security-violation-msg": "A security issue was detected with this card. Please contact your bank for assistance.", "add-card": "Add Card", "account-type": "Account Type", "personal-account": "Personal Account", "business-account": "Business Account", "first-name": "First Name", "last-name": "Last Name", "name-on-card": "Name on the card", "next": "Next", "submit": "Submit", "street-address": "Street Address", "apt-ste-bldg": "Apt., ste., bldg.", "city": "City", "state": "State", "zip": "Zip", "card-details-are-tokenized": "Card details are tokenized and properly secured. The details entered is protected and all transactions are safe and secure.", "success-title": "Card Added", "success-msg": "You have successfully added your customer’s card to their BlueTape account.", "done": "Done"}, "Statement": {"Title": "Statement", "Caption": "To generate your report, please select a month and then click the download button.", "MonthPickerLabel": "Select month", "YearPickerLabel": "Select year", "BtnDownloadReport": "Download report", "InfoTitle": "Looking for a previous month? We’re here to help!", "InfoText": "Upon request, we can generate a report for the current month for you right now. We can also help you find an old report if you didn't find it in the list. Please email us at ", "InfoTextEmail": "<EMAIL>"}, "PaymentPlanCard": {"dates-finalized-when-loan-disbursed": "Dates will be finalized when loan is disbursed", "down-payment-card-fee-hint": "*Card payments for down payments may be subject of a processing fee", "one-time-due-on-day": "One-time payment \ndue on day {{days}}", "term-on-pay-invoice-modal": "{{days}} days", "radio-days": "{{days}} days", "fee": "Fee: {{fee}}% Per Month", "sample-schedule": "Sample schedule <Icon/>", "down-payment-amount-line": "Down Payment due at approval* - <styled>{{paymentAmount}}<styled>", "down-payment-amount-with-fee-line": "Down payment due at approval including fee* - <styled>{{paymentAmount}}<styled>", "one-time-payment-amount-line": "One-time payment due on Day {{days}} - <styled>{{paymentAmount}}<styled>", "single-payment-amount-line": "1 payment due on Day {{days}} - <styled>{{paymentAmount}}<styled>", "multiple-payments-amount-line": "{{term}} weekly payments of - <styled>{{paymentAmount}}<styled>", "down-payment-required-message": "<styled>*A Down Payment of {{percentage}}% is required for this invoice.</styled> You can select payment method on next step."}, "DownPaymentModal": {"title": "A down payment is required in order to process the disbursement to your vendor. How would you like to pay?", "downPaymentPercentage": "Down Payment %", "downPaymentAmount": "Down Payment Amount", "selectPaymentMethod": "Select a payment method", "paymentMethodTooltipTitle": "What’s the difference between these options?\n", "paymentMethodTooltip": "Card payments are processed automatically after invoice approval.\n\nWire payments must be initiated by you and received within 5 days to avoid invoice cancellation.", "payWithCard": "I will pay with a card", "payWithWire": "I will send a wire transfer", "cardsDropdownLabel": "Select from your previously added cards or add a new card.", "addNewCard": "Add New Card", "wireHintTitle": "How to make a payment with wire transfer?", "wireHint": "Click <downloadLink>here</downloadLink> to download the instructions. Otherwise the instructions are in your Account Overview tab. Reminder that the down payment is required within 5 days of the invoice approval, otherwise late fees will be applied. Need help? Contact us at <email><EMAIL>.</email>", "continue": "Continue"}, "AccountingSystem": {"title": "Accounting systems", "connected": "Connected", "manual-setup-sidebar-header": "Set up your manual invoice system"}, "AccountManagement": {"Deactivated": "Deactivated", "ResendInvite": "Resend invite", "AllUsers": "All users", "AddUser": "Add user", "Cancel": "Cancel", "UserWillNoLonger": "User will no longer have access to your Material Get Paid Console.", "Status": "Status", "Email": "Email", "Phone": "Cell phone number", "FirstName": "First name", "LastName": "Last name", "User": "User", "Admin": "Admin", "Permission": "Permission", "WithThisPermission": "With this permission user can add customers and add & send invoices.", "PaymentEmailNotification": "Payment Notification E-mail"}, "InvoiceViewSidebar": {"subtotal-label": "Subtotal", "total-label": "Total Amount", "paid-label": "<PERSON><PERSON>", "processing-label": "Processing Payment", "remaining-label": "Remaining Amount", "tax-label": "Tax Amount", "customer-fees": "Customer Fees", "late-fee": "Interest Fee", "attachment": "Attachment", "due-date-label": "Due date", "expires-on-label": "Expires on", "deliver-label": "Deliver to", "delivery-type-label": "Delivery Type", "business-name-label": "Business Name", "customer-number-label": "Customer number", "contact-name-label": "Contact Name", "recipients-label": "Recipient(s)", "phone-label": "Cell phone number", "dismiss-reasons": "Dismiss Reasons", "billing-contact": "Billing Contact", "subtotal-invalid": "Subtotal is Invalid.", "invalid-expiration": "Invalid Expiration Date.", "due-date-invalid": "Due Date is Invalid.", "collect-payment": "Collect Payment", "collected-by-label": "Collected By", "supplier": "Supplier"}, "integrations": {"buttons": {"redirect-back": "Go back to origin site", "login-to-pay": "Log in & Pay", "sign-up-and-pay": "Sign up & Pay", "sign-up": "Sign up", "login-and-pay": "Log in and Pay Now", "pay-by-card": "Pay by Card", "continue": "Continue", "login-to-another-account": "Log in to another account", "pay-invoice": "Pay Invoice"}, "invoice": {"status": "Status", "attachment": "Attachment", "invoice-num": "Invoice", "invoice-amount": "Invoice Amount", "service-fee": "Service Fee", "total": "Total"}, "modals": {"add-new-card": {"success-common": "Your card has been successfully linked to your BlueTape account.", "success-guest": "Card has been successfully linked."}}, "pay-as-guest": {"contact-section": {"contact-information": "Contact Information", "contact-information-details": "We will send a receipt to this e-mail address.", "first-name": "First Name", "last-name": "Last Name", "email-address": "Email address", "business-name": "Business Name", "not-a-business": "I’m not a Business", "phone-number": "Phone Number", "phone-invalid": "Phone number is invalid.", "phone-invalid-length": "Phone Number must be 10 digits.", "email-invalid": "Please enter a valid email.", "empty-field": "This field is required."}, "payment-method-section": {"payment-method": "Payment Method", "billing-address": "Billing Address", "add-card-payment": "Add Card Payment", "add-card-hint": "*In order to add a Card Payment, please fill in Phone Number in the form above.", "pay-with-ach": "Pay with ACH", "data-are-secured": "All data are secured by BlueTape", "data-are-secured-details": "Card details are tokenized and securely protected. Transactions are safe, and card information will not be stored."}, "invoice-processed": {"title": "Invoice is processing", "info": "Payment processing typically takes up to 2 business days.", "mobile-title": "Invoice is being processed", "mobile-info": "Payment processing typically takes up to 2 business days. You will receive an email notification once the invoice has been successfully paid."}, "payment-details": {"title": "Payment Details", "supplier": "Supplier", "invoice-num": "Invoice #", "invoice-due-date": "Invoice Due Date", "payment-method": "Payment Method", "total-paid": "Total Paid", "card-ending-in": "Card ending in {{last4}}"}, "enter-password": {"title": "Create your password", "faster-checkout": "For a faster checkout process next time, please enter a password.", "info": "By creating an account, you can save your payment methods, view previous invoices, and do so much more.", "enter-safe": "Password", "password-info": "*You can use this password to log in to your BlueTape account next time.", "password-hint": "<chrs>8 characters,</chrs> <uprcs>1 uppercase,</uprcs> <nmbr>1 number</nmbr>", "password-hint-correct": "8 characters, 1 uppercase, 1 number", "create-account": "Sign Up"}, "payment-failed": "We were unable to process the payment. Please try again", "payment-error-again": "If the error occurs again,", "contact-support": " please contact BlueTape at <email><EMAIL></email>", "try-again": "Okay, Try Again"}, "not-enough-permissions-text": "You don’t have access to this invoice.", "not-enough-permissions-info": "Please, try to log in into another account.", "invoice-not-found": "Invoice not found.", "ask-sync-invoice": "Please, ask administrator to sync invoice with BlueTape again.", "pay-terms-and-conditions": "By continuing, you agree to our <CA>Customer Agreement</CA>, <terms>Terms & Conditions</terms> and <privacy>Privacy Policy</privacy>", "redirect-back": "Go back to origin site", "login-to-pay": "Log in to make a payment", "login-to-another-account": "Log in to another account", "invoice-status": "Status"}, "zohobooks": {"connect": "Connect to", "disconnect": "Disconnect", "connection-failed": "Zoho Books connection failed", "disconnection-failed": "Zoho Books disconnection failed", "show-settings-failed": "Failed to show Zoho Books settings", "update-settings-failed": "Failed to update Zoho Books settings", "show-settings": "Show Settings", "title": "Zoho Books Online", "service-description": "Sync customers and send out invoices from BlueTape via Zoho Books Online", "integration-exists": "This account is already integrated with another BlueTape account. Please disconnect the previous BlueTape account which is connected to Zoho Books, and retry connecting Zoho Books with this account.", "importDialog": {"header": "Connect Zoho Books to your BlueTape account", "import-customers": {"label": "All customers will be synced automatically", "note": "All existing and new customers will be synced automatically."}, "import-invoices": {"label": "All new invoices will be sent to customers automatically", "note": "You can turn this setting off for each customer in the Customers tab."}, "continue": "Continue"}, "settings": {"title": "Zoho Books Settings", "bluetape-secret-key": "BlueTape API Key", "bluetape-account-id": "BlueTape API Account ID", "bluetape-api-url": "BlueTape API URL", "bluetape-application-url": "BlueTape Application URL", "ar-advance-support": "AR Advance Support", "quote-type": {"label": "Quote Type", "none": "None", "sales-order": "Sales Order", "estimate": "Estimate"}, "sales-order-strategy": {"label": "Sales Order Strategy", "none": "None", "as-quote": "As Quote", "as-invoice": "As Invoice"}, "disable-pay-now-flow": "Disable Pay Now Flow", "disable-quote-authorization-flow": "Disable Quote Authorization Flow", "bt-sync-message": "BlueTape Sync Message", "credit-info": "Credit Info", "customer-type": "Customer Type", "synced-successfully-to-bt": "Synced successfully to BlueTape", "cancel": "Cancel", "copy": "Copy value", "disabled": "Disabled", "enabled": "Enabled", "save": "Save Changes"}}, "netsuite": {"connect": "Connect to", "disconnect": "Disconnect", "connection-failed": "NetSuite connection failed", "disconnection-failed": "NetSuite disconnection failed", "show-settings-failed": "Failed to show NetSuite settings", "update-settings-failed": "Failed to update NetSuite settings", "show-settings": "Show Settings", "title": "NetSuite", "service-description": "Sync customers and send out invoices from BlueTape via NetSuite", "integration-exists": "This account is already integrated with another BlueTape account. Please disconnect the previous BlueTape account which is connected to NetSuite, and retry connecting NetSuite with this account.", "settings": {"title": "NetSuite Settings", "consumer-key": "Consumer Key", "consumer-secret": "Consumer Secret", "token-id": "Token ID", "token-secret": "Token Secret", "company-url": "Company URL", "bluetape-secret-key": "BlueTape Secret Key", "bluetape-account-id": "BlueTape Account ID", "ar-advance-support": "AR Advance Support", "ar-advance-support-tooltip": "Determines the default customer type: In-House Credit if enabled, Trade Credit if disabled.", "disable-pay-now-flow": "Disable Pay Now Flow", "disable-pay-now-flow-tooltip": "Place true if you want to work only with Quote Authorization Flow.", "disable-quote-authorization-flow": "Disable Quote Authorization Flow", "disable-quote-authorization-flow-tooltip": "Place true if you want to work only with Pay Now flow (ACH, Card, Trade Credit).", "cancel": "Cancel", "save": "Save Changes", "connect": "Connect to NetSuite", "errors": {"invalid-url": "Please enter a valid URL."}}}, "generic": {"connect": "Connect to", "disconnect": "Disconnect", "connection-failed": "E-Commerce connection failed", "disconnection-failed": "E-Commerce disconnection failed", "show-settings-failed": "Failed to show E-Commerce settings", "update-settings-failed": "Failed to update E-Commerce settings", "show-settings": "Show Settings", "title": "E-Commerce", "service-description": "Sync customers and send out invoices from BlueTape via E-Commerce", "integration-exists": "This account is already integrated with another BlueTape account. Please disconnect the previous BlueTape account which is connected to E-Commerce, and retry connecting E-Commerce with this account.", "paid-invoice-redirection-label": "You will be redirected in {{redirectDelay}} seconds", "paid-invoice-redirecting-label": "Redirecting...", "settings": {"title": "E-Commerce Settings", "bluetape-secret-key": "BlueTape Secret Key", "bluetape-account-id": "BlueTape Account ID", "bluetape-api-url": "BlueTape API URL", "bluetape-application-url": "BlueTape Application URL", "client-secret-key": "Client Secret Key", "quote-response-url": "Quote Response URL", "invoice-response-url": "Invoice Response URL", "invoice-status-update-url": "Invoice Status Update URL", "cancel": "Cancel", "save": "Save Changes", "connect": "Connect to E-Commerce", "errors": {"invalid-url": "Please enter a valid URL."}}}, "quickbooks": {"connect": "Connect to", "login": "Log in", "logout": "Log out", "invoice-is-paid": "This invoice has been successfully paid.", "payment-is-processing": "Payment is processing", "no-further-actions": "No further actions needed.", "login-to-pay": "Log in to make a payment", "disconnect": "Disconnect", "title": "QuickBooks Online", "service-description": "Sync customers and send out invoices from BlueTape via Quickbooks Online", "integration-exists": "This account is already integrated with another BlueTape account. Please disconnect the previous BlueTape account which is connected to Quickbooks, and retry connecting Quickbooks with this account.", "connection-failed": "QuickBooks connection failed", "disconnection-failed": "QuickBooks disconnection failed", "update-settings-failed": "Failed to update QuickBooks settings", "show-settings-failed": "Failed to show QuickBooks settings", "show-settings": "Show Settings", "import-started": "QuickBooks import started", "import-failed": "QuickBooks import failed", "go-back": "Go Back", "go-to-app-web": "Go to Bluetape App", "go-to-app-mobile": "Go to App", "start-import": "Start Sync", "stop-import": "Stop Sync", "start-import-title": "Are you sure you want to start syncing invoices for {{customer}}?", "stop-import-title": "Are you sure you want to stop syncing invoices for {{customer}}?", "start-import-description": "From now on, new invoices will be sent to customers via BlueTape. You can change this setting at any time.", "stop-import-description": "From now on, new invoices will no longer be sent to customers via BlueTape. Previously synced invoices will remain unchanged. You can resume the import at any time.", "import-completed-title": "QuickBooks sync is completed!", "import-completed-description": "All customer details have been successfully synced. You can now send invoices to customers from QuickBooks via BlueTape. You can also disable sync for customer(s) manually at any time from the Customers tab.", "go-to-customers-tab": "Go to Customers tab", "importDialog": {"header": "Connect QuickBooks to your BlueTape account", "import-customers": {"label": "All customers will be synced automatically", "note": "All existing and new customers will be synced automatically."}, "import-invoices": {"label": "All new invoices will be sent to customers automatically", "note": "You can turn this setting off for each customer in the Customers tab."}, "continue": "Continue"}, "settings": {"title": "<PERSON><PERSON><PERSON><PERSON> Settings", "ar-advance-support": "AR Advance Support", "ar-advance-support-tooltip": "Determines the default customer type: In-House Credit if enabled, Trade Credit if disabled. Available when AR Advance application is approved.", "disable-pay-now-flow": "Disable Pay Now Flow", "disable-pay-now-flow-tooltip": "Place True if you want to work only with Quote Authorization Flow. Invoices will not be synced to BT if there is no Authorized Quote.", "disable-quote-authorization-flow": "Disable Quote Authorization Flow", "disable-quote-authorization-flow-tooltip": "Place True if you don’t want to synchronize Estimates from QuickBooks to BlueTape as Quotes.", "sync-back-errors-in-file": "Sync Back Errors in File", "sync-back-errors-in-file-tooltip": "Allows to sync error details from QuickBooks to BlueTape into dedicated error file attached to each invoice, estimate or customer that you are syncing.", "sync-back-customer-credit-info-in-file": "Sync Back Customer Credit Info in File", "sync-back-customer-credit-info-in-file-tooltip": "Allows to sync customer credit info from QuickBooks to BlueTape on each create or update of customer in QuickBooks, and attaches it as a dedicated file to the customer’s details.", "cancel": "Cancel", "save": "Save Changes"}}, "DataAreSecuredByBluetape": "All data are secured by <styled>BlueTape</styled>", "PaymentMethodsListing": {"business-saving": "Business Saving ... {{accNum}}", "business-checking": "Business Checking ... {{accNum}}", "debit-card": "Debit Card ... {{accNum}}", "credit-card": "Credit Card ... {{accNum}}"}, "CustomerAccountAddPayment": {"add-payment-method": "Add Payment Method", "payment-method": "Payment method", "add-customer-card": "Add Customer Card", "show-all": "Show All", "add-new-payment": "Add New Payment Method", "add-a-payment-for-company": "Add A Payment Method for\n{{- company}}", "add-method-without-company-name": "Add A Payment Method", "list-methods-title": "{{- company}}'s Cards", "list-methods-title-without-company-name": "Payment methods", "card-linked-successfully": "Card has been successfully linked.", "remove-method-confirmation": "Are you sure you want to remove {{method}} payment method?", "remove-method-button": "Remove", "remove-method-success": "Payment method removed successfully"}, "supplierPayInvoice": {"pay-invoice": "Pay Invoice", "agree-and-pay": "Agree & Pay", "invoice-amount": "Invoice or SO amount", "total-amount": "Total Amount", "amount": "Amount:", "loan-fee": "<PERSON>an <PERSON>", "select-autodebit-account": "Select account to autodebit", "payment-method": "Payment Method", "payment-terms-conditions-text": "By clicking \"Agree & Pay\", you agree to the Borrower Terms and Conditions <1>found here.</1>", "payment_will_proceed": "Payment will proceed", "To": "To", "From": "From", "fee": "Processing Fee", "processing-title": "Payment is processing", "processing-message": "<simple>A payment of <styled>{{amount}}</styled> is processing. A notification has been sent to your customer.</simple>", "try-again-title": "Please Try Again", "try-again-msg": "System failure", "go-back-try-again": "Go back and try again"}, "paymentCollectionSuccessAlert": {"title": "Invoice #{{invoiceNumber}} has been paid", "message": "<simple>Invoice <styled>#{{invoiceNumber}}</styled> has been paid by {{- companyName}}, a notification has been sent to your customer.</simple>", "done": "Done"}, "change-payment-method": "Change payment method", "choose-payment-method": "Choose payment method", "add-new-payment-method": "Add new payment method", "pastduePayments": {"title": "Past Due Payments", "view-all": "View All", "pay-now": "Pay Now", "was-due-on": "Was due on\n<bold>{{date}}</bold>"}, "BtSecureInput": {"Show": "Show", "Hide": "<PERSON>de"}, "BtEncryptedInput": {"EncryptedAndSecured": "Encrypted & {{lineBreak}}Secured"}, "BtEncryptedAndSecuredBadge": {"Text": "Encrypted & Secured"}, "PastdueAlert": {"title": "Payment is Past Due", "manyPastdueLoans": "<simple>Your payment(s) for <h2>several</h2> invoices was not successfully paid. To avoid late fee charges click below to make a payments now.</simple>", "manyPastdueWithLatefee": "<simple>Your payment(s) for <h2>several invoices</h2> was not successfully paid. You have been charged a <highlight>${{lateFee}} late fee</highlight>.</simple>", "go-to-credit-page": "Go to Credit page", "singlePastDueLoanWithLateFee": "<simple>Your payment(s) for <h2>{{- name}}'s</h2> invoice was not successfully paid. You have been charged a <highlight>${{lateFee}} late fee</highlight>.</simple>", "pay-loan-now": "Pay Your Loan Now", "singlePastDueLoan": "<simple>Your payment(s) for <h2>{{- name}}’s</h2> invoice was not successfully paid. To avoid late fee charges click below to make a payment now.</simple>", "make-payment": "Make Payment"}, "ProjectForm": {"main-heading-when-no-projects-exist": "Please add a new project\nto use BlueTape Trade Credit", "main-heading-when-projects-exist": "Please fill out the following details.", "main-subheading": "The following information is required before you request trade credit for this invoice.", "mobile-title": "Create New Project", "address-title": "What is the Job Address?", "address-subtitle": "This is where you are furnishing labor or material.", "project-info-title": "Please enter project information", "project-type-title": "Please choose project type", "property-owner-title": "Please enter property owner information", "enter-home-owner-information": "Please enter home owner information", "street-address": "Street Address", "state": "State", "zip": "ZIP code", "city": "City", "house-num": "House #", "unit-num": "Unit # (if applicable)", "subdivision": "Subdivision / Project Name", "lot-num": "Lot #", "block-num": "Block #", "invalid-zip": "Invalid ZIP", "please-enter-manually": "Please enter manually", "commercial": "Commercial", "residential": "Residential", "spec": "Spec", "new-building": "Property Owner, New Building  ", "renovation": "Property Owner, Renovation", "contract-value-label": "Contract Value", "first-day-on-job": "First day on the job", "last-day-on-job": "Expected last day on the job", "date-placeholder": "mm/dd/yyyy", "invalid-first-day": "Invalid First Day", "invalid-last-day": "Invalid Last Day", "role-label": "Role", "role-placeholder": "Please select role", "other-label": "Other", "business-name-label": "Business Name", "first-name-label": "First Name", "last-name-label": "Last Name", "business-phone": "Business Phone Number", "phone-invalid": "Please enter a valid US phone number", "email-invalid": "Please enter a valid email", "email-label": "Email", "address-label": "Business Address", "back-button-label": "Back", "next-button-label": "Next", "individual-owner": "Individual Owner", "business": "Business", "add-another-owner": "Add another business or owner", "another-owner": "Another owner", "phone-number": "Phone Number", "home-address": "Home Address", "update": "Update Project", "create": "Create Project", "delete": "Delete", "description-label": "Description", "jobid-label": "Job ID", "what-is-jobid": "What is the Job ID", "mobile": {"header": "Please add a new project to use BlueTape Trade Credit", "subHeader": "The address is required for trade credit.", "subTitle": "This is where you are furnishing labor or material."}}, "UploadFile": {"upload-file": "Upload File", "delete": "Delete", "select-file": "Select a file or drag and drop here", "accepted-files": "JPG, PNG or PDF, file size no more than 10MB", "on-upload-error": "We couldn’t upload the file. Please try again.", "on-size-error": "File size exceeded. We couldn’t upload the file."}, "SelectProject": {"title": "Add or select a project for this invoice to continue your trade credit request.", "subtitle": "To request trade credit for this invoice, project details are required. Select or add project details pertaining to this purchase.", "select-button-label": "Select", "back-to-invoice-details": "Back to Invoice Details", "suggested": "Suggested Project", "title-mobile": "Select a project for this invoice", "subtitle-mobile": "To request trade credit for this invoice, project details are required.", "create-new-project": "Create new project", "close-button-label": "Close", "switch-to-desktop": "Unfortunately, in order to use trade credit, you must create a project. Please switch to the desktop version to continue."}, "GroupedInvoicesProjectWarning": {"title": "To use BlueTape Trade Credit, please make sure all invoices are for the same project", "subtitle": "If the invoices are for different projects, please select only invoices from the same project and then continue."}, "SelectPurchaseType": {"title": "What is this project for?", "inventory": "Inventory", "specific-project": "Specific Project"}, "DocumentApproval": {"submit": "Agree & Submit", "setup-account": "Setup your account", "agreement-submitted-title": "Your agreement has been submitted!", "agreement-submitted-message": "Our team will review the application and contact the person who submitted the application.", "approval-expired-title": "Link is expired!", "approval-expired-message": "We’re sorry, but the credit request agreement link you clicked has expired. To receive a new link, please contact customer support at <email><EMAIL></email>.", "approval-error-title": "Something went wrong", "approval-error-message": "We’re sorry, we are unable to process your request at this time. Please try again later or contact customer support <email><EMAIL></email>.", "credit": {"header": "Credit Request", "title": "You have been listed as a co-owner of {{company}}!", "individualOwnerMessage": "In order to proceed with the Credit Request, we need to obtain your agreement to the <tradeCredit>BlueTape Commercial Installment Sale Account Materials and Services Agreement</tradeCredit>, <customer>Customer Agreement for Purchasers</customer>, <pga>Commercial Sale Personal and Corporate Guaranty Agreement</pga>, <terms>Terms & Conditions</terms>, and <privacy>Privacy Policy</privacy>.", "entityOwnerMessage": "In order to proceed with the Credit Request, we need to obtain your agreement to the <tradeCredit>BlueTape Commercial Installment Sale Account Materials and Services Agreement</tradeCredit>, <customer>Customer Agreement for Purchasers</customer>, <terms>Terms & Conditions</terms>, and <privacy>Privacy Policy</privacy>."}, "get-paid": {"header": "Get Paid Application", "title1": "You have been listed as", "title2": "a co-owner of {{company}}!", "subtitle": "Please, read the agreement and click on <br /> Agree & Submit button in the end of the page.", "agreementsList": "In order to proceed with the submission of this application, we need to obtain your agreement to all listed agreements: <invoicePurchase>Invoice Purchase & Security Agreement</invoicePurchase>, <seller>BlueTape Customer Agreement for Sellers</seller>, <terms>Terms & Conditions</terms>, and <privacy>Privacy Policy</privacy>."}}, "Supplier": {"agreement-updated-alert": {"title": "We've updated our Terms of Use", "message": "Our Terms of Use have been updated, effective immediately. Your continued use of our services constitutes your acceptance of these revised terms. Click here to review the updates in the <agreement>Seller Agreement</agreement>.", "agree": "Agree & Continue"}}, "ConnectBankWithPlaid": {"header": "Connect Business Bank Accounts", "reconnect-all-banks": "To use BlueTape Trade Credit, you must reconnect all your previously connected bank account with our new third-party vendor, Plaid. Previous bank connections will no longer function.", "connect-accounts": "Connect the bank account(s) associated with your business.", "to-review-business-cashflow": "We need to review your business cash flow and transaction history for underwriting purposes.", "connected-accounts-shown-below": "Connected accounts are shown below.", "banks-connected": "Congratulations, your business bank account(s) are connected.", "selected-as-primary": "*Selected as Primary account", "or-connect-new-bank": "Or connect a new bank:", "select-primary-for-loan-payment": "Select a primary account for your loan payment.", "continue-button": "Continue", "connect-bank-account": "Connect Bank Account", "disconnected-badge": "Disconnected"}, "AutoCompleteInput": {"no-results-found": "No results found"}, "Factoring": {"in-house-credit-card": {"title": "Submit {{- supplierName}} Credit Form", "credit-score-info": "Does not affect your credit score.", "button-label": "Complete Form", "rejected-title": "Your {{- supplierName}} Credit Form has been declined", "rejected-info": "You will be able to request In-House Trade Credit in {{days}} days.", "rejected-info-1-day": "You will be able to request In-House Trade Credit in 1 day."}}}