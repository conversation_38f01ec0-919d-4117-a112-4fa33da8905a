import * as libPhone from 'libphonenumber-js'
import { Document, Query, Schema } from 'mongoose'

export function wirePhoneValidator<T extends Document>(
  schema: Schema<any>,
  column: string,
) {
  schema.pre('validate', preValidate<T>(column))
  schema.pre('updateOne', preUpdateOne<T>(column))
}

export function preValidate<T extends Document>(column: string) {
  return function (this: T, next: () => void) {
    ;(this as any)[column] = validatePhone(
      (this as any)[column] as unknown as string,
    ) as any
    next()
  }
}

export function preUpdateOne<T extends Document>(column: string) {
  return function (this: Query<T, T>, next: () => void) {
    const data = this.getUpdate() as any
    if (data) {
      if (column in data) {
        data[column] = validatePhone(data[column] as unknown as string) as any
        this.setUpdate(data)
      }
    }

    next()
  }
}

export function validatePhone(phone: string) {
  try {
    const _phone = libPhone.parse(phone, { defaultCountry: 'US' })
    return libPhone.formatNumber(_phone, 'E.164')
  } catch (e) {
    return undefined
  }
}
