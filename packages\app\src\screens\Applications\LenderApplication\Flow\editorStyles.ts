import { StyleSheet } from 'react-native'

export const editorStyles = StyleSheet.create({
  formContainer: {
    flexShrink: 1,
  },

  // Row layout
  row: {
    flexDirection: 'row',
    marginBottom: 32,
    minHeight: 68, // preserve space for validator
    maxHeight: 68,
  },
  singleColumnRow: {
    marginBottom: 32,
    minHeight: 68,
    maxHeight: 68,
  },

  // Column layout
  columnLeft: {
    flex: 1,
    marginRight: 12,
  },
  columnRight: {
    flex: 1,
    marginLeft: 12,
  },

  // Radio group
  radioGroupHorizontal: {
    flexDirection: 'row',
    gap: 32,
  },
  radioGroupVertical: {
    flexDirection: 'column',
    gap: 6,
  },
  radioLabel: {
    fontFamily: 'Inter, sans-serif',
    fontWeight: '500',
    fontSize: 14,
    color: '#001929',
  },
})
