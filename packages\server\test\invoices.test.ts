import { CompanyStatus, dictionaries, routes, routes2 } from '@linqpal/models'
import SgMail from '@sendgrid/mail'
import chai from 'chai'
import chaiHttp from 'chai-http'
import {
  ACH_TRANSACTION_TYPE,
  AzureService,
  BankAccount,
  CardPricingPackage,
  CardProducts,
  Company,
  crypt,
  CustomerAccount,
  Invoice,
  LoanApplication,
  LoanPricingPackage,
  Operation,
  referralService,
  signupService,
  Transaction,
  User,
  UserRole,
} from '@linqpal/common-backend'
import { _FirebaseTokenVerifier, FakeUsers } from './_firebase_admin'
import * as sinon from 'sinon'
import * as admin from 'firebase-admin'
import { auth as firebaseAuth } from 'firebase-admin'
import { _Authorization } from './_axios'
import {
  beforeEachFirebase,
  beforeEachMockEncryption,
  beforeEachMockEventBridgeClient,
  beforeEachMockSFN,
  beforeEachMockSNS,
  beforeEachMockSQS,
  beforeEachReferralRock,
  beforeEachSendGrid,
  beforeEachSms,
} from './helper'
import './hooks'
import nock from 'nock'
import {
  invoiceStatus,
  LOAN_APPLICATION_STATUS,
  OPERATION_STATUS,
  OPERATION_TYPES,
} from '@linqpal/models/src/dictionaries'
import md5 from 'crypto-js/md5'
import mongoose from 'mongoose'
import moment from 'moment'
import { initCbwApiRequester } from '@linqpal/common-backend/src/services/cbw'
import {
  ICompany,
  ICustomerAccount,
  IInvoice,
  IOperation,
} from '@linqpal/common-backend/src/models/types'
import {
  CREATE_PAY_NOW_INVOICE_TEMPLATE_CODE,
  DiscountDetail,
  EDiscountType,
  EFeeType,
  EPaymentMethod,
  EPaymentType,
  FeeDetail,
  IFeeDetails,
  IPayload,
  PayablesDetail,
} from '@linqpal/common-backend/src/services/payment/types'
import _ from 'lodash'
import { TEST_ID } from '@linqpal/common-backend/src/services/payment/achPayment.service'
import { beforeEachMockSecretsManager } from '@linqpal/services/test/helper'
import { onBoardingService } from '@linqpal/common-backend/src/services/onBoarding/onBoarding.service'

const feeDetails: IFeeDetails[] = [
  new FeeDetail(TEST_ID, 0, 'Merchant fee', EFeeType.merchantFee),
]

const defaultPaymentPayload: IPayload = {
  flowTemplateCode: CREATE_PAY_NOW_INVOICE_TEMPLATE_CODE,
  blueTapeCorrelationId: '',
  createdBy: 'BlueTape',
  details: {
    date: '',
    currency: 'USD',
    paymentMethod: EPaymentMethod.ach,
    payablesDetails: [],
    requestedAmount: 55,
    customerDetails: {
      id: '',
      name: 'Contractor name',
      accountId: '',
    },
    sellerDetails: {
      companyId: '',
      name: 'Mel',
      paymentSettings: { merchantAchDelayDays: 0 },
    },
    feeDetails,
  },
}
let paymentServicePayload: IPayload = defaultPaymentPayload

const resetPaymentPayload = () => {
  paymentServicePayload = defaultPaymentPayload
}

chai.use(chaiHttp)
chai.should()
let mockFirebaseAuth: sinon.SinonStub, obsMock: sinon.SinonStub

describe('Invoices', () => {
  beforeEachMockEncryption()

  let referralCode: sinon.SinonStub, mailSend: sinon.SinonStub
  let generateUniqueSubStub: sinon.SinonStub<any[], any>

  beforeEach(() => {
    generateUniqueSubStub = sinon
      .stub(signupService, 'generateUniqueSub')
      .resolves('sub')
    referralCode = sinon
      .stub(referralService, 'getReferralCode')
      .callsFake(() => Promise.resolve({ referralCode: '' }))
    mailSend = sinon
      .stub(SgMail, 'send')
      .callsFake(() =>
        Promise.resolve([{ statusCode: 200, body: {}, headers: [] }, {}]),
      )
    obsMock = sinon
      .stub(onBoardingService, 'getCreditApplications')
      .resolves([])
  })

  afterEach(() => {
    generateUniqueSubStub.restore()
    referralCode.restore()
    mailSend.restore()
    obsMock.restore()
    resetPaymentPayload()
  })

  async function createSupplier(
    cardPricingPackageId = 'A',
    loanPricingPackageId = 'optOut',
    discount = 0,
    achDelayDisabled = true,
  ) {
    const { supplier1 } = FakeUsers

    const mockAuth = _Authorization(supplier1.auth)

    let resp = await routes.company.addBankAccount({
      accountNumber: '**********',
      isPrimary: true,
      name: 'GreatBank',
      routingNumber: '123123',
      accountType: 'checking',
      paymentMethodType: 'bank',
    })
    resp.result.should.be.equal('ok')
    resp = await routes.company.updateInfo({
      name: 'Mel',
      address: {
        address: 'Some street',
        city: 'Kansas',
        zip: '11111',
        state: 'KS',
      },
      phone: '**********',
      email: '<EMAIL>',
      ein: '**********',
      settings: {
        cardPricingPackageId,
        loanPricingPackageId,
        achDelayDisabled,
        achDiscount:
          discount > 0 ? { validityInDays: 30, percentage: discount } : {},
      },
    })

    mockAuth.restore()

    const supplier_id = resp.id
    const supplier = await Company.findById(supplier_id)
    supplier!.status = CompanyStatus.Approved
    await supplier!.save()

    return supplier_id
  }

  async function createBuilder() {
    const { constructor1 } = FakeUsers

    const mockAuth = _Authorization(constructor1.auth)
    generateUniqueSubStub.resolves(constructor1.info.sub)

    let resp = await routes.company.addBankAccount({
      accountNumber: '********91',
      isPrimary: true,
      name: 'GreatBank',
      routingNumber: '123123',
      accountType: 'checking',
    })
    resp.result.should.be.equal('ok')

    resp = await routes2.user.updateInfo({
      firstName: 'John',
      lastName: 'Doe',
      phone: '**********',
      email: '<EMAIL>',
      addresses: [
        { city: 'Kansas', zip: '11111', address: 'Some street', state: 'KS' },
      ],
    })
    resp.result.should.be.equal('ok')

    mockAuth.restore()
  }

  async function createCustomer() {
    const { constructor1, supplier1 } = FakeUsers

    const mockAuth = _Authorization(supplier1.auth)

    const resp = await routes.supplier.saveAccount(
      {
        phone: constructor1.info.phone_number,
        name: 'Contractor name',
        email: '<EMAIL>',
      },
      true,
    )
    mockAuth.restore()

    return resp.id
  }

  async function createInvoice(
    amount: number,
    cardPricingPackageId = 'A',
    loanPricingPackageId = 'optOut',
    invoiceData: Partial<IInvoice> = {},
    discount = 0,
    achDelayDisabled = true,
  ) {
    const supplier_id = await createSupplier(
      cardPricingPackageId,
      loanPricingPackageId,
      discount,
      achDelayDisabled,
    )
    await createBuilder()

    if (!invoiceData.customer_account_id) {
      invoiceData.customer_account_id = await createCustomer()
    }

    const { supplier1 } = FakeUsers
    const mockAuth = _Authorization(supplier1.auth)

    const resp = await routes.invoices.saveInvoice({
      status: dictionaries.invoiceSchemaStatus.placed,
      material_subtotal: amount,
      total_amount: amount,
      invoice_date: new Date().toISOString(),
      ...invoiceData,
    })

    mockAuth.restore()

    const inv_id = resp.id
    const op = await Operation.findOne({ owner_id: inv_id })
    op!.type.should.equal(dictionaries.OPERATION_TYPES.INVOICE.PAYMENT)

    return {
      supplier_id,
      inv_id,
      customer_account_id: invoiceData.customer_account_id,
    }
  }

  it('should get invoice for non-auth constructor', async () => {
    mockFirebaseAuth = _FirebaseTokenVerifier()
    const { constructor1 } = FakeUsers
    console.log(constructor1)
    const com = await Company.create({
      name: 'Test supplier',
      type: 'supplier',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      settings: { cardPricingPackageId: 'optOut', loanPricingPackageId: '' },
    })
    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
    })
    await User.create({
      sub: constructor1.info.sub,
      firebaseId: constructor1.info.firebaseId,
      email: '<EMAIL>',
      login: constructor1.info.phone_number,
    })
    const inv = await Invoice.create({
      customer_account_id: acc._id,
      company_id: com.id,
    })
    const bankAccount = await BankAccount.create({
      accountNumber: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      isPrimary: true,
      name: 'GreatBank',
      routingNumber: '********',
      accountType: 'checking',
      paymentMethodType: 'bank',
    })
    const creditCard = await BankAccount.create({
      accountNumber: {
        display: '********1111',
      },
      cardMetadata: {
        accountId: 'ASASx6587ghkjj',
        avsAuthorizeID: '234',
        avsCode: 'Y',
        avsNetworkRC: '44',
        avsResultText: 'NOT DECLINED',
        avsSecurityCode: 'M',
        expirationDate: '202303',
        isPullEnabled: true,
        isRegulated: true,
        lastFour: '1111',
        network: 'Visa',
        token: '80E1iEMJ243WsTF0pBM',
        type: 'Credit',
      },
      name: 'Visa - Card',
      paymentMethodType: 'card',
      isManualEntry: true,
      accountholderName: 'Test Card 77',
      billingAddress: {
        addressLine1: 'Traction Street',
        addressLine2: '3094 ',
        city: 'Spartanburg',
        stateCode: 'South Carolina',
        zipCode: '29303',
      },
    })
    const company = await Company.create({
      name: 'Test supplier',
      type: 'supplier',
      bankAccounts: [
        new mongoose.Types.ObjectId(bankAccount.id),
        new mongoose.Types.ObjectId(creditCard.id),
      ],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
    })
    await LoanApplication.create({
      company_id: company._id.toString(),
      invoiceDetails: { invoiceId: inv._id.toString() },
      status: LOAN_APPLICATION_STATUS.PROCESSING,
    })
    const ur = await UserRole.create({
      sub: constructor1.info.sub,
      company_id: company._id,
      role: 'Owner',
    })
    console.log(ur)
    const mockByPhone = sinon
      .stub(admin.auth(), 'getUserByPhoneNumber')
      .callsFake(() =>
        Promise.resolve(null as unknown as firebaseAuth.UserRecord),
      )
    const mockCreate = sinon
      .stub(admin.auth(), 'createUser')
      .callsFake(() =>
        Promise.resolve({ sub: '123' } as unknown as firebaseAuth.UserRecord),
      )
    const mockToken = sinon
      .stub(admin.auth(), 'createCustomToken')
      .callsFake(() => Promise.resolve('123'))
    const resp = await routes.invoices.show({ id: `${inv._id}` })
    console.log(resp)
    resp.should.have.property('invoices')
    resp.should.not.have.property('bankAccounts')
    resp.should.have.property('loanApplicationStatus')
    resp.loanApplicationStatus.should.be.equal(
      LOAN_APPLICATION_STATUS.PROCESSING,
    )
    resp.should.have.property('currentInvoiceCreditStatus')
    resp.currentInvoiceCreditStatus.should.be.equal(
      LOAN_APPLICATION_STATUS.PROCESSING,
    )
    mockByPhone.restore()
    mockCreate.restore()
    mockToken.restore()
    mockFirebaseAuth.restore()
  })
  it('should show invoice for non-existent constructor', async () => {
    const { constructor1 } = FakeUsers
    mockFirebaseAuth = _FirebaseTokenVerifier()
    const auth = _Authorization(constructor1.auth)
    const com = await Company.create({
      name: 'Test supplier',
      type: 'supplier',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      settings: { cardPricingPackageId: 'optOut', loanPricingPackageId: '' },
    })
    const acc = await CustomerAccount.create({ phone: '+***********' })
    const mockByPhone = sinon
      .stub(admin.auth(), 'getUserByPhoneNumber')
      .callsFake(() =>
        Promise.resolve({
          phoneNumber: constructor1.info.phone_number,
        } as unknown as firebaseAuth.UserRecord),
      )

    const inv = await Invoice.create({
      customer_account_id: acc._id,
      company_id: com.id,
    })
    const resp = await routes.invoices.show({ id: `${inv._id}` })
    console.log(resp)
    const invoice = await Invoice.findById(inv._id)
    invoice!.seen.should.equal(true)
    resp.should.have.a.property('invoices')
    resp.should.not.have.a.property('bankAccounts')
    resp.should.have.a.property('loanApplicationStatus')
    resp.invoices.should.be.an('array')
    resp.loanApplicationStatus.should.be.a('string')
    mockByPhone.restore()
    auth.restore()
    mockFirebaseAuth.restore()
  })
  it('should return currentInvoiceCreditStatus - status of credit application applied for this invoice', async () => {
    const { constructor1 } = FakeUsers
    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
    })
    await User.create({
      sub: constructor1.info.sub,
      firebaseId: constructor1.info.firebaseId,
      email: '<EMAIL>',
      login: constructor1.info.phone_number,
    })
    const com = await Company.create({
      name: 'Test supplier',
      type: 'supplier',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      settings: { cardPricingPackageId: '', loanPricingPackageId: 'A' },
    })
    const company = await Company.create({
      name: 'Test supplier',
      type: 'supplier',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
    })
    await UserRole.create({
      sub: constructor1.info.sub,
      company_id: company._id,
      role: 'Owner',
    })
    const inv = await Invoice.create({
      customer_account_id: acc._id,
      company_id: com.id,
    })
    await LoanApplication.create({
      company_id: company._id,
      invoiceDetails: { invoiceId: inv._id.toString() },
      status: LOAN_APPLICATION_STATUS.NEW,
    })
    const resp = await routes.invoices.show({ id: `${inv._id}` })
    console.log(resp)
    resp.should.have.a.property('invoices')
    resp.should.have.a.property('loanApplicationStatus')
    resp.invoices.should.be.an('array')
    resp.loanApplicationStatus.should.be.a('string')
    resp.currentInvoiceCreditStatus.should.be.equal(LOAN_APPLICATION_STATUS.NEW)
  })
  it('should return loanPaymentAvailable as false if no loan package selected and cardPaymentAvailable as false if opted out', async () => {
    const { constructor1 } = FakeUsers
    mockFirebaseAuth = _FirebaseTokenVerifier()
    const auth = _Authorization(constructor1.auth)
    const acc = await CustomerAccount.create({ phone: '+***********' })
    const com = await Company.create({
      name: 'Test supplier',
      type: 'supplier',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      settings: { cardPricingPackageId: 'optOut', loanPricingPackageId: '' },
    })
    const inv = await Invoice.create({
      customer_account_id: acc._id,
      company_id: com.id,
    })
    const mockByPhone = sinon
      .stub(admin.auth(), 'getUserByPhoneNumber')
      .callsFake(() =>
        Promise.resolve({
          phoneNumber: constructor1.info.phone_number,
        } as unknown as firebaseAuth.UserRecord),
      )
    const resp = await routes.invoices.show({ id: `${inv._id}` })
    console.log(resp)
    const invoice = await Invoice.findById(inv._id)
    invoice!.seen.should.equal(true)
    resp.should.have.a.property('invoices')
    resp.should.have.a.property('loanApplicationStatus')
    resp.invoices.should.be.an('array')
    resp.loanApplicationStatus.should.be.a('string')
    resp.bnplPaymentAvailable.should.be.equal(false)
    resp.cardPaymentAvailable.should.be.equal(false)
    mockByPhone.restore()
    auth.restore()
    mockFirebaseAuth.restore()
  })
  it('should return loanPaymentAvailable as true if valid loan package selected and cardPaymentAvailable as true if blank', async () => {
    const { constructor1 } = FakeUsers
    mockFirebaseAuth = _FirebaseTokenVerifier()
    const auth = _Authorization(constructor1.auth)
    const acc = await CustomerAccount.create({ phone: '+***********' })
    const com = await Company.create({
      name: 'Test supplier',
      type: 'supplier',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      settings: { cardPricingPackageId: 'A', loanPricingPackageId: 'optOut' },
    })
    const mockByPhone = sinon
      .stub(admin.auth(), 'getUserByPhoneNumber')
      .callsFake(() =>
        Promise.resolve({
          phoneNumber: constructor1.info.phone_number,
        } as unknown as firebaseAuth.UserRecord),
      )
    const inv = await Invoice.create({
      customer_account_id: acc._id,
      company_id: com.id,
    })
    const resp = await routes.invoices.show({ id: `${inv._id}` })
    console.log(resp)
    const invoice = await Invoice.findById(inv._id)
    invoice!.seen.should.equal(true)
    resp.should.have.a.property('invoices')
    resp.should.have.a.property('loanApplicationStatus')
    resp.invoices.should.be.an('array')
    resp.loanApplicationStatus.should.be.a('string')
    resp.bnplPaymentAvailable.should.be.equal(false)
    resp.cardPaymentAvailable.should.be.equal(true)
    mockByPhone.restore()
    auth.restore()
    mockFirebaseAuth.restore()
  })
  it('should return loanPaymentAvailable as true if valid loan package selected and cardPaymentAvailable as false if blank', async () => {
    const { constructor1 } = FakeUsers
    mockFirebaseAuth = _FirebaseTokenVerifier()
    const auth = _Authorization(constructor1.auth)
    const acc = await CustomerAccount.create({ phone: '+***********' })
    const com = await Company.create({
      name: 'Test supplier',
      type: 'supplier',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      settings: { cardPricingPackageId: '', loanPricingPackageId: 'A' },
    })
    const mockByPhone = sinon
      .stub(admin.auth(), 'getUserByPhoneNumber')
      .callsFake(() =>
        Promise.resolve({
          phoneNumber: constructor1.info.phone_number,
        } as unknown as firebaseAuth.UserRecord),
      )
    const inv = await Invoice.create({
      customer_account_id: acc._id,
      company_id: com.id,
    })
    const resp = await routes.invoices.show({ id: `${inv._id}` })
    console.log(resp)
    const invoice = await Invoice.findById(inv._id)
    invoice!.seen.should.equal(true)
    resp.should.have.a.property('invoices')
    resp.should.have.a.property('loanApplicationStatus')
    resp.invoices.should.be.an('array')
    resp.loanApplicationStatus.should.be.a('string')
    resp.bnplPaymentAvailable.should.be.equal(true)
    resp.cardPaymentAvailable.should.be.equal(false)
    mockByPhone.restore()
    auth.restore()
    mockFirebaseAuth.restore()
  })
  it('should get invoice for auth constructor status expired', async () => {
    const { constructor1 } = FakeUsers
    mockFirebaseAuth = _FirebaseTokenVerifier()
    const auth = _Authorization(constructor1.auth)
    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
    })
    await User.create({
      sub: constructor1.info.sub,
      firebaseId: constructor1.info.firebaseId,
      email: '<EMAIL>',
      login: constructor1.info.phone_number,
    })
    const com = await Company.create({
      name: 'Test supplier',
      type: 'supplier',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      settings: { cardPricingPackageId: 'optOut', loanPricingPackageId: '' },
    })
    const inv = await Invoice.create({
      customer_account_id: acc._id,
      company_id: com.id,
      expiration_date: moment('2021-08-11T00:00:00.000Z').format(),
      status: 'PLACED',
    })
    await Operation.create({
      owner_id: inv._id,
      amount: 100,
      metadata: {
        payee_id: acc._id.toString(),
      },
      status: 'PLACED',
      type: 'invoice_payment',
    })
    const mockByPhone = sinon
      .stub(admin.auth(), 'getUserByPhoneNumber')
      .callsFake(() =>
        Promise.resolve({
          phoneNumber: constructor1.info.phone_number,
        } as unknown as firebaseAuth.UserRecord),
      )

    const company = await Company.create({
      name: 'Test supplier',
      type: 'contractor',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
    })
    await UserRole.create({
      sub: constructor1.info.sub,
      company_id: company._id,
      role: 'Owner',
    })

    const resp = await routes.invoices.show({ id: `${inv._id}` })
    resp.should.have.property('invoices')
    console.log(resp.invoices)
    resp.invoices[0].status.should.equal('EXPIRED')

    mockByPhone.restore()
    auth.restore()
    mockFirebaseAuth.restore()
  })
  it('should list invoice with Application status - to contractor', async () => {
    const { supplier1, constructor1 } = FakeUsers
    mockFirebaseAuth = _FirebaseTokenVerifier()
    let auth = _Authorization(supplier1.auth)
    generateUniqueSubStub.resolves(supplier1.info.sub)

    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
    })

    const invoice1 = await routes.invoices.saveInvoice({
      status: 'PLACED',
      invoice_number: '213',
      material_subtotal: 10,
      expiration_date: moment().add(10, 'days').format('MM/DD/YYYY'),
      invoice_due_date: moment().add(10, 'days').format('MM/DD/YYYY'),
      invoice_date: moment().format('MM/DD/YYYY'),
      total_amount: 10,
      customer_account_id: acc._id.toString(),
      approved: true,
    })

    //mailSend.calledThrice.should.be.true

    auth.restore()
    const app = await LoanApplication.create({
      'invoiceDetails.invoiceId': invoice1.id.toString(),
      status: LOAN_APPLICATION_STATUS.CANCELED,
      createdAt: moment().format('MM/DD/YYYY'),
    })

    auth = _Authorization(constructor1.auth)
    generateUniqueSubStub.resolves(constructor1.info.sub)

    let resp = await routes.invoices.list()
    resp.should.have.property('invoices')
    resp.invoices[0].status.should.equal(invoiceStatus.applicationCancelled)

    auth = _Authorization(supplier1.auth)
    generateUniqueSubStub.resolves(supplier1.info.sub)

    let supplierRes = await routes.invoices.allInvoices({
      currentDate: moment().format('MM-DD-YYYY'),
      search: '213',
    })
    supplierRes.items.length.should.equal(1)
    supplierRes.items[0].status.should.equal(invoiceStatus.applicationCancelled) // Status does not change for supplier

    auth = _Authorization(constructor1.auth)
    generateUniqueSubStub.resolves(constructor1.info.sub)

    await LoanApplication.findByIdAndUpdate(app._id, {
      status: LOAN_APPLICATION_STATUS.PENDING,
    })
    resp = await routes.invoices.list()
    resp.should.have.property('invoices')
    resp.invoices[0].status.should.equal(invoiceStatus.applicationProcessing)

    auth = _Authorization(supplier1.auth)
    generateUniqueSubStub.resolves(supplier1.info.sub)

    supplierRes = await routes.invoices.allInvoices({
      currentDate: moment().format('MM-DD-YYYY'),
      search: '213',
    })
    supplierRes.items.length.should.equal(1)
    supplierRes.items[0].status.should.equal(
      invoiceStatus.applicationProcessing,
    ) // Status does not change for supplier

    auth = _Authorization(constructor1.auth)
    generateUniqueSubStub.resolves(constructor1.info.sub)

    await LoanApplication.findByIdAndUpdate(app._id, {
      status: LOAN_APPLICATION_STATUS.REJECTED,
    })
    resp = await routes.invoices.list()
    resp.should.have.property('invoices')
    resp.invoices[0].status.should.equal(invoiceStatus.applicationRejected)

    auth = _Authorization(supplier1.auth)
    generateUniqueSubStub.resolves(supplier1.info.sub)

    supplierRes = await routes.invoices.allInvoices({
      currentDate: moment().format('MM-DD-YYYY'),
      search: '213',
    })
    supplierRes.items.length.should.equal(1)
    supplierRes.items[0].status.should.equal(invoiceStatus.applicationRejected)

    auth = _Authorization(constructor1.auth)
    generateUniqueSubStub.resolves(constructor1.info.sub)

    auth.restore()
    mockFirebaseAuth.restore()
  })
  it('should list invoice with Processing status instead of ApplicationCancelled when its paid with a different method - to contractor', async () => {
    const { supplier1, constructor1 } = FakeUsers
    mockFirebaseAuth = _FirebaseTokenVerifier()
    let auth = _Authorization(supplier1.auth)
    generateUniqueSubStub.resolves(supplier1.info.sub)

    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
    })

    const invoice1 = await routes.invoices.saveInvoice({
      status: 'PLACED',
      invoice_number: '213',
      material_subtotal: 10,
      expiration_date: moment().add(10, 'days').format('MM/DD/YYYY'),
      invoice_due_date: moment().add(10, 'days').format('MM/DD/YYYY'),
      invoice_date: moment().format('MM/DD/YYYY'),
      total_amount: 10,
      customer_account_id: acc._id.toString(),
      approved: true,
    })
    await Operation.create({
      owner_id: invoice1.id.toString(),
      amount: 10,
      metadata: {
        payee_id: acc._id.toString(),
      },
      status: OPERATION_STATUS.PROCESSING,
      type: 'invoice_payment',
    })

    //mailSend.calledThrice.should.be.true

    auth.restore()
    await LoanApplication.create({
      'invoiceDetails.invoiceId': invoice1.id.toString(),
      status: LOAN_APPLICATION_STATUS.CANCELED,
      createdAt: moment().format('MM/DD/YYYY'),
    })

    auth = _Authorization(constructor1.auth)
    generateUniqueSubStub.resolves(constructor1.info.sub)

    const resp = await routes.invoices.list({})

    resp.should.have.property('invoices')
    resp.invoices[0].status.should.equal(invoiceStatus.paymentProcessing)

    auth.restore()
    mockFirebaseAuth.restore()
  })
  it('should list invoice with Processing status instead of ApplicationRejected when its paid with a different method - to contractor', async () => {
    const { supplier1, constructor1 } = FakeUsers
    mockFirebaseAuth = _FirebaseTokenVerifier()
    let auth = _Authorization(supplier1.auth)
    generateUniqueSubStub.resolves(supplier1.info.sub)

    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
    })

    const invoice1 = await routes.invoices.saveInvoice({
      status: 'PLACED',
      invoice_number: '213',
      material_subtotal: 10,
      expiration_date: moment().add(10, 'days').format('MM/DD/YYYY'),
      invoice_due_date: moment().add(10, 'days').format('MM/DD/YYYY'),
      invoice_date: moment().format('MM/DD/YYYY'),
      total_amount: 10,
      customer_account_id: acc._id.toString(),
      approved: true,
    })
    await Operation.create({
      owner_id: invoice1.id.toString(),
      amount: 10,
      metadata: {
        payee_id: acc._id.toString(),
      },
      status: OPERATION_STATUS.PROCESSING,
      type: 'invoice_payment',
    })

    //mailSend.calledThrice.should.be.true

    auth.restore()
    await LoanApplication.create({
      'invoiceDetails.invoiceId': invoice1.id.toString(),
      status: LOAN_APPLICATION_STATUS.REJECTED,
      createdAt: moment().format('MM/DD/YYYY'),
    })

    auth = _Authorization(constructor1.auth)
    generateUniqueSubStub.resolves(constructor1.info.sub)

    const resp = await routes.invoices.list({})

    resp.should.have.property('invoices')
    resp.invoices[0].status.should.equal(invoiceStatus.paymentProcessing)

    auth.restore()
    mockFirebaseAuth.restore()
  })
  it('should list invoice with Cancelled status when invoice is cancelled, instead of ApplicationCancelled when a loan app with cancelled status exists - to contractor', async () => {
    // If a cancelled loan app exists for an invoice, and the invoice was cancelled later, list api should return status as Cancelled instaed of ApplicationCancelled
    // Invoice status has higher priority than application status
    const { supplier1, constructor1 } = FakeUsers
    mockFirebaseAuth = _FirebaseTokenVerifier()
    let auth = _Authorization(supplier1.auth)
    generateUniqueSubStub.resolves(supplier1.info.sub)

    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
    })

    const invoice1 = await routes.invoices.saveInvoice({
      status: invoiceStatus.cancelled,
      invoice_number: '213',
      material_subtotal: 10,
      expiration_date: moment().add(10, 'days').format('MM/DD/YYYY'),
      invoice_due_date: moment().add(10, 'days').format('MM/DD/YYYY'),
      invoice_date: moment().format('MM/DD/YYYY'),
      total_amount: 10,
      customer_account_id: acc._id.toString(),
      approved: true,
    })

    auth.restore()
    await LoanApplication.create({
      'invoiceDetails.invoiceId': invoice1.id.toString(),
      status: LOAN_APPLICATION_STATUS.CANCELED,
      createdAt: moment().format('MM/DD/YYYY'),
    })

    auth = _Authorization(constructor1.auth)
    generateUniqueSubStub.resolves(constructor1.info.sub)

    const resp = await routes.invoices.list({})

    resp.should.have.property('invoices')
    resp.invoices[0].status.should.equal(invoiceStatus.cancelled)

    auth.restore()
    mockFirebaseAuth.restore()
  })
  it('should list invoice with Dismissed status when invoice is dismissed, instead of ApplicationCancelled when a loan app with cancelled status exists - to contractor', async () => {
    // If a cancelled loan app exists for an invoice, and the invoice was dismissed later, list api should return status as Dismissed instaed of ApplicationCancelled
    // Invoice status has higher priority than application status
    const { supplier1, constructor1 } = FakeUsers
    mockFirebaseAuth = _FirebaseTokenVerifier()
    let auth = _Authorization(supplier1.auth)
    generateUniqueSubStub.resolves(supplier1.info.sub)

    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
    })

    const invoice1 = await routes.invoices.saveInvoice({
      status: invoiceStatus.dismissed,
      invoice_number: '213',
      material_subtotal: 10,
      expiration_date: moment().add(10, 'days').format('MM/DD/YYYY'),
      invoice_due_date: moment().add(10, 'days').format('MM/DD/YYYY'),
      invoice_date: moment().format('MM/DD/YYYY'),
      total_amount: 10,
      customer_account_id: acc._id.toString(),
      approved: true,
    })

    auth.restore()
    await LoanApplication.create({
      'invoiceDetails.invoiceId': invoice1.id.toString(),
      status: LOAN_APPLICATION_STATUS.CANCELED,
      createdAt: moment().format('MM/DD/YYYY'),
    })

    auth = _Authorization(constructor1.auth)
    generateUniqueSubStub.resolves(constructor1.info.sub)

    const resp = await routes.invoices.list({})

    resp.should.have.property('invoices')
    resp.invoices[0].status.should.equal(invoiceStatus.dismissed)

    auth.restore()
    mockFirebaseAuth.restore()
  })
  it('should get invoice for auth constructor status pastdue', async () => {
    const { constructor1 } = FakeUsers
    mockFirebaseAuth = _FirebaseTokenVerifier()
    const auth = _Authorization(constructor1.auth)
    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
    })
    await User.create({
      sub: constructor1.info.sub,
      firebaseId: constructor1.info.firebaseId,
      email: '<EMAIL>',
      login: constructor1.info.phone_number,
    })
    const com = await Company.create({
      name: 'Test supplier',
      type: 'supplier',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      settings: { cardPricingPackageId: 'optOut', loanPricingPackageId: '' },
    })
    const inv = await Invoice.create({
      customer_account_id: acc._id,
      company_id: com.id,
      expiration_date: moment().add(2, 'weeks').format(),
      invoice_due_date: moment().subtract(2, 'days').format(),
      status: 'PLACED',
    })
    await Operation.create({
      owner_id: inv._id,
      amount: 100,
      metadata: {
        payee_id: acc._id.toString(),
      },
      status: 'PLACED',
      type: 'invoice_payment',
    })
    const mockByPhone = sinon
      .stub(admin.auth(), 'getUserByPhoneNumber')
      .callsFake(() =>
        Promise.resolve({
          phoneNumber: constructor1.info.phone_number,
        } as unknown as firebaseAuth.UserRecord),
      )

    const company = await Company.create({
      name: 'Test supplier',
      type: 'contractor',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
    })
    await UserRole.create({
      sub: constructor1.info.sub,
      company_id: company._id,
      role: 'Owner',
    })

    const resp = await routes.invoices.show({ id: `${inv._id}` })
    resp.should.have.property('invoices')
    resp.invoices[0].status.should.equal('PASTDUE')

    mockByPhone.restore()
    auth.restore()
    mockFirebaseAuth.restore()
  })
  it('should list invoices (multiple invoices) with Application Rejected status - to contractor', async () => {
    const { supplier1, constructor1 } = FakeUsers
    mockFirebaseAuth = _FirebaseTokenVerifier()
    let auth = _Authorization(supplier1.auth)
    generateUniqueSubStub.resolves(supplier1.info.sub)

    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
    })

    const invoice1 = await routes.invoices.saveInvoice({
      status: 'PLACED',
      invoice_number: '213',
      material_subtotal: 10,
      expiration_date: moment().add(10, 'days').format('MM/DD/YYYY'),
      invoice_due_date: moment().add(10, 'days').format('MM/DD/YYYY'),
      invoice_date: moment().format('MM/DD/YYYY'),
      total_amount: 10,
      customer_account_id: acc._id.toString(),
      approved: true,
    })
    const invoice2 = await routes.invoices.saveInvoice({
      status: 'PLACED',
      invoice_number: '214',
      material_subtotal: 12,
      expiration_date: moment().add(10, 'days').format('MM/DD/YYYY'),
      invoice_due_date: moment().add(10, 'days').format('MM/DD/YYYY'),
      invoice_date: moment().format('MM/DD/YYYY'),
      total_amount: 12,
      customer_account_id: acc._id.toString(),
      approved: true,
    })

    auth.restore()
    await LoanApplication.create({
      'invoiceDetails.invoiceId': [
        invoice1.id.toString(),
        invoice2.id.toString(),
      ],
      status: LOAN_APPLICATION_STATUS.REJECTED,
      createdAt: moment().format('MM/DD/YYYY'),
    })

    auth = _Authorization(constructor1.auth)
    generateUniqueSubStub.resolves(constructor1.info.sub)

    const resp = await routes.invoices.list({})

    resp.should.have.property('invoices')
    resp.invoices[0].status.should.equal(invoiceStatus.applicationRejected)
    resp.invoices[1].status.should.equal(invoiceStatus.applicationRejected)

    auth.restore()
    mockFirebaseAuth.restore()
  })
  it('should get invoice for auth constructor', async () => {
    const { constructor1 } = FakeUsers
    mockFirebaseAuth = _FirebaseTokenVerifier()
    const auth = _Authorization(constructor1.auth)
    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
    })
    await User.create({
      sub: constructor1.info.sub,
      firebaseId: constructor1.info.firebaseId,
      email: '<EMAIL>',
      login: constructor1.info.phone_number,
    })
    const com = await Company.create({
      name: 'Test supplier',
      type: 'supplier',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      settings: { cardPricingPackageId: 'optOut', loanPricingPackageId: '' },
    })
    const inv = await Invoice.create({
      customer_account_id: acc._id,
      company_id: com.id,
    })
    const mockByPhone = sinon
      .stub(admin.auth(), 'getUserByPhoneNumber')
      .callsFake(() =>
        Promise.resolve({
          phoneNumber: constructor1.info.phone_number,
        } as unknown as firebaseAuth.UserRecord),
      )
    const bankAccount = await BankAccount.create({
      accountNumber: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      isPrimary: true,
      name: 'GreatBank',
      routingNumber: '********',
      accountType: 'checking',
      paymentMethodType: 'bank',
    })
    const creditCard = await BankAccount.create({
      accountNumber: {
        display: '********1111',
      },
      cardMetadata: {
        accountId: 'ASASx6587ghkjj',
        avsAuthorizeID: '234',
        avsCode: 'Y',
        avsNetworkRC: '44',
        avsResultText: 'NOT DECLINED',
        avsSecurityCode: 'M',
        expirationDate: '202303',
        isPullEnabled: true,
        isRegulated: true,
        lastFour: '1111',
        network: 'Visa',
        token: '80E1iEMJ243WsTF0pBM',
        type: 'Credit',
      },
      name: 'Visa - Card',
      paymentMethodType: 'card',
      isManualEntry: true,
      accountholderName: 'Test Card 77',
      billingAddress: {
        addressLine1: 'Traction Street',
        addressLine2: '3094 ',
        city: 'Spartanburg',
        stateCode: 'South Carolina',
        zipCode: '29303',
      },
    })
    const company = await Company.create({
      name: 'Test supplier',
      type: 'contractor',
      bankAccounts: [
        new mongoose.Types.ObjectId(bankAccount.id),
        new mongoose.Types.ObjectId(creditCard.id),
      ],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
    })
    await UserRole.create({
      sub: constructor1.info.sub,
      company_id: company._id,
      role: 'Owner',
    })
    const resp = await routes.invoices.show({ id: `${inv._id}` })
    resp.should.have.property('invoices')
    resp.should.not.have.property('token')
    resp.should.not.have.property('bankAccounts')
    mockByPhone.restore()
    auth.restore()
    mockFirebaseAuth.restore()
  })
  it('should get invoices from list of ids', async () => {
    const { constructor1 } = FakeUsers
    mockFirebaseAuth = _FirebaseTokenVerifier()
    const auth = _Authorization(constructor1.auth)
    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
    })
    await User.create({
      sub: constructor1.info.sub,
      firebaseId: constructor1.info.firebaseId,
      email: '<EMAIL>',
      login: constructor1.info.phone_number,
    })
    const createdCompany = await Company.create({ type: 'supplier' })
    const invoice = await Invoice.create({
      customer_account_id: acc._id,
      company_id: createdCompany._id,
    })
    const invoice2 = await Invoice.create({
      customer_account_id: acc._id,
      company_id: createdCompany._id,
    })
    const invoice3 = await Invoice.create({
      customer_account_id: acc._id,
      company_id: createdCompany._id,
    })

    const invoiceArray = [
      invoice._id.toString(),
      invoice2._id.toString(),
      invoice3._id.toString(),
    ]

    console.log('invoiceArray', invoiceArray)

    const res = await routes.invoices.listFromArray(invoiceArray)
    res.result.should.equal('ok')
    res.invoices.length.should.equal(3)
    auth.restore()
    mockFirebaseAuth.restore()
  })
  it('should get invoice with correct status', async () => {
    const { supplier1, constructor1 } = FakeUsers
    mockFirebaseAuth = _FirebaseTokenVerifier()
    let auth = _Authorization(supplier1.auth)
    generateUniqueSubStub.resolves(supplier1.info.sub)

    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
    })

    const invoice1 = await routes.invoices.saveInvoice({
      status: 'PLACED',
      invoice_number: '213',
      material_subtotal: 10,
      expiration_date: moment().add(10, 'days').format('MM/DD/YYYY'),
      invoice_due_date: moment().add(10, 'days').format('MM/DD/YYYY'),
      invoice_date: moment().format('MM/DD/YYYY'),
      total_amount: 10,
      customer_account_id: acc._id.toString(),
      approved: true,
    })

    //mailSend.calledThrice.should.be.true

    const res = await routes.invoices.allInvoices({
      currentDate: moment().format('MM-DD-YYYY'),
      search: '213',
    })
    res.items.length.should.equal(1)
    res.items[0].status.should.equal('PLACED')

    const resNew = await routes.invoices.allInvoices({
      currentDate: moment().format('MM-DD-YYYY'),
      search: '213',
      status: 'EXCEPT_DRAFT',
    })
    resNew.items.length.should.equal(1)
    resNew.items[0].status.should.equal('PLACED')
    auth.restore()
    auth = _Authorization(constructor1.auth)
    generateUniqueSubStub.resolves(constructor1.info.sub)

    let resp = await routes.invoices.show({ id: `${invoice1.id}` })
    resp.should.have.property('invoices')
    auth.restore()

    auth = _Authorization(supplier1.auth)
    generateUniqueSubStub.resolves(supplier1.info.sub)

    const res1 = await routes.invoices.allInvoices({
      currentDate: moment().format('MM-DD-YYYY'),
      search: '213',
    })
    res1.items.length.should.equal(1)
    res1.items[0].status.should.equal('SEEN')
    auth.restore()

    await Operation.create({
      owner_id: res1.items[0].id,
      amount: 100,
      metadata: {
        payee_id: acc._id.toString(),
      },
      status: 'SUCCESS',
      type: 'invoice_payment',
      createdAt: moment().subtract(2, 'days').toDate(),
    })
    await Operation.create({
      owner_id: res1.items[0].id,
      amount: 100,
      metadata: {
        payee_id: acc._id.toString(),
      },
      status: 'SUCCESS',
      type: OPERATION_TYPES.INVOICE.REFUND,
      createdAt: moment().toDate(),
    })

    auth = _Authorization(constructor1.auth)
    generateUniqueSubStub.resolves(constructor1.info.sub)

    resp = await routes.invoices.list()
    resp.should.have.property('invoices')
    resp.invoices[0].status.should.equal(invoiceStatus.refunded)
    auth.restore()

    auth = _Authorization(supplier1.auth)
    generateUniqueSubStub.resolves(supplier1.info.sub)

    resp = await routes.invoices.allInvoices({
      currentDate: moment().format('MM-DD-YYYY'),
      search: '213',
    })
    resp.items.length.should.equal(1)
    resp.items[0].status.should.equal(invoiceStatus.refunded)
    auth.restore()

    mockFirebaseAuth.restore()
  })
  it('should return if invoice is eligible for achDiscount ', async () => {
    const { constructor1 } = FakeUsers
    mockFirebaseAuth = _FirebaseTokenVerifier()
    const auth = _Authorization(constructor1.auth)
    const acc = await CustomerAccount.create({ phone: '+***********' })
    const com = await Company.create({
      name: 'Test supplier',
      type: 'supplier',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      settings: {
        cardPricingPackageId: 'packageA',
        loanPricingPackageId: 'A',
        achDiscount: { percentage: 10, validityInDays: 7 },
      },
    })
    const mockByPhone = sinon
      .stub(admin.auth(), 'getUserByPhoneNumber')
      .callsFake(() =>
        Promise.resolve({
          phoneNumber: constructor1.info.phone_number,
        } as unknown as firebaseAuth.UserRecord),
      )
    // when invoice is not past due and date is within validity
    const inv = await Invoice.create({
      status: invoiceStatus.placed,
      company_id: com.id,
      invoice_number: '214',
      material_subtotal: 10,
      expiration_date: null,
      invoice_due_date: moment().add(5, 'days').format('MM/DD/YYYY'),
      invoice_date: moment().format('MM/DD/YYYY'),
      total_amount: 10,
      customer_account_id: acc._id.toString(),
      approved: true,
    })
    await Operation.create({
      owner_id: inv._id,
      amount: 10,
      metadata: {
        payee_id: acc._id.toString(),
      },
      status: 'PLACED',
      type: 'invoice_payment',
    })
    const resp = await routes.invoices.show({ id: `${inv._id}` })
    const invoice = await Invoice.findById(inv._id)
    invoice!.seen.should.equal(true)
    resp.should.have.a.property('invoices')
    resp.should.have.a.property('loanApplicationStatus')
    resp.invoices.should.be.an('array')
    resp.loanApplicationStatus.should.be.a('string')
    resp.invoices[0].should.have.a.property('achDiscount')
    resp.invoices[0].should.have.a.property('achDiscountValidUpto')
    resp.invoices[0].achDiscount.should.be.equal(1)

    // when invoice is past due and date is within validity

    const inv2 = await Invoice.create({
      status: invoiceStatus.placed,
      company_id: com.id,
      invoice_number: '214',
      material_subtotal: 10,
      expiration_date: null,
      invoice_due_date: moment().subtract(1, 'days').format('MM/DD/YYYY'),
      invoice_date: moment().subtract(1, 'days').format('MM/DD/YYYY'),
      total_amount: 10,
      customer_account_id: acc._id.toString(),
      approved: true,
    })
    await Operation.create({
      owner_id: inv2._id,
      amount: 10,
      metadata: {
        payee_id: acc._id.toString(),
      },
      status: 'PLACED',
      type: 'invoice_payment',
    })
    const resp2 = await routes.invoices.show({ id: `${inv2._id}` })
    const invoice2 = await Invoice.findById(inv2._id)
    invoice2!.seen.should.equal(true)
    resp2.should.have.a.property('invoices')
    resp2.invoices.should.be.an('array')
    resp2.loanApplicationStatus.should.be.a('string')
    resp2.invoices[0].should.have.a.property('achDiscount')
    resp2.invoices[0].should.have.a.property('achDiscountValidUpto')
    resp2.invoices[0].achDiscount.should.be.equal(0)

    // when invoice is not past due and date is not within validity
    const inv3 = await Invoice.create({
      status: invoiceStatus.placed,
      company_id: com.id,
      invoice_number: '214',
      material_subtotal: 10,
      expiration_date: null,
      invoice_due_date: moment().add(15, 'days').format('MM/DD/YYYY'),
      invoice_date: moment().subtract(10, 'days').format('MM/DD/YYYY'),
      total_amount: 10,
      customer_account_id: acc._id.toString(),
      approved: true,
    })
    await Operation.create({
      owner_id: inv3._id,
      amount: 10,
      metadata: {
        payee_id: acc._id.toString(),
      },
      status: 'PLACED',
      type: 'invoice_payment',
    })
    const resp3 = await routes.invoices.show({ id: `${inv3._id}` })
    const invoice3 = await Invoice.findById(inv3._id)
    invoice3!.seen.should.equal(true)
    resp3.should.have.a.property('invoices')
    resp3.invoices.should.be.an('array')
    resp3.loanApplicationStatus.should.be.a('string')
    resp3.invoices[0].should.have.a.property('achDiscount')
    resp3.invoices[0].should.have.a.property('achDiscountValidUpto')
    resp3.invoices[0].achDiscount.should.be.equal(0)

    // when invoice is not past due and withing discount validity and is in payment failed status
    const inv4 = await Invoice.create({
      status: invoiceStatus.placed,
      company_id: com.id,
      invoice_number: '214',
      material_subtotal: 10,
      expiration_date: null,
      invoice_due_date: moment().add(15, 'days').format('MM/DD/YYYY'),
      invoice_date: moment().add(1, 'days').format('MM/DD/YYYY'),
      total_amount: 10,
      customer_account_id: acc._id.toString(),
    })
    await Operation.create({
      owner_id: inv4._id,
      amount: 10,
      metadata: {
        payee_id: acc._id.toString(),
      },
      status: 'FAIL',
      type: 'invoice_payment',
    })
    const resp4 = await routes.invoices.show({ id: `${inv4._id}` })
    resp4.should.have.a.property('invoices')
    resp4.invoices.should.be.an('array')
    resp4.invoices[0].status.should.be.equal(invoiceStatus.paymentFailed)
    resp4.loanApplicationStatus.should.be.a('string')
    resp4.invoices[0].should.have.a.property('achDiscount')
    resp4.invoices[0].should.have.a.property('achDiscountValidUpto')
    resp4.invoices[0].achDiscount.should.be.equal(1)

    mockByPhone.restore()
    auth.restore()
    mockFirebaseAuth.restore()
  })

  describe('Payment', () => {
    beforeEachFirebase()
    beforeEachMockEncryption()
    beforeEachMockSecretsManager()
    beforeEachMockSQS()
    beforeEachMockSNS()
    beforeEachSms()
    let aiMock: sinon.SinonStub
    let azureSendServiceBusMessage: sinon.SinonStub
    beforeEach(async () => {
      process.env.LP_CBW_ACH_IDENTIFICATION = '*********'
      process.env.AZ_PAYMENT_SERVICE_QUEUE_CONNECTION_STRING =
        'Endpoint=sb://service-bus-namespace-payment-service-dev.servicebus.windows.net/;SharedAccessKeyName=payment-bus-queue-connection-auth-rule;SharedAccessKey=bdH5pRhWdbo/Kkdp0qkZQOTwM7pxDh8n3+ASbAgpYz4=;EntityPath=paymentrequestqueue-dev'
      process.env.AZ_PAYMENT_SERVICE_QUEUE_NAME = 'paymentrequestqueue-dev'

      const cbwApi = initCbwApiRequester()
      aiMock = sinon.stub(cbwApi, 'post').callsFake((_path, { payload }) => {
        return Promise.resolve({
          transactionNumber: '123',
          transactionAmountCents: parseInt(payload.transactionAmount.amount),
          api: {
            reference: '123',
            dateTime: '',
            originalReference: payload.reference,
          },
          statusCode: '000',
          statusDescription: 'SUCCESS',
        })
      })
      azureSendServiceBusMessage = sinon
        .stub(AzureService, 'sendServiceBusMessage')
        .callsFake(() => Promise.resolve())
    })
    afterEach(async () => {
      aiMock.restore()
      azureSendServiceBusMessage.restore()
    })

    it('should return error if card package is not selected and payment with card is initiated', async () => {
      const acc = await CustomerAccount.create({ phone: '+***********' })
      const com = await Company.create({
        name: 'Test supplier',
        type: 'supplier',
        bankAccounts: [],
        email: '<EMAIL>',
        phone: '+***********',
        address: {
          address: '100 Main St',
          city: 'San Francisco',
          zip: '94061',
          state: 'CA',
        },
        ein: {
          cipher: await crypt.encrypt('**********'),
          hash: md5('**********').toString(),
          display: '******7890',
        },
        settings: { cardPricingPackageId: '' },
      })
      const inv = await Invoice.create({
        customer_account_id: acc._id,
        company_id: com.id,
        total_amount: 100,
      })
      await Operation.create({
        owner_id: inv._id,
        amount: 100,
        metadata: {
          payee_id: acc._id.toString(),
        },
        status: 'PLACED',
        type: 'invoice_payment',
      })
      const { constructor1 } = FakeUsers
      const mockAuth = _Authorization(constructor1.auth)

      const response = await routes2.invoice.pay({
        invoiceIds: [inv._id.toString()],
        paymentMethod: 'card',
        account_id: acc._id.toString(),
        accountId: acc._id.toString(),
      })
      response.should.have.property('result')
      response.result!.should.be.equal('LogicalError')
      response.should.have.property('message')
      mockAuth.restore()
    })

    it('should return error if pay with loan and loanpackage not selected', async () => {
      const acc = await CustomerAccount.create({ phone: '+***********' })
      const com = await Company.create({
        name: 'Test supplier',
        type: 'supplier',
        bankAccounts: [],
        email: '<EMAIL>',
        phone: '+***********',
        address: {
          address: '100 Main St',
          city: 'San Francisco',
          zip: '94061',
          state: 'CA',
        },
        ein: {
          cipher: await crypt.encrypt('**********'),
          hash: md5('**********').toString(),
          display: '******7890',
        },
        settings: { loanPricingPackageId: '' },
      })
      const inv = await Invoice.create({
        customer_account_id: acc._id,
        company_id: com.id,
      })
      const { constructor1 } = FakeUsers
      const mockAuth = _Authorization(constructor1.auth)
      const response = await routes2.user.loanApplication({
        invoiceId: inv._id,
      } as any)
      response.should.have.property('result')
      response.result!.should.be.equal('LogicalError')
      response.should.have.property('message')
      response.message!.should.be.equal(
        'The supplier does not accept this payment type.',
      )
      mockAuth.restore()
    })

    it('should pay with bank account', async () => {
      await CardProducts.create({
        productCode: 'G',
        productName: 'Visa Business',
        personalOrBusiness: 'business',
        cardType: 'credit',
        isbpsp: true,
        isl2eligible: true,
        isl3eligible: true,
        isTravel: false,
      })
      const PRICING_PACK_MIN_AMOUNT = 1
      await CardPricingPackage.create({
        name: 'A',
        metadata: {
          ach: {
            merchant: { min: PRICING_PACK_MIN_AMOUNT, max: 10, percentage: 1 },
            customer: { percentage: 1, amount: PRICING_PACK_MIN_AMOUNT },
          },
          creditCardVisa: {
            merchant: { percentage: 1, amount: 1 },
            customer: { percentage: 2, amount: 2 },
          },
          debitCardRegulated: {
            merchant: { percentage: 1, amount: 2 },
            customer: { percentage: 2, amount: 1 },
          },
          debitCardUnregulated: {
            merchant: { percentage: 2, amount: 3 },
            customer: { percentage: 3, amount: 2 },
          },
          creditCardMasterCard2: {
            merchant: { percentage: 3, amount: 4 },
            customer: { percentage: 4, amount: 3 },
          },
          creditCardVisa2: {
            merchant: { percentage: 8, amount: 9 },
            customer: { percentage: 9, amount: 8 },
          },
          creditCardTravel: {
            merchant: { percentage: 4, amount: 5 },
            customer: { percentage: 5, amount: 4 },
          },
          creditCardBusiness: {
            merchant: { percentage: 5, amount: 6 },
            customer: { percentage: 6, amount: 5 },
          },
          amex: {
            merchant: { percentage: 6, amount: 7 },
            customer: { percentage: 7, amount: 6 },
          },
        },
      })
      const { constructor1, supplier1 } = FakeUsers
      const { supplier_id, inv_id } = await createInvoice(55)

      let mockAuth = _Authorization(supplier1.auth)
      const me_user = (await User.findOne({ firebaseId: supplier1.auth }))!
      const me_role = (await UserRole.findOne({ sub: me_user.sub }))!
      const login = (me_user.email || '').replace('|', '.')
      await routes.company.userUpsert({
        firstName: 'Supplier',
        lastName: '1',
        ...me_user.toJSON(),
        login, // Required for Email validation
        role: me_role.role,
        status: me_role.status,
        roleSettings: {
          ...me_role.settings,
          paymentEmailNotificationsEnabled: false,
        },
      })
      mockAuth.restore()

      mailSend.getCalls().forEach((call) => console.log('=====', call.firstArg))

      mailSend.callCount.should.equal(5)
      const supplier_email_used_count = mailSend
        .getCalls()
        .filter((call) => call.firstArg.to.includes(me_user.email)).length

      mockAuth = _Authorization(constructor1.auth)
      let resp = await routes.company.info()
      const constructor_id = resp.company.id

      resp = await routes.company.bankAccounts()
      const account = resp.bankAccounts[0]
      resp = await routes2.invoice.pay({
        invoiceIds: [inv_id],
        paymentMethod: 'ach',
        accountId: account._id,
        account_id: account._id,
      })
      resp.result.should.eq('ok')

      feeDetails[0].amount = 1
      const pay = new PayablesDetail(inv_id, EPaymentType.invoice, 55, 55, 0)
      paymentServicePayload.blueTapeCorrelationId = constructor_id
      paymentServicePayload.details.payablesDetails = [pay]
      paymentServicePayload.details.customerDetails = {
        id: constructor_id,
        name: 'Contractor name',
        accountId: account._id,
      }
      paymentServicePayload.details.sellerDetails!.companyId = supplier_id
      azureSendServiceBusMessage.callCount.should.equal(1)
      const dets = azureSendServiceBusMessage.getCall(0).args[2].details
      paymentServicePayload.details.date = dets.date
      chai
        .expect(azureSendServiceBusMessage.getCall(0).args[2])
        .to.deep.equal(paymentServicePayload)

      mailSend.callCount.should.equal(6)
      mailSend
        .getCalls()
        .filter((call) => call.firstArg.to.includes(me_user.email))
        .length.should.eq(supplier_email_used_count)

      const invoice = await Invoice.findById(inv_id).populate('operation')
      const op = await Operation.findById(invoice!.operation!.id)
      op!.status = OPERATION_STATUS.PROCESSING
      await op?.save()
      resp = await routes2.invoice.pay({
        invoiceIds: [inv_id],
        paymentMethod: 'ach',
        accountId: account._id,
        account_id: account._id,
      })
      resp.result.should.eq('LogicalError')
      resp.message.should.eq('Invoice already paid')
      mockAuth.restore()
      mockAuth = _Authorization(supplier1.auth)
      await op!.updateOne({ status: OPERATION_STATUS.SUCCESS })
      // const transactions = await Transaction.find({
      //   operation_id: invoice!.operation!.id,
      // })
      // await transactions[0].updateOne({ status: TRANSACTION_STATUS.SUCCESS })
      // await transactions[1].updateOne({ status: TRANSACTION_STATUS.SUCCESS })

      //Refund
      // resp = await routes.invoices.refundAmount(inv_id, op!.id, 10)
      // resp.should.have.a.property('id')

      // resp = await routes.invoices.refundAmount(inv_id, op!.id, 20)
      // resp.should.have.a.property('id')

      // resp = await routes.invoices.refundAmount(inv_id, op!.id, 30)
      // resp.result.should.equal('LogicalError')

      // resp = await routes.invoices.refundAmount(inv_id, op!.id, 25)
      // resp.should.have.a.property('id')

      // resp = await routes.invoices.refundAmount(inv_id, op!.id, 0)
      // resp.result.should.equal('LogicalError')

      // resp = await routes.invoices.refundAmount(inv_id, op!.id, 1)
      // resp.result.should.equal('LogicalError')

      mockAuth.restore()
    })

    it('should pay with bank account even if no package selected', async () => {
      await CardPricingPackage.create({
        name: 'A',
        metadata: {
          ach: {
            merchant: { min: 1, max: 10, percentage: 1 },
            customer: { percentage: 1, amount: 1 },
          },
          creditCardVisa: {
            merchant: { percentage: 1, amount: 1 },
            customer: { percentage: 2, amount: 2 },
          },
          debitCardRegulated: {
            merchant: { percentage: 1, amount: 2 },
            customer: { percentage: 2, amount: 1 },
          },
          debitCardUnregulated: {
            merchant: { percentage: 2, amount: 3 },
            customer: { percentage: 3, amount: 2 },
          },
          creditCardMasterCard2: {
            merchant: { percentage: 3, amount: 4 },
            customer: { percentage: 4, amount: 3 },
          },
          creditCardVisa2: {
            merchant: { percentage: 8, amount: 9 },
            customer: { percentage: 9, amount: 8 },
          },
          creditCardTravel: {
            merchant: { percentage: 4, amount: 5 },
            customer: { percentage: 5, amount: 4 },
          },
          creditCardBusiness: {
            merchant: { percentage: 5, amount: 6 },
            customer: { percentage: 6, amount: 5 },
          },
          amex: {
            merchant: { percentage: 6, amount: 7 },
            customer: { percentage: 7, amount: 6 },
          },
        },
      })
      const { constructor1, supplier1 } = FakeUsers
      const { supplier_id, inv_id } = await createInvoice(
        55,
        'optOut',
        'optOut',
        { invoice_date: moment().subtract(32, 'days').format('MM/DD/YYYY') },
      )
      let mockAuth = _Authorization(constructor1.auth)
      let resp = await routes.company.info()
      resp.result.should.be.equal('ok')
      resp = await routes.company.bankAccounts()
      resp.result.should.be.equal('ok')
      const account = resp.bankAccounts[0]
      resp = await routes2.invoice.pay({
        invoiceIds: [inv_id],
        paymentMethod: 'ach',
        accountId: account._id,
        account_id: account._id,
      })
      resp.result.should.be.equal('ok')
      const invoice = await Invoice.findById(inv_id).populate('operation')
      invoice!.operation!.metadata.payee_id!.should.equal(supplier_id)
      /*const transactions = await Transaction.find({
        operation_id: invoice!.operation!.id,
      })*/
      const op = await Operation.findById(invoice!.operation!.id)
      op!.metadata.payee_id!.should.equal(supplier_id)
      op!.status.should.equal(OPERATION_STATUS.PROCESSING)
      resp = await routes2.invoice.pay({
        invoiceIds: [inv_id],
        paymentMethod: 'ach',
        accountId: account._id,
        account_id: account._id,
      })
      resp.result.should.be.equal('LogicalError')
      mockAuth.restore()
      mockAuth = _Authorization(supplier1.auth)
      await op!.updateOne({ status: OPERATION_STATUS.SUCCESS })
      //await transactions[0].updateOne({ status: TRANSACTION_STATUS.SUCCESS })
      //await transactions[1].updateOne({ status: TRANSACTION_STATUS.SUCCESS })
      mockAuth.restore()
    })

    it('should pay with pricing package', async () => {
      await CardProducts.create({
        productCode: 'G',
        productName: 'Visa Business',
        personalOrBusiness: 'business',
        cardType: 'credit',
        isbpsp: true,
        isl2eligible: true,
        isl3eligible: true,
        isTravel: false,
      })
      await CardPricingPackage.create({
        name: 'A',
        metadata: {
          ach: {
            merchant: { min: 1, max: 10, percentage: 1 },
            customer: { percentage: 1, amount: 1 },
          },
          creditCardVisa: {
            merchant: { percentage: 1, amount: 1 },
            customer: { percentage: 2, amount: 2 },
          },
          debitCardRegulated: {
            merchant: { percentage: 1, amount: 2 },
            customer: { percentage: 2, amount: 1 },
          },
          debitCardUnregulated: {
            merchant: { percentage: 2, amount: 3 },
            customer: { percentage: 3, amount: 2 },
          },
          creditCardMasterCard2: {
            merchant: { percentage: 3, amount: 4 },
            customer: { percentage: 4, amount: 3 },
          },
          creditCardVisa2: {
            merchant: { percentage: 8, amount: 9 },
            customer: { percentage: 9, amount: 8 },
          },
          creditCardTravel: {
            merchant: { percentage: 4, amount: 5 },
            customer: { percentage: 5, amount: 4 },
          },
          creditCardBusiness: {
            merchant: { percentage: 5, amount: 6 },
            customer: { percentage: 6, amount: 5 },
          },
          amex: {
            merchant: { percentage: 6, amount: 7 },
            customer: { percentage: 7, amount: 6 },
          },
        },
      })
      const { constructor1 } = FakeUsers

      const { supplier_id, inv_id } = await createInvoice(100)
      // TODO: This one has no effect something is wrong with pricing package for ACH
      await CardPricingPackage.create({
        name: 'A',
        metadata: {
          ach: { merchant: { min: 1, max: 25, percentage: 0.5, amount: 0 } },
        },
      })
      const mockAuth = _Authorization(constructor1.auth)
      let resp = await routes.company.info()
      resp = await routes.company.bankAccounts()
      const account = resp.bankAccounts[0]

      mailSend.callCount.should.equal(5)

      resp = await routes2.invoice.pay({
        invoiceIds: [inv_id],
        paymentMethod: 'ach',
        accountId: account._id,
        account_id: account._id,
      })
      resp.result.should.be.equal('ok')
      const invoice = await Invoice.findById(inv_id).populate('operation')
      invoice!.operation!.metadata.payee_id!.should.equal(supplier_id)
      mockAuth.restore()
    })

    it('should pay multiple with pricing package', async () => {
      async function placeInvoice(
        amount: number,
        invoiceData: Partial<IInvoice> = {},
      ) {
        if (!invoiceData.customer_account_id) {
          invoiceData.customer_account_id = customer_account_id
        }

        const { supplier1 } = FakeUsers
        const mockAuth = _Authorization(supplier1.auth)

        const resp = await routes.invoices.saveInvoice({
          status: dictionaries.invoiceSchemaStatus.placed,
          material_subtotal: amount,
          total_amount: amount,
          invoice_date: new Date().toISOString(),
          ...invoiceData,
        })

        mockAuth.restore()

        const inv_id = resp.id
        const op = await Operation.findOne({ owner_id: inv_id })
        op!.type.should.equal(dictionaries.OPERATION_TYPES.INVOICE.PAYMENT)

        return {
          supplier_id,
          inv_id,
          customer_account_id: invoiceData.customer_account_id,
        }
      }

      await CardProducts.create({
        productCode: 'G',
        productName: 'Visa Business',
        personalOrBusiness: 'business',
        cardType: 'credit',
        isbpsp: true,
        isl2eligible: true,
        isl3eligible: true,
        isTravel: false,
      })
      await CardPricingPackage.create({
        name: 'A',
        metadata: {
          ach: {
            merchant: { min: 10, max: 20, percentage: 2 },
            customer: { percentage: 1, amount: 1 },
          },
        },
      })
      const { constructor1 } = FakeUsers

      const supplier_id = await createSupplier(undefined, undefined, 5, false)
      await createBuilder()
      const customer_account_id = await createCustomer()

      const { inv_id: inv1_id } = await placeInvoice(100, {
        company_id: supplier_id,
        customer_account_id,
      })
      const { inv_id: inv2_id } = await placeInvoice(200, {
        company_id: supplier_id,
        customer_account_id,
      })
      const { inv_id: inv3_id } = await placeInvoice(300, {
        company_id: supplier_id,
        customer_account_id,
      })

      // TODO: This one has no effect something is wrong with pricing package for ACH
      await CardPricingPackage.create({
        name: 'A',
        metadata: {
          ach: { merchant: { min: 1, max: 25, percentage: 0.5, amount: 0 } },
        },
      })
      const mockAuth = _Authorization(constructor1.auth)
      let resp = await routes.company.info()
      const constructor_id = resp.company.id

      resp = await routes.company.bankAccounts()
      const account = resp.bankAccounts[0]
      resp = await routes2.invoice.pay({
        invoiceIds: [inv1_id, inv2_id, inv3_id],
        paymentMethod: 'ach',
        accountId: account._id,
        account_id: account._id,
      })
      resp.result.should.be.equal('ok')

      feeDetails[0].amount = 11.4
      const pay1 = new PayablesDetail(
        inv1_id,
        EPaymentType.invoice,
        100,
        100,
        5,
      )
      const pay2 = new PayablesDetail(
        inv2_id,
        EPaymentType.invoice,
        200,
        200,
        10,
      )
      const pay3 = new PayablesDetail(
        inv3_id,
        EPaymentType.invoice,
        300,
        300,
        15,
      )
      paymentServicePayload.blueTapeCorrelationId = constructor_id
      paymentServicePayload.details.requestedAmount = 600
      paymentServicePayload.details.payablesDetails = [pay1, pay2, pay3]
      paymentServicePayload.details.customerDetails = {
        id: constructor_id,
        name: 'Contractor name',
        accountId: account._id,
      }
      paymentServicePayload.details.sellerDetails!.companyId = supplier_id
      azureSendServiceBusMessage.callCount.should.equal(1)
      const dets = azureSendServiceBusMessage.getCall(0).args[2].details
      paymentServicePayload.details.date = dets.date
      paymentServicePayload.details.sellerDetails!.paymentSettings.merchantAchDelayDays = 2
      paymentServicePayload.details.discountDetails = [
        new DiscountDetail(TEST_ID, 30, EDiscountType.achEarlyPaymentDiscount),
      ]
      chai
        .expect(azureSendServiceBusMessage.getCall(0).args[2])
        .to.deep.equal(paymentServicePayload)
      mockAuth.restore()
    })

    it('should pay invoice memo with pricing package', async () => {
      await CardPricingPackage.create({
        name: 'A',
        metadata: {
          ach: {
            merchant: { min: 1, max: 10, percentage: 1 },
            customer: { percentage: 1, amount: 1 },
          },
        },
      })
      const { constructor1 } = FakeUsers

      const {
        supplier_id,
        inv_id: inv_id1,
        customer_account_id,
      } = await createInvoice(250, undefined, undefined, {
        invoice_number: 'inv1234',
      })
      const { inv_id: inv_id2 } = await createInvoice(
        -20,
        undefined,
        undefined,
        { customer_account_id },
      )
      const mockAuth = _Authorization(constructor1.auth)
      let resp = await routes.company.info()
      const constructor_id = resp.company.id

      resp = await routes.company.bankAccounts()
      const account = resp.bankAccounts[0]
      resp = await routes2.invoice.pay({
        invoiceIds: [inv_id1, inv_id2],
        paymentMethod: 'ach',
        accountId: account._id,
        account_id: account._id,
      })
      resp.result.should.be.equal('ok')

      feeDetails[0].amount = 2.3
      const pay1 = new PayablesDetail(
        inv_id1,
        EPaymentType.invoice,
        250,
        250,
        0,
      )
      const pay2 = new PayablesDetail(
        inv_id2,
        EPaymentType.invoice,
        -20,
        -20,
        0,
      )
      paymentServicePayload.details.payablesDetails = [pay1, pay2]
      paymentServicePayload.details.requestedAmount = 230
      paymentServicePayload.blueTapeCorrelationId = constructor_id
      paymentServicePayload.details.customerDetails = {
        id: constructor_id,
        name: 'Contractor name',
        accountId: account._id,
      }
      paymentServicePayload.details.sellerDetails!.companyId = supplier_id
      azureSendServiceBusMessage.callCount.should.equal(1)
      const dets = azureSendServiceBusMessage.getCall(0).args[2].details
      paymentServicePayload.details.date = dets.date
      _.unset(paymentServicePayload, 'details.discountDetails')
      paymentServicePayload.details.sellerDetails!.paymentSettings.merchantAchDelayDays = 0
      chai
        .expect(azureSendServiceBusMessage.getCall(0).args[2])
        .to.deep.equal(paymentServicePayload)
      mockAuth.restore()
    })

    it('should pay invoice memo with pricing package (discount)', async () => {
      await CardPricingPackage.create({
        name: 'A',
        metadata: {
          ach: {
            merchant: { min: 1, max: 10, percentage: 1 },
            customer: { percentage: 0, amount: 0 },
          },
        },
      })
      const { constructor1 } = FakeUsers

      const {
        supplier_id,
        inv_id: inv_id1,
        customer_account_id,
      } = await createInvoice(
        250,
        undefined,
        undefined,
        {
          invoice_number: 'inv1234',
        },
        5,
        false,
      )
      const { inv_id: inv_id2 } = await createInvoice(
        -20,
        undefined,
        undefined,
        { customer_account_id },
        5,
        false,
      )
      const mockAuth = _Authorization(constructor1.auth)
      let resp = await routes.company.info()
      const constructor_id = resp.company.id

      resp = await routes.company.bankAccounts()
      const account = resp.bankAccounts[0]
      resp = await routes2.invoice.pay({
        invoiceIds: [inv_id1, inv_id2],
        paymentMethod: 'ach',
        accountId: account._id,
        account_id: account._id,
      })
      resp.result.should.be.equal('ok')

      feeDetails[0].amount = 2.18
      const pay1 = new PayablesDetail(
        inv_id1,
        EPaymentType.invoice,
        250,
        250,
        12.5,
      )
      const pay2 = new PayablesDetail(
        inv_id2,
        EPaymentType.invoice,
        -20,
        -20,
        0,
      )
      paymentServicePayload.details.payablesDetails = [pay1, pay2]
      paymentServicePayload.details.requestedAmount = 230
      paymentServicePayload.blueTapeCorrelationId = constructor_id
      paymentServicePayload.details.customerDetails = {
        id: constructor_id,
        name: 'Contractor name',
        accountId: account._id,
      }
      paymentServicePayload.details.discountDetails = [
        new DiscountDetail(
          TEST_ID,
          12.5,
          EDiscountType.achEarlyPaymentDiscount,
        ),
      ]
      paymentServicePayload.details.sellerDetails!.companyId = supplier_id
      paymentServicePayload.details.sellerDetails!.paymentSettings.merchantAchDelayDays = 2
      azureSendServiceBusMessage.callCount.should.equal(1)
      const dets = azureSendServiceBusMessage.getCall(0).args[2].details
      paymentServicePayload.details.date = dets.date
      chai
        .expect(azureSendServiceBusMessage.getCall(0).args[2])
        .to.deep.equal(paymentServicePayload)
      mockAuth.restore()
    })

    it.skip('should pay multiple with fee rounding', async () => {
      async function placeInvoice(
        amount: number,
        invoiceData: Partial<IInvoice> = {},
      ) {
        if (!invoiceData.customer_account_id) {
          invoiceData.customer_account_id = customer_account_id
        }

        const { supplier1 } = FakeUsers
        const mockAuth = _Authorization(supplier1.auth)

        const resp = await routes.invoices.saveInvoice({
          status: dictionaries.invoiceSchemaStatus.placed,
          material_subtotal: amount,
          total_amount: amount,
          invoice_date: new Date().toISOString(),
          ...invoiceData,
        })

        mockAuth.restore()

        const inv_id = resp.id
        const op = await Operation.findOne({ owner_id: inv_id })
        op!.type.should.equal(dictionaries.OPERATION_TYPES.INVOICE.PAYMENT)

        return {
          supplier_id,
          inv_id,
          customer_account_id: invoiceData.customer_account_id,
        }
      }

      await CardProducts.create({
        productCode: 'G',
        productName: 'Visa Business',
        personalOrBusiness: 'business',
        cardType: 'credit',
        isbpsp: true,
        isl2eligible: true,
        isl3eligible: true,
        isTravel: false,
      })
      await CardPricingPackage.create({
        name: 'A',
        metadata: {
          ach: {
            merchant: { min: 1, max: 10, percentage: 1 },
            customer: { percentage: 1, amount: 1 },
          },
        },
      })
      const { constructor1 } = FakeUsers

      const supplier_id = await createSupplier()
      await createBuilder()
      const customer_account_id = await createCustomer()

      const { inv_id: inv1_id } = await placeInvoice(100)
      const { inv_id: inv2_id } = await placeInvoice(70)
      const { inv_id: inv3_id } = await placeInvoice(30)

      const { inv_id: inv4_id } = await placeInvoice(60)
      const { inv_id: inv5_id } = await placeInvoice(40)
      const { inv_id: inv6_id } = await placeInvoice(30)

      const mockAuth = _Authorization(constructor1.auth)

      let resp = await routes.company.info()
      const constructor_id = resp.company.id
      resp = await routes.company.bankAccounts()
      const account = resp.bankAccounts[0]

      resp = await routes2.invoice.pay({
        invoiceIds: [inv1_id, inv2_id, inv3_id],
        paymentMethod: 'ach',
        accountId: account._id,
        account_id: account._id,
      })
      resp.result.should.be.equal('ok')

      resp = await routes2.invoice.pay({
        invoiceIds: [inv4_id, inv5_id, inv6_id],
        paymentMethod: 'ach',
        accountId: account._id,
        account_id: account._id,
      })
      resp.result.should.be.equal('ok')

      mockAuth.restore()

      const invoice = await Invoice.findById(inv1_id).populate('operation')
      const invoice2 = await Invoice.findById(inv2_id).populate('operation')
      const invoice3 = await Invoice.findById(inv3_id).populate('operation')
      const transaction = await Transaction.findOne({
        operation_id: invoice!.operation!.id,
        'metadata.transactionType': ACH_TRANSACTION_TYPE.OUT,
      })
      const transaction2 = await Transaction.findOne({
        operation_id: invoice2!.operation!.id,
        'metadata.transactionType': ACH_TRANSACTION_TYPE.OUT,
      })
      const transaction3 = await Transaction.findOne({
        operation_id: invoice3!.operation!.id,
        'metadata.transactionType': ACH_TRANSACTION_TYPE.OUT,
      })

      invoice!.operation!.metadata.payee_id!.should.equal(supplier_id)
      invoice!.operation!.metadata.payer_id!.should.equal(constructor_id)
      invoice!.operation!.metadata.achDiscount!.should.equal(0)

      transaction!.payee_id.should.equal(supplier_id)
      transaction!.amount.should.equal(99.34)
      transaction!.fee.should.equal(0.66)

      invoice2!.operation!.metadata.payee_id!.should.equal(supplier_id)
      invoice2!.operation!.metadata.payer_id!.should.equal(constructor_id)
      invoice2!.operation!.metadata.achDiscount!.should.equal(0)

      transaction2!.payee_id.should.equal(supplier_id)
      transaction2!.amount.should.equal(69.33)
      transaction2!.fee.should.equal(0.67)

      invoice3!.operation!.metadata.payee_id!.should.equal(supplier_id)
      invoice3!.operation!.metadata.payer_id!.should.equal(constructor_id)
      invoice3!.operation!.metadata.achDiscount!.should.equal(0)

      transaction3!.payee_id.should.equal(supplier_id)
      transaction3!.amount.should.equal(29.33)
      transaction3!.fee.should.equal(0.67)

      const invoice4 = await Invoice.findById(inv4_id).populate('operation')
      const invoice5 = await Invoice.findById(inv5_id).populate('operation')
      const invoice6 = await Invoice.findById(inv6_id).populate('operation')
      const transaction4 = await Transaction.findOne({
        operation_id: invoice4!.operation!.id,
        'metadata.transactionType': ACH_TRANSACTION_TYPE.OUT,
      })
      const transaction5 = await Transaction.findOne({
        operation_id: invoice5!.operation!.id,
        'metadata.transactionType': ACH_TRANSACTION_TYPE.OUT,
      })
      const transaction6 = await Transaction.findOne({
        operation_id: invoice6!.operation!.id,
        'metadata.transactionType': ACH_TRANSACTION_TYPE.OUT,
      })

      invoice4!.operation!.metadata.payee_id!.should.equal(supplier_id)
      invoice4!.operation!.metadata.payer_id!.should.equal(constructor_id)
      invoice4!.operation!.metadata.achDiscount!.should.equal(0)

      transaction4!.payee_id.should.equal(supplier_id)
      transaction4!.amount.should.equal(59.56)
      transaction4!.fee.should.equal(0.44)

      invoice5!.operation!.metadata.payee_id!.should.equal(supplier_id)
      invoice5!.operation!.metadata.payer_id!.should.equal(constructor_id)
      invoice5!.operation!.metadata.achDiscount!.should.equal(0)

      transaction5!.payee_id.should.equal(supplier_id)
      transaction5!.amount.should.equal(39.57)
      transaction5!.fee.should.equal(0.43)

      invoice6!.operation!.metadata.payee_id!.should.equal(supplier_id)
      invoice6!.operation!.metadata.payer_id!.should.equal(constructor_id)
      invoice6!.operation!.metadata.achDiscount!.should.equal(0)

      transaction6!.payee_id.should.equal(supplier_id)
      transaction6!.amount.should.equal(29.57)
      transaction6!.fee.should.equal(0.43)
    })
  })

  describe('Card Payment', () => {
    beforeEachFirebase()
    beforeEachMockEncryption()
    beforeEachMockSecretsManager()
    beforeEachMockEventBridgeClient()
    beforeEachMockSNS()
    beforeEachMockSQS()
    const tabapayBaseUrl = 'https://api.sandbox.tabapay.net:10443'
    const { constructor1 } = FakeUsers

    process.env.LP_TABAPAY_CLIENT_ID = 'test-client-id'
    process.env.LP_TABAPAY_BEARER_TOKEN = 'test-token'
    process.env.LP_TABAPAY_SETTLEMENT_ACCOUNT_ID = 'test-settlement-account-id'
    process.env.LP_TABAPAY_MID_NO_CONVENIENCE_FEE = 'test-mid-0001'
    process.env.LP_TABAPAY_MID_WITH_CONVENIENCE_FEE = 'test-mid-0002'

    it('should throw error when operation status is not PLACED', async () => {
      const mockAuth = _Authorization(constructor1.auth)
      const {
        supplierCompany,
        customerAccount,
        invoice,
        operation,
        creditCardAccId,
      } = await createCardPaymentTestData()

      operation.status = dictionaries.OPERATION_STATUS.SUCCESS
      await operation.save()

      const response = await routes2.invoice.pay({
        invoiceIds: [invoice._id.toString()],
        paymentMethod: 'card',
        account_id: creditCardAccId.toString(),
        accountId: creditCardAccId.toString(),
      })
      response.should.have.property('result')
      response.result!.should.be.equal('LogicalError')
      response.should.have.property('message')
      response.message!.should.be.equal('Invoice already paid')

      // cleanup mongodb records
      await Company.findByIdAndDelete(supplierCompany._id.toString())
      await CustomerAccount.findByIdAndDelete(customerAccount._id.toString())
      await Invoice.findByIdAndDelete(invoice._id.toString())
      await Operation.findByIdAndDelete(operation._id.toString())

      mockAuth.restore()
    })

    it('should throw error when operation does not have payee id', async () => {
      const mockAuth = _Authorization(constructor1.auth)
      const {
        supplierCompany,
        customerAccount,
        invoice,
        operation,
        creditCardAccId,
      } = await createCardPaymentTestData()

      await operation.updateOne({ 'metadata.payee_id': '' })

      const response = await routes2.invoice.pay({
        invoiceIds: [invoice._id.toString()],
        paymentMethod: 'card',
        account_id: creditCardAccId.toString(),
        accountId: creditCardAccId.toString(),
      })
      response.should.have.property('result')
      response.result!.should.be.equal('LogicalError')
      response.should.have.property('code')
      response.code!.should.contain('operation/payee-or-payer-missing')

      // cleanup mongodb records
      await Company.findByIdAndDelete(supplierCompany._id.toString())
      await CustomerAccount.findByIdAndDelete(customerAccount._id.toString())
      await Invoice.findByIdAndDelete(invoice._id.toString())
      await Operation.findByIdAndDelete(operation._id.toString())

      mockAuth.restore()
    })

    it("should throw error when tabapay's pull transaction fails", async () => {
      const mockAuth = _Authorization(constructor1.auth)
      const {
        supplierCompany,
        customerAccount,
        invoice,
        operation,
        creditCardAccId,
      } = await createCardPaymentTestData()

      // mock transaction api call
      const createTransactionErrorResponse = {
        SC: 429,
        EC: 'EC149DFG',
        EM: 'tabapay transaction failed',
      }
      nock(tabapayBaseUrl)
        .post((uri) => uri.includes('transactions'), /"type":"pull"/i)
        .reply(429, createTransactionErrorResponse)

      const response = await routes2.invoice.pay({
        invoiceIds: [invoice._id.toString()],
        paymentMethod: 'card',
        account_id: creditCardAccId.toString(),
        accountId: creditCardAccId.toString(),
      })

      response.should.have.property('result')
      response.result!.should.be.equal('LogicalError')

      // cleanup mongodb records
      await Company.findByIdAndDelete(supplierCompany._id.toString())
      await CustomerAccount.findByIdAndDelete(customerAccount._id.toString())
      await Invoice.findByIdAndDelete(invoice._id.toString())
      await Operation.findByIdAndDelete(operation._id.toString())

      mockAuth.restore()
    })

    it('should process visa and mastercard payments with fees successfully', async () => {
      const mockAuth = _Authorization(constructor1.auth)
      const {
        supplierCompany,
        customerAccount,
        invoice,
        operation,
        creditCardAccId,
      } = await createCardPaymentTestData()

      // mock transaction api call
      const createTransactionSuccessResponse = {
        SC: 201,
        transactionID: 'adfhgs98sdf89',
        network: 'Visa',
        networkRC: '00',
        status: 'COMPLETED',
        approvalCode: '000000',
        AVS: {
          codeAVS: 'Y',
          codeSecurityCode: 'M',
        },
        fees: {
          interchange: '0.25',
          network: '0.5',
          tabapay: '0.4',
        },
        card: {
          last4: '1111',
          expirationDate: '202202',
        },
      }

      nock(tabapayBaseUrl)
        .post((uri) => uri.includes('transactions'), /"type":"pull"/i)
        .reply(200, createTransactionSuccessResponse)

      const response = await routes2.invoice.pay({
        invoiceIds: [invoice._id.toString()],
        paymentMethod: 'card',
        account_id: creditCardAccId.toString(),
        accountId: creditCardAccId.toString(),
      })
      response.should.have.property('result')
      response.result!.should.be.equal('ok')

      // cleanup mongodb records
      await Company.findByIdAndDelete(supplierCompany._id.toString())
      await CustomerAccount.findByIdAndDelete(customerAccount._id.toString())
      await Invoice.findByIdAndDelete(invoice._id.toString())
      await Operation.findByIdAndDelete(operation._id.toString())

      mockAuth.restore()
    })

    it('should process amex payments successfully', async () => {
      const mockAuth = _Authorization(constructor1.auth)
      const {
        supplierCompany,
        customerAccount,
        invoice,
        operation,
        creditCardAccId,
      } = await createCardPaymentTestData()

      // mock transaction api call
      const createTransactionSuccessResponse = {
        SC: 201,
        transactionID: 'adfhgs98sdf89',
        network: 'Visa',
        networkRC: '00',
        status: 'COMPLETED',
        approvalCode: '000000',
        AVS: {
          codeAVS: 'Y',
        },
        card: {
          last4: '1111',
          expirationDate: '202202',
        },
      }

      nock(tabapayBaseUrl)
        .post((uri) => uri.includes('transactions'), /"type":"pull"/i)
        .reply(200, createTransactionSuccessResponse)

      const response = await routes2.invoice.pay({
        invoiceIds: [invoice._id.toString()],
        paymentMethod: 'card',
        account_id: creditCardAccId.toString(),
        accountId: creditCardAccId.toString(),
      })
      response.should.have.property('result')
      response.result!.should.be.equal('ok')

      // cleanup mongodb records
      await Company.findByIdAndDelete(supplierCompany._id.toString())
      await CustomerAccount.findByIdAndDelete(customerAccount._id.toString())
      await Invoice.findByIdAndDelete(invoice._id.toString())
      await Operation.findByIdAndDelete(operation._id.toString())

      mockAuth.restore()
    })
  })

  describe('Card Refund', () => {
    // mock authorization
    beforeEachFirebase()
    beforeEachMockEncryption()
    const { supplier1, constructor1 } = FakeUsers

    let mockAuth: ReturnType<typeof _Authorization>, aiMock: sinon.SinonStub
    let supplierCompany: ICompany,
      customerAccount: ICustomerAccount,
      invoice: IInvoice,
      operation: IOperation
    beforeEach(async () => {
      process.env.LP_CBW_ACH_IDENTIFICATION = '*********'
      mockAuth = _Authorization(supplier1.auth)
      generateUniqueSubStub.resolves(supplier1.info.sub)

      const bankAccount = await BankAccount.create({
        accountNumber: {
          cipher: await crypt.encrypt('**********'),
          hash: md5('**********').toString(),
          display: '******7890',
        },
        isPrimary: true,
        name: 'GreatBank',
        routingNumber: '********',
        accountType: 'checking',
        paymentMethodType: 'bank',
      })

      supplierCompany = await Company.create({
        name: 'Test supplier',
        status: 'approved',
        bankAccounts: [new mongoose.Types.ObjectId(bankAccount.id)],
        email: supplier1.info.email,
        login: supplier1.info.email,
        phone: '+***********',
        address: {
          address: '100 Main St',
          city: 'San Francisco',
          zip: '94061',
          state: 'CA',
        },
        ein: {
          cipher: await crypt.encrypt('**********'),
          hash: md5('**********').toString(),
          display: '******7890',
        },
      })
      await UserRole.create({
        company_id: supplierCompany.id,
        sub: supplier1.info.sub,
      })
      customerAccount = await CustomerAccount.create({
        company_id: supplierCompany._id,
        phone: constructor1.info.phone_number,
      })

      invoice = await Invoice.create({
        status: 'PLACED',
        total_amount: 10.0,
        customer_account_id: customerAccount._id,
        company_id: supplierCompany._id,
      })

      operation = await Operation.create({
        owner_id: invoice._id,
        amount: 10,
        metadata: {
          payee_id: supplierCompany._id.toString(),
        },
        status: 'PLACED',
        type: 'invoice_payment',
      })
      const achApi = initCbwApiRequester()
      aiMock = sinon.stub(achApi, 'post').callsFake((path, { payload }) => {
        return Promise.resolve({
          transactionNumber: '123',
          transactionAmountCents: parseInt(payload.transactionAmount.amount),
          api: {
            reference: '123',
            dateTime: '',
            originalReference: payload.reference,
          },
        })
      })
    })

    afterEach(async () => {
      await Company.findByIdAndDelete(supplierCompany._id.toString())
      await CustomerAccount.findByIdAndDelete(customerAccount._id.toString())
      await Invoice.findByIdAndDelete(invoice._id.toString())
      await Operation.findByIdAndDelete(operation._id.toString())

      mockAuth.restore()
      aiMock.restore()
    })

    it('should throw error when operation status is not SUCCESS', async () => {
      const response = await routes.invoices.refundAmount(
        invoice._id.toString(),
        operation._id.toString(),
        10.0,
      )
      response.should.have.property('result')
      response.result.should.be.equal('LogicalError')
      response.should.have.property('message')
      response.message.should.be.equal('Operation cannot be refunded')
    })

    it('should throw error when operation does not have any associated transactions', async () => {
      operation.status = 'SUCCESS'
      await operation.save()

      const response = await routes.invoices.refundAmount(
        invoice._id.toString(),
        operation._id.toString(),
        10.0,
      )
      response.should.have.property('result')
      response.result.should.be.equal('LogicalError')
      response.should.have.property('message')
      response.message.should.be.equal('Operation cannot be refunded')
    })

    it('should throw error when refundable amount is 0', async () => {
      operation.status = 'SUCCESS'
      await operation.save()

      // create transaction and associate it with operation
      await Transaction.create({
        amount: operation.amount,
        operation_id: operation._id,
        status: dictionaries.TRANSACTION_STATUS.PENDING,
        type: dictionaries.TRANSACTION_TYPES.ACH.TRANSFER,
        payment_method: dictionaries.PAYMENT_METHODS.CARD,
      })

      // create another operation for refund
      await Operation.create({
        owner_id: invoice._id,
        amount: 10,
        metadata: {
          payee_id: supplierCompany._id.toString(),
        },
        status: 'SUCCESS',
        type: 'invoice_refund',
      })

      const response = await routes.invoices.refundAmount(
        invoice._id.toString(),
        operation._id.toString(),
        10.0,
      )
      response.should.have.property('result')
      response.result.should.be.equal('LogicalError')
      response.should.have.property('message')
      response.message.should.be.equal('Operation totally refunded')
    })

    it('should throw error when refundable amount is less than refund amount', async () => {
      operation.status = 'SUCCESS'
      await operation.save()

      // create transaction and associate it with operation
      await Transaction.create({
        amount: operation.amount,
        operation_id: operation._id,
        status: dictionaries.TRANSACTION_STATUS.PENDING,
        type: dictionaries.TRANSACTION_TYPES.ACH.TRANSFER,
        payment_method: dictionaries.PAYMENT_METHODS.CARD,
      })

      const response = await routes.invoices.refundAmount(
        invoice._id.toString(),
        operation._id.toString(),
        20.0,
      )
      response.should.have.property('result')
      response.result.should.be.equal('LogicalError')
      response.should.have.property('message')
      response.message.should.be.equal(
        'Partial refund amount cannot be more than original payment',
      )
    })

    it('should throw error when supplier company does not have a primary bank account', async () => {
      operation.status = 'SUCCESS'
      await operation.save()

      // create transaction and associate it with operation
      await Transaction.create({
        amount: operation.amount,
        operation_id: operation._id,
        status: dictionaries.TRANSACTION_STATUS.PENDING,
        type: dictionaries.TRANSACTION_TYPES.ACH.TRANSFER,
        payment_method: dictionaries.PAYMENT_METHODS.CARD,
      })

      supplierCompany.bankAccounts = []
      await supplierCompany.save()

      const response = await routes.invoices.refundAmount(
        invoice._id.toString(),
        operation._id.toString(),
        5.0,
      )
      response.should.have.property('result')
      response.result.should.be.equal('LogicalError')
      response.should.have.property('message')
      response.message.should.be.equal(
        `supplier ${supplierCompany.name} does not have a primary bank account`,
      )
    })

    it('should process card refund successfully', async () => {
      operation.status = 'SUCCESS'
      await operation.save()

      // create transaction and associate it with operation
      await Transaction.create({
        amount: operation.amount,
        operation_id: operation._id,
        status: dictionaries.TRANSACTION_STATUS.PENDING,
        type: dictionaries.TRANSACTION_TYPES.ACH.TRANSFER,
        payment_method: dictionaries.PAYMENT_METHODS.CARD,
      })

      const response = await routes.invoices.refundAmount(
        invoice._id.toString(),
        operation._id.toString(),
        10.0,
      )
      response.should.have.property('result')
      response.result.should.be.equal('ok')
      response.should.have.property('id')
    })
  })

  it('get action required invoices (list of invoices with due and pastDue status for builder)', async () => {
    const { constructor1 } = FakeUsers
    mockFirebaseAuth = _FirebaseTokenVerifier()
    const auth = _Authorization(constructor1.auth)
    const acc = await CustomerAccount.create({
      phone: constructor1.info.phone_number,
      email: '<EMAIL>',
    })

    await User.create({
      sub: constructor1.info.sub,
      firebaseId: constructor1.info.firebaseId,
      email: '<EMAIL>',
      login: constructor1.info.phone_number,
      phone: constructor1.info.phone_number,
    })
    const com = await Company.create({
      name: 'Test supplier',
      type: 'supplier',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
      settings: { cardPricingPackageId: 'optOut', loanPricingPackageId: '' },
    })
    const invoice1 = await Invoice.create({
      customer_account_id: acc._id,
      company_id: com.id,
      expiration_date: moment().add(10, 'days').format('MM/DD/YYYY'),
      invoice_due_date: moment().add(10, 'days').format('MM/DD/YYYY'),
      status: 'PLACED',
    })
    const invoice2 = await Invoice.create({
      customer_account_id: acc._id,
      company_id: com.id,
      expiration_date: moment().add(2, 'weeks').format(),
      invoice_due_date: moment().subtract(2, 'days').format(),
      status: 'PLACED',
    })
    await Operation.create({
      owner_id: invoice1._id,
      amount: 100,
      metadata: {
        payee_id: acc._id.toString(),
      },
      status: 'PLACED',
      type: 'invoice_payment',
    })
    await Operation.create({
      owner_id: invoice2._id,
      amount: 100,
      metadata: {
        payee_id: acc._id.toString(),
      },
      status: 'PLACED',
      type: 'invoice_payment',
    })
    sinon.stub(admin.auth(), 'getUserByPhoneNumber').callsFake(() =>
      Promise.resolve({
        phoneNumber: constructor1.info.phone_number,
      } as unknown as firebaseAuth.UserRecord),
    )

    const company = await Company.create({
      name: 'Test supplier',
      type: 'contractor',
      bankAccounts: [],
      email: '<EMAIL>',
      phone: '+***********',
      address: {
        address: '100 Main St',
        city: 'San Francisco',
        zip: '94061',
        state: 'CA',
      },
      ein: {
        cipher: await crypt.encrypt('**********'),
        hash: md5('**********').toString(),
        display: '******7890',
      },
    })
    await UserRole.create({
      sub: constructor1.info.sub,
      company_id: company._id,
      role: 'Owner',
    })

    const resp = await routes.invoices.list({ actionRequired: true })

    resp.should.have.property('invoices')
    resp.invoices.length.should.equal(2)
    resp.invoices[0].status.should.equal('PASTDUE')
    resp.invoices[1].status.should.equal('DUE')

    auth.restore()
    mockFirebaseAuth.restore()
  })
})
describe('get transactions for an invoice', async () => {
  async function createInvoice(amount: number) {
    const { constructor1, supplier1 } = FakeUsers
    let mockAuth = _Authorization(supplier1.auth)
    let resp = await routes.company.updateInfo({
      name: 'Mel',
      address: {
        address: 'Some street',
        city: 'Kansas',
        zip: '11111',
        state: 'KS',
      },
      phone: '**********',
      email: '<EMAIL>',
      ein: '**********',
    })
    const supplier_id = resp.id
    await Company.findOneAndUpdate({ _id: supplier_id }, { status: 'approved' })
    resp = await routes.supplier.saveAccount(
      {
        phone: constructor1.info.phone_number,
      },
      true,
    )
    const customer_account_id = resp.id
    resp = await routes.invoices.saveInvoice({
      status: 'PLACED',
      material_subtotal: amount,
      total_amount: amount,
      customer_account_id,
    })
    const inv_id = resp.id
    const op = await Operation.findOne({ owner_id: inv_id })
    op!.type.should.equal(dictionaries.OPERATION_TYPES.INVOICE.PAYMENT)
    mockAuth.restore()

    mockAuth = _Authorization(constructor1.auth)
    resp = await routes2.user.updateInfo({
      firstName: 'John',
      lastName: 'Doe',
      phone: '**********',
      email: '<EMAIL>',
      addresses: [
        { city: 'Kansas', zip: '11111', address: 'Some street', state: 'KS' },
      ],
    })
    resp.result.should.be.equal('ok')
    mockAuth.restore()

    return { supplier_id, inv_id }
  }

  beforeEachFirebase()
  beforeEachMockEncryption()
  beforeEachSms()
  beforeEachSendGrid()
  beforeEachReferralRock()
  beforeEach(async () => {
    obsMock = sinon
      .stub(onBoardingService, 'getCreditApplications')
      .resolves([])
  })
  afterEach(() => {
    obsMock.restore()
  })
  it('should not fetch for the wrong supplier', async () => {
    const { supplier2 } = FakeUsers
    const { inv_id: invoiceId } = await createInvoice(10)

    const mockAuth = _Authorization(supplier2.auth)
    const resp = await routes.transactions.allTransactions({ invoiceId })
    resp.result.should.eq('LogicalError')
    mockAuth.restore()
  })
  it('should not fetch for the wrong contractor', async () => {
    const { constructor2 } = FakeUsers
    const { inv_id: invoiceId } = await createInvoice(10)

    const mockAuth = _Authorization(constructor2.auth)
    const resp = await routes.transactions.allTransactions({ invoiceId })
    resp.result.should.eq('LogicalError')
    mockAuth.restore()
  })
  it('should fetch for the correct supplier', async () => {
    const { constructor1 } = FakeUsers
    const { inv_id: invoiceId } = await createInvoice(10)

    const mockAuth = _Authorization(constructor1.auth)
    const resp = await routes.transactions.allTransactions({ invoiceId })
    resp.should.have.property('items')
    resp.should.have.property('count')
    mockAuth.restore()
  })
  it('should fetch for the correct contractor', async () => {
    const { supplier1 } = FakeUsers
    const { inv_id: invoiceId } = await createInvoice(10)

    const mockAuth = _Authorization(supplier1.auth)
    const resp = await routes.transactions.allTransactions({ invoiceId })
    resp.should.have.property('count')
    resp.should.have.property('items')
    mockAuth.restore()
  })
})

const createCardPaymentTestData = async () => {
  const { constructor1 } = FakeUsers
  const creditCard = await BankAccount.create({
    accountNumber: {
      display: '********1111',
    },
    cardMetadata: {
      accountId: 'ASASx6587ghkjj',
      avsAuthorizeID: '234',
      avsCode: 'Y',
      avsNetworkRC: '44',
      avsResultText: 'NOT DECLINED',
      avsSecurityCode: 'M',
      expirationDate: '202303',
      isPullEnabled: true,
      isRegulated: true,
      lastFour: '1111',
      network: 'Visa',
      token: '80E1iEMJ243WsTF0pBM',
      type: 'Credit',
    },
    name: 'Visa - Card',
    paymentMethodType: 'card',
    isManualEntry: true,
    accountholderName: 'Test Card 77',
    billingAddress: {
      addressLine1: 'Traction Street',
      addressLine2: '3094 ',
      city: 'Spartanburg',
      stateCode: 'South Carolina',
      zipCode: '29303',
    },
  })
  const company = await Company.create({
    name: 'Test supplier',
    type: 'contractor',
    bankAccounts: [creditCard._id],
    email: '<EMAIL>',
    phone: '+***********',
    address: {
      address: '100 Main St',
      city: 'San Francisco',
      zip: '94061',
      state: 'CA',
    },
    ein: {
      cipher: await crypt.encrypt('**********'),
      hash: md5('**********').toString(),
      display: '******7890',
    },
  })
  await UserRole.create({
    sub: constructor1.info.sub,
    company_id: company._id,
    role: 'Owner',
  })

  await CardPricingPackage.create({
    name: 'packageA',
    tabapayMID: 'tabapay-test-mid',
    metadata: {
      ach: {
        merchant: { min: 1, max: 10, percentage: 1 },
        customer: { percentage: 1, amount: 1 },
      },
      creditCardVisa: {
        merchant: { percentage: 1, amount: 1 },
        customer: { percentage: 2, amount: 2 },
      },
      debitCardRegulated: {
        merchant: { percentage: 1, amount: 2 },
        customer: { percentage: 2, amount: 1 },
      },
      debitCardUnregulated: {
        merchant: { percentage: 2, amount: 3 },
        customer: { percentage: 3, amount: 2 },
      },
      creditCardMasterCard2: {
        merchant: { percentage: 3, amount: 4 },
        customer: { percentage: 4, amount: 3 },
      },
      creditCardVisa2: {
        merchant: { percentage: 8, amount: 9 },
        customer: { percentage: 9, amount: 8 },
      },
      creditCardTravel: {
        merchant: { percentage: 4, amount: 5 },
        customer: { percentage: 5, amount: 4 },
      },
      creditCardBusiness: {
        merchant: { percentage: 5, amount: 6 },
        customer: { percentage: 6, amount: 5 },
      },
      amex: {
        merchant: { percentage: 6, amount: 7 },
        customer: { percentage: 7, amount: 6 },
      },
    },
  })
  const supplierCompany = await Company.create({
    name: 'Test supplier',
    type: 'supplier',
    address: {
      address: '100 Main St',
      city: 'San Francisco',
      state: 'CA',
      zip: '11111',
    },
    email: '<EMAIL>',
    phone: '+***********',
    status: 'approved',
    settings: {
      cardPricingPackageId: 'packageA',
    },
  })

  const customerAccount = await CustomerAccount.create({
    company_id: supplierCompany._id,
    phone: supplierCompany.phone,
    name: 'Contractor Inc.',
  })

  const invoice = await Invoice.create({
    status: 'PLACED',
    total_amount: 100.0,
    customer_account_id: customerAccount._id,
    company_id: supplierCompany._id,
  })

  const operation = await Operation.create({
    owner_id: invoice._id,
    amount: 100,
    metadata: {
      payee_id: supplierCompany._id.toString(),
    },
    status: 'PLACED',
    type: 'invoice_payment',
  })

  return {
    supplierCompany,
    customerAccount,
    invoice,
    operation,
    creditCardAccId: creditCard._id,
  }
}

describe('approve and reject invoices', async () => {
  async function createInvoice(
    supplierCompanyId = '',
    amountArr = [10, 20, 30, 40, 50],
    paymentMethod = 'account',
  ) {
    const { constructor1, supplier1 } = FakeUsers
    const mockAuth = _Authorization(constructor1.auth)

    const resp1 = (await routes2.user.updateInfo({
      firstName: 'John',
      lastName: 'Doe',
      phone: '**********',
      email: '<EMAIL>',
      addresses: [
        { city: 'Kansas', zip: '11111', address: 'Some street', state: 'KS' },
      ],
    })) as any

    const resp = await routes.company.updateInfo({
      name: 'Mels',
      status: 'approved',
    })
    const contractorCompanyId = resp.id
    const contractorUserId = resp1.user._id.toString()

    let resp2 = await routes.company.addBankAccount({
      accountNumber: '**********',
      isPrimary: true,
      name: 'GreatBank',
      routingNumber: '123123',
      accountType: 'checking',
    })
    resp2.result.should.be.equal('ok')

    resp2 = await routes.company.bankAccounts()
    const account = resp2.bankAccounts[0]

    await Promise.all(
      amountArr.map(async (am, index) => {
        await Invoice.create({
          customer_account_id: '',
          payer_id: contractorCompanyId,
          supplierInvitationDetails: {
            email: supplier1.info.email,
            userId: contractorUserId,
            paymentMethodId:
              index === 0
                ? paymentMethod === 'account'
                  ? account._id
                  : 'credit'
                : '',
          },
          company_id: supplierCompanyId,
          expiration_date: moment().add(1, 'days').format(),
          status: 'DRAFT',
          total_amount: am,
          approved: false,
        })
      }),
    )

    mockAuth.restore()
  }

  beforeEachFirebase()
  beforeEachMockEncryption()
  beforeEachMockSQS()
  beforeEachMockSFN()
  beforeEachMockSNS()
  beforeEachMockEventBridgeClient()
  beforeEachSms()
  let aiMock: sinon.SinonStub
  let azureSendServiceBusMessage: sinon.SinonStub
  beforeEach(async () => {
    process.env.LP_CBW_ACH_IDENTIFICATION = '*********'
    process.env.AZ_PAYMENT_SERVICE_QUEUE_CONNECTION_STRING =
      'Endpoint=sb://service-bus-namespace-payment-service-dev.servicebus.windows.net/;SharedAccessKeyName=payment-bus-queue-connection-auth-rule;SharedAccessKey=bdH5pRhWdbo/Kkdp0qkZQOTwM7pxDh8n3+ASbAgpYz4=;EntityPath=paymentrequestqueue-dev'
    process.env.AZ_PAYMENT_SERVICE_QUEUE_NAME = 'paymentrequestqueue-dev'

    const cbwApi = initCbwApiRequester()
    aiMock = sinon.stub(cbwApi, 'post').callsFake((path, { payload }) => {
      return Promise.resolve({
        transactionNumber: '123',
        transactionAmountCents: parseInt(payload.transactionAmount.amount),
        api: {
          reference: '123',
          dateTime: '',
          originalReference: payload.reference,
        },
        statusCode: '000',
        statusDescription: 'SUCCESS',
      })
    })
    azureSendServiceBusMessage = sinon
      .stub(AzureService, 'sendServiceBusMessage')
      .callsFake(() => Promise.resolve())
    obsMock = sinon
      .stub(onBoardingService, 'getCreditApplications')
      .resolves([])
  })
  afterEach(async () => {
    aiMock.restore()
    obsMock.restore()
    azureSendServiceBusMessage.restore()
  })
  it('should fetch action required invoices', async () => {
    const { supplier1 } = FakeUsers
    await createInvoice()
    const mockAuth = _Authorization(supplier1.auth)

    const resp = await routes.invoices.getActionRequiredInvoices()
    resp.should.have.property('items')
    resp.items.should.have.length(5)
    console.log(await Invoice.find({}))
    mockAuth.restore()
  })

  it('should approve invoices, and should be removed from action required', async () => {
    const { supplier1 } = FakeUsers
    await CardProducts.create({
      productCode: 'G',
      productName: 'Visa Business',
      personalOrBusiness: 'business',
      cardType: 'credit',
      isbpsp: true,
      isl2eligible: true,
      isl3eligible: true,
      isTravel: false,
    })
    await CardPricingPackage.create({
      name: 'A',
      metadata: {
        ach: {
          merchant: { min: 1, max: 10, percentage: 1 },
          customer: { percentage: 1, amount: 1 },
        },
        creditCardVisa: {
          merchant: { percentage: 1, amount: 1 },
          customer: { percentage: 2, amount: 2 },
        },
        debitCardRegulated: {
          merchant: { percentage: 1, amount: 2 },
          customer: { percentage: 2, amount: 1 },
        },
        debitCardUnregulated: {
          merchant: { percentage: 2, amount: 3 },
          customer: { percentage: 3, amount: 2 },
        },
        creditCardMasterCard2: {
          merchant: { percentage: 3, amount: 4 },
          customer: { percentage: 4, amount: 3 },
        },
        creditCardVisa2: {
          merchant: { percentage: 8, amount: 9 },
          customer: { percentage: 9, amount: 8 },
        },
        creditCardTravel: {
          merchant: { percentage: 4, amount: 5 },
          customer: { percentage: 5, amount: 4 },
        },
        creditCardBusiness: {
          merchant: { percentage: 5, amount: 6 },
          customer: { percentage: 6, amount: 5 },
        },
        amex: {
          merchant: { percentage: 6, amount: 7 },
          customer: { percentage: 7, amount: 6 },
        },
      },
    })

    const mockAuth = _Authorization(supplier1.auth)

    let resp = await routes.company.updateInfo({
      name: 'Mel',
      address: {
        address: 'Some street',
        city: 'Kansas',
        zip: '11111',
        state: 'KS',
      },
      phone: '**********',
      email: '<EMAIL>',
      ein: '**********',
      settings: { cardPricingPackageId: 'A', loanPricingPackageId: '' },
    })

    await routes2.user.updateInfo({
      firstName: 'John',
      lastName: 'Doe',
      addresses: [
        { city: 'Kansas', zip: '11111', address: 'Some street', state: 'KS' },
      ],
    })
    const supplierCompanyId = resp.id
    await createInvoice(supplierCompanyId)
    await Company.findByIdAndUpdate(supplierCompanyId, {
      status: 'approved',
    })

    const resp2 = await routes.company.addBankAccount({
      accountNumber: '***********',
      isPrimary: true,
      name: 'GreatBank',
      routingNumber: '123123',
      accountType: 'checking',
    })
    resp2.result.should.be.equal('ok')

    resp = await routes.invoices.getActionRequiredInvoices()
    resp.should.have.property('items')
    resp.items.should.have.length(10)

    await Invoice.findById(resp.items[0].id)
    const resp1 = await routes.invoices.approveInvoice({
      ...resp.items[0],
      approved: true,
    })
    resp1.result.should.eq('ok')

    const customerAccount = await CustomerAccount.find({
      company_id: supplierCompanyId,
    })
    customerAccount.should.have.length(1)
    customerAccount[0].name!.should.equal('Mels')
    customerAccount[0].first_name!.should.equal('John')
    customerAccount[0].last_name!.should.equal('Doe')

    const invoice = await Invoice.findById(resp.items[0]._id.toString())
    invoice!.company_id.should.equal(supplierCompanyId)
    invoice!.customer_account_id.should.equal(customerAccount[0]._id.toString())
    invoice!.status.should.equal('PLACED')

    const operations = await Operation.find({})
    operations.length.should.equal(1)

    const newActionRequired = await routes.invoices.getActionRequiredInvoices()
    newActionRequired.should.have.property('items')
    newActionRequired.items.should.have.length(8)

    mockAuth.restore()
  })

  it('should reject invoice and should be removed from action required', async () => {
    const { supplier1 } = FakeUsers
    const mockAuth = _Authorization(supplier1.auth)
    await createInvoice()
    const resp = await routes.invoices.getActionRequiredInvoices()
    resp.should.have.property('items')
    resp.items.should.have.length(5)

    const resp2 = await routes.invoices.cancel(resp.items[2].id)
    resp2.result.should.be.equal('ok')

    const invoice = await Invoice.findById(resp.items[2].id)
    invoice!.status.should.equal(invoiceStatus.cancelled)

    const resp3 = await routes.invoices.getActionRequiredInvoices()
    resp3.should.have.property('items')
    resp3.items.should.have.length(4)
    mockAuth.restore()
  })

  it('should reject invoices', async () => {
    const { supplier1 } = FakeUsers
    await createInvoice()
    const mockAuth = _Authorization(supplier1.auth)

    let resp = await routes.company.updateInfo({
      name: 'Mel',
      address: {
        address: 'Some street',
        city: 'Kansas',
        zip: '11111',
        state: 'KS',
      },
      phone: '**********',
      email: '<EMAIL>',
      ein: '**********',
      settings: { cardPricingPackageId: 'A' },
    })

    await routes2.user.updateInfo({
      firstName: 'John',
      lastName: 'Doe',
      addresses: [
        { city: 'Kansas', zip: '11111', address: 'Some street', state: 'KS' },
      ],
    })
    const supplierCompanyId = resp.id
    await Company.findByIdAndUpdate(supplierCompanyId, { status: 'approved' })

    resp = await routes.invoices.getActionRequiredInvoices()
    resp.should.have.property('items')
    resp.items.should.have.length(5)

    const resp1 = await routes.invoices.rejectInvoice(
      resp.items[0]._id.toString(),
    )
    resp1.result.should.be.equal('ok')

    const customerAccount = await CustomerAccount.find({
      company_id: supplierCompanyId,
    })
    customerAccount.should.have.length(1)
    customerAccount[0].name!.should.equal('Mels')
    customerAccount[0].first_name!.should.equal('John')
    customerAccount[0].last_name!.should.equal('Doe')

    const invoice = await Invoice.findById(resp.items[0]._id.toString())
    invoice!.company_id.should.equal(supplierCompanyId)
    invoice!.customer_account_id.should.equal(customerAccount[0]._id.toString())
    invoice!.status.should.equal('REJECTED')

    mockAuth.restore()
  })

  it('should approve invoices with bnpl payment method', async () => {
    process.env.LP_MODE = 'test'
    const { supplier1 } = FakeUsers
    await LoanPricingPackage.create({
      name: 'packageA',
      description: 'Willing to Wait',
      metadata: {
        merchant: 2.5,
        maxAmountReceived: 97.5,
        advanceRate: 80,
        finalPayment: 20,
        merchantRebate: 0.5,
        merchantFeeAfterRebate: 2,
        maxAmountReceivedAfterRebate: 98,
        CustomerFees30: 0,
        CustomerFees6090: 'Based on customer credit',
      },
    })

    const mockAuth = _Authorization(supplier1.auth)

    let resp = await routes.company.updateInfo({
      name: 'Mel',
      address: {
        address: 'Some street',
        city: 'Kansas',
        zip: '11111',
        state: 'KS',
      },
      phone: '**********',
      email: '<EMAIL>',
      ein: '**********',
      settings: { loanPricingPackageId: 'packageA' },
    })

    await routes2.user.updateInfo({
      firstName: 'John',
      lastName: 'Doe',
      addresses: [
        { city: 'Kansas', zip: '11111', address: 'Some street', state: 'KS' },
      ],
    })
    const supplierCompanyId = resp.id
    await createInvoice(supplierCompanyId, [10, 20, 30, 40, 50], 'credit')
    await Company.findByIdAndUpdate(supplierCompanyId, { status: 'approved' })

    const resp2 = await routes.company.addBankAccount({
      accountNumber: '***********',
      isPrimary: true,
      name: 'GreatBank',
      routingNumber: '123123',
      accountType: 'checking',
    })
    resp2.result.should.be.equal('ok')

    resp = await routes.invoices.getActionRequiredInvoices()
    resp.should.have.property('items')
    resp.items.should.have.length(9)

    const invoiceToPay = await Invoice.findById(resp.items[0].id)

    await LoanApplication.create({
      company_id: invoiceToPay!.payer_id,
      invoiceDetails: {
        invoiceId: invoiceToPay!._id.toString(),
        paymentPlan: 30,
      },
      status: 'processing',
    })

    const resp1 = await routes.invoices.approveInvoice({
      ...resp.items[0],
      approved: true,
    })
    resp1.result.should.be.equal('ok')

    const customerAccount = await CustomerAccount.find({
      company_id: supplierCompanyId,
    })
    customerAccount.should.have.length(1)
    customerAccount[0].name!.should.equal('Mels')
    customerAccount[0].first_name!.should.equal('John')
    customerAccount[0].last_name!.should.equal('Doe')

    const invoice = await Invoice.findById(resp.items[0]._id.toString())
    invoice!.company_id.should.equal(supplierCompanyId)
    invoice!.customer_account_id.should.equal(customerAccount[0]._id.toString())
    invoice!.status.should.equal('PLACED')

    const operations = await Operation.find({})
    operations.length.should.equal(1)

    const loanApplications = await LoanApplication.find({
      company_id: invoice!.payer_id,
      'invoiceDetails.invoiceId': invoice!._id.toString(),
    })
    loanApplications.length.should.equal(1)

    mockAuth.restore()
  })

  describe('invoice notes', async () => {
    it('should add/update/delete note to/from invoice', async () => {
      const { supplier1 } = FakeUsers
      await createInvoice()
      const mockAuth = _Authorization(supplier1.auth)
      const resp = await routes.invoices.getActionRequiredInvoices()
      resp.should.have.property('items')
      resp.items.should.have.length(5)

      const invoiceId = resp.items[0]._id.toString()
      const note = {
        id: '',
        invoiceId,
        message: 'First message',
      }
      const respSave = await routes.invoices.invoiceNotesSave(note)
      respSave.should.have.property('invoiceNoteId')
      const noteId = respSave.invoiceNoteId

      let respGet = await routes.invoices.invoiceNotes(invoiceId)
      respGet.should.have.property('invoiceNotes')
      respGet.invoiceNotes.should.have.length(1)
      let invoiceNote = respGet.invoiceNotes[0]
      invoiceNote.should.have.property('id')
      invoiceNote.should.have.property('message')
      invoiceNote.should.have.property('owner')
      invoiceNote.should.have.property('createdDate')
      invoiceNote.should.have.property('updatedDate')
      invoiceNote.id.should.equal(noteId)

      note.id = invoiceNote.id
      note.message = 'First Message Edited'
      const respUpdate = await routes.invoices.invoiceNotesSave(note)
      respUpdate.result.should.be.equal('ok')

      respGet = await routes.invoices.invoiceNotes(invoiceId)
      respGet.should.have.property('invoiceNotes')
      respGet.invoiceNotes.should.have.length(1)
      invoiceNote = respGet.invoiceNotes[0]
      invoiceNote.message.should.equal('First Message Edited')

      const respDelete = await routes.invoices.invoiceNotesDelete(
        invoiceNote.id,
      )
      respDelete.result.should.be.equal('ok')
      respGet = await routes.invoices.invoiceNotes(invoiceId)
      respGet.should.have.property('invoiceNotes')
      respGet.invoiceNotes.should.have.length(0)

      mockAuth.restore()
    })
  })
})
