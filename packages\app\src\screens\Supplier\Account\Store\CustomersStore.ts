import { CancellablePromise } from '@linqpal/models/src/helpers/caller'
import { routes, SupplierAccount } from '@linqpal/models'
import { dateMask, phoneMask } from '../../../../utils/helpers/masking'
import { BaseTableStore } from '../../../../store/BaseTableStore'
import { ICustomerTableItem } from './Types/ICustomerTableItem'
import { ICustomersFilter } from './Types/ICustomersFilter'
import { TradeCreditStatus } from '@linqpal/models/src/dictionaries/TradeCreditStatus'
import { CustomerAccountType } from '@linqpal/models/src/dictionaries/customerAccountType'
import { TFunction } from 'react-i18next'
import { makeObservable, observable } from 'mobx'
import { InHouseCreditStatus } from '@linqpal/models/src/dictionaries'

export class CustomersStore extends BaseTableStore<
  ICustomerTableItem,
  ICustomersFilter
> {
  filter: ICustomersFilter = {
    search: '',
    status: '',
  }

  type: CustomerAccountType

  t: TFunction<'global'> | undefined

  customerToSwitchImportState: SupplierAccount | null = null

  constructor(type: CustomerAccountType) {
    super()

    this.type = type
    this.exportFileName = `customers-${type}`

    makeObservable(this, { customerToSwitchImportState: observable })
    this.initObservable()
  }

  callApi(params: any): CancellablePromise<any> {
    return routes.supplier.allCustomersOfSupplier({
      ...params,
      type: this.type,
      isConnectorParent: 'false',
    })
  }

  formatTableItem(customer: SupplierAccount) {
    return {
      status: customer.status,
      business: customer.name, // TODO: VK: handle sort field name
      contact: this.formatCustomerName(customer),
      phone: phoneMask(customer.phone),
      creditStatus: this.formatCreditStatus(customer),
      lastPurchaseDate: customer?.last_purchase_date
        ? dateMask(customer.last_purchase_date)
        : '-',
      customer,
    }
  }

  async switchQuickBooksImportState(customer: SupplierAccount) {
    const newImportState = !customer.invoice_import_enabled
    const tableItem = this.tableData.items.find(
      (c) => c.customer.id === customer.id,
    )

    // To avoid whole table reload refresh just an updated record
    if (tableItem) {
      tableItem.isQuickBooksImportUpdating = true

      try {
        await routes.quickBooks.switchCustomerImport(
          customer.id,
          newImportState,
        )

        tableItem.customer.invoice_import_enabled = newImportState
      } catch (e) {
        console.log(e)
        // TODO: VK: add error message support to stores
        // self.setErrorMessage(e.message)
      } finally {
        tableItem.isQuickBooksImportUpdating = false
      }
    }
  }

  private formatCustomerName(customer: SupplierAccount) {
    const { first_name, last_name, display_name, name } = customer
    return (
      [first_name, last_name].join(' ').trim() ||
      display_name?.trim() ||
      name?.trim() ||
      ''
    )
  }

  private formatCreditStatus(customer: SupplierAccount) {
    const { credit_status, customer_credit_limit } = customer
    if (!this.t) {
      throw new Error('call setTranslation() first')
    }

    if (this.type === CustomerAccountType.TradeCredit) {
      if (
        [
          TradeCreditStatus.BankDisconnected,
          TradeCreditStatus.BankDataMissing,
          TradeCreditStatus.AccountOnHold,
        ].includes(credit_status as any)
      ) {
        return this.t('Account.trade-credit-status.account-in-review')
      }
    }

    return [
      TradeCreditStatus.Approved,
      TradeCreditStatus.GoodStanding,
      TradeCreditStatus.PastDue,
      InHouseCreditStatus.GoodStanding,
      InHouseCreditStatus.Closed,
      InHouseCreditStatus.InCollection,
      InHouseCreditStatus.PastDue,
    ].some((s) => s === credit_status) && customer_credit_limit > 0
      ? customer_credit_limit.toLocaleString('en-US', {
          style: 'currency',
          currency: 'USD',
        })
      : credit_status
  }

  formatExportItem(tableItem: ICustomerTableItem): { [p: string]: string } {
    if (!this.t) {
      throw new Error('call setTranslation() first')
    }

    return {
      [this.t('TabAccount.columns.status')]: tableItem.status,
      [this.t('TabAccount.columns.business')]: tableItem.business,
      [this.t('TabAccount.columns.contact')]: tableItem.contact,
      [this.t('TabAccount.columns.phone')]: tableItem.phone,
      [this.t(
        this.type === CustomerAccountType.TradeCredit
          ? 'TabAccount.columns.tradeCreditStatus'
          : 'TabAccount.columns.inHouseCreditStatus',
      )]: tableItem.creditStatus,
      [this.t('TabAccount.columns.lastPurchaseDate')]:
        tableItem.lastPurchaseDate,
    }
  }

  setTranslation(t: TFunction<'global'>) {
    this.t = t
  }
}
