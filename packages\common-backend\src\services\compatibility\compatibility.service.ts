import { exceptions, IDraftModel } from '@linqpal/models'
import { LOAN_APPLICATION_TYPE } from '@linqpal/models/src/dictionaries/loanApplicationTypes'
import { DefaultSettings } from '@linqpal/models/src/dictionaries/settings'
import { isEmpty, isString } from 'lodash'
import * as math from 'mathjs'
import { Moment } from 'moment'
import moment from 'moment-timezone'
import { LoanApplication } from '../../models/decision.model'
import type { ICompany, ILoanApplication } from '../../models/types'
import awsService from '../aws.service'
import { getLoanApplicationDraftFullInfo } from '../company.service'
import { invoiceService } from '../invoice.dotnet.service'
import { getLoanOrigin, LoanOrigin } from '../lms.service'
import { getPlan } from '../loanplan.service'
import { Logger } from '../logger/logger.service'
import { onBoardingService } from '../onBoarding/onBoarding.service'
import { QuoteService } from '../quote/quote.service'
import { CreditApplicationType } from '@linqpal/models/src/dictionaries/creditApplicationType'
import {
  Company,
  CustomerAccount,
  Draft,
  Invoice,
  User,
  UserRole,
} from '../../models'
import { Document } from 'mongoose'
import { loanService } from '../loan.dotnet.service'
import { LMS } from '../../..'
import { PricingProduct } from '@linqpal/models/src/dictionaries/pricingProduct'
import { ExecutionStatus } from '@aws-sdk/client-sfn'
import { extractNoSupplierDetails } from '../invoices.service/extractNoSupplierDetails'
import { handleNoSupplierFlow } from '../invoices.service/handleNoSupplierFlow'
import { DotNetMapper } from '../../helpers/DotNetMapper'
import {
  IDownPaymentDetails,
  IExecuteDrawApprovalDERequest,
} from '../onBoarding/types'

const log = new Logger({
  module: 'Compatibility',
  subModule: 'compatibility.service',
})

const isLoanApplicationPrequal = (item: ILoanApplication) =>
  !item.invoiceDetails || isEmpty(item.invoiceDetails.invoiceId)

const checkIsExecutionRunning = async (executionArn?: string | null) => {
  const execution = executionArn
    ? await awsService.describeStateMachineExecution({
        executionArn: executionArn,
      })
    : null

  return execution?.status === ExecutionStatus.RUNNING
}

async function runDotNetDEForLineOfCredit(loanAppId: string) {
  log.info(
    `runDotNetDEForLineOfCredit function is started for loanAppId ${loanAppId}`,
  )

  const item = await LoanApplication.findById(loanAppId)
  if (!item) {
    log.info(`Loan application with ${loanAppId} is not found`)

    throw new exceptions.LogicalError('Loan application is not found')
  }
  log.info(item, `Loan application with ${loanAppId} is found`)

  if (
    await onBoardingService.isCreditApplicationProcessingShouldBeSkipped(
      item.company_id,
    )
  ) {
    log.info(
      'Skip running dotnet credit application DE because the company already has a credit application',
    )
    return
  }

  const isRunning = await checkIsExecutionRunning(
    item.newDECreditApplicationExecutionArn,
  )
  if (isRunning) {
    log.info(
      "Skip running dotnet credit application DE because it's already running",
    )
    return
  }

  const detailedDraft = await getLoanApplicationDraftFullInfo(item.company_id)

  const data = {
    draftId: detailedDraft._id,
    creditApplicationId: null,
    type: CreditApplicationType.LineOfCredit,
    merchantId: '',
  }

  log.info(data, 'Try running dotnet-decision-engine')

  try {
    const { executionArn } = await onBoardingService.executeCreditApplicationDE(
      data,
    )

    if (executionArn) {
      log.info({ executionArn }, 'DE is started')

      await LoanApplication.findByIdAndUpdate(loanAppId, {
        newDECreditApplicationExecutionArn: executionArn,
      })
    } else log.warn('Execution arn is null in response from OBS')

    log.info('runDotNetDEForLineOfCredit is completed')
  } catch (err) {
    log.error(
      { err },
      'The error happened during running credit application dotnet-decision-engine',
    )

    throw err
  }
}

async function runDotNetDEForDrawApplication(
  loanAppId: string,
  downPaymentDetails: IDownPaymentDetails | null,
) {
  log.info(
    `runDotNetDEForDrawApplication function is started for loanAppId ${loanAppId}`,
  )

  const item = await LoanApplication.findById(loanAppId)

  if (!item) {
    log.info(`Loan application with ${loanAppId} is not found`)
    throw new exceptions.LogicalError('Loan application is not found')
  }

  log.info(item, `Loan application with ${loanAppId} is found`)

  if (isLoanApplicationPrequal(item)) {
    log.info(
      'Skip running dotnet draw approval DE because the loan application is prequal',
    )
    return
  }

  const isRunning = await checkIsExecutionRunning(
    item.newDEDrawApprovalExecutionArn,
  )

  if (isRunning) {
    log.info(
      "Skip running dotnet draw approval DE because it's already running",
    )
    return
  }

  const invoices = (
    Array.isArray(item.invoiceDetails.invoiceId)
      ? item.invoiceDetails.invoiceId
      : [item.invoiceDetails.invoiceId]
  ).filter(isString)

  const payables = [] as {
    id: string
    amount: number
    type: string
    invoiceNumber: string | null
  }[]
  let drawAmount = 0

  log.info({ invoices }, `Getting the invoices' data`)

  const invoicesData = await invoiceService.getInvoicesById(invoices)

  log.info({ invoicesData }, `Got the data from invoices service`)

  // find the first non-empty project_id
  let projectId =
    invoicesData.find((invoice) => invoice.project_id ?? false)?.project_id ??
    null

  if (projectId) {
    const invoiceWithDifferentProjectId = invoicesData.find(
      (invoice) =>
        invoice.project_id !== null && invoice.project_id !== projectId,
    )
    if (invoiceWithDifferentProjectId) {
      log.warn(
        `Found different project_id "${invoiceWithDifferentProjectId.project_id}" than "${projectId}" in invoice "${invoiceWithDifferentProjectId.id}". Setting projectId to null`,
      )

      projectId = null
    }
  }

  log.info({ projectId }, 'Calculating drawAmount')

  for (const invoice of invoicesData) {
    if (invoice == null || invoice.total_amount == null) continue

    payables.push({
      id: invoice.id,
      amount: invoice.total_amount,
      type: invoice.type,
      invoiceNumber: invoice.invoice_number,
    })

    drawAmount += invoice.total_amount
  }

  drawAmount = math.round(drawAmount, 2)

  log.info(`drawAmount is ${drawAmount}. Getting payment plan`)

  const paymentPlanId = (
    await getPlan(item.invoiceDetails.paymentPlan)
  )._id.toString()

  log.info({ paymentPlanId }, `Getting approved credit application`)

  const creditApplication = await onBoardingService.getApprovedLoCApplication(
    item.company_id,
  )

  log.info({ creditApplication }, 'Getting creditId')

  const credit = creditApplication
    ? await loanService.getActiveCredit(creditApplication.id)
    : null

  let quoteDetails: {
    creditHoldAmount: number
    expirationDate: Moment
  } | null = null

  if (item.type === LOAN_APPLICATION_TYPE.QUOTE) {
    quoteDetails = {
      creditHoldAmount: QuoteService.calculateAuthorizationLimit(drawAmount),
      expirationDate: await QuoteService.getAuthorizationDeadline(),
    }
  }

  const noSupplierDetails = await extractNoSupplierDetails(invoicesData)

  if (noSupplierDetails) {
    log.info(
      `Found noSupplierDetails in invoices: ${noSupplierDetails}. Start updating invoices details`,
    )
    await handleNoSupplierFlow(invoicesData)
    log.info(`invoices were updated for noSupplier flow`)
  }

  // TODO Temporary added to test down payment details, should be removed after
  const company = await Company.findById(item.company_id)
  log.info(
    {
      companyName: company?.name,
      downPaymentDetails: company?.settings?.downPaymentDetails,
    },
    'Company down payment details',
  )

  const data: IExecuteDrawApprovalDERequest = {
    creditId: credit?.id ?? null,
    companyId: item.company_id,
    merchantId: null,
    paymentPlanId,
    projectId,
    drawApprovalId: null,
    drawAmount,
    payables,
    loanOrigin: getLoanOrigin(item, noSupplierDetails),
    creditHoldAmount: quoteDetails?.creditHoldAmount ?? null,
    expirationDate: quoteDetails?.expirationDate.toISOString() ?? null,
    arAdvanceCreditId: null,
    inHouseCreditId: null,
    noSupplierDetails,
    downPaymentDetails,
  }

  log.info(data, 'Try running dotnet-draw-approval')
  try {
    const { executionArn } = await onBoardingService.executeDrawApprovalDE(data)

    if (executionArn) {
      log.info({ executionArn }, 'DE is started')

      await LoanApplication.findByIdAndUpdate(loanAppId, {
        newDEDrawApprovalExecutionArn: executionArn,
      })
    } else {
      log.warn('Execution arn is null in response from OBS')
    }

    log.info('runDotNetDEForDrawApplication is completed')
  } catch (err) {
    log.error(
      { err },
      'The error happened during running draw approval dotnet-decision-engine',
    )

    throw err
  }
}

async function runDotNetDEForInHouseInvoice(invoiceId: string) {
  log.info(
    `runDotNetDEForInHouseInvoice function is started for invoiceId ${invoiceId}`,
  )

  const invoice = await Invoice.findById(invoiceId)

  if (!invoice) {
    log.info(`Invoice with ${invoiceId} is not found`)
    throw new exceptions.LogicalError('Invoice is not found')
  }

  if (!invoice.company_id) {
    log.info(`Invoice company_id or invoice ${invoiceId} is not found`)
    throw new exceptions.LogicalError('Invoice company id is not found')
  }

  let companyId = invoice.payer_id

  if (!companyId) {
    if (!invoice.customer_account_id) {
      log.info(
        `Invoice customer_account_is for invoice ${invoiceId} is not found`,
      )
      throw new exceptions.LogicalError(
        'Invoice customer account id is not found',
      )
    }

    const customerAccount = await CustomerAccount.findById(
      invoice.customer_account_id,
    )
    if (!customerAccount) {
      log.info(`Customer account for invoice ${invoiceId} is not found`)
      throw new exceptions.LogicalError('Customer account is not found')
    }
    const user = (
      await User.aggregate([
        { $match: { email: customerAccount.email } },
        {
          $lookup: {
            from: UserRole.collection.name,
            localField: 'sub',
            foreignField: 'sub',
            as: 'userRole',
          },
        },
        { $unwind: '$userRole' },
      ])
    )[0]

    companyId = user.userRole.company_id
  }

  if (!invoice.paymentDetails?.loanPlanId) {
    log.info(`Invoice loanPlanId for invoice ${invoiceId} is not found`)
    throw new exceptions.LogicalError('Invoice loanPlan id is not found')
  }
  log.info(invoice, `Invoice with ${invoiceId} is found`)

  const isRunning = await checkIsExecutionRunning(invoice.DEExecutionArn)

  if (isRunning) {
    log.info(
      "Skip running dotnet draw approval DE because it's already running",
    )
    return
  }

  const [supplierCredits, customerCredits] = await Promise.all([
    LMS.getCreditCompanyInfo(invoice.company_id),
    LMS.getCreditCompanyInfo(companyId),
  ])

  const arAdvanceCredit = supplierCredits.find(
    (credit) => credit.product === PricingProduct.ArAdvance,
  )
  const inHouseCredit = customerCredits.find(
    (credit) =>
      credit.product === PricingProduct.InHouseCredit &&
      credit.merchantId === invoice.company_id,
  )

  if (!arAdvanceCredit) {
    log.info(
      `AR Advance Credit does not exist for supplier ${invoice.company_id} of invoice ${invoiceId}.`,
    )
    throw new exceptions.LogicalError('Ar Advance Credit is not found')
  }

  if (!inHouseCredit) {
    log.info(
      `In-House Credit does not exist for customer ${companyId} of invoice ${invoiceId}.`,
    )
    throw new exceptions.LogicalError('In-House Credit is not found')
  }

  const data: IExecuteDrawApprovalDERequest = {
    arAdvanceCreditId: arAdvanceCredit.id,
    inHouseCreditId: inHouseCredit.id,
    merchantId: invoice.company_id,
    companyId,
    paymentPlanId: invoice.paymentDetails.loanPlanId,
    projectId: null,
    drawAmount: invoice.total_amount,
    payables: [
      {
        id: invoice.id,
        amount: invoice.total_amount,
        type: DotNetMapper.invoiceTypeToPayableType(invoice.type),
        invoiceNumber: invoice.invoice_number,
      },
    ],
    loanOrigin: LoanOrigin.Factoring,
    drawApprovalId: null,
    creditId: null,
    creditHoldAmount: null,
    expirationDate: null,
  }

  if (invoice.type === LOAN_APPLICATION_TYPE.QUOTE) {
    const expirationDate = await QuoteService.getAuthorizationDeadline()

    data.creditHoldAmount = QuoteService.calculateAuthorizationLimit(
      invoice.total_amount,
    )
    data.expirationDate = expirationDate?.toISOString() ?? null
  }

  log.info(data, 'Try running dotnet-draw-approval')
  try {
    const { executionArn } = await onBoardingService.executeDrawApprovalDE(data)

    if (executionArn) {
      log.info({ executionArn }, 'Dotnet invoice approval DE is started')

      await Invoice.findByIdAndUpdate(invoice._id, {
        DEExecutionArn: executionArn || '',
      })
    } else {
      log.warn('Execution arn is null in response from OBS')
    }

    log.info('runDotNetDEForInHouseInvoice is completed')
  } catch (err) {
    log.error(
      { err },
      'The error happened during running invoice approval dotnet-decision-engine',
    )

    throw err
  }
}

async function runDotNetDEForInHouseCreditApplication(
  draftId: string,
  supplierId: string,
) {
  let draft: (Document<unknown, any, IDraftModel> & IDraftModel) | null

  try {
    draft = await Draft.findById(draftId)
  } catch (err) {
    log.error(
      { err, draftId },
      'Something went wrong during draft fetching from database',
    )

    throw new exceptions.SystemError(
      'unknown',
      'Something went wrong during draft fetching from database',
    )
  }

  if (!draft) {
    throw new exceptions.LogicalError(`Draft with id ${draftId} is not found`)
  }

  let isProcessingShouldBeSkipped: boolean

  try {
    isProcessingShouldBeSkipped =
      await onBoardingService.isInHouseCreditApplicationProcessingShouldBeSkipped(
        draft.company_id,
        supplierId,
      )
  } catch (err) {
    log.error(
      { err, draft: draft.toJSON(), supplierId },
      'Request to onBoarding service has failed',
    )

    throw new exceptions.SystemError(
      'unknown',
      'Request to onBoarding service has failed',
    )
  }

  if (isProcessingShouldBeSkipped) {
    log.error(
      { draft: draft.toJSON(), supplierId },
      `Creating In-House Credit Application should be skipped for company with id ${draft.company_id}`,
    )

    throw new exceptions.LogicalError(
      `Company with id ${draft.company_id} already have the In-House Credit Application`,
    )
  }

  const payload = {
    draftId,
    merchantId: supplierId,
    type: CreditApplicationType.InHouseCredit,
  }

  log.info(payload, `starting DE for IHC application`)

  let response: { id: string }

  try {
    response = await onBoardingService.submitCreditApplication(payload)
  } catch (err) {
    log.error({ err }, 'Request to onBoarding service has failed')

    throw new exceptions.SystemError(
      'unknown',
      'Request to onBoarding service has failed',
    )
  }

  log.info({ response }, `DE is started for credit application ${response.id}`)
}

async function runDotNetDEForGetPaidApplication(
  company: ICompany,
  draftId: string,
) {
  const isRunning = await checkIsExecutionRunning(
    company.newDECreditApplicationExecutionArn,
  )

  if (isRunning) {
    log.info(
      "Skip running dotnet credit application DE because it's already running",
    )
    return
  }

  const data = {
    draftId,
    creditApplicationId: null,
    type: CreditApplicationType.GetPaid,
    merchantId: '',
  }

  log.info(data, 'Try running dotnet-decision-engine')

  try {
    const { executionArn } = await onBoardingService.executeCreditApplicationDE(
      data,
    )

    if (executionArn) {
      company.newDECreditApplicationExecutionArn = executionArn

      await company.save()
    } else {
      log.warn('Execution arn is null in response from OBS')
    }

    log.info('runDotNetDEForGetPaidApplication is completed')
  } catch (err) {
    log.error(
      { err },
      'The error happened during running credit application dotnet-decision-engine',
    )

    throw err
  }
}

async function getCreateLoanDrawApprovalDetails(
  loanApplication: ILoanApplication,
) {
  if (!loanApplication.drawApprovalId)
    return {
      companyName: '',
      projectId: undefined,
      merchantId: undefined,
      merchantName: undefined,
      drawApprovalId: undefined,
      debtInvestor: undefined,
      loanPayables: undefined,
      downPaymentPercentage: undefined,
      downPaymentAmount: undefined,
      downPaymentExpireAt: undefined,
    }

  const drawApproval = await onBoardingService.getDrawApprovalById(
    loanApplication.drawApprovalId,
  )

  const invoiceDetails = await invoiceService.getInvoicesById(
    drawApproval.payables.map((item) => item.id),
  )

  const payablesDetails = drawApproval.payables.map((item) => ({
    payableId: item.id,
    amount: item.amount,
    invoiceNumber: invoiceDetails.find((invoice) => invoice.id === item.id)
      ?.invoice_number,
  }))

  const company = await Company.findById(drawApproval.companyId)

  const downPaymentPercentage = drawApproval.downPaymentDetails?.percentage
  const downPaymentAmount = drawApproval.downPaymentDetails?.amount

  let downPaymentExpireAt: string | undefined

  if (downPaymentPercentage || downPaymentAmount) {
    const expirationDays =
      company?.settings?.downPaymentDetails?.expireDays ||
      DefaultSettings.DownPaymentExpireDays
    downPaymentExpireAt = moment
      .tz('UTC')
      .add(expirationDays, 'days')
      .toISOString()
  }

  return {
    companyName: drawApproval.companyName,
    projectId: drawApproval.projectId,
    merchantId: drawApproval.merchantId,
    merchantName: drawApproval.merchantName,
    drawApprovalId: drawApproval.id,
    debtInvestor: drawApproval.debtInvestor,
    loanPayables: payablesDetails,
    downPaymentPercentage: downPaymentPercentage,
    downPaymentAmount: downPaymentAmount,
    downPaymentExpireAt: downPaymentExpireAt,
  }
}

export const compatibilityService = {
  runDotNetDEForLineOfCredit,
  runDotNetDEForDrawApplication,
  runDotNetDEForInHouseInvoice,
  getCreateLoanDrawApprovalDetails,
  runDotNetDEForInHouseCreditApplication,
  runDotNetDEForGetPaidApplication,
}
