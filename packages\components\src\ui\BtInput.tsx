import React, {
  forwardRef,
  ReactNode,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { ImageProps, Platform, View, ViewProps } from 'react-native'
import {
  TextInputMask,
  TextInputMaskOptionProp,
  TextInputMaskTypeProp,
} from 'react-native-masked-text'
import {
  useFocusedErrorProps,
  useInputValidateInput,
  useInputValidateInputOnBlur,
  useResponsive,
  useTheme,
  withEditableConverter,
} from '../hooks'
import { composeStyle, removeEmojiRegex } from '../helpers'
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons'
import { BtText } from './BtText'
import { Input as KittenInput, InputProps } from '@ui-kitten/components'
import { EvaInputSize, RenderProp } from '@ui-kitten/components/devsupport'
import { AutoCapitalize } from '../../../models/src/dictionaries'
import * as Masks from 'react-native-masked-text/dist/lib/masks'
import { BtInputLabel } from './BtInputLabel'

const oldMoneyMaskGetValue: (
  value: string,
  settings: TextInputMaskOptionProp,
) => string = Masks.MoneyMask.prototype.getValue
Masks.MoneyMask.prototype.getValue = function (
  value: string,
  settings: TextInputMaskOptionProp,
) {
  const isNegative = value.indexOf('-') >= 0
  const appliedValue = oldMoneyMaskGetValue.apply(this, [value, settings])
  if (!isNegative || !appliedValue || appliedValue.indexOf('-') >= 0) {
    return appliedValue
  }

  if (appliedValue.startsWith('$')) {
    return '$-' + appliedValue.substring(1)
  }

  return '-' + appliedValue
}

interface Props extends Omit<InputProps, 'label'> {
  rightComponent?: ReactNode
  leftComponent?: ReactNode
  inputComponent?: (...any) => void
  clearable?: boolean
  onClear?: () => void
  errorText?: string
  mandatory?: boolean
  label?: string
  inputMask?: string
  maskType?: TextInputMaskTypeProp
  maskOptions?: TextInputMaskOptionProp
  model?: any
  name?: string
  showClearIcon?: boolean
  containerStyle?: any
  labelStyle?: any
  textStyle?: any
  errorTextComponent?: React.ReactElement | null
}

const Input = forwardRef(
  (
    {
      rightComponent,
      leftComponent,
      value,
      clearable = false,
      disabled = false,
      errorText,
      onChangeText,
      onClear,
      status = 'basic',
      mandatory = false,
      label,
      style,
      size,
      showClearIcon,
      labelStyle,
      textStyle,
      errorTextComponent,
      ...rest
    }: Props,
    ref: React.Ref<any>,
  ) => {
    const renderLeftComponent = useMemo(() => {
      if (leftComponent) {
        return leftComponent
      }
      return undefined
    }, [leftComponent])

    const renderRightComponent = useMemo(() => {
      if ((clearable || !!onClear) && !!value && !disabled && showClearIcon) {
        return (
          <>
            {rightComponent}
            <CloseIcon
              onPress={() => {
                onChangeText && onChangeText('')
                onClear && onClear()
              }}
            />
          </>
        )
      }

      if (rightComponent) {
        return <>{rightComponent}</>
      }

      return undefined
    }, [
      clearable,
      disabled,
      onChangeText,
      onClear,
      rightComponent,
      value,
      showClearIcon,
    ])

    const { sm } = useResponsive()

    return (
      <KittenInput
        autoCorrect={false}
        autoCapitalize={AutoCapitalize.NONE}
        label={() => (
          <BtInputLabel labelStyle={labelStyle} required={mandatory}>
            {label}
          </BtInputLabel>
        )}
        onChangeText={onChangeText}
        status={errorText ? 'danger' : status}
        caption={errorTextComponent ? errorTextComponent : errorText}
        size={size ? size : sm ? 'large' : 'medium'}
        value={value}
        disabled={disabled}
        accessoryLeft={renderLeftComponent as RenderProp<Partial<ImageProps>>}
        accessoryRight={renderRightComponent as RenderProp<Partial<ImageProps>>}
        style={style}
        textStyle={textStyle}
        ref={ref}
        {...rest}
      />
    )
  },
)

interface ContainerProps extends ViewProps {
  focused?: boolean
  children: React.ReactNode
  hasError: boolean
  size?: EvaInputSize
}

export const InputContainer = ({
  style,
  focused = false,
  children,
  hasError = false,
  size,
  ...rest
}: ContainerProps) => {
  const theme = useTheme()

  let height = 40
  if (size === 'large') height = 48
  if (size === 'small') height = 32
  return (
    <View
      style={composeStyle(
        {
          minHeight: height,
          backgroundColor: theme['background-basic-color-1'],
          borderWidth: 1,
          borderRadius: 5,
          flex: 1,
          borderColor: hasError
            ? theme['border-danger-color-1']
            : theme['border-basic-color-4'],
        },
        focused && {
          backgroundColor: theme['background-basic-color-1'],
          borderColor: hasError
            ? theme['border-danger-color-1']
            : theme['color-primary-default'],
        },
        style,
      )}
      {...rest}
    >
      {children}
    </View>
  )
}

export const MaskedInput = forwardRef(
  (
    {
      caption,
      label,
      rightComponent,
      leftComponent,
      inputComponent,
      placeholder,
      value,
      onChangeText,
      onFocus,
      onBlur,
      inputMask,
      maskType,
      maskOptions,
      errorText,
      clearable = false,
      onClear,
      mandatory = false,
      disabled = false,
      size,
      style,
      containerStyle,
      textStyle = {},
      ...rest
    }: Props,
    ref: React.Ref<any>,
  ) => {
    const [focus, setFocused] = useState(false)
    const theme = useTheme()

    const { sm } = useResponsive()

    const _inputStyle = composeStyle(
      {
        flex: 1,
        // marginHorizontal: 5,
        paddingHorizontal: 10,
        marginVertical: 2,
        fontSize: 15,
        paddingVertical: 0,
        color: theme['text-basic-color'],
      },
      Platform.select({ web: { outlineColor: 'transparent' } }),
      textStyle,
    )

    const onFocused = (e) => {
      onFocus && onFocus(e)
      setFocused(true)
    }
    const onBlured = (e) => {
      onBlur && onBlur(e)
      setFocused(false)
    }

    const bottomText = useMemo(() => {
      if (!errorText && !caption) return null
      return (
        <BtText
          size={12}
          style={{
            color: errorText
              ? theme['text-danger-color']
              : theme['text-hint-color'],
          }}
        >
          {errorText || caption}
        </BtText>
      )
    }, [caption, errorText, theme])

    const renderInput = () => {
      if (inputComponent) {
        return inputComponent({
          onFocus,
          onBlur,
          onChangeText,
          value,
          autoCorrect: false,
          autoCapitalize: AutoCapitalize.NONE,
          placeholder,
          disabled,
          style: composeStyle(_inputStyle, style),
          placeholderTextColor: theme['text-hint-color'],
          ...rest,
        })
      }
      return (
        <TextInputMask
          autoCorrect={false}
          style={composeStyle(_inputStyle, style)}
          autoCapitalize={AutoCapitalize.NONE}
          onFocus={onFocused}
          onBlur={onBlured}
          editable={!disabled}
          type={maskType || 'custom'}
          options={{ mask: inputMask, ...maskOptions }}
          placeholder={placeholder}
          value={!value ? '' : String(value)}
          placeholderTextColor={theme['text-hint-color']}
          onChangeText={onChangeText}
          ref={ref}
          {...rest}
        />
      )
    }

    return (
      <View>
        <BtInputLabel required={mandatory}>{label}</BtInputLabel>
        <InputContainer
          style={composeStyle(
            {
              flexDirection: 'row',
              alignItems: 'center',
            },
            containerStyle,
          )}
          focused={focus}
          size={size ? size : sm ? 'large' : 'medium'}
          hasError={!!errorText}
        >
          {leftComponent}
          {renderInput()}
          {rightComponent}
          {(clearable || !!onClear) && !!value && !disabled && (
            <CloseIcon
              onPress={() => {
                onClear && onClear()
                onChangeText && onChangeText('')
              }}
            />
          )}
        </InputContainer>
        {bottomText}
      </View>
    )
  },
)

export const CloseIcon = ({ onPress }) => {
  const theme = useTheme()
  return (
    <MaterialCommunityIcons
      name="close"
      size={18}
      style={{ marginRight: 5 }}
      onPress={onPress}
      color={theme['text-hint-color']}
    />
  )
}

export interface BtInputProps extends Props {
  toRemoveRegex?: string | RegExp
  validateInput?: (a: string | undefined) => string
  validateInputOnBlur?: boolean
  containerStyle?: any
  errorTextComponent?: any
}

const BtInputCombined = forwardRef<KittenInput | TextInputMask, BtInputProps>(
  (
    {
      maskType,
      inputMask,
      errorText: initialError,
      onFocus: onInputFocus,
      onBlur: onInputBlur,
      inputComponent,
      onChangeText,
      toRemoveRegex,
      validateInput,
      validateInputOnBlur,
      value,
      maskOptions,
      ...rest
    }: BtInputProps,
    ref: React.Ref<any>,
  ) => {
    const { onBlur, onFocus } = useFocusedErrorProps()
    const [error, setError] = React.useState(initialError)

    const [blur, setBlur] = useState(false)

    const finalOnFocus = (e) => {
      onInputFocus && onInputFocus(e)
      onFocus()
    }

    const newOnBlur = useInputValidateInputOnBlur({
      validateInput,
      validateInputOnBlur,
      onInputBlur,
      setError,
      toRemoveRegex,
    })
    const finalOnBlur = (e) => {
      newOnBlur(e.nativeEvent.text)
      onBlur()
      setBlur(true)
    }

    const newOnChangeText = useInputValidateInput({
      validateInput,
      validateInputOnBlur,
      onChangeText,
      setError,
      toRemoveRegex,
    })

    const valid = !!value && (!validateInput || !validateInput(value))
    useEffect(() => {
      blur && newOnChangeText && newOnChangeText(value || '')
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [blur, valid, value])

    useEffect(() => {
      error && setBlur(true)
    }, [error])

    const onChange = useCallback(
      (text) => {
        const textWithoutEmojis = text.replace(removeEmojiRegex, '')
        newOnChangeText && newOnChangeText(textWithoutEmojis)
      },
      [newOnChangeText],
    )

    if (maskType || inputMask || maskOptions || inputComponent)
      return (
        <MaskedInput
          inputMask={inputMask}
          maskType={maskType}
          maskOptions={maskOptions}
          onFocus={finalOnFocus}
          value={value}
          onBlur={finalOnBlur}
          inputComponent={inputComponent}
          errorText={error}
          onChangeText={onChange}
          ref={ref}
          {...rest}
        />
      )
    return (
      <Input
        onFocus={finalOnFocus}
        onBlur={finalOnBlur}
        value={value}
        errorText={error}
        onChangeText={onChange}
        ref={ref}
        {...rest}
      />
    )
  },
)

export const BtInput = withEditableConverter<
  KittenInput | TextInputMask,
  BtInputProps
>(BtInputCombined)
