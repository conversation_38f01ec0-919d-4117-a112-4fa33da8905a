import React, { FC } from 'react'
import { observer } from 'mobx-react-lite'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { AddressEditor } from '../../Components/AddressEditor'
import { IApplicationAddress } from '@linqpal/models'
import { runInAction } from 'mobx'
import { IUnifiedApplicationEditor } from '../getUnifiedApplicationEditor'

const BusinessAddressStepEditor: FC = () => {
  const store = useUnifiedApplication()

  const handleChange = (value: IApplicationAddress) => {
    runInAction(() => (store.draft.data.businessInfo.businessAddress = value))
  }

  return (
    <AddressEditor
      address={store.draft.data.businessInfo.businessAddress || {}}
      onChange={handleChange}
      testID="UnifiedApplication.BusinessInfo.Address"
    />
  )
}

export const BusinessAddressStep: IUnifiedApplicationEditor = {
  options: {
    title: 'Business.Address',
    description: 'Business.AddressDescription',
  },
  component: observer(BusinessAddressStepEditor),
}
