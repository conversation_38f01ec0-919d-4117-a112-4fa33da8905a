import React, { use<PERSON>allback, useMemo, useState } from 'react'
import { StyleSheet, View } from 'react-native'
import { currencyMask } from '../../../../../../utils/helpers/masking'

import { PaymentMethod } from '../../../components/PaymentMethodsSection'
import { InvoicePanel } from '../../../components/InvoicePanel'
import { PayTermsAndConditionsForIntegration } from '../../../../../Auth/TermsAndConditions'
import SecurityNotice from '../../../../../../ui/molecules/SecurityNotice'
import { BillingAddress } from '../../../components/BillingAdress'
//import { Attachment } from '../../../components/Attachment'
import ContactInformation from '../../../components/ContactInformation'
import { useTranslation } from 'react-i18next'
import { AddNewCreditCardFlow } from '../../../../InvoiceDetails/flows/AddNewCreditCardFlow'
import { GUEST_FLOWS } from '../../../../InvoiceDetails/utils/flows'
import { makeGuestCardPayment } from '../../../requests/makeGuestCardPayment'
import PaymentErrorModal from '../../../components/PaymentErrorModal'
import { IPayAsGuestWebProps, IPaymentMethodData } from '../types'
import { getGuestPaymentMethod } from '../../../requests/getGuestPaymentMethod'
import { InputFieldNames } from '../../../components/ContactInformation/types'
import { useResponsive } from '@linqpal/components/src/hooks'
import { formatPhoneNumberToInternational } from '../../../helpers/formatPhoneNumber'
import { PayWithCreditCardFlow_PROCESSING } from '../../../../InvoiceDetails/components/PayWithCreditCardFlow_PROCESSING'

const PayAsGuestWeb: React.FC<IPayAsGuestWebProps> = ({
  invoice,
  paymentMethodData,
  setPaymentMethodData,
  onPaymentSuccess,
  contactDetails,
  setContactDetails,
  customer,
  onUserExists,
}) => {
  const { screenWidth } = useResponsive()

  const paddingHorizontal = useMemo(() => {
    if (screenWidth < 600) {
      return 20
    } else if (screenWidth < 1200) {
      return 100
    } else {
      return 200
    }
  }, [screenWidth])

  const { t } = useTranslation('global')
  const [isContactValid, setIsContactValid] = useState(false)
  const [phoneNumber, setPhoneNumber] = useState('')
  const [currentFlow, setCurrentFlow] = useState<GUEST_FLOWS>(GUEST_FLOWS.NONE)
  const [paymentLoading, setPaymentLoading] = useState(false)

  const handleContactValidation = useCallback(
    (isValid, validPhone, formValues) => {
      setContactDetails(formValues)
      setIsContactValid(isValid)
      setPhoneNumber(validPhone)
    },
    [setContactDetails],
  )

  const onAddCardPress = useCallback(() => {
    setCurrentFlow(GUEST_FLOWS.ADD_NEW_CREDIT_CARD)
  }, [])

  const onModalClose = useCallback(() => {
    setCurrentFlow(GUEST_FLOWS.NONE)
  }, [])

  const paymentMethodSubtitle = useMemo(() => {
    const accNum = paymentMethodData?.card?.last4 || ''
    return paymentMethodData?.card?.type === 'Debit'
      ? t('PaymentMethodsListing.debit-card', { accNum })
      : t('PaymentMethodsListing.credit-card', { accNum })
  }, [paymentMethodData, t])

  const handlePaymentMethodAdded = useCallback(
    (paymentMethodDetails: IPaymentMethodData) => {
      setPaymentMethodData(paymentMethodDetails)
      setCurrentFlow(GUEST_FLOWS.NONE)
    },
    [setPaymentMethodData],
  )

  const onRemovePaymentMethod = useCallback(() => {
    setPaymentMethodData(null)
  }, [setPaymentMethodData])

  const handlePayment = useCallback(async () => {
    if (!contactDetails || !paymentMethodData?.bankAccount?.id) {
      console.error(
        'Payment cannot be processed: Missing contact details or payment method',
      )
      return
    }
    setPaymentLoading(true)

    const response = await makeGuestCardPayment({
      user: {
        ...contactDetails,
        phone: formatPhoneNumberToInternational(contactDetails.phone),
      },
      company: {
        name: contactDetails.businessName ?? '',
        isBusiness: !!contactDetails.isBusiness,
      },
      payment: paymentMethodData.payment,
      bankAccountId: paymentMethodData.bankAccount.id,
      invoiceId: invoice._id,
    })
    setPaymentLoading(false)

    if (response?.result === 'ok') {
      localStorage.setItem('contactDetails', JSON.stringify(contactDetails))
      localStorage.setItem(
        'paymentMethodData',
        JSON.stringify(paymentMethodData),
      )

      console.log('Payment Successful')
      setCurrentFlow(GUEST_FLOWS.PAYMENT_SUCCESS)
    } else {
      console.error('Payment Failed')
      if (response?.code === 'auth/user-already-exists') {
        onUserExists({
          type: 'email',
          login: contactDetails.email,
          isNewUser: false,
        })
      } else {
        setCurrentFlow(GUEST_FLOWS.PAYMENT_ERROR)
      }
    }
  }, [contactDetails, paymentMethodData, invoice, onUserExists])

  const additionalFields = useMemo(
    () => [
      /*{
      label: t('integrations.invoice.attachment'),
      value: (
        <Attachment
          invoiceNumber={invoice.invoice_number}
          onPress={() => Linking.openURL('https://google.com')}
        />
      ),
    },*/
      ...(isContactValid && paymentMethodData
        ? [
            {
              label: t('integrations.invoice.invoice-amount'),
              value: currencyMask(invoice.total_amount),
            },
            {
              label: t('integrations.invoice.service-fee'),
              value: currencyMask(paymentMethodData.payment?.fee),
            },
            {
              label: t('integrations.invoice.total'),
              value: currencyMask(
                Number(invoice.total_amount) +
                  Number(paymentMethodData.payment?.fee),
              ),
              labelStyle: styles.totalStyle,
              valueStyle: styles.totalStyle,
            },
          ]
        : []),
    ],
    [isContactValid, invoice, t, paymentMethodData],
  )

  return (
    <>
      <View style={[styles.pageContainer, { paddingHorizontal }]}>
        {/* Left Section */}
        <View style={styles.leftSection}>
          <ContactInformation
            onValidationChange={handleContactValidation}
            initialValues={{
              [InputFieldNames.FIRST_NAME]: customer?.firstName ?? '',
              [InputFieldNames.LAST_NAME]: customer?.lastName ?? '',
              [InputFieldNames.EMAIL]: customer?.email ?? '',
              [InputFieldNames.PHONE]: (customer?.phone ?? '')
                .replace(/\D/g, '')
                .slice(-10),
              [InputFieldNames.BUSINESS_NAME]: customer?.businessName ?? '',
              [InputFieldNames.IS_BUSINESS]: true,
            }}
          />

          <PaymentMethod
            paymentMethod={paymentMethodData}
            onAddCardPress={onAddCardPress}
            onRemovePaymentMethod={onRemovePaymentMethod}
            phoneNumber={phoneNumber}
          />

          {paymentMethodData?.bankAccount?.billingAddress && (
            <BillingAddress
              billingAddress={paymentMethodData.bankAccount.billingAddress}
            />
          )}

          <SecurityNotice />
        </View>

        {/* Right Section */}
        <View style={styles.rightSection}>
          <InvoicePanel
            invoice={invoice}
            additionalFields={additionalFields}
            buttons={[
              {
                label: t('integrations.buttons.pay-invoice'),
                onPress: handlePayment,
                disabled:
                  !isContactValid || !paymentMethodData || paymentLoading,
              },
            ]}
            footer={<PayTermsAndConditionsForIntegration />}
            // eslint-disable-next-line i18next/no-literal-string
            backgroundColor="#F8F9F9"
          />
        </View>
      </View>
      {currentFlow === GUEST_FLOWS.ADD_NEW_CREDIT_CARD && (
        <AddNewCreditCardFlow
          onClose={onModalClose}
          onSuccess={handlePaymentMethodAdded}
          customRoute={getGuestPaymentMethod}
          invoiceId={invoice._id}
          phoneNumber={phoneNumber}
          isGuest={true}
        />
      )}
      {currentFlow === GUEST_FLOWS.PAYMENT_ERROR && (
        <PaymentErrorModal onClose={onModalClose} visible={true} />
      )}
      {currentFlow === GUEST_FLOWS.PAYMENT_SUCCESS && (
        <PayWithCreditCardFlow_PROCESSING
          invoiceTotalAmount={paymentMethodData?.payment?.totalAmount ?? 0}
          invoiceCompanyName={invoice?.company?.name ?? ''}
          paymentMethodName={paymentMethodData?.bankAccount?.name ?? ''}
          subtitle={paymentMethodSubtitle}
          onClose={() => {
            onPaymentSuccess?.()
            onModalClose()
          }}
        />
      )}
    </>
  )
}

const styles = StyleSheet.create({
  pageContainer: {
    flexDirection: 'row',
    width: '100%',
    paddingHorizontal: 200,
    justifyContent: 'center',
    paddingTop: 40,
    gap: 40,
  },
  leftSection: {
    flex: 1,
    marginRight: 20,
    maxWidth: 600,
  },
  rightSection: {
    flex: 1,
    maxWidth: 600,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#001929',
    marginBottom: 8,
  },
  billingAddressContainer: {
    marginTop: 20,
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 5,
    shadowOffset: { width: 0, height: 2 },
  },
  billingAddressText: {
    fontSize: 14,
    color: '#394D5A',
  },
  footerText: {
    fontSize: 12,
    color: '#394D5A',
    textAlign: 'center',
    marginTop: 20,
  },
  totalStyle: {
    fontWeight: '700',
    fontSize: 16,
    lineHeight: 21,
  },
})

export default PayAsGuestWeb
