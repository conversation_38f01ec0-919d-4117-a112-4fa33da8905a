import {
  BankAccount,
  Company,
  CustomerAccount,
  Invoice,
  LoanApplication,
  Operation,
  Project,
} from '../../models'
import {
  ICompany,
  ICustomerAccount,
  IInvoice,
  ILoanApplication,
  ILoanPaymentPlan,
  ILoanPricingPackage,
  IOperation,
} from '../../models/types'
import moment from 'moment-timezone'
import {
  DownPaymentStatus,
  ICredit,
  ILoan,
  LoanOrigin,
  LoanParametersChangeType,
  ReceivableStatus,
  ReceivableType,
  ScheduleStatus,
} from '../lms.service'
import round from 'lodash/round'
import {
  formatDate,
  formatNumericScore,
  formatPercentage,
  getAverageMonthlyCashFlow,
  getCohort,
  getCredits,
  getInterchangeOnVC,
  getLoans,
  getLoanType,
  getPaymentPlan,
  getPaymentPlans,
  getPricingPackage,
  getPricingPackages,
  getReceivablesAssessedTotal,
  getReceivablesCollectedTotal,
  getSixMonthAverageBalance,
  mapCoOwnersKnockouts,
  yesForRejected,
} from './utils'
import max from 'lodash/max'
import sum from 'lodash/sum'
import {
  IBankAccountModel,
  ILoanPaymentPlanModel,
  ILoanPriceModel,
  IProjectModel,
} from '@linqpal/models'
import { readKnockout } from './knockoutReader'
import { Logger } from '../logger/logger.service'
import {
  LmsCreditStatus,
  LOAN_APPLICATION_STATUS,
  PlaidStatus,
  States,
  statesHashMap,
} from '@linqpal/models/src/dictionaries'
import { readDraft } from '@linqpal/models/src/helpers/draftReader'
import { ReportFormatter } from './reportFormatter'
import { LOAN_STATUS } from '@linqpal/models/src/dictionaries/loanStatuses'
import { LoanApplicationService } from '../loanApplication.service'
import {
  AutomatedApprovalResult,
  AutomatedDecisionResult,
  IDrawApproval,
} from '../onBoarding/types'
import { LOAN_APPLICATION_TYPE } from '@linqpal/models/src/dictionaries/loanApplicationTypes'
import TradeCreditService from '../tradeCredit/tradeCredit.service'
import { DocumentVersioningService } from '../../../index'
import { AgreementType } from '../agreement/types'
import { min } from 'lodash'

type DrawType =
  | ''
  | 'VC'
  | 'Refi'
  | 'Supplier'
  | 'No Supplier' // legacy type for Direct Term for loans before March 2025
  | 'Direct Term'
  | 'Pilot'
  | 'Test'
  | 'Settlement'

interface ILoanApplicationInfo extends ILoanApplication {
  company: ICompany // customer / borrower
  bankAccount: IBankAccountModel
  customerAccount: ICustomerAccount
  latestDraft: any
  drawApproval: IDrawApproval
  supplier: ICompany
  invoice: IInvoice
  project: IProjectModel
  issueOperation: IOperation
}

export default async function transactionsReport(logger: Logger): Promise<any> {
  // use $project to reduce amount of data as much as possible to keep memory low on lambda
  const pipeline = [
    {
      $match: {
        issueDate: { $ne: null },
        status: {
          $in: [
            LOAN_APPLICATION_STATUS.APPROVED,
            LOAN_APPLICATION_STATUS.CLOSED,
          ],
        },
        lms_id: { $exists: true },
        $expr: { $and: [{ $ne: ['$lms_id', null] }] },
      },
    },
    // borrower company
    {
      $lookup: {
        from: Company.collection.name,
        as: 'company',
        let: { companyId: { $toObjectId: '$company_id' } },
        pipeline: [
          { $match: { $expr: { $eq: ['$_id', '$$companyId'] } } },
          {
            $project: {
              name: 1,
              legalName: 1,
              credit: 1,
              type: 1,
              bankAccounts: 1,
              settings: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: {
        path: '$company',
        preserveNullAndEmptyArrays: false,
      },
    },
    // bank account
    {
      $lookup: {
        from: BankAccount.collection.name,
        as: 'bankAccount',
        let: {
          ids: { $ifNull: ['$company.bankAccounts', []] },
        },
        pipeline: [
          {
            $match: {
              $and: [
                {
                  $expr: {
                    $in: ['$_id', '$$ids'],
                  },
                },
                {
                  $expr: {
                    $in: ['$status', ['verified', 'manualverified']],
                  },
                },
                {
                  $or: [
                    { isDeactivated: { $exists: true, $eq: false } },
                    { isDeactivated: { $exists: true, $eq: null } },
                    { isDeactivated: { $exists: false } },
                  ],
                },
                {
                  $or: [
                    { isPrimaryForCredit: true },
                    {
                      $and: [
                        { isPrimaryForCredit: { $exists: false } },
                        { isPrimary: true },
                      ],
                    },
                    {
                      $and: [
                        {
                          $or: [
                            { isPrimaryForCredit: null },
                            { isPrimaryForCredit: { $exists: false } },
                          ],
                        },
                        { isPrimary: true },
                      ],
                    },
                  ],
                },
              ],
            },
          },
          {
            $sort: {
              isPrimaryForCredit: -1, // prefer isPrimaryForCredit if any
              createdAt: -1,
            },
          },
          { $limit: 1 },
        ] as any, // workaround for $sort in $lookup
      },
    },
    {
      $unwind: {
        path: '$bankAccount',
        preserveNullAndEmptyArrays: true,
      },
    },
    // issue operation
    {
      $lookup: {
        from: Operation.collection.name,
        as: 'issueOperation',
        let: { appId: { $toString: '$_id' } },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$owner_id', '$$appId'] },
                  { $eq: ['$type', 'loan_issue'] },
                ],
              },
            },
          },
        ],
      },
    },
    {
      $unwind: {
        path: '$issueOperation',
        preserveNullAndEmptyArrays: true,
      },
    },
    // invoice
    {
      $lookup: {
        from: Invoice.collection.name,
        as: 'invoice',
        let: {
          invoiceId: {
            $convert: {
              input: {
                $cond: {
                  if: {
                    $eq: [{ $type: '$invoiceDetails.invoiceId' }, 'array'],
                  },
                  then: { $first: '$invoiceDetails.invoiceId' },
                  else: '$invoiceDetails.invoiceId',
                },
              },
              to: 'objectId',
              onError: null,
            },
          },
        },
        pipeline: [
          { $match: { $expr: { $eq: ['$_id', '$$invoiceId'] } } },
          {
            $project: {
              company_id: 1,
              project_id: 1,
              customer_account_id: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: {
        path: '$invoice',
        preserveNullAndEmptyArrays: true,
      },
    },
    // customer account
    {
      $lookup: {
        from: CustomerAccount.collection.name,
        as: 'customerAccount',
        let: {
          customerAccountId: {
            $convert: {
              input: '$invoice.customer_account_id',
              to: 'objectId',
              onError: null,
            },
          },
        },
        pipeline: [
          { $match: { $expr: { $eq: ['$_id', '$$customerAccountId'] } } },
        ],
      },
    },
    {
      $unwind: {
        path: '$customerAccount',
        preserveNullAndEmptyArrays: true,
      },
    },
    // latest draft
    {
      $lookup: {
        from: 'drafts',
        as: 'latestDraft',
        let: { companyId: '$company_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$company_id', '$$companyId'] },
                  { $eq: ['$type', 'general_application'] },
                ],
              },
            },
          },
          { $limit: 1 },
        ],
      },
    },
    {
      $unwind: {
        path: '$latestDraft',
        preserveNullAndEmptyArrays: true,
      },
    },
    // draw approval
    {
      $lookup: {
        from: 'drawapprovals',
        as: 'drawApproval',
        let: {
          drawApprovalId: {
            $convert: {
              input: '$drawApprovalId',
              to: 'objectId',
              onError: null,
            },
          },
        },
        pipeline: [
          { $match: { $expr: { $eq: ['$_id', '$$drawApprovalId'] } } },
        ],
      },
    },
    {
      $unwind: {
        path: '$drawApproval',
        preserveNullAndEmptyArrays: true,
      },
    },
    // project
    {
      $lookup: {
        from: Project.collection.name,
        as: 'project',
        let: {
          projectId: {
            $convert: {
              input: { $toString: '$invoice.project_id' },
              to: 'objectId',
              onError: null,
            },
          },
        },
        pipeline: [
          { $match: { $expr: { $eq: ['$_id', '$$projectId'] } } },
          {
            $project: {
              _id: 1,
              address: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: {
        path: '$project',
        preserveNullAndEmptyArrays: true,
      },
    },
    // supplier
    {
      $lookup: {
        from: Company.collection.name,
        as: 'supplier',
        let: {
          supplierId: {
            $convert: {
              input: '$invoice.company_id',
              to: 'objectId',
              onError: null,
            },
          },
        },
        pipeline: [
          { $match: { $expr: { $eq: ['$_id', '$$supplierId'] } } },
          {
            $project: {
              name: 1,
              legalName: 1,
              isGuest: 1,
              settings: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: {
        path: '$supplier',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $project: {
        // There are test companies with large number of bank accounts & loan applications,
        // which result in huge amount of errors in Giact step output, so join result exceeds Mongo limits.
        // So take only necessary output steps
        company_id: 1,
        issueDate: 1,
        approvedAmount: 1,
        invoiceDetails: 1,
        draft: 1,
        latestDraft: 1,
        metadata: 1,
        loanpro_id: 1,
        lms_id: 1,
        fundingSource: 1,
        type: 1,
        outputs: {
          $filter: {
            input: '$outputs',
            as: 'output',
            cond: {
              $in: [
                '$$output.step',
                [
                  'Knockout',
                  'creditStatus',
                  'ProcessManualData',
                  'ProcessFinicityData',
                  'ProcessPlaidData',
                ],
              ],
            },
          },
        },
        company: 1,
        bankAccount: 1,
        invoice: 1,
        customerAccount: 1,
        drawApproval: 1,
        project: 1,
        supplier: 1,
        issueOperation: 1,
      },
    },
  ]

  const apps = await LoanApplication.aggregate(pipeline)
    .sort({ issueDate: -1 })
    .allowDiskUse(true)

  logger.info(`found ${apps.length} applications`)

  const [loans, paymentPlans, pricingPackages] = await Promise.all([
    getLoans(apps),
    getPaymentPlans(apps),
    getPricingPackages(apps),
  ])

  const companyIds = Array.from(new Set(apps.map((app) => app.company_id)))

  // postgres may fail on multiple simultaneous requests, so execute sequentially
  const credits = await getCredits(companyIds)
  const agreementApprovalsPerCompany = await getAgreementApprovalsPerCompany(
    apps,
  )
  const daysLatePerCompany = getDaysLatePerCompany(companyIds, loans)
  const bankAccountActivityPerCompany = getBankAccountActivityPerCompany(
    companyIds,
    apps,
  )

  logger.info(`found ${loans.length} loans`)
  logger.info(
    `pre-fetched ${paymentPlans?.size} missed payment plans and ${pricingPackages?.size} missed pricing packages`,
  )

  return Promise.all(
    apps.map(async (app) => {
      const loan = loans.find((a) => a.id === app.lms_id) ?? null
      return mapReport(
        app,
        loan,
        apps[apps.length - 1],
        paymentPlans,
        pricingPackages,
        credits.get(app.company_id),
        daysLatePerCompany.get(app.company_id) ?? 0,
        bankAccountActivityPerCompany.get(app.company_id) ?? false,
        agreementApprovalsPerCompany.get(app.company_id) ?? true, // no approval requests for legacy companies
        logger,
      )
    }),
  )
}

async function mapReport(
  app: ILoanApplicationInfo,
  loan: ILoan | null,
  firstEverApp: ILoanApplication,
  paymentPlans: Map<string, ILoanPaymentPlan | null>,
  pricingPackages: Map<string, ILoanPricingPackage | null>,
  credit: ICredit | undefined,
  daysLate: number,
  isBankAccountActive: boolean,
  areAgreementsApproved: boolean,
  logger: Logger,
) {
  const { company, supplier, invoice, project, issueOperation } = app

  const [paymentPlan, pricingPackage] = await Promise.all([
    getPaymentPlan(app, paymentPlans),
    getPricingPackage(app, pricingPackages, supplier),
  ])

  const draft = readDraft(app.draft)
  const knockout = readKnockout(app)
  const coOwnersReport = mapCoOwnersKnockouts(knockout)

  const drawType = getDrawType(app, loan)

  const creditStatus =
    app.outputs.find((o) => o.step === 'creditStatus')?.data || {}

  const fees = getDrawFees(loan, paymentPlan)
  const drawSchedule = getDrawSchedule(loan, paymentPlan)
  const payments = getDrawPayments(loan)
  const supplierPayments = getSupplierPayments(
    app,
    pricingPackage,
    issueOperation,
    drawType,
    logger,
  )

  const interchangeOnVC = getInterchange(app, loan, paymentPlan)
  const debtCapital = getDebtCapital(app, issueOperation)
  const downPayment = getDownPayment(loan)

  // prettier-ignore
  return {
    'Draw / Transaction ID': app.loanpro_id || app.lms_id,
    'Draw Type': drawType,
    'LOC Security Deposit': (company.settings as any)?.depositDetails?.isSecured
        ? ReportFormatter.addCurrencyCell(company.settings?.depositDetails?.depositAmount ?? 0)
        : ReportFormatter.addCurrencyCell(undefined, ''),
    'GL': getGeneralLedgerNumber(app, paymentPlan),

    'Customer': company?.legalName || company?.name || '',
    'CustomerID': app.company_id,
    'Borrower State': getBorrowerState(app),
    'Borrower Zip': app.draft?.businessOwner_address?.zip,

    'LOC ID': company?.credit?.LoCnumber?.toString() ?? '',
    'Supplier': supplier?.name || supplier?.legalName || '',
    'Cohort ID': getCohort(app, firstEverApp),
    'Draw Current Status': getDrawStatus(loan),
    'Product Account Status': mapAccountStatus(credit, daysLate, isBankAccountActive, areAgreementsApproved),
    'Acquisition Channel': '',
    'Auto-pay': app.metadata?.payment_cancelled ? 'Disabled' : 'Enabled',
    'Refinancing Date': getRefinancingDate(app.lms_id),
    'Transaction Merchant / SupplierID': supplier?._id?.toString() ?? '',

    'Invoices': Array.isArray(app.invoiceDetails?.invoiceId)
      ? app.invoiceDetails.invoiceId.join(', ')
      : app.invoiceDetails?.invoiceId ?? '',

    'Project ID': project?._id?.toString() ?? '',
    'Project Address': project?.address ?? '',
    'Transaction Date': formatDate(app.issueDate),
    'Quote or Invoice approval': app.type === LOAN_APPLICATION_TYPE.QUOTE ? 'Quote' : 'Invoice',
    'ATC On': app.metadata?.repayment?.autoTradeCreditEnabled ? 'Yes' : 'No',
    'Automation on':
      app.drawApproval?.automatedApprovalResult === AutomatedApprovalResult.Passed &&
      app.drawApproval?.automatedDecisionResult === AutomatedDecisionResult.Pass
        ? 'Yes'
        : 'No',
    'Original Principal Balance (Amount requested by borrower)': ReportFormatter.addCurrencyCell(payments.requestedAmount),
    'Original Draw Amount (with origination fee)': ReportFormatter.addCurrencyCell(payments.originalDrawAmount),
    'First Draw Repayment Date Scheduled': formatDate(drawSchedule.firstPayment),
    'Original Repayment Term': drawSchedule.originalPaymentTerm,
    'Current Repayment Term': drawSchedule.currentPaymentTerm,
    'Draw Down Payment %': formatPercentage(downPayment.percentage),
    'Original Maturity date': formatDate(drawSchedule.originalLastPayment),
    'Current maturity date': formatDate(drawSchedule.currentLastPayment),

    'Payment Amount at origination': ReportFormatter.addCurrencyCell(payments.originalPaymentAmount),
    'Down Payment Collection Date': downPayment.collectedAt,
    'Payment Amount after extension':
      payments.currentPaymentAmount !== 'custom'
        ? ReportFormatter.addCurrencyCell(Number(payments.currentPaymentAmount))
        : payments.currentPaymentAmount,

    'Remaining number of Payments': drawSchedule.remainingNumberOfPayments,
    'Days late': drawSchedule.daysLate,
    'Total Balance': ReportFormatter.addCurrencyCell(payments.totalBalance),
    'Outstanding Balance': ReportFormatter.addCurrencyCell(payments.outstandingBalance),
    'Principal Balance with origination fee': ReportFormatter.addCurrencyCell(payments.currentPrincipalBalance),
    'Principal Balance without origination fee': ReportFormatter.addCurrencyCell(payments.principalBalanceWithoutFee),
    'Refinanced Outstanding Principal': ReportFormatter.addCurrencyCell(payments.refinancedOutstandingPrincipal),
    'Refinanced Outstanding Fees and Penalties': ReportFormatter.addCurrencyCell(payments.refinancedOutstandingFees),
    'Total Fee and Interest Balance': ReportFormatter.addCurrencyCell(fees.unpaidFeeTotal),
    'Total Fees Collected': ReportFormatter.addCurrencyCell(fees.paidFeeTotal),
    'Number of payments late': loan?.loanDetails?.numberOfLatePayments ?? 0,
    'Number of payments missed': loan?.loanDetails?.numberOfMissedPayments ?? 0,
    'Number of payments failed': loan?.loanDetails?.numberOfFailedPayments ?? 0,
    'Next Payment due amount': ReportFormatter.addCurrencyCell(loan?.loanDetails?.nextPaymentAmount),
    'Past Due Amount': ReportFormatter.addCurrencyCell(loan?.loanDetails?.lateAmount),

    'Number of Payments Collected for this draw so far': payments.numberOfPayments,
    'Total Payment Amount Received': ReportFormatter.addCurrencyCell(loan?.loanDetails?.totalPaid),
    'Down Payment Amount Received': ReportFormatter.addCurrencyCell(downPayment.paidAmount, ''),
    'Amount refunded to the customer': '', // placeholder
    'Processing Payments Amount': ReportFormatter.addCurrencyCell(loan?.loanDetails?.totalProcessingPaymentsAmount),
    'Next payment due date': formatDate(loan?.loanDetails?.nextPaymentDate),

    'Origination Fee rate': formatPercentage(fees.loanFeeRate),
    'Origination Fee amount': ReportFormatter.addCurrencyCell(fees.loanFee),
    'Penalty Interest Assessed Amount': ReportFormatter.addCurrencyCell(fees.assessedPenaltyInterestAmount),
    'Penalty Interest Collected Amount': ReportFormatter.addCurrencyCell(fees.paidPenaltyInterestAmount),
    'Late Fee Assessed Amount': ReportFormatter.addCurrencyCell(fees.assessedLateFeeAmount),
    'Late Fee Collected Amount': ReportFormatter.addCurrencyCell(fees.paidLateFeeAmount),
    'Returned Payment Fee Collected Amount': '', // placeholder

    'Early Payment Discount %': '', // placeholder
    'Early Payment Discount Period': '', // placeholder
    'Early Payment Discount Amount': '', // placeholder

    'Supplier Fee Rate': formatPercentage(supplierPayments.feeRate),
    'Supplier Fee Amount': ReportFormatter.addCurrencyCell(supplierPayments.feeAmount),
    'Rescheduling Fee Collected': ReportFormatter.addCurrencyCell(fees.paidExtensionFeeAmount),
    'Rescheduling Fee Assessed': ReportFormatter.addCurrencyCell(fees.assessedExtensionFeeAmount),
    'Supplier Advance Rate': formatPercentage(supplierPayments.advanceRate),
    'Supplier Advance Amount': ReportFormatter.addCurrencyCell(supplierPayments.advanceAmount),
    '% of Recourse to supplier': formatPercentage(supplierPayments.recoursePercentage),
    'Supplier Final Payment Amount': ReportFormatter.addCurrencyCell(supplierPayments.finalPaymentAmount),

    'Interchange Rate': formatPercentage(interchangeOnVC.rate),
    'Interchange Amount': ReportFormatter.addCurrencyCell(interchangeOnVC.amount),
    'Rebate Pct': pricingPackage?.merchantRebate ?? 0,
    'Applied rebate': '',

    'Payment Source': 'Borrower',
    'Payment Type': 'ACH-Auto',
    'Pay Frequency': paymentPlan?.term === 1 ? 'S' : 'W',
    'BT Risk Pct': 100,
    'Debt Capital Provider': debtCapital.provider,
    'Debt Capital %': debtCapital.percentage,
    'Debt Funding Amount': ReportFormatter.addCurrencyCell(debtCapital.fundingAmount),
    'Total Amount Re-aged': '', // placeholder
    'Max Days late': drawSchedule.maxDaysLate,

    'LOC credit limit on date of draw request': '', // placeholder
    'BT Balance to credit ratio before draw': '', // placeholder
    'BT Balance to credit ratio after draw': '', // placeholder
    'Supplier ID for the draw is the sponsoring supplier': invoice?.company_id ? 'Yes' : 'No',
    'Invoice is only for materials': '', // placeholder
    'Mechanics lien notice sent?': '', // placeholder
    'Mechanics lien in place?': '', // placeholder
    'Exceptions made to draw approval?': '', // placeholder

    'Company bankruptcy in the last 24 months': yesForRejected(knockout.companyBankruptcy),
    'Any $5,000+ judgement in the past 12 months': yesForRejected(knockout.judgments),
    'Any $5,000+ lien in the past 12 months': yesForRejected(knockout.liens),
    'Lexis Nexis BVI score': formatNumericScore(knockout.BVI),
    'Lexis Nexis BRI score': formatNumericScore(knockout.BRI),

    // principal owner initial
    'Principal Owner First Name': draft?.businessOwner_firstName,
    'Principal Owner Last Name': draft?.businessOwner_lastName,
    "Latest principal owner's FICo, acceptable is >620 or null": knockout.FICO?.score?.toString() ?? '',
    "Principal owner's personal bankruptcy in the last 24 months": yesForRejected(knockout.personalBankruptcy),
    'Principal Owner Lexis Nexis CVI score': formatNumericScore(knockout.CVI),
    'Principal Owner Lexis Nexis CRI score': formatNumericScore(knockout.CRI),
    'Principal Owner Email age': formatNumericScore(knockout.emailAge),

    // co-owners
    'Co-owner 1 First Name / Business Entity': coOwnersReport[0]?.firstOrBusinessName ?? '',
    'Co-owner 1 Last Name': coOwnersReport[0]?.lastName ?? '',
    "Latest co-owner1's FICo, acceptable is >620 or null": coOwnersReport[0]?.FICO ?? '',
    "Co-owner1's personal bankruptcy in the last 24 months": coOwnersReport[0]?.bankruptcy ?? '',
    'Co-owner 1 Lexis Nexis CVI score': coOwnersReport[0]?.CVI ?? '',
    'Co-owner 1 Lexis Nexis CRI score': coOwnersReport[0]?.CRI ?? '',
    'Co-owner 1 Email age': coOwnersReport[0]?.emailAge ?? '',

    'Co-owner 2 First Name / Business Entity': coOwnersReport[1]?.firstOrBusinessName ?? '',
    'Co-owner 2 Last Name': coOwnersReport[1]?.lastName ?? '',
    "Latest co-owner2's FICo, acceptable is >620 or null": coOwnersReport[1]?.FICO ?? '',
    "Co-owner2's personal bankruptcy in the last 24 months": coOwnersReport[1]?.bankruptcy ?? '',
    'Co-owner 2 Lexis Nexis CVI score': coOwnersReport[1]?.CVI ?? '',
    'Co-owner 2 Lexis Nexis CRI score': coOwnersReport[1]?.CRI ?? '',
    'Co-owner 2 Email age': coOwnersReport[1]?.emailAge ?? '',

    'Co-owner 3 First Name / Business Entity': coOwnersReport[2]?.firstOrBusinessName ?? '',
    'Co-owner 3 Last Name': coOwnersReport[2]?.lastName ?? '',
    "Latest co-owner3's FICo, acceptable is >620 or null": coOwnersReport[2]?.FICO ?? '',
    "Co-owner3's personal bankruptcy in the last 24 months": coOwnersReport[2]?.bankruptcy ?? '',
    'Co-owner 3 Lexis Nexis CVI score': coOwnersReport[2]?.CVI ?? '',
    'Co-owner 3 Lexis Nexis CRI score': coOwnersReport[2]?.CRI ?? '',
    'Co-owner 3 Email age': coOwnersReport[2]?.emailAge ?? '',

    'Business Experian Credit Score': creditStatus?.reliabilityCode ?? '',
    '% of Account +60 DBT': creditStatus?.tradelinesPercentage ?? '',
    'Amount of Account +60 DBT': ReportFormatter.addCurrencyCell(creditStatus?.tradelinesDebt),
    'Business Experian Total Trade Lines': ReportFormatter.addCurrencyCell(creditStatus.tradelinesBalance),
    'Experian debt-to-credit ratio with non-BT trade lines': '', // placeholder
    'Experian Monthly Debt Obligations': '', // placeholder
    'Six month average bank balance': ReportFormatter.addCurrencyCell(getSixMonthAverageBalance(app)),
    'BT-computed Yearly Income based on Plaid Data': '', // TODO: VK: Plaid
    'Plaid-computed Yearly Business Income': '', // TODO: VK: Plaid
    'Average Monthly Cashflow': ReportFormatter.addCurrencyCell(getAverageMonthlyCashFlow(app)),
    'DTI1 (actual DTI based on monthly obligations before issuance) (latest)': '', // TODO: VK: Plaid
    'DTI2 (based on total debt before issuance) (latest)': '', // TODO: VK: Plaid
    'Credit Policy used (latest)': app.metadata?.creditPolicyVersion ?? 'v2.0',
    'Was schedule extension a re-age?': '', // placeholder
  }
}

function getBorrowerState(app: ILoanApplicationInfo) {
  let abbreviation =
    statesHashMap[app.draft?.businessOwner_address?.state as States]

  if (!abbreviation) {
    const state = app.latestDraft?.data?.businessInfo?.items?.find(
      (i: any) => i.identifier === 'businessAddress',
    )?.content?.state

    abbreviation = statesHashMap[state as States]
  }

  return abbreviation ?? ''
}

function getDrawType(app: ILoanApplicationInfo, loan: ILoan | null) {
  // North American Builders Supply Inc
  if (
    ['62ec7620de543206b9982584', '640786b9d1c8ad47e2d08367'].includes(
      app.supplier?._id?.toString(),
    )
  ) {
    return 'Test'
  }

  // NABS Settlement Account
  if (app.company_id?.toString() === '66a3fb9a71bb4d6db2213bae') {
    return 'Settlement'
  }

  if (
    [
      '638f5e87eaeb94619ed07393', // Timeline Construction LLC
      '6312596cc2d29774b59fb6d5', // ADU Works
      '63172a535adbc766e571189d', // ADU Works
      '60fabce7baddc00009205199', // Bob's Container
      '60fabb4dbaddc00009205136', // Bob's Container / Masholding
      '62ffe79c9a8bbda141b9548c', // Bob's Containers / Quality Operating Company LLC
      '64f738789185a654ea9dce56', // Bob's Containers
    ].includes(app.company_id)
  ) {
    return 'Pilot'
  }

  if (
    [
      '59311f54-37cb-4550-970d-c5d59b6cbc17',
      'd1d3203b-83bf-404f-b214-d35712210f06',
      '1cf2c18e-e3ca-4c5c-b05b-a646cea97ea1',
      '1a9447f2-ae01-4ce2-9ab8-ff3aaa117835',
      '1d954105-09d1-4af5-90b2-c6f2a2798138',
      '0948a12a-10d5-44f8-a1c0-dd77f325e788',
      '18bd4485-43f4-4792-a1e3-f781eb897134',
      'c0787cd3-5b7e-4d9f-b831-f30a892ad82e',
      'c812ed01-a1b2-493f-acc2-6328c9aaa7df',
      '73a52375-7d45-4cad-a5ad-0e162afa9859',
      '5d7481ae-31f3-4d64-9fd5-44e170db32da',
      'c0787cd3-5b7e-4d9f-b831-f30a892ad82e',
      '0f036dca-29cc-457c-8932-009e814723c1',
      '6002e4bb-e56e-4d3b-8272-07a84aefefa8',
    ].includes(app.lms_id)
  ) {
    return 'Refi'
  }

  if (app.invoice?.company_id && !app.supplier?.isGuest) {
    return 'Supplier'
  } else if (
    loan?.loanOrigin?.toLowerCase() === LoanOrigin.NoSupplier.toLowerCase()
  ) {
    return moment(app.issueDate).isBefore('2025-03-01')
      ? 'No Supplier'
      : 'Direct Term'
  } else if (
    app.invoiceDetails?.invoiceId &&
    app.invoiceDetails.invoiceId.length > 0
  ) {
    return 'VC'
  } else {
    return ''
  }
}

function getDrawStatus(loan: ILoan | null) {
  if (!loan) return ''

  const receivables = loan.loanReceivables.filter(
    (r) =>
      r.scheduleStatus === ScheduleStatus.Current &&
      r.status !== ReceivableStatus.Canceled,
  )

  const allPaid = receivables.every((r) => r.status === ReceivableStatus.Paid)

  const hasLateReceivables = receivables.some(
    (r) =>
      // paid late
      (r.status === ReceivableStatus.Paid &&
        moment(r.paidDate).isAfter(moment(r.expectedDate))) ||
      // overdue handled by overdue detector
      r.status === ReceivableStatus.Late ||
      // overdue not yet handled by overdue detector
      (r.status === ReceivableStatus.Pending &&
        moment.utc().isAfter(moment.tz(r.expectedDate, 'UTC').endOf('day'))),
  )

  const hasUnpaidPenalties = receivables.some(
    (r) =>
      r.type === ReceivableType.PenaltyInterestFee &&
      [ReceivableStatus.Late, ReceivableStatus.Pending].includes(r.status),
  )

  return loan.status === LOAN_STATUS.REFINANCED
    ? 'Refinanced'
    : allPaid
    ? 'Fully repaid'
    : hasUnpaidPenalties
    ? 'Penalty Status'
    : hasLateReceivables
    ? 'Late (no penalty status)'
    : 'Good standing'
}

function getDrawPayments(loan: ILoan | null) {
  const requestedAmount = round(loan?.amount || 0, 2)
  const loanFee = round(loan?.fee || 0, 2)
  const refund = round(loan?.refundAmount || 0, 2)

  const outstandingBalance = round(
    loan?.loanDetails?.loanOutstandingAmount || 0,
    2,
  )

  const totalBalance = round(loan?.loanDetails?.totalBalance || 0, 2)

  const currentPrincipalBalance = round(
    loan?.loanDetails?.principalBalance || 0,
    2,
  )

  const principalBalanceWithoutFee = round(
    loan?.loanDetails?.principalBalanceWithoutFee || 0,
    2,
  )

  const refinancedOutstandingPrincipal = round(
    loan?.loanDetails?.refinancedPrincipalBalance || 0,
    2,
  )

  const refinancedOutstandingFees = round(
    loan?.loanDetails?.refinancedOutstandingFeesAmount || 0,
    2,
  )

  const originalDrawAmount = round(requestedAmount + loanFee, 2)

  const currentLoanParams = loan?.loanParameters?.find((p) => p.isActive)
  const initialLoanParams = loan?.loanParameters?.find(
    (p) => p.changeType === LoanParametersChangeType.Initial,
  )

  const originalPaymentAmount = initialLoanParams?.installmentsNumber
    ? round(originalDrawAmount / initialLoanParams.installmentsNumber, 2)
    : 0

  const currentPaymentAmount =
    loan?.loanReceivables?.length && currentLoanParams?.installmentsNumber
      ? round(
          (originalDrawAmount - refund) / currentLoanParams.installmentsNumber,
          2,
        )
      : 0

  const wasRescheduled = loan?.loanReceivables?.some(
    (r) =>
      r.type === ReceivableType.Installment &&
      r.scheduleStatus === ScheduleStatus.Rescheduled,
  )

  const payments = loan?.payments.filter((p) => p.status === 'Success') ?? []

  return {
    requestedAmount,
    originalDrawAmount,
    originalPaymentAmount,
    currentPaymentAmount: wasRescheduled ? 'custom' : currentPaymentAmount,
    totalBalance,
    outstandingBalance,
    currentPrincipalBalance,
    principalBalanceWithoutFee,
    refinancedOutstandingPrincipal,
    refinancedOutstandingFees,
    numberOfPayments: payments.length,
  }
}

function getGeneralLedgerNumber(
  app: ILoanApplicationInfo | null,
  paymentPlan: ILoanPaymentPlanModel | null,
) {
  // keep this separated just for information
  const ledgerType = app?.invoiceDetails?.cardId
    ? 'VC'
    : paymentPlan?.frequency === 'single'
    ? 'BT Single'
    : paymentPlan?.type === 'nosupplier'
    ? 'BT NS'
    : 'BT Regular'

  const accountNumbers = {
    'BT Regular': '18501',
    VC: '16001',
    'BT NS': '17501',
    'BT Single': '15501',
  }

  return accountNumbers[ledgerType]
}

function getDrawFees(
  loan: ILoan | null,
  paymentPlan: ILoanPaymentPlanModel | null,
) {
  const fees =
    loan?.loanReceivables?.filter(
      (r) =>
        r.scheduleStatus === ScheduleStatus.Current &&
        r.status !== ReceivableStatus.Canceled &&
        [
          ReceivableType.ExtensionFee,
          ReceivableType.LoanFee,
          ReceivableType.LatePaymentFee,
          ReceivableType.ManualLatePaymentFee,
          ReceivableType.PenaltyInterestFee,
        ].includes(r.type),
    ) ?? []

  const unpaidFeeTotal = round(
    loan?.loanDetails?.totalOutstandingFeesAmount ?? 0,
    2,
  )

  const paidFees = fees.filter((f) => f.status === ReceivableStatus.Paid)
  const paidFeeTotal = sum(paidFees.map((f) => f.paidAmount))

  const paidPenaltyInterestAmount = getReceivablesCollectedTotal(
    paidFees,
    ReceivableType.PenaltyInterestFee,
  )

  const paidLateFeeAmount = getReceivablesCollectedTotal(paidFees, [
    ReceivableType.LatePaymentFee,
    ReceivableType.ManualLatePaymentFee,
  ])

  const paidExtensionFeeAmount = getReceivablesCollectedTotal(
    paidFees,
    ReceivableType.ExtensionFee,
  )

  const assessedPenaltyInterestAmount = getReceivablesAssessedTotal(
    fees,
    ReceivableType.PenaltyInterestFee,
  )

  const assessedLateFeeAmount = getReceivablesAssessedTotal(fees, [
    ReceivableType.LatePaymentFee,
    ReceivableType.ManualLatePaymentFee,
  ])

  const assessedExtensionFeeAmount = getReceivablesAssessedTotal(
    fees,
    ReceivableType.ExtensionFee,
  )

  return {
    paidFeeTotal,
    unpaidFeeTotal,
    paidPenaltyInterestAmount,
    paidLateFeeAmount,
    paidExtensionFeeAmount,
    assessedPenaltyInterestAmount,
    assessedLateFeeAmount,
    assessedExtensionFeeAmount,
    loanFeeRate: paymentPlan?.fee,
    loanFee: round(loan?.fee || 0, 2),
  }
}

function getDrawSchedule(
  loan: ILoan | null,
  paymentPlan: ILoanPaymentPlanModel | null,
) {
  // oldest first
  const receivables =
    loan?.loanReceivables?.sort((earlier, later) =>
      moment(earlier.expectedDate).diff(moment(later.expectedDate)),
    ) || []

  const wasRescheduled = receivables.some(
    (r) =>
      r.type === ReceivableType.Installment &&
      r.scheduleStatus === ScheduleStatus.Rescheduled,
  )

  const originalPaymentTerm = paymentPlan?.days ?? 0

  const currentLastPayment = receivables?.length
    ? receivables[receivables.length - 1].expectedDate
    : ''

  const daysLate = loan?.loanDetails?.daysLate || 0

  let maxDaysLate =
    max(
      receivables.map((r) =>
        moment(r.paidDate || undefined).diff(moment(r.expectedDate), 'days'),
      ),
    ) || 0

  if (maxDaysLate < 0) maxDaysLate = 0
  if (daysLate > maxDaysLate) maxDaysLate = daysLate

  return {
    firstPayment: receivables[0]?.expectedDate || '',
    originalPaymentTerm,
    currentPaymentTerm: wasRescheduled ? 'custom' : originalPaymentTerm,
    originalLastPayment: '',
    currentLastPayment,
    remainingNumberOfPayments:
      loan?.loanDetails?.remainingNumberOfPayments ?? 0,
    daysLate,
    maxDaysLate,
  }
}

function getSupplierPayments(
  app: ILoanApplicationInfo,
  packageMetadata: ILoanPriceModel | undefined,
  issueOperation: IOperation | null,
  drawType: DrawType,
  logger: Logger,
) {
  const paidToMerchant = round(issueOperation?.amount ?? 0, 2)

  const feeRate = packageMetadata?.merchant || 0
  const advanceRate = packageMetadata?.advanceRate || 100
  const advanceAmount = drawType !== 'Refi' ? paidToMerchant : 0

  const recoursePercentage =
    app.supplier?.settings?.sendFinalPaymentWhenLoanIsPaid &&
    app.customerAccount?.settings?.sendFinalPaymentWhenLoanIsPaid
      ? round(100 - advanceRate, 2)
      : 0

  let feeAmount = 0
  let finalPaymentAmount = 0

  if (packageMetadata) {
    const payment = LoanApplicationService.calculatePayments(
      app,
      app.approvedAmount,
      packageMetadata,
    )

    finalPaymentAmount = payment.final.amount
    feeAmount = payment.merchantFee

    if (Number(payment.advance.amount.toFixed(2)) !== paidToMerchant)
      logger.warn(
        { payment, paidToMerchant },
        `advance amount mismatch for app ${app.lms_id}`,
      )
  }

  return {
    feeRate,
    feeAmount,
    advanceRate,
    advanceAmount,
    finalPaymentAmount,
    recoursePercentage,
  }
}

function getInterchange(
  app: ILoanApplication,
  loan: ILoan | null,
  paymentPlan: ILoanPaymentPlanModel | null,
) {
  const type = getLoanType(app, paymentPlan)
  return type === 'VC' ? getInterchangeOnVC(loan) : { rate: 0, amount: 0 }
}

function getDebtCapital(
  app: ILoanApplication,
  issueOperation: IOperation | null,
) {
  const issuedAmount = round(issueOperation?.amount ?? 0, 2)

  const provider =
    app.fundingSource === 'arcadia'
      ? moment(app.issueDate).isAfter(moment('2023-01-19'))
        ? 'ARCT 2022-BlueTape'
        : 'CC IV'
      : app.fundingSource ?? ''

  const percentage = app.fundingSource ? '100%' : ''
  const fundingAmount = app.fundingSource ? issuedAmount : 0 // Amount from debt facility to issue loan

  return {
    provider,
    percentage,
    fundingAmount,
  }
}

function getDaysLatePerCompany(
  companyIds: any[],
  loans: FlatArray<ILoan[][], 1>[],
) {
  const daysLatePerCompany = new Map<string, number>(
    companyIds.map((companyId) => {
      const companyLoans = loans.filter((l: ILoan) => l.companyId === companyId)
      const maxDaysLate = Math.max(
        ...companyLoans.map((loan) => loan?.loanDetails?.daysLate ?? 0),
      )
      return [companyId, maxDaysLate]
    }),
  )
  return daysLatePerCompany
}

function getBankAccountActivityPerCompany(
  companyIds: any[],
  apps: ILoanApplicationInfo[],
) {
  // true = active account, false = inactive
  const bankAccountActivityPerCompany = new Map<string, boolean>(
    companyIds.map((id) => {
      const companyApps = apps.filter((app) => app.company_id === id)
      const latestApp = companyApps[0]

      if (!latestApp?.bankAccount) {
        return [id, false]
      }

      const plaidStatus = latestApp.bankAccount.plaid?.status

      if (
        plaidStatus === PlaidStatus.DISCONNECTED ||
        plaidStatus === PlaidStatus.EXPIRED
      ) {
        return [id, false]
      }

      if (latestApp.bankAccount.isManualEntry) {
        return [id, TradeCreditService.hasActualCashFlow(latestApp)]
      }

      return [id, true]
    }),
  )

  return bankAccountActivityPerCompany
}

function getDownPayment(loan: ILoan | null) {
  if (loan?.downPaymentStatus === DownPaymentStatus.NotRequired) {
    return {}
  }

  return {
    paidAmount: min([
      loan?.loanDetails?.totalPaid ?? 0,
      loan?.loanParameters?.[0]?.downPaymentAmount ?? 0,
    ]),
    percentage: loan?.loanParameters?.[0]?.downPaymentPercentage,
    collectedAt:
      loan?.downPaymentStatus === DownPaymentStatus.Paid
        ? moment(loan.downPaymentStatusAt).format('MM/DD/YYYY')
        : '',
  }
}

async function getAgreementApprovalsPerCompany(apps: ILoanApplicationInfo[]) {
  const approvalsMap = new Map<string, boolean>()

  for (const app of apps) {
    const companyId = app.company_id

    if (approvalsMap.has(companyId)) {
      continue
    }

    const approvalsResponse =
      await DocumentVersioningService.getApprovalsForInitialVersion(
        companyId,
        AgreementType.MASTER_AGREEMENT,
      )

    approvalsMap.set(
      companyId,
      approvalsResponse?.approvals?.every((a) => a.isApproved) ?? true,
    )
  }

  return approvalsMap
}

function mapAccountStatus(
  credit: ICredit | undefined,
  daysLate: number,
  isBankAccountActive: boolean,
  areAgreementsApproved: boolean,
) {
  if (credit?.status === LmsCreditStatus.InCollection) {
    return 'Delinquent/Collection'
  }

  if (credit?.status === LmsCreditStatus.Closed) {
    return 'Closed'
  }

  if (!isBankAccountActive) {
    return 'Bank connection hold'
  }

  if (daysLate > 0 && daysLate < 10) {
    return '0-10d Late'
  }

  if (daysLate >= 10 && daysLate < 60) {
    return 'Past Due'
  }

  if (daysLate >= 60) {
    return 'Delinquent/Collection'
  }

  if (!areAgreementsApproved) {
    return 'Pending Agreements'
  }

  if (credit?.status === LmsCreditStatus.OnHold) {
    return 'On Hold'
  }

  return 'Good Standing'
}

function getRefinancingDate(lmsId: string) {
  const refinancingDates = [
    '2024-08-03',
    '2024-08-03',
    '2024-08-03',
    '2024-08-03',
    '2024-08-03',
    '2024-08-03',
    '2024-08-03',
    '2024-10-07',
    '2024-10-07',
    '2024-08-03',
    '2024-08-03',
    '2024-10-07',
    '2024-08-03',
    '2024-08-03',
    '2024-08-03',
    '2024-08-03',
    '2024-08-03',
    '2024-08-03',
    '2024-08-03',
    '2024-10-21',
    '2024-08-03',
    '2024-08-03',
    '2024-10-21',
    '2024-08-03',
    '2024-08-03',
    '2024-08-03',
    '2024-08-03',
    '2024-08-03',
    '2024-08-03',
    '2024-08-03',
    '2024-08-03',
    '2024-10-21',
    '2024-08-03',
    '2024-11-07',
    '2024-05-31',
    '2024-05-31',
    '2024-08-03',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-05-31',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-05-31',
    '2024-05-31',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-01-22',
    '2024-07-19',
    '2024-07-19',
    '2024-07-19',
    '2024-07-19',
    '2024-07-19',
    '2023-09-07',
    '2023-09-07',
    '2023-09-07',
    '2023-09-06',
    '2024-07-19',
    '2024-07-19',
    '2024-07-19',
    '2024-07-19',
    '2024-07-19',
    '2024-07-19',
    '2024-07-19',
    '2024-07-19',
    '2024-07-19',
    '2024-07-19',
    '2025-01-31',
  ]

  const drawIndex = REFINANCED_DRAWS.indexOf(lmsId)

  return drawIndex >= 0 ? refinancingDates[drawIndex] : ''
}

export const REFINANCED_DRAWS = [
  'b52c2b1e-73b4-4d03-80f3-60fa109752b1',
  'e5e6cc48-c259-437b-afe6-a9dac0be4df1',
  '11f278b1-304e-48c0-be47-936b979f0285',
  '5ac6b4bf-80b2-4561-983a-b4c9001c44ea',
  'cb3a8034-ebd7-4e1b-8112-c9409cfee2a0',
  'b2fe3d23-2610-4a70-b3fe-a25417b1ac9c',
  '8c8d1fd3-0f57-4661-8e1c-42491b1c468c',
  '839dfd1e-a0e6-4112-9a68-b4e3421aaff0',
  'e00566a9-bc27-4305-8f40-3f141044f629',
  '6fbfe5e3-0685-40ed-847d-c7642a8e51b8',
  '71987f8c-0a7a-4e8f-a87c-fadb9e1cf621',
  'a6831ebf-addd-4c1e-9968-bf5d4a207cf6',
  '1e719059-edb7-43f6-a2a3-d0cceaf1ede6',
  '64b23ce3-4bc2-4f69-bb68-caa071326f83',
  '4d123b14-d31f-42a7-9245-827eadca7fb2',
  '8a4dd7e3-ccf3-40bf-a41d-df8f794fb630',
  'd466070a-8d8f-42df-9b2b-a1ef7e93cc3d',
  'be6e4b84-d89d-4b33-8d7b-016f18669a5b',
  '14fcbfa4-a20c-479e-b554-ee8e38eafdb9',
  'b98e0046-4f86-44b7-bdf9-ef1aca622484',
  '3ea360a3-7811-4427-82a3-c99d9f8e3aef',
  '1ce79b72-d9ad-42d3-933a-efec1853a0a6',
  '0ef69062-e8a6-4e4d-9543-1f821c926bd8',
  '1c2d5329-d2ff-49d9-8d98-77e43488dbfd',
  '4b87ed34-7e60-42e7-bcad-6614d1e82e2c',
  'b6e361b9-6e3e-4ccd-b6d2-1a91b2f5e4eb',
  'e82b389e-98fb-41be-97eb-5d5f9ce0433a',
  'd6ff8288-49ed-4773-b4e7-30a35a63b8b0',
  '067f1195-03ff-4f4c-8bb8-184e52f07cda',
  '525a2e1f-7fcd-4ad8-bea4-c6cd1b41ad3a',
  '9196921a-cd3f-45a6-9810-5c4d925e69dc',
  '89ad7ae4-4d81-4134-b9ab-1353248eaf39',
  '8f8d71a3-d090-40f6-9cc2-cd2040279db6',
  '10a9fc9e-74b4-47e9-8889-0a66fb7f2254',
  '68f5abde-00b9-45b5-97a9-bef9d043da7b',
  'cde6c0da-2b7c-4f2b-bd98-9e9e8f45df53',
  '89cfa061-9cd9-4baf-b4aa-b52e0a120e90',
  '1e443b8e-0d81-46a7-acf4-38d9505c57fa',
  'f7929af3-e41e-4ccc-93c2-af3ed6321b5d',
  '0f1048c1-fb7e-46df-8d5f-9a2234c43aaf',
  '4489408d-9d97-4fb7-81af-d429594af597',
  '124cfb5a-8056-4db1-ab2e-47375aa5d84b',
  '22b6fc22-9208-483c-a6c3-7e3c65a8eb77',
  '10322cf7-9c32-4fa6-a403-1e8ca3793332',
  '715d3617-ef2c-4606-8973-f8106aa6a17f',
  'd929ce2b-c8b3-488c-be20-95e95a745d1c',
  '08b660e8-d0ec-44d1-86f0-c01dbfad14fc',
  '6b15c24e-d16e-4e32-98e3-1d1174657c00',
  '63695eb0-6ace-47f2-b9d2-e45b7892407b',
  '459933ad-4721-4299-ac16-e1dfb09207b8',
  'c0629e67-b92d-43f0-8b9f-688114fc6198',
  'e3b1fee5-af38-4d83-8e55-32a39be07329',
  '537e309b-c84d-4de5-9234-57ab15e73193',
  '8037626c-3c4c-493f-935c-37da3ffa7aae',
  '00eb3880-5c13-4242-88a7-61781e1aed5c',
  'a26c68f5-7a2a-4f1c-837b-9dcb27c0f914',
  'b88dc09f-9a75-45f6-b1f1-e5264614ec3d',
  '7992d047-c6ab-4492-bdfe-a9c1823be675',
  '5cb41104-bcb4-491c-8e68-8a1a77d8e53f',
  '651f7c89-d80e-49f8-b6b0-6556d88c3cff',
  'bff96b74-82a9-435d-a7d4-6d0058a33a95',
  'ada476dc-aa00-46e2-9cce-66de3cba59a1',
  '11f730ba-c19b-4b63-a96b-59a52f5c9107',
  '96678237-64fb-4579-b96e-d7e3fa0a12eb',
  '00903630-47f3-4286-9fad-96643cec6311',
  'e3cbfe75-186f-4bcf-b642-b5775e3bd1a9',
  'dede6200-d213-48dc-a007-22dbd6b305d4',
  '78842000-bcfb-4930-af58-7bb29d44694b',
  '4d5977fb-019e-4ba3-aa1f-5086f09e62d8',
  '7ac66a73-366c-4a63-82d1-445d94bf8054',
  'd5213019-888e-4fa4-8933-aade3abdfbf7',
  '9a38f913-4306-4ebd-99b5-bb4734d38d79',
  '60690d03-b2c5-4785-8e01-d7410cd6a6a0',
  'ef9c2188-6e1e-449a-aa46-6540c814d8e9',
  '03893e77-9388-4a0c-bf03-28eaed6883ef',
  'b1f1b15a-7a37-499e-bf85-727770aeb5f5',
  'c573f794-e30b-4795-b26b-9a3dac388f41',
  'eaeb255f-edc9-4ba9-bc5d-141f383abf77',
  '79b736e7-876a-452d-8b87-72de417ad93a',
  '53e4263d-6dc0-4b81-99cb-59bfe3866873',
  '4baff9bf-440f-4abd-a918-11451f561773',
  '303ed3ac-1dfd-4d0e-9e47-92cdfab290b5',
  'c3f2a3b1-b447-48fa-927d-570ac692ffaf',
  '62b0da38-c4f7-4ee8-852f-08e2221c9c53',
  'c08276b6-a784-475f-8a60-4bd69e05f431',
  '7bfaef49-9657-4837-b6ff-5eca797b2490',
  '15de8395-b977-4022-a7a1-a09013f64c2e',
  'cf89470d-575b-4b76-8053-4391a671d6d9',
  '38f0e9ac-2126-4af2-8139-781d5284deb5',
  '7b644c93-a3fd-4244-b61c-b7d3210395cf',
  '4e307618-ced5-4a53-b9a7-0372e1fd48c8',
  '03ae8652-c878-4039-8d5e-80b5b8f1c975',
  '0f036dca-29cc-457c-8932-009e814723c1',
]
