import { CustomerAccount, User } from '@linqpal/common-backend'
import { parsePhoneNumber } from 'libphonenumber-js'
import { exceptions } from '@linqpal/models'
import { customerStatus } from '@linqpal/models/src/dictionaries'
import mongoose from 'mongoose'
import { inviteCustomerAccount } from './inviteCustomerAccount'
import { Request } from 'express'

const checkCredentialInUsersAndAccounts = async (
  credential: string,
  type: 'phone' | 'email' = 'phone',
) => {
  const user = await User.findOne(
    type === 'phone' ? { phone: credential } : { email: credential },
  )
  if (user) {
    const error =
      type === 'phone'
        ? `Phone number ${credential} exists`
        : `Email address ${credential} exists`
    throw new exceptions.LogicalError(error)
  }

  const account = await CustomerAccount.findOne({
    ...(type === 'phone' ? { phone: credential } : { email: credential }),
    IsDeleted: { $ne: true },
  })

  if (account) {
    let error =
      type === 'phone'
        ? `The Billing Contact with phone number ${credential} exists`
        : `The Billing Contact with email address ${credential} exists`
    error = `${error}. Please check details and try again`
    throw new exceptions.LogicalError(error)
  }
}

export async function saveBillingContacts({
  companyId,
  customerAccountId,
  contacts,
  req,
}: {
  companyId: string
  customerAccountId: string
  contacts: {
    id: string
    first_name: string
    last_name: string
    email: string
    status: string
    phone: string
  }[]
  req: Request
}) {
  await Promise.all(
    contacts.map(async (contact) => {
      const { id, first_name, last_name, email, status } = contact
      let { phone } = contact
      let shouldInvite = false
      if (!phone) {
        throw new exceptions.LogicalError('Phone number is required')
      } else {
        try {
          phone = parsePhoneNumber(phone, 'US').number.toString()
        } catch {
          throw new exceptions.LogicalError('Invalid phone number')
        }
      }
      const billingContactAccount = {
        company_id: companyId,
        first_name: first_name,
        last_name: last_name,
        email: email,
        phone: phone,
        parent_id: customerAccountId,
      }
      let billingContactId = id
      if (!billingContactId) {
        await checkCredentialInUsersAndAccounts(phone, 'phone')
        await checkCredentialInUsersAndAccounts(email, 'email')
        const [bc] = await CustomerAccount.create([billingContactAccount], {
          session: req.session,
        })
        billingContactId = bc._id.toString()
        shouldInvite = true
      } else if (status === customerStatus.deleted) {
        await CustomerAccount.findOneAndDelete({
          _id: new mongoose.Types.ObjectId(id),
        }).session(req.session)
      } else {
        const bc = await CustomerAccount.findOne({
          _id: billingContactId,
        }).session(req.session)
        if (bc) {
          if (bc.phone !== phone) {
            await checkCredentialInUsersAndAccounts(phone, 'phone')
            shouldInvite = true
          }
          if (bc.email !== email) {
            await checkCredentialInUsersAndAccounts(email, 'email')
            shouldInvite = true
          }
          await bc.updateOne(billingContactAccount).session(req.session)
        }
      }
      if (shouldInvite) {
        await inviteCustomerAccount({
          customerAccountId: billingContactId,
          req,
          phone: phone,
          name: '',
          role: 'User',
        })
      }
      //await emitCustomerEvent(id, id ? 'Updated' : 'Created')
    }),
  )
}
/*
async function emitCustomerEvent(
  customerId: string,
  eventType: 'Created' | 'Updated',
) {
  try {
    await AwsService.sendSQSMessage(
      'customer-syncback',
      JSON.stringify({ customerId, eventType }),
    )
  } catch (e) {
    console.error(e)
  }
}*/
