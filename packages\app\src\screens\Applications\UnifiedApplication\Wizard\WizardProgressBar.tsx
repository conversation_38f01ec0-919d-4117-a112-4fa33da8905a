import { StyleSheet, View } from 'react-native'
import { colors } from '@linqpal/components/src/theme'
import React, { FC, useEffect } from 'react'
import { useUnifiedApplication } from '../UnifiedApplicationContext'

export const ProgressBar: FC = () => {
  const store = useUnifiedApplication()

  const [progress, setProgress] = React.useState(0)

  useEffect(() => {
    const flowSteps = store.getFlowSteps()

    const totalFilled = flowSteps.reduce((filledCount: number, step) => {
      // optional fields are valid by default so they have to be visited to count in progress
      const isVisited = store.draft.visitedSteps.includes(step)
      const isValid = store.validateStep(step)

      return isVisited && isValid ? filledCount + 1 : filledCount
    }, 0)

    setProgress(totalFilled / flowSteps.length)
  }, [store, store.currentStep])

  return (
    <View style={styles.progressBar}>
      <View style={[styles.filledProgress, { flex: progress }]} />
      <View style={[styles.emptyProgress, { flex: 1 - progress }]} />
    </View>
  )
}

const styles = StyleSheet.create({
  progressBar: {
    width: '100%',
    flexDirection: 'row',
    height: 3,
  },
  filledProgress: {
    height: '100%',
    backgroundColor: colors.accentText,
  },
  emptyProgress: {
    height: '100%',
    backgroundColor: '#CCD6DD',
  },
})
