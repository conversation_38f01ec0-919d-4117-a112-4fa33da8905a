// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`LoanSchedule should render correctly 1`] = `
<View
  style={
    Object {
      "flex": 1,
    }
  }
>
  <View
    style={
      Object {
        "backgroundColor": "#F8F9F9",
        "borderRadius": 8,
        "marginBottom": 20,
        "marginTop": 10,
        "padding": 15,
      }
    }
  >
    <View
      style={
        Object {
          "flexDirection": "row",
          "justifyContent": "space-between",
          "marginBottom": 10,
          "marginTop": 5,
        }
      }
    >
      <View>
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#19262F",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          $
          0.00
        </Text>
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#394D5A",
                "fontSize": 12,
                "fontWeight": "500",
              },
            ]
          }
        >
          Total Paid
           (0)
        </Text>
      </View>
      <View
        style={
          Object {
            "alignItems": "flex-end",
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#19262F",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          $
          7,476.60
        </Text>
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#394D5A",
                "fontSize": 12,
                "fontWeight": "500",
              },
            ]
          }
        >
          Remaining
           (5)
        </Text>
      </View>
    </View>
    <View
      style={
        Object {
          "backgroundColor": "rgba(0, 160, 243, 0.06)",
          "borderRadius": 9,
          "flexDirection": "row",
          "height": 8,
          "marginTop": 10,
        }
      }
    >
      <View
        style={
          Array [
            Object {
              "backgroundColor": "#00A0F3",
              "flex": 0,
              "height": 8,
            },
            Object {
              "borderRadius": 9,
            },
          ]
        }
      />
      <View
        style={
          Array [
            Object {
              "backgroundColor": "#DB2424",
              "flex": 0,
              "height": 8,
            },
            Object {
              "borderRadius": 9,
            },
          ]
        }
      />
      <View
        style={
          Array [
            Object {
              "backgroundColor": "#FF7926",
              "flex": 0,
              "height": 8,
            },
            Object {
              "borderRadius": 9,
            },
          ]
        }
      />
    </View>
    <View
      style={
        Array [
          Object {
            "borderTopColor": "#CCD6DD",
            "borderTopWidth": 1,
            "flexDirection": "row",
            "justifyContent": "space-between",
            "paddingVertical": 15,
          },
          Object {
            "borderTopWidth": 0,
            "marginTop": 10,
          },
        ]
      }
    >
      <Text
        appearance="default"
        category="p1"
        ellipsizeMode="tail"
        style={
          Array [
            Object {
              "color": "#003353",
              "fontFamily": "System",
              "fontSize": 15,
              "fontWeight": "400",
            },
            Object {
              "color": "#19262F",
              "fontFamily": "Inter",
              "fontSize": 14,
              "fontWeight": "500",
            },
          ]
        }
      >
        Past Due Amount
      </Text>
      <Text
        appearance="default"
        category="p1"
        ellipsizeMode="tail"
        style={
          Array [
            Object {
              "color": "#003353",
              "fontFamily": "System",
              "fontSize": 15,
              "fontWeight": "400",
            },
            Object {
              "color": "#19262F",
              "fontFamily": "Inter",
              "fontSize": 14,
              "fontWeight": "500",
              "letterSpacing": 0.16,
            },
          ]
        }
      >
        $0.00
      </Text>
    </View>
    <View
      style={
        Object {
          "borderTopColor": "#CCD6DD",
          "borderTopWidth": 1,
          "flexDirection": "row",
          "justifyContent": "space-between",
          "paddingVertical": 15,
        }
      }
    >
      <Text
        style={
          Object {
            "color": "#19262F",
            "fontFamily": "Inter",
            "fontSize": 14,
            "fontWeight": "500",
            "lineHeight": 21,
          }
        }
      >
        Processing Amount
      </Text>
      <Text
        style={
          Object {
            "color": "#19262F",
            "fontFamily": "Inter",
            "fontSize": 14,
            "fontWeight": "500",
            "lineHeight": 21,
          }
        }
      >
        $0.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "height": 5,
        "width": 0,
      }
    }
  />
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 33,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          06/18/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Due Next
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#003353",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $1,495.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 33,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          06/25/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Due Next
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#003353",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $1,495.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 33,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          07/02/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Due Next
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#003353",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $1,495.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 33,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          07/09/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Due Next
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#003353",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $1,495.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 33,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          07/14/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Due Next
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#003353",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $1,496.60
      </Text>
    </View>
  </View>
</View>
`;

exports[`LoanSchedule should render correctly with Failed payment 1`] = `
<View
  style={
    Object {
      "flex": 1,
    }
  }
>
  <View
    style={
      Object {
        "backgroundColor": "#F8F9F9",
        "borderRadius": 8,
        "marginBottom": 20,
        "marginTop": 10,
        "padding": 15,
      }
    }
  >
    <View
      style={
        Object {
          "flexDirection": "row",
          "justifyContent": "space-between",
          "marginBottom": 10,
          "marginTop": 5,
        }
      }
    >
      <View>
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#19262F",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          $
          0.00
        </Text>
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#394D5A",
                "fontSize": 12,
                "fontWeight": "500",
              },
            ]
          }
        >
          Total Paid
           (0)
        </Text>
      </View>
      <View
        style={
          Object {
            "alignItems": "flex-end",
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#19262F",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          $
          1,616.00
        </Text>
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#394D5A",
                "fontSize": 12,
                "fontWeight": "500",
              },
            ]
          }
        >
          Remaining
           (5)
        </Text>
      </View>
    </View>
    <View
      style={
        Object {
          "backgroundColor": "rgba(0, 160, 243, 0.06)",
          "borderRadius": 9,
          "flexDirection": "row",
          "height": 8,
          "marginTop": 10,
        }
      }
    >
      <View
        style={
          Array [
            Object {
              "backgroundColor": "#00A0F3",
              "flex": NaN,
              "height": 8,
            },
            Object {
              "borderRadius": 9,
            },
          ]
        }
      />
      <View
        style={
          Array [
            Object {
              "backgroundColor": "#DB2424",
              "flex": NaN,
              "height": 8,
            },
            Object {
              "borderRadius": 9,
            },
          ]
        }
      />
      <View
        style={
          Array [
            Object {
              "backgroundColor": "#FF7926",
              "flex": NaN,
              "height": 8,
            },
            Object {
              "borderRadius": 9,
            },
          ]
        }
      />
    </View>
    <View
      style={
        Array [
          Object {
            "borderTopColor": "#CCD6DD",
            "borderTopWidth": 1,
            "flexDirection": "row",
            "justifyContent": "space-between",
            "paddingVertical": 15,
          },
          Object {
            "borderTopWidth": 0,
            "marginTop": 10,
          },
        ]
      }
    >
      <Text
        appearance="default"
        category="p1"
        ellipsizeMode="tail"
        style={
          Array [
            Object {
              "color": "#003353",
              "fontFamily": "System",
              "fontSize": 15,
              "fontWeight": "400",
            },
            Object {
              "color": "#19262F",
              "fontFamily": "Inter",
              "fontSize": 14,
              "fontWeight": "500",
            },
          ]
        }
      >
        Past Due Amount
      </Text>
      <Text
        appearance="default"
        category="p1"
        ellipsizeMode="tail"
        style={
          Array [
            Object {
              "color": "#003353",
              "fontFamily": "System",
              "fontSize": 15,
              "fontWeight": "400",
            },
            Object {
              "color": "#19262F",
              "fontFamily": "Inter",
              "fontSize": 14,
              "fontWeight": "500",
              "letterSpacing": 0.16,
            },
          ]
        }
      >
        $0.00
      </Text>
    </View>
    <View
      style={
        Object {
          "borderTopColor": "#CCD6DD",
          "borderTopWidth": 1,
          "flexDirection": "row",
          "justifyContent": "space-between",
          "paddingVertical": 15,
        }
      }
    >
      <Text
        style={
          Object {
            "color": "#19262F",
            "fontFamily": "Inter",
            "fontSize": 14,
            "fontWeight": "500",
            "lineHeight": 21,
          }
        }
      >
        Processing Amount
      </Text>
      <Text
        style={
          Object {
            "color": "#19262F",
            "fontFamily": "Inter",
            "fontSize": 14,
            "fontWeight": "500",
            "lineHeight": 21,
          }
        }
      >
        $0.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "height": 5,
        "width": 0,
      }
    }
  />
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#DB081C",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 33,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          05/17/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#DB2424",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Past Due
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#DB2424",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $316.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 33,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          05/24/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#DB2424",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Failed
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#DB2424",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $316.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 33,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          05/31/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Due Next
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#003353",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $316.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 33,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          06/07/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Due Next
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#003353",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $316.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 33,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          06/14/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Due Next
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#003353",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $317.00
      </Text>
    </View>
  </View>
</View>
`;

exports[`LoanSchedule should render correctly with Rescheduled entries and extension fees 1`] = `
<View
  style={
    Object {
      "flex": 1,
    }
  }
>
  <View
    style={
      Object {
        "backgroundColor": "#F8F9F9",
        "borderRadius": 8,
        "marginBottom": 20,
        "marginTop": 10,
        "padding": 15,
      }
    }
  >
    <View
      style={
        Object {
          "flexDirection": "row",
          "justifyContent": "space-between",
          "marginBottom": 10,
          "marginTop": 5,
        }
      }
    >
      <View>
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#19262F",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          $
          0.00
        </Text>
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#394D5A",
                "fontSize": 12,
                "fontWeight": "500",
              },
            ]
          }
        >
          Total Paid
        </Text>
      </View>
      <View
        style={
          Object {
            "alignItems": "flex-end",
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#19262F",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          $
          1,040.00
        </Text>
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#394D5A",
                "fontSize": 12,
                "fontWeight": "500",
              },
            ]
          }
        >
          Remaining
        </Text>
      </View>
    </View>
    <View
      style={
        Object {
          "backgroundColor": "rgba(0, 160, 243, 0.06)",
          "borderRadius": 9,
          "flexDirection": "row",
          "height": 8,
          "marginTop": 10,
        }
      }
    >
      <View
        style={
          Array [
            Object {
              "backgroundColor": "#00A0F3",
              "flex": 0,
              "height": 8,
            },
            Object {
              "borderRadius": 9,
            },
          ]
        }
      />
      <View
        style={
          Array [
            Object {
              "backgroundColor": "#DB2424",
              "flex": NaN,
              "height": 8,
            },
            Object {
              "borderRadius": 9,
            },
          ]
        }
      />
      <View
        style={
          Array [
            Object {
              "backgroundColor": "#FF7926",
              "flex": 0,
              "height": 8,
            },
            Object {
              "borderRadius": 9,
            },
          ]
        }
      />
    </View>
    <View
      style={
        Array [
          Object {
            "borderTopColor": "#CCD6DD",
            "borderTopWidth": 1,
            "flexDirection": "row",
            "justifyContent": "space-between",
            "paddingVertical": 15,
          },
          Object {
            "borderTopWidth": 0,
            "marginTop": 10,
          },
        ]
      }
    >
      <Text
        appearance="default"
        category="p1"
        ellipsizeMode="tail"
        style={
          Array [
            Object {
              "color": "#003353",
              "fontFamily": "System",
              "fontSize": 15,
              "fontWeight": "400",
            },
            Object {
              "color": "#19262F",
              "fontFamily": "Inter",
              "fontSize": 14,
              "fontWeight": "500",
            },
          ]
        }
      >
        Past Due Amount
      </Text>
      <Text
        appearance="default"
        category="p1"
        ellipsizeMode="tail"
        style={
          Array [
            Object {
              "color": "#003353",
              "fontFamily": "System",
              "fontSize": 15,
              "fontWeight": "400",
            },
            Object {
              "color": "#19262F",
              "fontFamily": "Inter",
              "fontSize": 14,
              "fontWeight": "500",
              "letterSpacing": 0.16,
            },
          ]
        }
      >
        $0.00
      </Text>
    </View>
    <View
      style={
        Object {
          "borderTopColor": "#CCD6DD",
          "borderTopWidth": 1,
          "flexDirection": "row",
          "justifyContent": "space-between",
          "paddingVertical": 15,
        }
      }
    >
      <Text
        style={
          Object {
            "color": "#19262F",
            "fontFamily": "Inter",
            "fontSize": 14,
            "fontWeight": "500",
            "lineHeight": 21,
          }
        }
      >
        Processing Amount
      </Text>
      <Text
        style={
          Object {
            "color": "#19262F",
            "fontFamily": "Inter",
            "fontSize": 14,
            "fontWeight": "500",
            "lineHeight": 21,
          }
        }
      >
        $0.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "height": 5,
        "width": 0,
      }
    }
  />
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 33,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#DB2424",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          08/25/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Rescheduled
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#DB2424",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $1,000.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 45,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "rgba(0, 51, 83, 0.3)",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          08/26/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Rescheduled
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "rgba(0, 51, 83, 0.3)",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $20.00
      </Text>
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "rgba(0, 51, 83, 0.3)",
              "fontSize": 16,
              "fontWeight": "500",
              "textAlign": "right",
            },
          ]
        }
      >
        Late Fee
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 45,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "rgba(0, 51, 83, 0.3)",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          08/26/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Rescheduled
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "rgba(0, 51, 83, 0.3)",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $20.00
      </Text>
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "rgba(0, 51, 83, 0.3)",
              "fontSize": 16,
              "fontWeight": "500",
              "textAlign": "right",
            },
          ]
        }
      >
        Extension Fee
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 45,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          08/26/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Due Next
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#003353",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $20.00
      </Text>
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#003353",
              "fontSize": 16,
              "fontWeight": "500",
              "textAlign": "right",
            },
          ]
        }
      >
        Extension Fee
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 33,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "rgba(0, 51, 83, 0.3)",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          08/28/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Rescheduled
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "rgba(0, 51, 83, 0.3)",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $1,000.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 33,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          08/28/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Due Next
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#003353",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $1,000.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 45,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          08/28/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Due Next
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#003353",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $20.00
      </Text>
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#003353",
              "fontSize": 16,
              "fontWeight": "500",
              "textAlign": "right",
            },
          ]
        }
      >
        Extension Fee
      </Text>
    </View>
  </View>
</View>
`;

exports[`LoanSchedule should render correctly with past due 1`] = `
<View
  style={
    Object {
      "flex": 1,
    }
  }
>
  <View
    style={
      Object {
        "backgroundColor": "#F8F9F9",
        "borderRadius": 8,
        "marginBottom": 20,
        "marginTop": 10,
        "padding": 15,
      }
    }
  >
    <View
      style={
        Object {
          "flexDirection": "row",
          "justifyContent": "space-between",
          "marginBottom": 10,
          "marginTop": 5,
        }
      }
    >
      <View>
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#19262F",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          $
          0.00
        </Text>
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#394D5A",
                "fontSize": 12,
                "fontWeight": "500",
              },
            ]
          }
        >
          Total Paid
           (0)
        </Text>
      </View>
      <View
        style={
          Object {
            "alignItems": "flex-end",
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#19262F",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          $
          1,616.00
        </Text>
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#394D5A",
                "fontSize": 12,
                "fontWeight": "500",
              },
            ]
          }
        >
          Remaining
           (5)
        </Text>
      </View>
    </View>
    <Text
      style={
        Object {
          "color": "#001929",
          "fontSize": 12,
          "fontWeight": "500",
          "lineHeight": 20,
          "marginVertical": 8,
        }
      }
    >
      Total remaining balance includes late fees.
    </Text>
    <View
      style={
        Object {
          "backgroundColor": "rgba(0, 160, 243, 0.06)",
          "borderRadius": 9,
          "flexDirection": "row",
          "height": 8,
          "marginTop": 10,
        }
      }
    >
      <View
        style={
          Array [
            Object {
              "backgroundColor": "#00A0F3",
              "flex": 0,
              "height": 8,
            },
            Object {
              "borderRadius": 9,
            },
          ]
        }
      />
      <View
        style={
          Array [
            Object {
              "backgroundColor": "#DB2424",
              "flex": 20,
              "height": 8,
            },
            Object {
              "borderRadius": 9,
            },
          ]
        }
      />
      <View
        style={
          Array [
            Object {
              "backgroundColor": "#FF7926",
              "flex": 0,
              "height": 8,
            },
            Object {
              "borderRadius": 9,
            },
          ]
        }
      />
    </View>
    <View
      style={
        Array [
          Object {
            "borderTopColor": "#CCD6DD",
            "borderTopWidth": 1,
            "flexDirection": "row",
            "justifyContent": "space-between",
            "paddingVertical": 15,
          },
          Object {
            "borderTopWidth": 0,
            "marginTop": 10,
          },
        ]
      }
    >
      <Text
        appearance="default"
        category="p1"
        ellipsizeMode="tail"
        style={
          Array [
            Object {
              "color": "#003353",
              "fontFamily": "System",
              "fontSize": 15,
              "fontWeight": "400",
            },
            Object {
              "color": "#19262F",
              "fontFamily": "Inter",
              "fontSize": 14,
              "fontWeight": "500",
            },
          ]
        }
      >
        Past Due Amount
      </Text>
      <Text
        appearance="default"
        category="p1"
        ellipsizeMode="tail"
        style={
          Array [
            Object {
              "color": "#003353",
              "fontFamily": "System",
              "fontSize": 15,
              "fontWeight": "400",
            },
            Object {
              "color": "#19262F",
              "fontFamily": "Inter",
              "fontSize": 14,
              "fontWeight": "500",
              "letterSpacing": 0.16,
            },
          ]
        }
      >
        $0.00
      </Text>
    </View>
    <View
      style={
        Object {
          "borderTopColor": "#CCD6DD",
          "borderTopWidth": 1,
          "flexDirection": "row",
          "justifyContent": "space-between",
          "paddingVertical": 15,
        }
      }
    >
      <Text
        style={
          Object {
            "color": "#19262F",
            "fontFamily": "Inter",
            "fontSize": 14,
            "fontWeight": "500",
            "lineHeight": 21,
          }
        }
      >
        Processing Amount
      </Text>
      <Text
        style={
          Object {
            "color": "#19262F",
            "fontFamily": "Inter",
            "fontSize": 14,
            "fontWeight": "500",
            "lineHeight": 21,
          }
        }
      >
        $0.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "height": 5,
        "width": 0,
      }
    }
  />
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#DB081C",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 33,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          05/17/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#DB2424",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Past Due
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#DB2424",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $316.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 33,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          05/24/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Due Now
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#003353",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $316.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 45,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          05/24/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Due Now
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#003353",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $35.00
      </Text>
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#DB2424",
              "fontSize": 16,
              "fontWeight": "500",
              "textAlign": "right",
            },
          ]
        }
      >
        Late Fee
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 33,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          05/31/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Due Next
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#003353",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $316.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 33,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          06/07/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Due Next
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#003353",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $316.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 33,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          06/14/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Due Next
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#003353",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $317.00
      </Text>
    </View>
  </View>
</View>
`;

exports[`LoanSchedule should render penalty interest block 1`] = `
<View
  style={
    Object {
      "flex": 1,
    }
  }
>
  <View
    style={
      Object {
        "backgroundColor": "#F8F9F9",
        "borderRadius": 8,
        "marginBottom": 20,
        "marginTop": 10,
        "padding": 15,
      }
    }
  >
    <View
      style={
        Object {
          "flexDirection": "row",
          "justifyContent": "space-between",
          "marginBottom": 10,
          "marginTop": 5,
        }
      }
    >
      <View>
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#19262F",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          $
          0.00
        </Text>
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#394D5A",
                "fontSize": 12,
                "fontWeight": "500",
              },
            ]
          }
        >
          Total Paid
           (0)
        </Text>
      </View>
      <View
        style={
          Object {
            "alignItems": "flex-end",
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#19262F",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          $
          1,310.00
        </Text>
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#394D5A",
                "fontSize": 12,
                "fontWeight": "500",
              },
            ]
          }
        >
          Remaining
           (5)
        </Text>
      </View>
    </View>
    <Text
      style={
        Object {
          "color": "#001929",
          "fontSize": 12,
          "fontWeight": "500",
          "lineHeight": 20,
          "marginVertical": 8,
        }
      }
    >
      Total remaining balance includes late fees and or penalty interest.
    </Text>
    <View
      style={
        Object {
          "backgroundColor": "rgba(0, 160, 243, 0.06)",
          "borderRadius": 9,
          "flexDirection": "row",
          "height": 8,
          "marginTop": 10,
        }
      }
    >
      <View
        style={
          Array [
            Object {
              "backgroundColor": "#00A0F3",
              "flex": NaN,
              "height": 8,
            },
            Object {
              "borderRadius": 9,
            },
          ]
        }
      />
      <View
        style={
          Array [
            Object {
              "backgroundColor": "#DB2424",
              "flex": NaN,
              "height": 8,
            },
            Object {
              "borderRadius": 9,
            },
          ]
        }
      />
      <View
        style={
          Array [
            Object {
              "backgroundColor": "#FF7926",
              "flex": NaN,
              "height": 8,
            },
            Object {
              "borderRadius": 9,
            },
          ]
        }
      />
    </View>
    <View
      style={
        Array [
          Object {
            "borderTopColor": "#CCD6DD",
            "borderTopWidth": 1,
            "flexDirection": "row",
            "justifyContent": "space-between",
            "paddingVertical": 15,
          },
          Object {
            "borderTopWidth": 0,
            "marginTop": 10,
          },
        ]
      }
    >
      <Text
        appearance="default"
        category="p1"
        ellipsizeMode="tail"
        style={
          Array [
            Object {
              "color": "#003353",
              "fontFamily": "System",
              "fontSize": 15,
              "fontWeight": "400",
            },
            Object {
              "color": "#19262F",
              "fontFamily": "Inter",
              "fontSize": 14,
              "fontWeight": "500",
            },
          ]
        }
      >
        Past Due Amount
      </Text>
      <Text
        appearance="default"
        category="p1"
        ellipsizeMode="tail"
        style={
          Array [
            Object {
              "color": "#003353",
              "fontFamily": "System",
              "fontSize": 15,
              "fontWeight": "400",
            },
            Object {
              "color": "#19262F",
              "fontFamily": "Inter",
              "fontSize": 14,
              "fontWeight": "500",
              "letterSpacing": 0.16,
            },
          ]
        }
      >
        $0.00
      </Text>
    </View>
    <View
      style={
        Object {
          "borderTopColor": "#CCD6DD",
          "borderTopWidth": 1,
          "flexDirection": "row",
          "justifyContent": "space-between",
          "paddingVertical": 15,
        }
      }
    >
      <Text
        style={
          Object {
            "color": "#19262F",
            "fontFamily": "Inter",
            "fontSize": 14,
            "fontWeight": "500",
            "lineHeight": 21,
          }
        }
      >
        Processing Amount
      </Text>
      <Text
        style={
          Object {
            "color": "#19262F",
            "fontFamily": "Inter",
            "fontSize": 14,
            "fontWeight": "500",
            "lineHeight": 21,
          }
        }
      >
        $0.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "backgroundColor": "#F8F9F9",
        "borderRadius": 12,
        "marginBottom": 20,
        "padding": 16,
        "width": "100%",
      }
    }
  >
    <View
      style={
        Object {
          "flexDirection": "row",
          "justifyContent": "space-between",
        }
      }
    >
      <View
        style={
          Object {
            "flexDirection": "column",
          }
        }
      >
        <Text
          style={
            Object {
              "color": "#172B4D",
              "fontSize": 16,
              "fontWeight": "700",
              "lineHeight": 20,
            }
          }
        >
          Total daily penalty 
        </Text>
        <View
          style={
            Object {
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Object {
                "color": "#172B4D",
                "fontSize": 16,
                "fontWeight": "700",
                "lineHeight": 20,
              }
            }
          >
            interest
          </Text>
          <View
            accessible={true}
            collapsable={false}
            focusable={true}
            nativeID="animatedComponent"
            onClick={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
            style={
              Object {
                "marginLeft": 5,
                "opacity": 1,
              }
            }
          >
            <View
              style={
                Object {
                  "height": 24,
                  "style": Object {
                    "height": 20,
                    "width": 20,
                  },
                  "width": 24,
                }
              }
            />
          </View>
        </View>
      </View>
      <Text
        style={
          Object {
            "color": "#FF7926",
            "fontSize": 16,
            "fontWeight": "700",
            "lineHeight": 24,
          }
        }
      >
        $10.00
      </Text>
    </View>
    <View
      style={
        Object {
          "flexDirection": "row",
          "marginBottom": 10,
        }
      }
    />
    <Text
      style={
        Object {
          "color": "#172B4D",
          "fontSize": 14,
          "fontWeight": "500",
          "lineHeight": 20,
        }
      }
    >
      Your account is past due. Please pay the past due amount now to avoid accruing penalty interest.
    </Text>
    <Modal
      animationType="none"
      deviceHeight={null}
      deviceWidth={null}
      hardwareAccelerated={false}
      hideModalContentWhileAnimating={false}
      onBackdropPress={[Function]}
      onModalHide={[Function]}
      onModalWillHide={[Function]}
      onModalWillShow={[Function]}
      onRequestClose={[Function]}
      panResponderThreshold={4}
      scrollHorizontal={false}
      scrollOffset={0}
      scrollOffsetMax={0}
      scrollTo={null}
      statusBarTranslucent={false}
      supportedOrientations={
        Array [
          "portrait",
          "landscape",
        ]
      }
      swipeThreshold={100}
      transparent={true}
      visible={false}
    >
      <View
        accessible={true}
        collapsable={false}
        focusable={true}
        forwardedRef={[Function]}
        nativeID="animatedComponent"
        onClick={[Function]}
        onResponderGrant={[Function]}
        onResponderMove={[Function]}
        onResponderRelease={[Function]}
        onResponderTerminate={[Function]}
        onResponderTerminationRequest={[Function]}
        onStartShouldSetResponder={[Function]}
        style={
          Object {
            "backgroundColor": "rgba(0, 0, 0, 0.6)",
            "bottom": 0,
            "height": 1334,
            "left": 0,
            "opacity": 0,
            "position": "absolute",
            "right": 0,
            "top": 0,
            "width": 750,
          }
        }
      />
      <View
        collapsable={false}
        deviceHeight={null}
        deviceWidth={null}
        forwardedRef={[Function]}
        hideModalContentWhileAnimating={false}
        nativeID="animatedComponent"
        onBackdropPress={[Function]}
        onModalHide={[Function]}
        onModalWillHide={[Function]}
        onModalWillShow={[Function]}
        panResponderThreshold={4}
        pointerEvents="box-none"
        scrollHorizontal={false}
        scrollOffset={0}
        scrollOffsetMax={0}
        scrollTo={null}
        statusBarTranslucent={false}
        style={
          Object {
            "flex": 1,
            "justifyContent": "flex-end",
            "margin": 37.5,
            "marginBottom": 0,
            "marginHorizontal": 16,
            "marginLeft": 16,
            "overflowY": "visible",
            "transform": Array [
              Object {
                "translateY": 0,
              },
            ],
            "width": 400,
          }
        }
        supportedOrientations={
          Array [
            "portrait",
            "landscape",
          ]
        }
        swipeThreshold={100}
        transparent={true}
      >
        <View
          style={
            Object {
              "backgroundColor": "white",
              "borderTopLeftRadius": 20,
              "borderTopRightRadius": 20,
              "height": "25%",
              "maxHeight": 1314,
              "overflowY": "auto",
              "paddingHorizontal": 20,
            }
          }
        >
          <View
            style={
              Object {
                "alignSelf": "center",
                "backgroundColor": "#E6EBEE",
                "borderRadius": 3,
                "height": 4,
                "marginBottom": 8,
                "marginTop": 8,
                "width": 32,
              }
            }
          />
          <View
            style={
              Object {
                "alignItems": "center",
                "flexDirection": "row",
                "marginBottom": 10,
              }
            }
          >
            <View
              style={
                Object {
                  "width": 30,
                }
              }
            />
            <Text
              style={
                Object {
                  "color": "#003353",
                  "flex": 1,
                  "fontSize": 20,
                  "fontWeight": "700",
                  "lineHeight": 28,
                  "marginLeft": -30,
                }
              }
            >
              Why am I being charged a penalty interest?
            </Text>
            <View
              style={
                Object {
                  "marginRight": 10,
                }
              }
            >
              <View
                accessible={true}
                collapsable={false}
                focusable={true}
                nativeID="animatedComponent"
                onClick={[Function]}
                onResponderGrant={[Function]}
                onResponderMove={[Function]}
                onResponderRelease={[Function]}
                onResponderTerminate={[Function]}
                onResponderTerminationRequest={[Function]}
                onStartShouldSetResponder={[Function]}
                style={
                  Object {
                    "alignItems": "center",
                    "backgroundColor": "#F5F7F8",
                    "borderRadius": 8,
                    "height": 40,
                    "justifyContent": "center",
                    "marginRight": "10",
                    "opacity": 1,
                    "width": 40,
                  }
                }
                testID="[object Object]_close_btn"
              >
                <View
                  style={
                    Object {
                      "height": 40,
                      "style": undefined,
                      "width": 40,
                    }
                  }
                />
              </View>
            </View>
          </View>
          <RCTScrollView
            contentContainerStyle={
              Object {
                "height": "100%",
              }
            }
            showsVerticalScrollIndicator={false}
          >
            <View>
              <Text
                style={
                  Object {
                    "color": "#003353",
                    "fontSize": 14,
                    "fontWeight": "500",
                    "lineHeight": 22,
                  }
                }
              >
                If you're past due on two different installment payments or late by 7 days or more on one installment payment, you will start accruing a periodic penalty interest charge on the outstanding balance, as detailed in your account agreement. This penalty interest accrual will end once you've paid the past due principal amounts as well as all late fees and penalty interest charges.
              </Text>
            </View>
          </RCTScrollView>
        </View>
      </View>
    </Modal>
  </View>
  <View
    style={
      Object {
        "height": 5,
        "width": 0,
      }
    }
  />
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#DB081C",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 33,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          05/17/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#DB2424",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Past Due
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#DB2424",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $316.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 33,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          05/24/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Due Now
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#003353",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $316.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 45,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          05/24/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Due Now
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#003353",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $35.00
      </Text>
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#DB2424",
              "fontSize": 16,
              "fontWeight": "500",
              "textAlign": "right",
            },
          ]
        }
      >
        Late Fee
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 33,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          05/31/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Due Next
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#003353",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $316.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
      <View
        style={
          Object {
            "borderLeftColor": "#9BAEBC",
            "borderLeftStyle": "dashed",
            "borderLeftWidth": 2,
            "flex": 1,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 33,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          06/07/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Due Next
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#003353",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $316.00
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "flexDirection": "row",
      }
    }
  >
    <View
      style={
        Object {
          "alignItems": "center",
        }
      }
    >
      <View
        style={
          Object {
            "backgroundColor": "#F5F7F8",
            "borderColor": "#9BAEBC",
            "borderRadius": 5,
            "borderWidth": 2,
            "height": 10,
            "width": 10,
          }
        }
      />
    </View>
    <View
      style={
        Object {
          "flex": 1,
          "flexDirection": "row",
          "paddingBottom": 33,
          "paddingLeft": 15,
        }
      }
    >
      <View
        style={
          Object {
            "marginRight": 10,
          }
        }
      >
        <Text
          style={
            Array [
              Object {
                "textAlign": "left",
              },
              Object {
                "color": "rgba(28, 27, 31, 1)",
                "fontFamily": "System",
                "fontWeight": "400",
                "letterSpacing": 0,
              },
              Object {
                "writingDirection": "ltr",
              },
              Object {
                "color": "#003353",
                "fontSize": 16,
                "fontWeight": "700",
                "marginBottom": 5,
              },
            ]
          }
        >
          06/14/2023
           
            
        </Text>
        <View
          style={
            Object {
              "alignItems": "center",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              Array [
                Object {
                  "textAlign": "left",
                },
                Object {
                  "color": "rgba(28, 27, 31, 1)",
                  "fontFamily": "System",
                  "fontWeight": "400",
                  "letterSpacing": 0,
                },
                Object {
                  "writingDirection": "ltr",
                },
                Object {
                  "color": "#003353",
                  "fontSize": 16,
                  "fontWeight": "500",
                },
              ]
            }
          >
            Due Next
          </Text>
        </View>
      </View>
    </View>
    <View
      style={
        Object {
          "paddingHorizontal": 10,
          "width": undefined,
        }
      }
    >
      <Text
        style={
          Array [
            Object {
              "textAlign": "left",
            },
            Object {
              "color": "rgba(28, 27, 31, 1)",
              "fontFamily": "System",
              "fontWeight": "400",
              "letterSpacing": 0,
            },
            Object {
              "writingDirection": "ltr",
            },
            Object {
              "color": "#003353",
              "fontSize": 16,
              "fontWeight": "700",
              "marginBottom": 5,
              "textAlign": "right",
            },
          ]
        }
      >
        $317.00
      </Text>
    </View>
  </View>
</View>
`;
