import {
  DeleteObjectCommand,
  ListObjectsCommand,
  S3Client,
} from '@aws-sdk/client-s3'
import {
  InvocationType,
  InvokeCommand,
  LambdaClient,
} from '@aws-sdk/client-lambda'
import { AwsService, FileService } from '@linqpal/common-backend'
import { utils } from '@linqpal/models'
import { Request, Response } from 'express'

const s3Client = new S3Client({ region: 'us-west-1' })

interface RecognizeData {
  bucket: string
  key: string
  region: string
  email: string
  attachmentsCount: number
  attachments: string[]
  errors: string[]
  companyId: string
}

const recognizeInvoiceBatch = async (data: RecognizeData) => {
  const lambdaClient = new LambdaClient({ region: 'us-west-1' })
  try {
    const result = await lambdaClient.send(
      new InvokeCommand({
        FunctionName: `linqpal-microservices-${process.env.LP_MODE}-recognizeInvoice`,
        InvocationType: InvocationType.Event,
        Payload: Buffer.from(JSON.stringify(data)),
      }),
    )
    return { result: result }
  } catch (e) {
    return { error: e }
  }
}

type IResult = {
  result: string
  data?: object
  key?: string
  error?: any
  url?: string
  file?: any
}

export const getRecResult = async (
  key: string,
  _req: Request,
  res: Response,
) => {
  const results: IResult[] = []
  try {
    const getFile = (await AwsService.getS3File(
      `${process.env.LP_MODE}.uw1.linqpal-user-assets`,
      `${key}.json`,
    )) as string
    if (getFile) {
      results.push({
        result: 'ok',
        data: JSON.parse(getFile),
        key: key,
      })
    }
  } catch (e) {
    if (e && (e as any).name === 'NoSuchKey') {
      results.push({ result: 'inprogress', key: key })
    } else {
      results.push({ result: 'failed', key: key, error: e })
    }
  }
  res.send({ results })
}

export const invokeRecognizing = async (
  key: string,
  req: Request,
  res: Response,
) => {
  const results: IResult[] = []
  // for (let f of files) {
  try {
    const eventData: RecognizeData = {
      bucket: `${process.env.LP_MODE}.uw1.linqpal-user-assets`,
      key: key,
      region: 'us-west-1',
      email: 'batch_upload',
      attachmentsCount: 0,
      attachments: [],
      errors: [],
      companyId: req.company!.id,
    }
    const recResult = await recognizeInvoiceBatch(eventData)

    if (recResult.result) {
      results.push({
        result: 'ok',
        key: key,
      })
    } else {
      console.log(recResult.error)
      results.push({ result: 'failed', key: key, error: recResult.error })
    }
  } catch (e) {
    console.log(e)
    results.push({ result: 'failed', error: e, key: key })
  }
  // }
  res.send({ results })
}

export const uploadFile = async (
  req: Request,
  res: Response,
  fileKeyPrimary = '',
) => {
  const files: string[] = req.body.files
  const companyId = req.company!.id
  const results: IResult[] = []
  for (const f of files) {
    try {
      const key = utils.generateFileKey({
        fileKeyPrimary,
        companyId,
        fileName: f,
      })
      const url = await AwsService.getPreSignedUrl({ key, method: 'put' })

      results.push({ url, key, result: 'ok', file: f })
    } catch (e) {
      console.log(e)
      results.push({ result: 'failed', error: e, file: f })
    }
  }
  res.send({ results })
}

export const uploadFileBase64 = async (
  content: string | Buffer,
  path: string,
  fileName: string,
) => {
  try {
    const bucketName = `${process.env.LP_MODE}.uw1.linqpal-user-assets`
    const { key, url } = await FileService.uploadFileBase64(
      content,
      path,
      fileName,
      bucketName,
    )

    return { url, key, result: 'ok', fileName }
  } catch (e) {
    console.log(e)
    return { result: 'failed' }
  }
}

export const deleteFile = async (Key: string) => {
  try {
    return await s3Client.send(
      new DeleteObjectCommand({
        Bucket: `${process.env.LP_MODE}.uw1.linqpal-user-assets`,
        Key,
      }),
    )
  } catch (err) {
    console.log('Error', err)
    return null
  }
}

export const downloadFile = async (
  key: string,
  _req: Request,
  res: Response,
) => {
  const url = await AwsService.getPreSignedUrl({ key, method: 'get' })
  res.send({ url, result: 'ok' })
}

export const listFiles = async (key: string, id: string) => {
  const params = {
    Bucket: `${process.env.LP_MODE}.uw1.linqpal-user-assets`,
    Prefix: `${key}/${id}/`,
  }
  const { Contents } = await s3Client.send(new ListObjectsCommand(params))
  const result: { URL: string; Key?: string }[] = []
  if (Contents) {
    for (const c of Contents) {
      if (c.Key) {
        result.push({
          URL: await AwsService.getPreSignedUrl({ key: c.Key, method: 'get' }),
          Key: c.Key,
        })
      }
    }
  }
  return result
}

export const downloadBluCognitionAttachmentUrl = async (
  key: string,
  req: Request,
  res: Response,
) => {
  const bucketName = `${process.env.LP_MODE}.uw1.linqpal-blucognition`
  const url = await AwsService.getPreSignedUrl({
    key,
    method: 'get',
    bucket: bucketName,
  })
  res.send({ url, result: 'ok' })
}

export const getFilePresignedUrl = async (
  key: string,
  bucketName = `${process.env.LP_MODE}.uw1.linqpal-user-assets`,
) => {
  return AwsService.getPreSignedUrl({ key, method: 'get', bucket: bucketName })
}

export const saveProjectDoc = async (req: Request, res: Response) => {
  const { folder, filename } = req.body
  const randomString = () => Math.random().toString(36)
  const key = `projects/${req.company?._id}/${
    folder || randomString()
  }/${filename}`
  const url = await AwsService.getPreSignedUrl({ key, method: 'put' })
  res.send({ url, key, result: 'ok' })
}
