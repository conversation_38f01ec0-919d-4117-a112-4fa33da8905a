import React from 'react'
import { observer } from 'mobx-react'
import { StyleSheet, View } from 'react-native'
import { DueContainer } from './DueContainer'
import { BalanceContainer } from './BalanceContainer'
import { Footer } from '../../../ViewLoan/Footer'
import Loading from '../../../../../Loading'
import { TransactionHistory } from './TransactionsHistory'
import { PenaltyInterest } from './PenaltyInterest'
import { AutoTradeCreditWarning } from './AutoTradeCreditWarning'
import { composeStyle } from '@linqpal/common-frontend/src/helpers'
import { roundVal } from '../../../LoanSchedule/roundVal'
import CreditStore from '../../../CreditStore'
import { AccountStatusOptions } from '@linqpal/models/src/dictionaries/tradeCredit'

export const ActivitySummary = observer(({ id, isMobile, drawStatus }) => {
  const { loading: isLoading, loan, isPaidOff } = CreditStore

  if (isLoading || !loan || !loan.loanDetails || !id) {
    return (
      <View style={{ paddingVertical: 30 }}>
        <Loading />
      </View>
    )
  }

  console.log('Loan Details:', loan.loanDetails)

  const {
    totalDailyPenaltyInterest,
    nextPaymentAmount,
    nextPaymentDate,
    totalProcessingPaymentsAmount,
    totalLoanAmount,
  } = loan?.loanDetails || {}

  const { totalLoanAmountPaid } = CreditStore
  const totalAmountPaid = totalLoanAmountPaid(loan)
  const canPay =
    roundVal(totalProcessingPaymentsAmount + totalAmountPaid) < totalLoanAmount
  return (
    <>
      <View
        style={
          isMobile
            ? composeStyle(styles.layout, { paddingTop: 5 })
            : styles.layout
        }
      >
        <View style={styles.statisticsLayout}>
          {!!totalDailyPenaltyInterest && canPay && (
            <PenaltyInterest
              isMobile={isMobile}
              amount={loan.loanDetails?.totalDailyPenaltyInterest ?? 0}
            />
          )}
          {!!nextPaymentAmount &&
            !!nextPaymentDate &&
            drawStatus === AccountStatusOptions.GOOD_STANDING && (
              <DueContainer isMobile={isMobile} />
            )}

          <BalanceContainer loan={loan} isMobile={isMobile} />
          {!isPaidOff && !isMobile && (
            <Footer lmsId={id} styles={styles.payButton} loan={loan} />
          )}
        </View>
        {!!loan?.payments?.length && (
          <View
            style={
              isMobile
                ? composeStyle(styles.transactionsLayout, {
                    marginTop: -40,
                  })
                : styles.transactionsLayout
            }
          >
            {<TransactionHistory loan={loan} isMobile={isMobile} />}
            {<AutoTradeCreditWarning isMobile={isMobile} />}
          </View>
        )}
      </View>
    </>
  )
})

const styles = StyleSheet.create({
  layout: {
    display: 'flex',
    flex: 2,
    flexWrap: 'wrap',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 12,
    gap: 60,
  },
  payButton: {
    paddingVertical: 4,
    justifyContent: 'center',
  },
  statisticsLayout: {
    display: 'flex',
    flexDirection: 'column',
    minWidth: 300,
    flex: 1,
    maxWidth: 680,
  },
  transactionsLayout: {
    display: 'flex',
    flex: 1,
    flexDirection: 'column',
    minWidth: 300,
    maxWidth: 680,
  },
})
