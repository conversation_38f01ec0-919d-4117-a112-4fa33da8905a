import {
  Company,
  getLoanPricingPackage,
  Invoice,
} from '@linqpal/common-backend'
import { dictionaries, InvoicePaymentType } from '@linqpal/models'
import {
  LOAN_APPLICATION_STATUS,
  OPERATION_STATUS,
  OPERATION_TYPES,
  PAYMENT_METHODS,
} from '@linqpal/models/src/dictionaries'
import moment from 'moment'
import { PipelineStage } from 'mongoose'
import { ControllerItem } from 'src/routes/controllerItem'

const getTwoYearsAgoDateRange = (): { dateFrom: Date; dateTo: Date } => {
  const dateFrom = moment().startOf('day').subtract(2, 'years').toDate()
  const dateTo = moment().toDate()
  return { dateFrom, dateTo }
}

async function checkAchCardSettlementsExist(
  companyId: string,
): Promise<boolean> {
  const { dateFrom, dateTo } = getTwoYearsAgoDateRange()
  const pipeline: PipelineStage[] = [
    {
      $match: {
        company_id: companyId,
      },
    },
    {
      $lookup: {
        from: 'operations',
        as: 'operation',
        let: { owner: { $toString: '$_id' } },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$owner_id', '$$owner'] },
                  { $ne: ['$status', 'PLACED'] },
                  {
                    $ne: ['$type', dictionaries.OPERATION_TYPES.INVOICE.REFUND],
                  },
                ],
              },
              type: OPERATION_TYPES.INVOICE.PAYMENT,
              'metadata.payment_method': {
                $in: [PAYMENT_METHODS.ACH, PAYMENT_METHODS.CARD],
              },
            },
          },
          { $unset: ['updatedAt', 'metadata.pullResult'] },
        ],
      },
    },
    {
      $unwind: {
        path: '$operation',
        preserveNullAndEmptyArrays: false,
      },
    },
    {
      $lookup: {
        from: 'transactions',
        as: 'operation.allTransactions',
        let: { operation_id: { $toString: '$operation._id' } },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$operation_id', '$$operation_id'] },
                  { $gte: ['$createdAt', dateFrom] },
                  { $lte: ['$createdAt', dateTo] },
                  { $eq: ['$metadata.transactionType', 'PULL'] },
                ],
              },
            },
          },
          { $limit: 1 }, // Only need to check existence
        ],
      },
    },
    {
      $addFields: {
        hasPullTransaction: {
          $cond: {
            if: { $gt: [{ $size: '$operation.allTransactions' }, 0] },
            then: true,
            else: false,
          },
        },
      },
    },
    {
      $match: {
        hasPullTransaction: true,
      },
    },
    {
      $project: {
        _id: 1,
      },
    },
    {
      $limit: 1,
    },
  ]

  const result = await Invoice.aggregate(pipeline)
  console.log(result)

  return !!result.length
}

async function checkTradeCreditSettlementsExist(
  companyId: string,
): Promise<boolean> {
  const { dateFrom, dateTo } = getTwoYearsAgoDateRange()
  const company = await Company.findById(companyId)

  const pricingPackage = await getLoanPricingPackage(
    company?.settings?.loanPricingPackageId,
  )

  if (!pricingPackage) {
    return false
  }

  const pipeline: PipelineStage[] = [
    {
      $match: {
        company_id: companyId,
        $or: [
          { paymentDetails: null },
          { paymentDetails: { $exists: false } },
          {
            $and: [
              { paymentDetails: { $ne: null } },
              {
                $or: [
                  { 'paymentDetails.paymentType': { $eq: null } },
                  { 'paymentDetails.paymentType': { $exists: false } },
                  {
                    'paymentDetails.paymentType': {
                      $ne: InvoicePaymentType.FACTORING,
                    },
                  },
                ],
              },
            ],
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'operations',
        as: 'paymentOperation',
        let: { id: { $toString: '$_id' } },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$owner_id', '$$id'] },
                  {
                    $in: [
                      '$status',
                      [OPERATION_STATUS.SUCCESS, OPERATION_STATUS.PROCESSING],
                    ],
                  },
                ],
              },
              type: OPERATION_TYPES.INVOICE.PAYMENT,
              'metadata.payment_method': PAYMENT_METHODS.LOAN,
            },
          },
        ],
      },
    },
    {
      $unwind: {
        path: '$paymentOperation',
        preserveNullAndEmptyArrays: false,
      },
    },
    {
      $lookup: {
        from: 'loanapplications',
        as: 'loanApplication',
        let: { invoiceId: { $toString: '$_id' } },
        pipeline: [
          {
            $addFields: {
              ids: {
                $cond: {
                  if: {
                    $eq: [{ $type: '$invoiceDetails.invoiceId' }, 'array'],
                  },
                  then: '$invoiceDetails.invoiceId',
                  else: ['$invoiceDetails.invoiceId'],
                },
              },
            },
          },
          {
            $match: {
              $expr: { $in: ['$$invoiceId', '$ids'] },
              status: {
                $in: [
                  LOAN_APPLICATION_STATUS.APPROVED,
                  LOAN_APPLICATION_STATUS.CLOSED,
                ],
              },
              lms_id: { $exists: true },
            },
          },
          {
            $project: {
              invoiceDetails: 1,
              metadata: 1,
              issueDate: 1,
              updatedAt: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: {
        path: '$loanApplication',
        preserveNullAndEmptyArrays: false,
      },
    },
    // Find payment plan duration. Since November 2022 paymentPlan is saved right into loanApplication
    // This query is only to support legacy records. In November 2024 this part can be removed.
    {
      $lookup: {
        from: 'loanpaymentplans',
        as: 'paymentPlan',
        let: {
          paymentPlanId: {
            $convert: {
              input: '$loanApplication.invoiceDetails.paymentPlan',
              to: 'objectId',
              onError: null,
            },
          },
          paymentPlanName: '$loanApplication.invoiceDetails.paymentPlan',
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $or: [
                  { $eq: ['$_id', '$$paymentPlanId'] },
                  { $eq: ['$name', '$$paymentPlanName'] },
                ],
              },
            },
          },
          { $project: { days: 1 } },
        ],
      },
    },
    {
      $unwind: {
        path: '$paymentPlan',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: 'operations',
        as: 'operations',
        let: { owner: { $toString: '$_id' } },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$owner_id', '$$owner'] },
                  {
                    $in: [
                      '$status',
                      [OPERATION_STATUS.SUCCESS, OPERATION_STATUS.PROCESSING],
                    ],
                  },
                ],
              },
              type: {
                $in: [
                  OPERATION_TYPES.INVOICE.PAYMENT,
                  OPERATION_TYPES.INVOICE.FINAL_PAYMENT,
                ],
              },
            },
          },
        ],
      },
    },
    {
      $addFields: {
        operationsIds: {
          $map: {
            input: '$operations',
            as: 'op',
            in: { $toString: '$$op._id' },
          },
        },
      },
    },
    {
      $lookup: {
        from: 'transactions',
        as: 'transactions',
        let: { ops: '$operationsIds' },
        pipeline: [
          {
            $match: { $expr: { $in: ['$operation_id', '$$ops'] } },
          },
          {
            $project: {
              operation_id: 1,
              date: 1,
              createdAt: 1,
            },
          },
        ],
      },
    },
    {
      $addFields: {
        advancePayment: { $arrayElemAt: ['$transactions', 0] },
      },
    },
    {
      $addFields: {
        finalPayment: {
          $cond: [
            { $eq: [{ $size: '$transactions' }, 2] },
            { $arrayElemAt: ['$transactions', 1] },
            undefined,
          ],
        },
      },
    },
    {
      $project: {
        id: { $toString: '$_id' },
        advancePayment: 1,
        finalPayment: 1,
        skipFinalPayment: {
          $cond: {
            if: {
              $eq: [
                {
                  $ifNull: [
                    '$loanApplication.metadata.loanPackage.finalPayment',
                    pricingPackage.metadata.finalPayment,
                  ],
                },
                0,
              ],
            },
            then: true,
            else: false,
          },
        },

        term: {
          $ifNull: [
            '$loanApplication.metadata.paymentPlan.days',
            '$paymentPlan.days',
            {
              $cond: {
                if: {
                  $isNumber: '$loanApplication.invoiceDetails.paymentPlan',
                },
                then: '$loanApplication.invoiceDetails.paymentPlan',
                else: null,
              },
            },
            0,
          ],
        },
        transactionDate: {
          $ifNull: ['$loanApplication.issueDate', '$loanApplication.updatedAt'],
        },
        advanceReleaseDate: {
          $ifNull: ['$advancePayment.date', '$advancePayment.createdAt'],
        },
      },
    },

    {
      $addFields: {
        finalReleaseDate: {
          $cond: {
            if: { $eq: ['$skipFinalPayment', false] },
            then: {
              $ifNull: [
                '$finalPayment.date',
                {
                  $dateAdd: {
                    startDate: '$transactionDate',
                    unit: 'day',
                    amount: '$term',
                  },
                },
              ],
            },
            else: null,
          },
        },
      },
    },
    {
      $match: {
        $or: [
          {
            advanceReleaseDate: {
              $gte: dateFrom,
              $lte: dateTo,
            },
          },
          {
            finalReleaseDate: {
              $gte: dateFrom,
              $lte: dateTo,
            },
          },
        ],
      },
    },
    {
      $limit: 1,
    },
    {
      $project: {
        id: 1,
      },
    },
  ]
  const result = await Invoice.aggregate(pipeline)

  return !!result.length
}

async function checkArAdvanceSettlementsExist(
  companyId: string,
): Promise<boolean> {
  const { dateFrom, dateTo } = getTwoYearsAgoDateRange()

  const pipeline: PipelineStage[] = [
    {
      $match: {
        company_id: companyId,
        'paymentDetails.paymentType': InvoicePaymentType.FACTORING,
        'paymentDetails.arAdvanceStatus': { $in: ['completed', 'approved'] },
        'paymentDetails.loanPlanId': { $exists: true, $ne: null },
        'paymentDetails.pricingPackageId': { $exists: true, $ne: null },
      },
    },
    {
      $lookup: {
        from: 'operations',
        as: 'paymentOperations',
        let: { id: { $toString: '$_id' } },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$owner_id', '$$id'] },
                  {
                    $in: [
                      '$status',
                      [OPERATION_STATUS.SUCCESS, OPERATION_STATUS.PROCESSING],
                    ],
                  },
                  {
                    $eq: ['$type', OPERATION_TYPES.FACTORING.DISBURSEMENT],
                  },
                ],
              },
            },
          },
          { $limit: 2 },
        ],
      },
    },
    {
      $unwind: {
        path: '$paymentOperations',
        preserveNullAndEmptyArrays: false,
      },
    },
    {
      $lookup: {
        from: 'transactions',
        as: 'transaction',
        let: { opId: { $toString: '$paymentOperations._id' } },
        pipeline: [
          {
            $match: {
              $expr: { $eq: [{ $toString: '$operation_id' }, '$$opId'] },
            },
          },
          {
            $sort: { createdAt: -1 },
          },
          {
            $project: {
              operation_id: 1,
              date: 1,
              createdAt: 1,
            },
          },
          { $limit: 1 },
        ],
      },
    },
    {
      $addFields: {
        advancePaymentTemp: {
          $cond: [
            {
              $eq: [
                '$paymentOperations.type',
                OPERATION_TYPES.FACTORING.DISBURSEMENT,
              ],
            },
            { $arrayElemAt: ['$transaction', 0] },
            null,
          ],
        },
      },
    },
    {
      $group: {
        _id: '$_id',
        advancePayment: {
          $max: {
            $cond: [
              { $ne: ['$advancePaymentTemp', null] },
              '$advancePaymentTemp',
              null,
            ],
          },
        },
      },
    },
    {
      $project: {
        id: { $toString: '$_id' },
        advanceReleaseDate: {
          $ifNull: ['$advancePayment.date', '$advancePayment.createdAt'],
        },
      },
    },
    {
      $match: {
        advanceReleaseDate: {
          $gte: dateFrom,
          $lte: dateTo,
        },
      },
    },
    {
      $limit: 1,
    },
    {
      $project: {
        id: 1,
      },
    },
  ]

  const result = await Invoice.aggregate(pipeline)

  return !!result.length
}

export default {
  get: async (req, res) => {
    const companyId = req.company!.id

    const [
      arAdvanceSettlementsExists,
      tradeCreditSettlementsExists,
      achCardSettlementsExists,
    ] = await Promise.all([
      checkArAdvanceSettlementsExist(companyId),
      checkTradeCreditSettlementsExist(companyId),
      checkAchCardSettlementsExist(companyId),
    ])

    // Use the results as needed
    console.log('AR Advance Settlements Exist:', arAdvanceSettlementsExists)
    console.log('Trade Credit Settlements Exist:', tradeCreditSettlementsExists)
    console.log('ACH/Card Settlements Exist:', achCardSettlementsExists)

    res.send({
      arAdvanceSettlementsExists,
      tradeCreditSettlementsExists,
      achCardSettlementsExists,
    })
  },
} as ControllerItem
