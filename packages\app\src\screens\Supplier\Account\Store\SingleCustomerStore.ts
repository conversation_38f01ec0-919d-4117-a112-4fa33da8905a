import { BillingContact, routes } from '@linqpal/models'
import {
  agingCode,
  InHouseCreditStatus,
} from '@linqpal/models/src/dictionaries'
import { ICustomerResponse } from '@linqpal/models/src/dictionaries/supplierCustomers'
import { TradeCreditStatus } from '@linqpal/models/src/dictionaries/TradeCreditStatus'
import { makeAutoObservable } from 'mobx'
import { CustomerSidebarMode } from './Types/ICustomerSidebarMode'
import { CustomerAccountType } from '@linqpal/models/src/dictionaries/customerAccountType'

export class SingleCustomerStore {
  customerId: string | null = null

  customer: ICustomerResponse | null = null

  sidebarMode: CustomerSidebarMode | null = null

  activeTabIndex = 0

  showDiscardChangesConfirmationModal = false

  isLoadingCustomerDetails = false

  isLoadingCustomerAdditionalInfo = false

  isLoadingCreditInfo = false

  constructor() {
    makeAutoObservable(this)
  }

  setSidebarMode(mode: CustomerSidebarMode | null) {
    this.sidebarMode = mode
  }

  setActiveTabIndex(value: number) {
    this.activeTabIndex = value
  }

  setShowDiscardChangesConfirmationModal(value: boolean) {
    this.showDiscardChangesConfirmationModal = value
  }

  showCustomer(
    customer: any, // TODO: VK: clarify type (partial of CustomerAccount?)
  ) {
    this.setSidebarMode(CustomerSidebarMode.View)
    this.activeTabIndex = 0

    if (typeof customer === 'string') {
      this.customerId = customer
      this.fetchCustomerDetails()
    } else {
      this.setCustomer(customer)

      Promise.allSettled([
        this.fetchCustomerAdditionalInfo(),
        this.fetchCreditInfo(),
      ]).catch((e) => console.error(e))
    }
  }

  setCustomer(customer: any) {
    const first_name = customer.first_name ?? ''
    const last_name = customer.last_name ?? ''

    this.customerId = customer.id
    this.customer = {
      ...customer,
      contact: [first_name, last_name].filter(Boolean).join(' ').trim(),
    }
  }

  resetCustomer() {
    this.customerId = null
    this.customer = null
    this.sidebarMode = null
  }

  fetchCustomerDetails() {
    if (!this.customerId) return

    this.isLoadingCustomerDetails = true

    routes.supplier
      .customerDetails(this.customerId)
      .then((data) => this.setCustomer(data))
      .catch((err) => {
        console.error('Error fetching customer details:', err)
      })
      .finally(() => {
        this.isLoadingCustomerDetails = false

        if (this.customer) {
          this.fetchCustomerAdditionalInfo()
          if (this.sidebarMode === CustomerSidebarMode.View) {
            this.fetchCreditInfo()
          }
        }
      })
  }

  fetchCustomerAdditionalInfo() {
    if (!this.customerId || !this.customer) return

    this.isLoadingCustomerAdditionalInfo = true

    return routes.supplier
      .customerAdditionalInfo(this.customerId)
      .then((data) => {
        this.customer = {
          ...this.customer,
          bankAccounts: data.bankAccounts,
          contacts:
            data.billingContacts?.map((contact: BillingContact) => {
              return {
                ...contact,
                name: this.generateContactName(
                  contact.first_name,
                  contact.last_name,
                ),
              }
            }) || [],
          isBillingContactAdded: !!data.billingContacts.find((c: any) => c.id),
          salesRepUserInfo: data.salesRepUserInfo,
        }
      })
      .catch((err) => {
        console.error('Error fetching customer additional info:', err)
      })
      .finally(() => {
        this.isLoadingCustomerAdditionalInfo = false
      })
  }

  async fetchCreditInfo() {
    if (!this.customerId || !this.customer) return

    this.isLoadingCreditInfo = true

    if (this.customer?.customer?.id) {
      routes.supplier
        .creditInfo(this.customer.customer?.id, this.customerId)
        .then((data) => {
          const pastDueBreakdown = this.getPastDueBreakdown(data.agingList)

          if (this.customer?.type === CustomerAccountType.IHC) {
            const hasPastDue =
              data.creditInfo?.account_status ===
                InHouseCreditStatus.GoodStanding &&
              this.hasPastDue(pastDueBreakdown)

            const creditStatus = hasPastDue
              ? TradeCreditStatus.PastDue
              : data.creditInfo.account_status
            this.customer = {
              ...this.customer,
              credit_info: {
                ...data.creditInfo,
                credit_status: creditStatus,
                credit_status_valid_till: null,
                past_due_breakdown: pastDueBreakdown,
              },
              credit_status: creditStatus,
            }
          } else {
            const hasPastDue =
              data.creditInfo?.account_status &&
              [TradeCreditStatus.Approved, TradeCreditStatus.GoodStanding].find(
                (status) => status === data.creditInfo.account_status,
              ) &&
              this.hasPastDue(pastDueBreakdown)

            const creditStatus = hasPastDue
              ? this.getPastDueStatus(pastDueBreakdown) ||
                TradeCreditStatus.PastDue
              : data.creditInfo.account_status

            this.customer = {
              ...this.customer,
              credit_info: {
                ...data.creditInfo,
                credit_status: creditStatus,
                credit_status_valid_till:
                  this.customer?.credit_status_valid_till,
                past_due_breakdown: pastDueBreakdown,
              },
            }
          }
        })
        .catch((err) => {
          console.error('Error fetching credit info:', err)
        })
        .finally(() => {
          this.isLoadingCreditInfo = false
        })
    } else {
      this.customer = {
        ...this.customer,
        credit_info: {
          ...this.customer.credit_info!,
          credit_status: TradeCreditStatus.NotApplied,
        },
      }
      this.isLoadingCreditInfo = false
    }
  }

  generateContactName(first_name?: string, last_name?: string): string {
    const fName = first_name ?? ''
    const lName = last_name ?? ''

    return [fName, lName].filter(Boolean).join(' ')
  }

  getPastDueBreakdown(agingList) {
    const defaultBreakdown = {
      pastDue30: 0,
      pastDue60: 0,
      pastDue90: 0,
      pastDue120: 0,
      pastDue120Plus: 0,
    }
    if (!agingList) return defaultBreakdown

    const findAmount = (agingItem, code) =>
      agingItem?.agingDetails?.find((item) => item.code === code)?.amount || 0

    const pastDueBreakdown = agingList.reduce((total, current) => {
      return {
        pastDue30: total.pastDue30 + findAmount(current, agingCode.PastDue30),
        pastDue60: total.pastDue60 + findAmount(current, agingCode.PastDue60),
        pastDue90: total.pastDue90 + findAmount(current, agingCode.PastDue90),
        pastDue120:
          total.pastDue120 + findAmount(current, agingCode.PastDue120),
        pastDue120Plus:
          total.pastDue120Plus +
          (current.agingDetails
            ?.filter((item) =>
              [
                agingCode.PastDue150,
                agingCode.PastDue180,
                agingCode.PastDue180Plus,
              ].includes(item.code),
            )
            ?.reduce((sum, item) => sum + item.amount, 0) || 0),
      }
    }, defaultBreakdown)

    return pastDueBreakdown
  }

  hasPastDue(pastDueBreakdown) {
    return (
      pastDueBreakdown?.pastDue30 ||
      pastDueBreakdown?.pastDue60 ||
      pastDueBreakdown?.pastDue90 ||
      pastDueBreakdown?.pastDue120 ||
      pastDueBreakdown?.pastDue120Plus
    )
  }

  getPastDueStatus(pastDueBreakdown) {
    if (!this.hasPastDue(pastDueBreakdown)) {
      return null
    }

    if (
      pastDueBreakdown?.pastDue30 &&
      !pastDueBreakdown?.pastDue60 &&
      !pastDueBreakdown?.pastDue90 &&
      !pastDueBreakdown?.pastDue120 &&
      !pastDueBreakdown?.pastDue120Plus
    ) {
      return TradeCreditStatus.PastDue10
    }

    if (
      (pastDueBreakdown?.pastDue30 || pastDueBreakdown?.pastDue60) &&
      !pastDueBreakdown?.pastDue90 &&
      !pastDueBreakdown?.pastDue120 &&
      !pastDueBreakdown?.pastDue120Plus
    ) {
      return TradeCreditStatus.PastDue60
    }

    return TradeCreditStatus.PastDue
  }
}
