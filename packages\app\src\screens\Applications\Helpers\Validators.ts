import i18next from 'i18next'
import {
  validateBirthdate,
  validateEin,
  validateEmail,
  validateSsn,
  validateUSPhoneNumber,
  validateZip,
} from '@linqpal/models/src/helpers/validations'

export class Validators {
  static required(value: string, message?: string): string {
    return value.trim()
      ? ''
      : message ?? i18next.t('ValidationErrors.FieldRequired')
  }

  static email(value: string): string {
    return validateEmail(value)
      ? ''
      : i18next.t('application:ValidationErrors.InvalidEmail')
  }

  static zip(value: string): string {
    return validateZip(value?.trim())
      ? ''
      : i18next.t('application:ValidationErrors.InvalidZip')
  }

  static phone(value: string): string {
    return validateUSPhoneNumber(value)
      ? ''
      : i18next.t('application:ValidationErrors.InvalidPhone')
  }

  static birthdate(value: string): string {
    return validateBirthdate(value.trim())
      ? ''
      : i18next.t('application:ValidationErrors.InvalidDate')
  }

  static ssn(value: string): string {
    const unformattedValue = value.replace(/\D/g, '')

    // hide validation message when 10th char is pressed
    if (unformattedValue.length > 9) return ''

    return validateSsn(unformattedValue)
      ? ''
      : i18next.t('application:ValidationErrors.InvalidSSN')
  }

  static ein(value: string) {
    const unformattedValue = value.replace(/\D/g, '')

    // hide validation message when 10th char is pressed
    if (unformattedValue.length > 9) return ''

    return validateEin(unformattedValue)
      ? ''
      : i18next.t('ValidationErrors.InvalidTaxID')
  }
}
