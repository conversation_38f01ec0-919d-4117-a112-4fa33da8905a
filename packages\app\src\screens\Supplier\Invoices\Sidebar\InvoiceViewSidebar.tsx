import React, { useMemo } from 'react'
import { observer } from 'mobx-react-lite'
import Sidebar from '../../../../ui/atoms/Sidebar'
import { Spacer } from '../../../../ui/atoms'
import { phoneMask } from '../../../../utils/helpers/masking'
import { Text } from 'react-native-paper'
import { StyleSheet } from 'react-native'
import { InvoicesSidebarFooter } from './InvoicesSidebarFooter'
import { InvoiceTransactions } from './InvoiceTransactions'
import { TouchableOpacity } from 'react-native-gesture-handler'
import {
  invoiceStatus,
  OPERATION_TYPES,
} from '@linqpal/models/src/dictionaries'
import { useTabInvoice } from '../../../../store/ScreensStore/Supplier/supplierInvoiceTab'
import { SidebarDetailItem } from '../../../../ui/molecules'
import { toJS } from 'mobx'
import { useStore } from '../../../../store'
import { AddContact } from '../../Account/AddLink'
import { EditContact } from '../../Account/EditLink'
import NotesComponent from '../Components/NotesComponent'
import { EInvoiceType, InvoicePaymentType } from '@linqpal/models'
import { useTranslation } from 'react-i18next'
import CustomerPaymentMethod from '../../Account/Sidebar/AccountSidebarView/AddCustomerPaymentMethod/CustomerPaymentMethodStore'
import { useNavigation } from '@react-navigation/core'
import { useTabAccount } from '../../../../store/ScreensStore/Supplier/SupplierAccountsTab'
import { AttachmentLine } from './ViewSidebarComponents/AttachmentLine'
import { DateLine } from './ViewSidebarComponents/DateLine'
import { DueDateLine } from './ViewSidebarComponents/DueDateLine'
import { ExpirationDateLine } from './ViewSidebarComponents/ExpirationDateLine'
import { AmountLine } from './ViewSidebarComponents/AmountLine'
import { AddressLine } from './ViewSidebarComponents/AddressLine'
import { RecipientsContainer } from './ViewSidebarComponents/RecipientsLine'
import { getReceivableLocalization } from '../receivableLocalization'
import { ReceivableStatusLabel } from '../../../../ui/molecules/StatusLabel/ReceivableStatusLabel'
import { InHouseCreditTermLine } from './ViewSidebarComponents/InHouseCreditTermLine'
import { ArAdvanceStatusLine } from './ViewSidebarComponents/ArAdvanceStatusLine'
import { ArAdvanceStatus } from '@linqpal/models/src/dictionaries/factoring'

/**
 *
 * @param props
 * @param props.error {boolean}
 * @param props.message {string}
 * @returns {boolean|JSX.Element}
 * @constructor
 */

interface InvoiceViewSidebarProps {
  hideTransactions?: boolean
  transactionType?:
    | typeof OPERATION_TYPES.ACH.PULL
    | typeof OPERATION_TYPES.ACH.OUT
  showAddBillingContacts?: boolean
  onClose?: () => void
}

const PayableStatuses = [
  invoiceStatus.placed,
  invoiceStatus.seen,
  invoiceStatus.pastDue,
  invoiceStatus.paymentFailed,
]

export const InvoiceViewSidebar: React.FC<InvoiceViewSidebarProps> = observer(
  (props) => {
    const {
      draftModel: invoice,
      customer,
      isView,
      cancelViewSideBar,
      typeOfInvoice,
    } = useTabInvoice()
    const tabAccount = useTabAccount()
    const { t } = useTranslation('global')

    const localization = getReceivableLocalization(invoice.type, t)

    const { userStore } = useStore()
    const navigation = useNavigation()
    const { onCollectPayment } = CustomerPaymentMethod

    const showFooter =
      invoice?.paymentDetails?.arAdvanceStatus !== ArAdvanceStatus.Approved
    const isRegularInvoice =
      invoice?.paymentDetails?.paymentType !== InvoicePaymentType.FACTORING

    const hideAddBillingContacts = useMemo(() => {
      return (
        !(props.showAddBillingContacts ?? true) ||
        invoice.status === 'EXPIRED' ||
        invoice.status === 'DISMISSED' ||
        invoice.status === 'REJECTED' ||
        invoice.status === 'CANCELLED'
      )
    }, [invoice.status, props.showAddBillingContacts])

    return (
      <Sidebar
        visible={isView && invoice.type !== EInvoiceType.QUOTE}
        width={600}
        title={`${typeOfInvoice} Details`}
        onClose={cancelViewSideBar}
        footer={
          showFooter && <InvoicesSidebarFooter canEdit={isRegularInvoice} />
        }
        footerStyle={{
          height: invoice.getImportSource.isImported ? 128 : 100,
          flexDirection: 'none',
        }}
        subTitle={customer.name}
        {...props}
      >
        <Spacer height={32} />

        <SidebarDetailItem
          label={localization.number}
          content={invoice.invoice_number}
        />
        <SidebarDetailItem
          label={localization.status}
          content={<ReceivableStatusLabel receivable={invoice} />}
        />
        {!isRegularInvoice && <ArAdvanceStatusLine receivable={invoice} />}

        {invoice.quote_number ? (
          <SidebarDetailItem
            label={t('Receivables.quote.number')}
            content={invoice.quote_number}
          />
        ) : null}

        <DateLine receivable={invoice} />
        <Spacer height={32} />

        <AmountLine receivable={invoice} />
        <Spacer height={32} />

        {!isRegularInvoice && <InHouseCreditTermLine receivable={invoice} />}
        <DueDateLine receivable={invoice} />
        <ExpirationDateLine receivable={invoice} />

        <AddressLine receivable={invoice} />

        <AttachmentLine receivable={invoice} />
        {invoice.status === invoiceStatus.collected && (
          <SidebarDetailItem
            label={t('InvoiceViewSidebar.collected-by-label')}
            content={t('InvoiceViewSidebar.supplier')}
          />
        )}
        <Spacer height={32} />

        <SidebarDetailItem
          label={t('InvoiceViewSidebar.business-name-label')}
          content={customer.name}
        />
        <SidebarDetailItem
          label={t('InvoiceViewSidebar.customer-number-label')}
          content={invoice.customer_account_id || 'N/A'}
        />
        <SidebarDetailItem
          label={t('InvoiceViewSidebar.contact-name-label')}
          content={customer.contact}
        />
        <SidebarDetailItem
          label={t('InvoiceViewSidebar.phone-label')}
          content={phoneMask(customer.phone)}
        />
        {invoice.status === invoiceStatus.dismissed && invoice.dismiss_reasons && (
          <>
            <SidebarDetailItem
              label={t('InvoiceViewSidebar.dismiss-reasons')}
            />
            {invoice.dismiss_reasons.map((dr) => (
              <SidebarDetailItem label={dr} />
            ))}
          </>
        )}
        {userStore.supplierCanPay && PayableStatuses.includes(invoice.status) && (
          <>
            <Spacer height={42} />
            <TouchableOpacity
              style={styles.collectPaymentContainer}
              onPress={() => {
                onCollectPayment(
                  toJS(invoice),
                  navigation,
                  tabAccount.setDraftModel,
                )
              }}
            >
              <Text style={styles.collectPaymentLabel}>
                {t('InvoiceViewSidebar.collect-payment')}
              </Text>
            </TouchableOpacity>
          </>
        )}
        {!!invoice.payersInfo?.length && (
          <SidebarDetailItem
            label={t('InvoiceViewSidebar.recipients-label')}
            style={{ justifyContent: 'flex-start', alignItems: 'center' }}
            content={<RecipientsContainer values={invoice.payersInfo} />}
          />
        )}
        <Spacer height={36} />
        <NotesComponent invoice={invoice} isItInvoiceCreate={false} />
        {customer.isBillingContactAdded && (
          <>
            <SidebarDetailItem
              label={t('InvoiceViewSidebar.billing-contact')}
              contentStyle={{ maxWidth: 300 }}
              content={customer.contacts.map((c) => c.name).join(', ')}
            />
            <EditContact />
          </>
        )}
        {!customer.isBillingContactAdded && !hideAddBillingContacts && (
          <AddContact />
        )}
        <Spacer height={52} />
        {!props.hideTransactions && invoice.status !== invoiceStatus.draft && (
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          <InvoiceTransactions transactionType={props.transactionType} />
        )}
      </Sidebar>
    )
  },
)

const styles = StyleSheet.create({
  collectPaymentContainer: {
    backgroundColor: '#EDF1F7',
    width: 166,
    borderWidth: 0,
    height: 48,
    borderRadius: 4,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  collectPaymentLabel: {
    fontWeight: '500',
    fontSize: 15,
    lineHeight: 24,
    color: '#222B45',
  },
})
