import { observer } from 'mobx-react-lite'
import { useTranslation } from 'react-i18next'
import { SidebarDetailItem } from '../../../../../ui/molecules'
import React from 'react'
import { IReceivableLineProps } from './IReceivableLineProps'
import { EInvoiceAddressType } from '@linqpal/models'

export const AddressLine = observer(({ receivable }: IReceivableLineProps) => {
  const { t } = useTranslation('global')

  console.log(receivable)

  return (
    <>
      {receivable.addressType === EInvoiceAddressType.DELIVERY ? (
        <SidebarDetailItem
          label={t('InvoiceViewSidebar.deliver-label')}
          content={receivable.address}
          contentStyle={{ maxWidth: 300 }}
        />
      ) : (
        <SidebarDetailItem
          label={t('InvoiceViewSidebar.delivery-type-label')}
          content={receivable.addressType}
        />
      )}
    </>
  )
})
