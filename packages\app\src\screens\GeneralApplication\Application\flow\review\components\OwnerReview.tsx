import React, { FC, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { formatAddress } from '@linqpal/models/src/helpers/addressFormatter'
import { IDraftModel } from '@linqpal/models'
import { BtRadioGroup, BtText } from '@linqpal/components/src/ui'
import RootStore from '../../../../../../store'
import { OwnerIcon } from '../../../components/OwnerIcon'
import { Spacer } from '../../../../../../ui/atoms'
import { Divider } from '@ui-kitten/components'
import { OwnerReviewStyles as styles } from './OwnerReviewStyles'
import { OwnerPropertyRow } from './OwnerPropertyRow'
import ApplicationStore from '../../../ApplicationStore'
import { FlowController, ModelUpdateInfo } from '../../../FlowController'
import { Address } from '../../commonQuestions/Address'
import { BirthdateInput } from '../../commonQuestions/Birthdate'
import { SSNInput } from '../../commonQuestions/SSN'
import { useResponsive } from '../../../../../../utils/hooks'
import { getGroupTitle } from '../../../groupTitles'
import { InvitationSent } from '../../../components'
import { formatDomesticPhone } from '@linqpal/models/src/helpers/phoneFormatter'

interface OwnerReviewProps {
  draft: IDraftModel
  flowController: FlowController
}

export const OwnerReview: FC<OwnerReviewProps> = ({
  draft,
  flowController,
}) => {
  const { t } = useTranslation('application')
  const { sm } = useResponsive()

  const onValueUpdate = (updateInfo: ModelUpdateInfo) =>
    ApplicationStore.handleDocumentUpdate(
      updateInfo.value,
      updateInfo.identifier,
      updateInfo.group,
      updateInfo.filled,
      getGroupTitle(updateInfo.group) || '',
    )

  const createProps = (identifier: string, testID: string) => {
    return {
      doc: draft,
      group: 'businessOwner',
      item: identifier,
      decidingQuestion: false,
      onValueUpdate,
      flowController,
      sm,
      review: true,
      testID,
    }
  }

  const owner = useMemo(() => {
    return {
      firstName: RootStore.userStore.user?.firstName,
      lastName: RootStore.userStore.user?.lastName,
      email: RootStore.userStore.user?.email || '',
      phone: RootStore.userStore.user?.phone?.slice(2) || '',
      percentOwned: draft.get('businessOwner', 'ownershipPercentage') || 0,
      address: draft.get('businessOwner', 'address'),
      birthdate: draft.get('businessOwner', 'birthdate'),
      ssn: draft.get('businessOwner', 'ssn'),
      isOwner: ApplicationStore.isOwner,
      isAuthorised: ApplicationStore.isAuthorised,
      isFilled: () => {
        const steps = flowController.getGroupSteps('businessOwner')
        return ApplicationStore.allStepsFilled(steps)
      },
    }
  }, [draft, flowController])

  return (
    <>
      <Spacer height={25} />
      <BtText size={12} weight={500}>
        {t('Preview.OwnerTitle')}
      </BtText>
      <BtRadioGroup
        value={owner.isOwner}
        disabled={true}
        options={[
          { label: t('Owner.AuthorizedLabelYes'), value: true },
          { label: t('Owner.AuthorizedLabelNo'), value: false },
        ]}
        radioStyle={{ marginRight: 32 }}
        groupStyle={{ flexDirection: 'row' }}
      />
      {!owner.isOwner ? (
        <>
          <Spacer height={10} />
          <BtText size={12} weight={500}>
            {t('Preview.AuthorizedSignerTitle')}
          </BtText>
          <BtRadioGroup
            value={owner.isAuthorised}
            disabled={true}
            options={[
              { label: t('Owner.AuthorizedLabelYes'), value: true },
              { label: t('Owner.AuthorizedLabelNo'), value: false },
            ]}
            radioStyle={{ marginRight: 32 }}
            groupStyle={{ flexDirection: 'row' }}
          />
        </>
      ) : null}
      {ApplicationStore.isOwner ? (
        <View style={styles.container}>
          <OwnerIcon isOwnerFilled={owner.isFilled()} />
          <View style={styles.properties}>
            {owner.firstName || owner.lastName ? (
              <BtText style={styles.ownerName}>
                {`${owner.firstName} ${owner.lastName}`}
              </BtText>
            ) : null}
            <View style={{ width: '100%' }}>
              <OwnerPropertyRow
                label={t('Preview.Owner')}
                value={t('Preview.Percentage', {
                  percentage: owner.percentOwned,
                })}
              />
              <OwnerPropertyRow
                label={t('CoOwners.HomeAddress')}
                value={formatAddress(owner.address)}
              />
              <OwnerPropertyRow
                label={t('CoOwners.Birthday')}
                value={owner.birthdate}
              />
              <OwnerPropertyRow
                label={t('CoOwners.SSN')}
                value={owner.ssn}
                secured
              />
              <OwnerPropertyRow
                label={t('CoOwners.PhoneNumber')}
                value={formatDomesticPhone(owner.phone)}
              />
              <OwnerPropertyRow
                label={t('CoOwners.EmailAddress')}
                value={owner.email}
              />
            </View>
            <Spacer height={25} />
          </View>
        </View>
      ) : ApplicationStore.isAuthorised ? (
        <View>
          <Address {...createProps('address', 'preview_auth_address')} />
          <BirthdateInput
            {...createProps('birthdate', 'preview_auth_birthdate')}
          />
          <SSNInput {...createProps('ssn', 'preview_auth_ssn')} />
          <Spacer height={25} />
        </View>
      ) : (
        <>
          <Spacer height={15} />
          <InvitationSent document={draft} />
        </>
      )}
      <Divider />
    </>
  )
}
