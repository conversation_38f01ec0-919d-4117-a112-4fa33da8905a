import { StyleSheet, View } from 'react-native'
import { colors } from '@linqpal/components/src/theme'
import React, { FC } from 'react'

export const WizardProgressBar: FC = () => {
  const progress = 0.33

  return (
    <View style={styles.progressBar}>
      <View style={[styles.filledProgress, { flex: progress }]} />
      <View style={[styles.emptyProgress, { flex: 1 - progress }]} />
    </View>
  )
}

const styles = StyleSheet.create({
  progressBar: {
    width: '100%',
    flexDirection: 'row',
    height: 3,
  },
  filledProgress: {
    height: '100%',
    backgroundColor: colors.accentText,
  },
  emptyProgress: {
    height: '100%',
    backgroundColor: '#CCD6DD',
  },
})
