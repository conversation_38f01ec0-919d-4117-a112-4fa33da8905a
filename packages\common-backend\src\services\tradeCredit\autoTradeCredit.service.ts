import {
  ICompany,
  ICustomerAccount,
  IInvoice,
  ILoanApplication,
  ISettings,
} from '../../models/types'
import { dictionaries, EInvoiceType } from '@linqpal/models'
import {
  Company,
  CustomerAccount,
  Invoice,
  LoanApplication,
  Settings,
} from '../../models'
import { findBuilderByAccountId } from '../invoices.service/findBuilderAccount'
import AwsService from '../aws.service'
import { Logger } from '../logger/logger.service'
import { invoicesService } from '../invoices.service'
import {
  DefaultSettings,
  invoiceStatus,
  LOAN_APPLICATION_STATUS,
  SettingKeys,
} from '@linqpal/models/src/dictionaries'
import {
  TradeCreditStatus,
  TradeCreditStatusType,
} from '@linqpal/models/src/dictionaries/TradeCreditStatus'
import TradeCreditService from './tradeCredit.service'
import { LOAN_APPLICATION_TYPE } from '@linqpal/models/src/dictionaries/loanApplicationTypes'
import moment from 'moment-timezone'
import { attachInvoiceIntegrationConnectorToCompanyConnectorReferences } from '../payment'

enum AutoTradeCreditState {
  Waiting = 'waiting',
  Permitted = 'permitted',
  Canceled = 'canceled',
}

export default class AutoTradeCreditService {
  private static logger = new Logger({
    module: 'bluetape.services',
    subModule: 'AutoTradeCreditService',
  })

  //  private static notifications = AutoTradeCreditNotifications

  public static async tryStartAutoTradeCredit(
    invoice: IInvoice,
    supplier: ICompany,
  ): Promise<boolean> {
    this.logger.info(`validating ATC eligibility for invoice ${invoice.id}`)

    const { customer_account_id } = invoice

    const customerCompany = await findBuilderByAccountId(
      invoice.customer_account_id,
    )

    if (!customerCompany) {
      this.logger.warn(
        `unable to find company for customer account ${invoice.customer_account_id}`,
      )
      return false
    }

    const [customerAccount, loanApplications, minInvoiceAmountSetting] =
      await Promise.all([
        CustomerAccount.findById(customer_account_id),
        LoanApplication.find({ company_id: customerCompany.id }),
        Settings.findOne({ key: SettingKeys.MinApprovalAmount }),
      ])

    const startAutoTradeCredit = this.canUseAutoTradeCredit(
      invoice,
      supplier,
      customerAccount,
      customerCompany,
      loanApplications,
      minInvoiceAmountSetting,
    )

    if (startAutoTradeCredit) {
      await AwsService.sendSQSMessage(
        'auto-trade-credit',
        JSON.stringify({ invoiceId: invoice.id }),
        'LP_AUTO_TRADE_CREDIT',
        invoice.id,
      )

      return true
    }

    return false
  }

  public static async issueApplication(invoiceId: string) {
    const [invoice, loanAppsCount] = await Promise.all([
      Invoice.findById(invoiceId),
      LoanApplication.find({
        'invoiceDetails.invoiceId': { $in: [invoiceId] },
        status: {
          $nin: [
            dictionaries.LOAN_APPLICATION_STATUS.CANCELED,
            dictionaries.LOAN_APPLICATION_STATUS.CLOSED,
          ],
        },
      }).countDocuments(),
    ])

    if (!invoice) {
      throw new Error(`Auto Trade Credit: invoice ${invoiceId} is not found`)
    }

    if (loanAppsCount > 0) {
      throw new Error(
        `cannot start Auto Trade Credit for invoice ${invoiceId} with ${loanAppsCount} active loan application(s)`,
      )
    }

    const [
      supplier,
      customerAccount,
      customerCompany,
      minInvoiceAmountSetting,
    ] = await Promise.all([
      Company.findById(invoice.company_id),
      CustomerAccount.findById(invoice.customer_account_id),
      invoicesService.findBuilderByAccountId(invoice.customer_account_id),
      Settings.findOne({ key: SettingKeys.MinApprovalAmount }),
    ])

    // prettier-ignore
    {
      if (!supplier) throw new Error(`supplier ${invoice.company_id} is missing`)
      if (!customerAccount) throw new Error(`customer account ${invoice.customer_account_id} is missing`)
      if (!customerCompany) throw new Error(`customer company for account ${invoice.customer_account_id} is missing`)
    }

    const loanApplications = await LoanApplication.find({
      company_id: customerCompany.id,
    })

    const canUseAutoTradeCredit = this.canUseAutoTradeCredit(
      invoice,
      supplier,
      customerAccount,
      customerCompany,
      loanApplications,
      minInvoiceAmountSetting,
    )

    if (canUseAutoTradeCredit) {
      const paymentPlan = customerAccount.settings?.autoTradeCreditPaymentPlan
      if (!paymentPlan)
        throw new Error(
          `Payment plan is missing for customer account ${invoice.customer_account_id}`,
        )

      const loanType =
        invoice.type === EInvoiceType.QUOTE
          ? LOAN_APPLICATION_TYPE.QUOTE
          : undefined

      const loanApp = await LoanApplication.create({
        company_id: customerCompany.id,
        invoiceDetails: {
          invoiceId: invoice.id,
          paymentPlan,
        },
        fundingSource: 'arcadia',
        status: LOAN_APPLICATION_STATUS.NEW,
        type: loanType,
        outputs: [],
        notes: [],
        metadata: {
          repayment: {
            autoTradeCreditEnabled: true,
            // TODO: VK: legacy fields, review and remove
            autoTradeCreditType: 'selected-customers',
            loanPaymentCollection: 'borrower',
          },
        },
      })

      await attachInvoiceIntegrationConnectorToCompanyConnectorReferences(
        customerCompany.id,
        [invoice.id],
      )

      this.logger.info(
        `starting DE for application ${loanApp.id}, invoice ${invoice.id}`,
      )

      await invoicesService.startDecisionEngine(loanApp.id, null)
    }
  }

  public static async getMerchantTransferState(app: ILoanApplication) {
    if (
      app.status === LOAN_APPLICATION_STATUS.CANCELED ||
      app.status === LOAN_APPLICATION_STATUS.REJECTED ||
      app.status === LOAN_APPLICATION_STATUS.ERROR
    ) {
      this.logger.info(
        `Application ${app.id} is ${app.status}, disbursement is canceled`,
      )
      return {
        merchantTransferState: AutoTradeCreditState.Canceled,
      }
    }

    const [checkDelaySetting, disburseDelaySetting] = await Promise.all([
      Settings.findOne({ key: SettingKeys.AtcRecheckDelay }),
      Settings.findOne({
        key: SettingKeys.AtcDisbursementDelay,
      }),
    ])

    const checkDelay =
      checkDelaySetting?.value ?? DefaultSettings.AutoTradeCreditRecheckDelay
    const disburseDelay =
      disburseDelaySetting?.value ??
      DefaultSettings.AutoTradeCreditDisbursementDelay

    if (app.metadata?.repayment?.disbursementPause?.enabled) {
      this.logger.info(
        `Application ${app.id} has ongoing dispute, disbursement is paused`,
      )
      return {
        merchantTransferState: AutoTradeCreditState.Waiting,
        merchantTransferDelay: checkDelay,
      }
    }

    this.logger.info(`No ongoing dispute for application ${app.id}`)

    // TODO: VK: disburse delay configuration was initially in seconds for testing purposes, to be reviewed
    const disburseDate = moment(app.issueDate)
      .clone()
      .businessAdd(disburseDelay, 'seconds')

    if (process.env.LP_MODE === 'prod') {
      disburseDate.startOf('day')
    }

    this.logger.info(
      `Expected disburse date for Auto Trade Credit is ${disburseDate}`,
    )

    if (moment().isBefore(disburseDate)) {
      this.logger.info(`Waiting before disbursement for application ${app.id}`)
      return {
        merchantTransferState: AutoTradeCreditState.Waiting,
        merchantTransferDelay: checkDelay,
      }
    }

    this.logger.info(`Auto Trade Credit is permitted for application ${app.id}`)
    return {
      merchantTransferState: AutoTradeCreditState.Permitted,
    }
  }

  private static canUseAutoTradeCredit(
    invoice: IInvoice,
    supplier: ICompany | null,
    customerAccount: ICustomerAccount | null,
    customerCompany: ICompany | null,
    loanApplications: ILoanApplication[],
    minInvoiceAmountSetting: ISettings | null,
  ) {
    if (!supplier || !customerAccount || !customerCompany) return false

    if (invoice.status !== invoiceStatus.placed) {
      this.logger.info(
        `Auto Trade Credit is disabled for invoice ${invoice.id} in status ${invoice.status}`,
      )
      return false
    }

    if (![EInvoiceType.INVOICE, EInvoiceType.QUOTE].includes(invoice.type)) {
      this.logger.info(
        `Auto Trade Credit is disabled for invoice ${invoice.id} with type ${invoice.type}`,
      )
      return false
    }

    if (invoice.quoteId) {
      // ATC is started for quote already
      this.logger.info(
        `Auto Trade Credit is disabled for invoice ${invoice.id} having quote ${invoice.quoteId}`,
      )
      return false
    }

    if (!supplier.settings?.repayment?.autoApprove) {
      this.logger.info(
        `Auto Trade Credit is disabled for supplier ${supplier.id}, invoice ${invoice.id}`,
      )
      return false
    }

    if (!customerAccount.settings?.autoTradeCreditEnabled) {
      this.logger.info(
        `Auto Trade Credit is disabled for customer account ${customerAccount.id}, invoice ${invoice.id}`,
      )
      return false
    }

    if (!customerCompany.credit?.limit) {
      this.logger.info(
        `Auto Trade Credit is not allowed for company ${customerCompany.id} w/o credit limit, invoice ${invoice.id}`,
      )
      return false
    }

    const accountStatus: TradeCreditStatusType =
      TradeCreditService.calculateStatus(loanApplications, null, 0)
        .status as TradeCreditStatusType

    if (
      accountStatus === TradeCreditStatus.Denied ||
      accountStatus === TradeCreditStatus.AccountOnHold ||
      accountStatus === TradeCreditStatus.AccountInReview
    ) {
      this.logger.info(
        `Auto Trade Credit is disabled for status ${accountStatus} of account ${customerAccount.id}, invoice ${invoice.id}`,
      )
      return false
    }

    if (invoice.total_amount < (minInvoiceAmountSetting?.value ?? 0)) {
      this.logger.info(
        `Auto Trade Credit is disabled for invoice ${invoice.id},
         invoice amount is less than ${SettingKeys.MinApprovalAmount}: ${minInvoiceAmountSetting?.value}`,
      )
      return false
    }

    if (
      invoice.total_amount <=
      (customerAccount.settings?.autoTradeCreditMaxInvoiceAmount ?? 0)
    ) {
      this.logger.info(`Auto Trade Credit is enabled for invoice ${invoice.id}`)
      return true
    } else {
      this.logger.info(
        `invoice ${invoice._id.toString()} amount ${invoice.total_amount}
        is greater than allowed for customer account ${customerAccount.id}`,
      )
      return false
    }
  }
}
