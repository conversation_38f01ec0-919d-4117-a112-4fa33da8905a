import { useTranslation } from 'react-i18next'
import { useUnifiedApplication } from '../../../../UnifiedApplicationContext'
import { View } from 'react-native'
import { BtLink, BtText } from '@linqpal/components/src/ui'
import React from 'react'
import { commonStyles } from './commonStyles'
import { IconGreenCheck } from '../../../../../../../assets/icons'
import { getGroupTitle } from '../../../../../../GeneralApplication/Application/groupTitles'

export const FilledGroupLine = ({ group }) => {
  const { t } = useTranslation('application')
  const store = useUnifiedApplication()

  const handleEdit = () => {
    if (!store.isSubmitting) {
      store.editGroup(group, false)
    }
  }

  return (
    <View style={commonStyles.groupLineWrapper}>
      <IconGreenCheck style={{ width: 16, height: 12 }} />

      <BtText style={commonStyles.groupLineText}>
        {t(getGroupTitle(group) as any)}
      </BtText>

      <BtLink
        title={t('Review.Edit')}
        textStyle={commonStyles.editGroupLink}
        onPress={handleEdit}
        disabled={store.isSubmitting}
        testID={`UnifiedApplication.Review.Edit.Filled.${group}`}
      />
    </View>
  )
}
