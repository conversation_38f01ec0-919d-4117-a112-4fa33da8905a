import { mockClient } from 'aws-sdk-client-mock'
import { GetObjectCommand, S3Client } from '@aws-sdk/client-s3'
import chai from 'chai'
import chaiHttp from 'chai-http'
import md5 from 'crypto-js/md5'
import mongoose from 'mongoose'
import sinon from 'sinon'

import {
  BankAccount,
  Company,
  crypt,
  initCbwApiRequester,
  Invoice,
  Operation,
  Transaction,
} from '@linqpal/common-backend'
import { dictionaries } from '@linqpal/models'
import {
  beforeEachMockEncryption,
  beforeEachMockSecretsManager,
} from './helper'
import { achPayment } from '../tabapay/achPayment'
import lambda from 'lambda-tester'
import { tabapayACHPaymentHandler } from '../tabapay/handler'
import { S3EventRecord } from 'aws-lambda'
import nock from 'nock'

chai.use(chaiHttp)
chai.should()

describe('Tabapay integration', () => {
  describe('Card Payment - Push to supplier bank account', async () => {
    let mockedS3: ReturnType<typeof mockClient>
    let aiMock: sinon.SinonStub
    beforeEachMockEncryption()
    beforeEachMockSecretsManager()
    beforeEach(() => {
      process.env.LP_TABAPAY_TRANSACTIONS_FILE_SUFFIX = 'transactions_v2-5.csv'
      process.env.LP_TABAPAY_ACH_IDENTIFICATION = '************'
      mockedS3 = mockClient(S3Client)
      mockedS3.on(GetObjectCommand).resolves({})
      const cbwApi = initCbwApiRequester()
      aiMock = sinon.stub(cbwApi, 'post').callsFake((path, { payload }) => {
        return Promise.resolve({
          transactionNumber: '123',
          transactionAmountCents: parseInt(payload.transactionAmount.amount),
          api: {
            reference: '123',
            dateTime: '',
            originalReference: payload.reference,
          },
          statusCode: '000',
          statusDescription: 'SUCCESS',
        })
      })
    })

    afterEach(() => {
      process.env.LP_TABAPAY_TRANSACTIONS_FILE_SUFFIX = undefined
      process.env.LP_TABAPAY_ACH_IDENTIFICATION = undefined
      aiMock.restore()
      mockedS3.reset()
    })

    const s3EventRecordExceptions: S3EventRecord = {
      s3: {
        bucket: {
          name: 'tabapay-reports',
        },
        object: {
          key: '2322_2012942322_exceptions_v2-5.csv',
        },
      },
    } as any
    const s3EventRecordTransactions: S3EventRecord = {
      s3: {
        bucket: {
          name: 'tabapay-reports',
        },
        object: {
          key: '2322_2012942322_transactions_v2-5.csv',
        },
      },
    } as any
    it.skip('should skip processing non-transaction file', async () => {
      const response: any = await achPayment(s3EventRecordExceptions)
      response.should.have.property('result')
      response.result.should.be.equal('ok')
    })

    it.skip('should throw error when retrieving transaction report from S3', async () => {
      mockedS3.on(GetObjectCommand).rejects(new Error('NoSuchKey'))
      const response = await lambda(tabapayACHPaymentHandler)
        .event({ Records: [s3EventRecordTransactions] })
        .expectResult()
      chai.expect(response.toString()).contains('Error')
    })
    it.skip('should skip transactions of type "Disbursement" and status "Processing"', async () => {
      mockedS3.on(GetObjectCommand).callsFake(function () {
        return {
          Body: Buffer.from(
            'Transaction ID,Type,Status,Transaction Amount,Settled  Amount' +
              '\n' +
              'dlj807sdjg9724,Disbursement,Complete,10,10' +
              '\n' +
              'iospw92830dkfj1,Purchase,Pending,10,10',
          ),
        }
      })

      const response: any = await achPayment(s3EventRecordTransactions)
      response.should.have.property('result')
      response.result.should.be.equal('ok')
    })

    it.skip('should skip transaction processing when transaction is not found in DB', async () => {
      mockedS3.on(GetObjectCommand).callsFake(function () {
        return {
          Body: Buffer.from(
            'Transaction ID,Type,Status,Transaction Amount,Settled  Amount' +
              '\n' +
              'dlj807sdjg9724,Purchase,Complete,10,10',
          ),
        }
      })

      const response: any = await achPayment(s3EventRecordTransactions)
      response.should.have.property('result')
      response.result.should.be.equal('ok')
    })

    it.skip('should skip transaction processing when ach out is already created', async () => {
      console.log('Started')
      const supplierCompany = await Company.create({
        name: 'Test supplier',
        type: 'supplier',
      })
      const invoice = await Invoice.create({
        company_id: supplierCompany._id.toString(),
        total_amount: 10,
      })
      const operation = await Operation.create({
        owner_id: invoice._id.toString(),
        type: dictionaries.OPERATION_TYPES.INVOICE.PAYMENT,
        status: dictionaries.OPERATION_STATUS.PROCESSING,
      })
      const transaction = await Transaction.create({
        operation_id: operation._id.toString(),
        type: dictionaries.TRANSACTION_TYPES.ACH.TRANSFER,
        payment_method: dictionaries.PAYMENT_METHODS.CARD,
        metadata: {
          transactionID: 'dlj807sdjg9725',
          isAchOutCreated: true,
        },
      })
      mockedS3.on(GetObjectCommand).callsFake(function () {
        return {
          Body: Buffer.from(
            'Transaction ID,Type,Status,Transaction Amount,Settled  Amount' +
              '\n' +
              transaction.metadata.transactionID! +
              ',Purchase,Complete,10,10',
          ),
        }
      })

      const response: any = await achPayment(s3EventRecordTransactions)
      response.should.have.property('result')
      response.result.should.be.equal('ok')
    })

    it.skip('should skip transaction processing when corresponding supplier company does not have a primary bank account', async () => {
      const supplierCompany = await Company.create({
        name: 'Test supplier',
        type: 'supplier',
      })
      const invoice = await Invoice.create({
        company_id: supplierCompany._id.toString(),
        total_amount: 10,
      })
      const operation = await Operation.create({
        owner_id: invoice._id.toString(),
        type: dictionaries.OPERATION_TYPES.INVOICE.PAYMENT,
        status: dictionaries.OPERATION_STATUS.PLACED,
      })
      const transaction = await Transaction.create({
        operation_id: operation._id.toString(),
        type: dictionaries.TRANSACTION_TYPES.ACH.TRANSFER,
        payment_method: dictionaries.PAYMENT_METHODS.CARD,
        metadata: {
          transactionID: 'dlj807sdjg9725',
        },
      })
      mockedS3.on(GetObjectCommand).callsFake(function () {
        return {
          Body: Buffer.from(
            'Transaction ID,Type,Status,Transaction Amount,Settled  Amount' +
              '\n' +
              transaction.metadata.transactionID +
              ',Purchase,Complete,10,10',
          ),
        }
      })

      const response: any = await achPayment(s3EventRecordTransactions)
      response.should.have.property('result')
      response.result.should.be.equal('ok')
    })

    it('should process transaction and initiate ach out payment to supplier successfully', async () => {
      nock('https://api.sendgrid.com').post('/v3/mail/send').reply(202, {
        message: 'Email sent successfully',
      })
      const bankAccount = await BankAccount.create({
        accountNumber: {
          cipher: await crypt.encrypt('**********'),
          hash: md5('**********').toString(),
          display: '******7890',
        },
        isPrimary: true,
        name: 'GreatBank',
        routingNumber: '********',
        accountType: 'CHECKING',
      })
      const supplierCompany = await Company.create({
        name: 'Test supplier',
        type: 'supplier',
        bankAccounts: [new mongoose.Types.ObjectId(bankAccount.id)],
        email: '<EMAIL>',
        phone: '+***********',
        address: {
          address: '100 Main St',
          city: 'San Francisco',
          zip: '94061',
          state: 'California',
        },
        ein: {
          cipher: await crypt.encrypt('**********'),
          hash: md5('**********').toString(),
          display: '******7890',
        },
      })
      const invoice = await Invoice.create({
        company_id: supplierCompany._id.toString(),
        total_amount: 10,
      })
      const operation = await Operation.create({
        owner_id: invoice._id.toString(),
        type: dictionaries.OPERATION_TYPES.INVOICE.PAYMENT,
        status: dictionaries.OPERATION_STATUS.PROCESSING,
        amount: invoice.total_amount,
        metadata: {
          payee_id: supplierCompany._id.toString(),
        },
      })
      const transaction = await Transaction.create({
        operation_id: operation._id.toString(),
        amount: operation.amount,
        status: dictionaries.TRANSACTION_STATUS.PROCESSING,
        type: dictionaries.TRANSACTION_TYPES.ACH.TRANSFER,
        payment_method: dictionaries.PAYMENT_METHODS.CARD,
        metadata: {
          transactionID: 'dlj807sdjg9726',
        },
      })
      mockedS3.on(GetObjectCommand).callsFake(function () {
        return {
          Body: Buffer.from(
            'Transaction ID,Type,Status,Transaction Amount,Settled  Amount,Convenience Fee' +
              '\n' +
              transaction.metadata.transactionID +
              ',Purchase,Complete,10,12.5,2.5',
          ),
        }
      })

      const response: any = await achPayment(s3EventRecordTransactions)
      response.should.have.property('result')
      response.result.should.be.equal('ok')

      const updatedOperation = await Operation.findById(
        operation._id.toString(),
      )
      updatedOperation!.metadata.should.have.property('pullResult')
    })
  })
})
