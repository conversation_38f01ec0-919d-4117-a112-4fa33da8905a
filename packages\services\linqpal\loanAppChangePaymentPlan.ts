import {
  AgreementService,
  cbw,
  Company,
  connectToDatabase,
  deactivateVirtualCard,
  Draft,
  getEnvironmentVariables,
  getPlan,
  getVirtualCard,
  Invoice,
  LMS,
  LoanApplication,
  loanDisburse,
  Logger,
  Operation,
  VirtualCard,
} from '@linqpal/common-backend'
import {
  IInvoice,
  IInvoiceDetails,
  ILoanApplication,
  ILoanPaymentPlan,
} from '@linqpal/common-backend/src/models/types'
import { compatibilityService } from '@linqpal/common-backend/src/services/compatibility/compatibility.service'
import { dictionaries, exceptions } from '@linqpal/models'
import { CbwTransactionStatus } from '@linqpal/models/src/dictionaries'
import moment from 'moment-timezone'
import type { ClientSession } from 'mongoose'
import mongoose from 'mongoose'
import { FundingSources } from '@linqpal/models/src/dictionaries/fundingSources'

interface InputEvent {
  app_id: string
  data: {
    supplierName: string
    routingNumber: string
    accountNumber: string
    paymentPlan: ILoanPaymentPlan
  }
}

interface OutputEvent {
  app_id: string
  data:
    | {
        status: 'pending'
        invoices_id: string[]
        prev_metadata: ILoanApplication['metadata']
        prevInvoiceDetails: IInvoiceDetails
      }
    | {
        status: 'success'
        invoices_id: string[]
      }
    | { status: 'rejected' }
}

const log = new Logger({
  module: 'Linqpal',
  subModule: 'loanAppChangePaymentPlan',
})

function wrapper<T>(
  fn: (event: T, session: ClientSession) => Promise<OutputEvent>,
) {
  return async (event: T) => {
    await getEnvironmentVariables()
    await connectToDatabase()
    const session = await mongoose.startSession()
    session.startTransaction()
    try {
      const result = await fn(event, session)
      if (session.inTransaction()) {
        await session.commitTransaction()
      }
      return result
    } catch (e) {
      if (session.inTransaction()) {
        await session.abortTransaction()
      }
      throw e
    } finally {
      await session.endSession()
    }
  }
}

// noinspection JSUnusedGlobalSymbols
export const loanAppChangePaymentPlan = wrapper<InputEvent>(
  async function loanAppChangePaymentPlan({ app_id, data }, session) {
    const item: ILoanApplication | null = await LoanApplication.findById(
      app_id,
    ).session(session)
    if (!item) return { app_id, data: { status: 'rejected' } }

    if (item.invoiceDetails.cardId) {
      try {
        const card = await getVirtualCard(item.invoiceDetails.cardId)
        const volumeLimit = card.limits.find((i) => i.type === 'VOLUME')
        if (volumeLimit && volumeLimit.remaining !== volumeLimit.value) {
          return { app_id, data: { status: 'rejected' } }
        }
        await deactivateVirtualCard(item.invoiceDetails.cardId)
      } catch (e) {
        console.log(e)
      }

      await VirtualCard.deleteOne({
        cardId: item.invoiceDetails.cardId,
      }).session(session)
    }
    if (!item.metadata) {
      item.metadata = {}
    }
    const itemObj = item.toObject()
    const prev_metadata = { ...itemObj.metadata }
    item.metadata.supplierName = data.supplierName
    item.metadata.routingNumber = data.routingNumber
    item.metadata.accountNumber = data.accountNumber
    item.metadata.paymentPlan = data.paymentPlan
    const prevInvoiceDetails = { ...itemObj.invoiceDetails }
    item.invoiceDetails.paymentPlan = data.paymentPlan._id.toString()
    item.invoiceDetails.cardId = undefined
    await item.save()

    const invoices_id = Array.isArray(item.invoiceDetails.invoiceId)
      ? item.invoiceDetails.invoiceId
      : item.invoiceDetails.invoiceId
      ? [item.invoiceDetails.invoiceId]
      : []
    let amount = 0
    await Promise.all(
      invoices_id.map(async (invoice_id) => {
        const invoice: IInvoice | null = await Invoice.findById(
          invoice_id,
        ).session(session)
        if (!invoice) return
        invoice.status = dictionaries.invoiceSchemaStatus.paid
        amount += invoice.total_amount
        await invoice.save()
      }),
    )
    item.usedAmount = item.approvedAmount = amount
    await item.save()

    return {
      app_id,
      data: {
        status: 'pending',
        invoices_id,
        prev_metadata,
        prevInvoiceDetails,
      },
    }
  },
)

// noinspection JSUnusedGlobalSymbols
export const loanAppLoanDisburse = wrapper<OutputEvent>(
  async function loanAppLoanDisburse({ app_id, data }, session) {
    if (data.status !== 'pending') return { app_id, data }

    const item: ILoanApplication | null = await LoanApplication.findById(
      app_id,
    ).session(session)
    if (!item) return { app_id, data: { status: 'rejected' } }

    const resp = await loanDisburse(item, item.approvedAmount, session)

    if (resp.transactionStatus === CbwTransactionStatus.Pending) {
      return { app_id, data: { ...data, status: 'pending' } }
    }

    if (resp.transactionStatus === CbwTransactionStatus.Rejected) {
      item.metadata = data.prev_metadata
      item.invoiceDetails = data.prevInvoiceDetails
      await item.save()
    }
    return {
      app_id,
      data: {
        ...data,
        status:
          resp.transactionStatus === CbwTransactionStatus.Rejected
            ? 'rejected'
            : 'success',
      },
    }
  },
)

async function createLoan(app: ILoanApplication) {
  const payerCompany = await Company.findById(app.company_id)
  if (!payerCompany) throw new exceptions.LogicalError('Company not found')
  const { invoiceDetails, metadata } = app
  const loanPlan =
    metadata?.paymentPlan || (await getPlan(invoiceDetails.paymentPlan))
  const loanTemplate = loanPlan.lmsTemplateId
  console.log({ payerCompany, app, loanTemplate })
  if (!loanPlan) {
    throw new exceptions.LogicalError('Loan plan is not selected')
  }
  if (!app.lms_id) {
    const draft = await Draft.findOne({
      company_id: payerCompany.id,
      type: 'general_application',
    })
    let einHash = ''

    draft?.data?.forEach((group) => {
      if (group.group !== 'businessInfo') {
        return
      }
      group.items.forEach((item) => {
        if (item.identifier !== 'ein') {
          return
        }
        einHash = item.content?.hash || ''
      })
    })

    if (einHash === '') {
      throw new exceptions.LogicalError('Ein was not found')
    }

    const drawApprovalDetails =
      await compatibilityService.getCreateLoanDrawApprovalDetails(app)

    log.info(
      { app: app.toJSON() },
      `Creating loan for loan application with id ${app.id}`,
    )

    const { id } = await LMS.createLoan(
      app.company_id,
      drawApprovalDetails.companyName,
      loanTemplate,
      app.usedAmount || 0,
      einHash,
      LMS.getLoanOrigin(app),
      drawApprovalDetails.debtInvestor ?? FundingSources.Arcadia,
      drawApprovalDetails.projectId,
      drawApprovalDetails.merchantId,
      drawApprovalDetails.merchantName,
      drawApprovalDetails.drawApprovalId,
      drawApprovalDetails.loanPayables,
    )
    app.lms_id = id
    app.issueDate = moment().toDate()
    await app.save()
  }
  await LMS.activateLoan(app.lms_id)
  await LMS.syncLoanApplicationFields(app)
  await AgreementService.createAgreementForLoanApplication(app.id, false, null)
}

async function createAchOut(
  item: ILoanApplication,
  invoice_id: string,
  session: mongoose.ClientSession,
) {
  const op = await Operation.findOne({
    owner_id: invoice_id,
  }).session(session)

  if (
    op &&
    [
      dictionaries.OPERATION_STATUS.PROCESSING,
      dictionaries.OPERATION_STATUS.SUCCESS,
    ].includes(op.status)
  ) {
    return
  }
  const [operation] = await Operation.create(
    [
      {
        owner_id: invoice_id,
        amount: item.usedAmount,
        date: moment().toDate(),
        status: dictionaries.OPERATION_STATUS.PROCESSING,
        type: dictionaries.OPERATION_TYPES.INVOICE.PAYMENT,
        metadata: {
          payer_id: item.company_id,
          payment_method: dictionaries.PAYMENT_METHODS.LOAN,
        },
      },
    ],
    { session },
  )

  await cbw.achOut(
    [operation],
    {
      [invoice_id]: { amount: operation.amount, discount: 0 },
      fee: 0.0,
      currency: 'USD',
      transactionDateTime: moment().format('YYYY-MM-DD HH:mm:ss'),
      reason: 'ACH OUT',
      customer: {
        firstName: item.metadata?.supplierName || 'UNKNOWN',
        userType: 'INDIVIDUAL',
      },
      customerAccount: {
        identification: item.metadata?.accountNumber || '',
        institution: {
          identification: item.metadata?.routingNumber || '',
        },
      },
      customerContact: { primaryEmail: null, primaryPhone: null },
      customerPostalAddress: {},
    },
    session,
    { identification: process.env.LP_CBW_FBO_IDENTIFICATION },
  )
}

// noinspection JSUnusedGlobalSymbols
export const loanAppLoanDisburseDone = wrapper<OutputEvent>(async function (
  { app_id, data },
  session,
) {
  const item: ILoanApplication | null = await LoanApplication.findById(
    app_id,
  ).session(session)
  if (!item || data.status === 'rejected')
    return { app_id, data: { status: 'rejected' } }
  await createAchOut(item, data.invoices_id[0], session)
  await createLoan(item)
  return { app_id, data: { status: 'success', invoices_id: data.invoices_id } }
})
