import {
  BankAccount,
  Company,
  Draft,
  LoanApplication,
  VirtualCard,
} from '@linqpal/common-backend'
import { ICompany } from '@linqpal/common-backend/src/models/types'
import { isLoanRejectedCompany } from '@linqpal/common-backend/src/services/company.service'
import {
  CompanyStatus,
  exceptions,
  IDraftModel,
  PrequalData,
} from '@linqpal/models'
import { DraftSections } from '@linqpal/models/src/dictionaries/UnifiedApplication'
import { GeneralApplicationDraftMapperFactory } from '@linqpal/models/src/helpers/draft/GeneralApplicationDraftMapperFactory'
import moment from 'moment'
import { ControllerItem } from 'src/routes/controllerItem'
import controllers from '../../../controllers'
import { mapUnifiedCoOwners } from '@linqpal/models/src/helpers/draft/mappers/CoOwnersMapper'
import { v4 } from 'uuid'
import { ApplicationType } from '@linqpal/models/src/dictionaries/applicationType'
import { LOAN_APPLICATION_STATUS } from '@linqpal/models/src/dictionaries'
import mongoose from 'mongoose'
import { GeneralApplicationDraftModel } from '@linqpal/models/src/helpers/draft/models/GeneralApplicationDraftModel'

const ensureCoOwnerIds = (document: IDraftModel) => {
  // generate coOwner ids for legacy drafts
  const coOwnersGroup = document.data?.get('coOwnerInfo')
  const coOwners = coOwnersGroup?.items?.filter((item) =>
    /^coOwner\d$/.test(item.identifier),
  )

  coOwners?.forEach((coOwner) => {
    if (!coOwner.content?.id) {
      coOwner.content.id = v4()
    }
  })
}

export const draft = {
  get: async (req, res, next) => {
    const { sub } = req.user!
    const { documentType: type, current } = req.query
    const company_id = req.company!._id.toString()
    const company: ICompany = req.company!

    const drafts = await Draft.where({ company_id })
      .where({
        $and: [
          {
            $or: [
              { type: 'general_application' },
              { type: 'supplier_application' },
              { type: 'loan_application' },
            ],
          },
        ],
      })
      .sort({ createdAt: -1 })
      .limit(3) //We can not have more of these types, worst case 2 old applications and 1 that got mapped already

    let doc: any
    if (drafts.length === 0) {
      //New user just started journey
      doc = new Draft({
        sub,
        type: 'general_application',
        company_id: req.company!._id,
        current,
      })
    }

    const generalApplication =
      doc || drafts.find((d) => d.type === 'general_application')
    //If we already have a general_application we do not care about other cases
    if (generalApplication) {
      doc = new Draft(mapUnifiedCoOwners(generalApplication.toJSON()))
    } else {
      let draftToUse

      if (drafts.length > 1) {
        draftToUse =
          company?.status === CompanyStatus.Approved
            ? drafts.find((d) => d.type === 'supplier_application')
            : drafts.find((d) => d.type === 'loan_application')
      }

      if (!draftToUse) {
        draftToUse = drafts.at(0)!
      }

      const mapper = GeneralApplicationDraftMapperFactory.create(
        draftToUse.type,
      )
      const mappedDraft = mapper.map(
        draftToUse.toObject() as unknown as GeneralApplicationDraftModel,
      )

      doc = new Draft(mappedDraft)
    }

    const document = await controllers.user.decryptDocumentFields(doc.toJSON())

    let loanApplication = await isLoanRejectedCompany(company_id)
    if (!loanApplication) {
      loanApplication = (
        await LoanApplication.aggregate([
          { $match: { company_id: company_id } },
          { $sort: { createdAt: 1 } }, // need the earliest application
          { $limit: 1 },
        ])
      )[0]
    }

    const status = loanApplication?.status
    const decisionDate = loanApplication?.decisionDate
    const loanStatus = controllers.company.getCreditAppStatus(
      status,
      decisionDate,
    )

    if (type === ApplicationType.Credit) {
      let virtualCardUnused = false
      const cardId = loanApplication?.invoiceDetails?.cardId
      const validTill = moment(loanApplication?.updatedAt, 'MM/DD/YY')
        .add(loanApplication?.invoiceDetails?.cardId ? 3 : 7, 'days')
        .format('MM/DD/YY')
      if (cardId) {
        const card = await VirtualCard.findOne({ cardId })
        virtualCardUnused = !(card?.useDate && card?.usedAmount)
      }
      const cardInfo = {
        cardExpiryDate: validTill,
        unused: virtualCardUnused,
      }
      const comp = await Company.findOne({ _id: company_id }).populate(
        'bankAccounts',
      )
      const primaryFromAccounts: any = comp?.bankAccounts?.find(
        (acc) => acc.isPrimaryForCredit,
      )
      const primaryFromApplication = document.data?.bank?.items.find(
        (i: any) => i.identifier === 'details',
      )?.content
      if (primaryFromAccounts) {
        primaryFromAccounts.accountNumber =
          primaryFromAccounts?.accountNumber.display
      }
      if (
        document.data &&
        primaryFromAccounts &&
        primaryFromApplication &&
        primaryFromAccounts._id !== primaryFromApplication._id
      ) {
        document.data.bank.items.find(
          (i: any) => i.identifier === 'details',
        ).content = primaryFromAccounts
      }
      res.locals.result = {
        _id: doc._id,
        ...document,
        invoice_id: loanApplication?.invoiceDetails?.invoiceId,
        isSentBack: loanApplication?.isSentBack,
        virtualCardDetails: loanApplication?.invoiceDetails?.cardId && cardInfo,
        loanApplicationId: loanApplication?._id,
        loanStatus,
        prequalData:
          status === LOAN_APPLICATION_STATUS.APPROVED
            ? PrequalData.create({
                approvedAmount: company.credit.limit,
                validTill,
              })
            : null,
        type,
      }
    } else {
      res.locals.result = {
        _id: doc._id,
        ...document,
        loanStatus,
        type,
      }
    }
    next()
  },
  post: async (req, res, next) => {
    const type = req.body.type

    const document = {
      _id: req.body._id,
      sub: req.body.sub,
      type,
      current: req.body.current,
      data: req.body.data,
      filled: req.body.filled,
      firstQuestion: req.body.firstQuestion,
      company_id: req.company!._id.toString(),
      version: req.body.version,
    }

    const { company_id } = document
    const verifiedDoc = { ...document }

    if (type === ApplicationType.Credit && document?.data) {
      // Verify and add only the sections needed for loan application
      verifiedDoc.data = {}
      DraftSections.forEach((section) => {
        if (document?.data && document?.data[section]) {
          verifiedDoc.data[section] = { ...document?.data[section] }
        }
      })
    }
    let doc = await Draft.findById(document._id)

    if (!doc) {
      doc = await Draft.findOne({ type: 'general_application', company_id })
    }

    if (doc && doc.company_id !== company_id) {
      throw new exceptions.LogicalError(
        `You don't have access to edit this document`,
      )
    }

    if (
      doc?.version &&
      verifiedDoc?.version &&
      verifiedDoc.version - 1 !== doc.version
    ) {
      throw new exceptions.InvalidDataError('version-mismatch')
    }

    const bankId = doc?.data?.get('bank')?.items[0]?.content?._id
    let isPlaidBankConnected = false

    if (mongoose.isValidObjectId(bankId)) {
      const bank = await BankAccount.findById(bankId)

      isPlaidBankConnected =
        bank?.plaid?.access_token && bank.isPrimaryForCredit
    }

    const val = controllers.user.validations.general_application
    if (verifiedDoc.data && val) {
      await Promise.all(
        Object.keys(verifiedDoc.data).map(async (group) => {
          const items = verifiedDoc.data[group]?.items || []
          if (items.length > 0)
            await Promise.all(
              items.map(async (item: { identifier: string; content: any }) => {
                if (item.identifier === 'businessName' && !!item.content) {
                  const company = await Company.findById(company_id)
                  if (company) {
                    company.name = item.content.dba || item.content.legalName

                    if (item.content.legalName) {
                      company.legalName = item.content.legalName
                    }

                    await company.save()
                  }
                }
                await controllers.user.encryptValidate(
                  item.content,
                  item.identifier,
                  val,
                  doc?.data?.get(group)?.items || [],
                  false,
                  verifiedDoc?.data?.[group]?.items,
                )
              }),
            )
        }),
      )
    }
    // req.company.status holds supplier application status
    if (
      (req.company!.status &&
        ['applied', 'approved'].includes(req.company!.status) &&
        type === ApplicationType.Supplier) ||
      (['processing', 'approved'].includes(req.body.loanStatus) &&
        isPlaidBankConnected &&
        type === ApplicationType.Credit)
    ) {
      throw new exceptions.LogicalError(
        'You cannot edit your draft at this stage',
      )
    }

    if (!doc) {
      doc = await Draft.create({
        ...verifiedDoc,
        sub: req.user!.sub,
        type: 'general_application',
      })
    }

    doc.filled = verifiedDoc.filled
    doc.data = verifiedDoc.data
    doc.current = verifiedDoc.current
    doc.version = verifiedDoc.version

    ensureCoOwnerIds(doc)

    await doc.save()

    res.locals.result = { _id: doc._id }

    next()
  },
  delete: async (req, res) => {
    const { draftId, loanAppId } = req.query
    await Draft.findByIdAndDelete(draftId).session(req.session)
    await LoanApplication.findByIdAndDelete(loanAppId).session(req.session)
    res.send({ result: 'ok' })
  },
} as ControllerItem
