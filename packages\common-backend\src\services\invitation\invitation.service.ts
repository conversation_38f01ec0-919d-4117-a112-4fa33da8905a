import { Invitation } from '../../models'
import SgMail from '@sendgrid/mail'
import { emailService } from '../email.service'
import EmailNotifications from '../../helpers/EmailNotifications'
import { Logger } from '../logger/logger.service'
import { ICompany, ICustomerAccount, IUser } from '../../models/types'
import { invitationTypes } from '@linqpal/models/src/dictionaries'
import { CustomerAccountType } from '@linqpal/models/src/dictionaries/customerAccountType'
import { CompanyUtils } from '@linqpal/models/src/helpers/companyUtils'
import SmsNotifications from '../../helpers/SmsNotifications'
import { getBranding, getReferralCode, Sms } from '../../..'

interface IProcessSupplierInviteCustomerParams {
  user: IUser
  company: ICompany
  account: ICustomerAccount
  origin: string
  role?: string
}

export class InvitationService {
  private static logger = new Logger({
    module: 'InvitationService',
  })

  static async processSupplierInviteCustomer({
    user,
    company,
    account,
    origin,
    role = 'Owner',
  }: IProcessSupplierInviteCustomerParams) {
    this.logger.info(
      { user, company, account },
      'start processing supplier invite customer',
    )
    const res = {
      mailSent: false,
      smsSent: false,
      message: 'Invitation sent',
      invitationId: undefined,
    }
    const result = await getBranding({
      companyId: company!.id,
    })
    const HOST = result?.host || origin

    let referralData:
      | string
      | {
          referralCode: string
        }
      | undefined
    try {
      referralData = await getReferralCode({
        company,
        type: 'supplier',
      })
    } catch (e) {
      console.log('getReferralCode', (e as any).message)
    }

    this.logger.info({ referralData }, 'found referral data')

    const signupMethod: string = result?.branding?.settings?.defaultSignupMethod
    const login = signupMethod === 'email' ? account.email : account.phone

    await Invitation.deleteMany({ login: login || account.phone })

    const invitation = await Invitation.create({
      firstName: account.first_name || account.display_name,
      lastName: account.last_name || '',
      login,
      phone: account.phone,
      email: account.email,
      company_id: company!._id,
      type: invitationTypes.SUPPLIER_INVITE_CUSTOMER,
      metadata: {
        referralCode:
          typeof referralData !== 'string'
            ? referralData?.referralCode
            : undefined,
        connector: account.connector,
        businessName: account.name || account.display_name,
      },
      role: role,
    })

    this.logger.info({ invitation }, 'invitation was created')

    let fullName = (account.first_name || '').trim()
    fullName = fullName
      ? fullName + ' ' + (account.last_name || '').trim()
      : (account.last_name || '').trim()
    fullName = fullName || account.display_name || ''

    const invitationLink = `${HOST}/invitation?code=${invitation._id}`

    if (account.email) {
      let emailMessage: Partial<SgMail.MailDataRequired>

      if (account.type === CustomerAccountType.IHC) {
        const templateId =
          company!.settings?.email?.sendInHouseCreditInvitationTemplate || null

        emailMessage = EmailNotifications.newCustomerInvitationFactoring(
          templateId,
          {
            customerName: account.first_name || fullName,
            supplierName: CompanyUtils.getCompanyName(company),
            invitationLink,
            logoUrl: company.settings?.email?.logoUrl,
          },
        )
      } else {
        const templateId =
          company!.settings?.email?.sendInvitationTemplate || null

        emailMessage = EmailNotifications.newCustomerInvitation(templateId, {
          link: invitationLink,
          fullName,
          companyName: company!.name,
          supplierName: user!.firstName ?? '',
          invitedBy: (user!.firstName || '') + ' ' + (user!.lastName || ''),
          email: user!.email ?? '',
          phone: user!.phone ?? '',
          logoUrl: company.settings?.email?.logoUrl,
        })
      }

      this.logger.info({ emailMessage }, 'sending email to customer')

      try {
        const from = company!.settings?.email?.senderEmail // if it doesn't exist, default email will be used

        await emailService.send({ ...emailMessage, from, to: account.email })
        res.mailSent = true
      } catch (e) {
        res.message = (e as any).message
        this.logger.error({ err: e }, 'MAIL SEND ERROR')
      }
    }

    if (account.phone) {
      const smsMessage =
        account.type === CustomerAccountType.IHC
          ? SmsNotifications.newCustomerInvitationFactoring({
              customerName: account.first_name ?? '',
              supplierName: company!.name,
              invitationLink,
            })
          : SmsNotifications.newCustomerInvitation({
              supplierName: company!.name,
              supplierUserName: user!.firstName ?? '',
              customerName: fullName,
              invitationLink,
            })

      try {
        await Sms.send(account.phone, smsMessage)
        res.smsSent = true
      } catch (e) {
        res.message = (e as any).message
        this.logger.error({ err: e }, 'SMS ERROR')
      }
    }
    res.invitationId = invitation.id
    return res
  }
}
