import { CustomerAccount } from '@linqpal/common-backend'
import moment from 'moment'
import mongoose from 'mongoose'
import { ControllerItem } from 'src/routes/controllerItem'

export default {
  get: async (req, res) => {
    const company_id = req.company!._id.toString()
    const all = await CustomerAccount.countDocuments({ company_id })
    const lastMonth = await CustomerAccount.countDocuments({
      company_id,
      createdAt: mongoose.trusted({
        $gte: moment().subtract(30, 'days').startOf('day').toDate(),
      }),
    })
    res.send({ result: 'ok', all, lastMonth })
  },
} as ControllerItem
