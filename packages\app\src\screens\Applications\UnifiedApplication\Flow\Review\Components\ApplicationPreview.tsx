import React, { useEffect } from 'react'
import { StyleSheet, View } from 'react-native'
import { BtLink, BtPlainText } from '@linqpal/components/src/ui'
import { useTranslation } from 'react-i18next'
import { commonColors } from '@linqpal/common-frontend/src/theme'
import { observer } from 'mobx-react-lite'
import { useUnifiedApplication } from '../../../UnifiedApplicationContext'
import { ApplicationType } from '@linqpal/models/src/dictionaries/applicationType'
import { OwnerReview } from './Review/OwnerReview'
import { reaction, toJS } from 'mobx'
import debounce from 'lodash/debounce'
import ApplicationStore from '../../../../../GeneralApplication/Application/ApplicationStore'
import { Groups } from '@linqpal/models/src/applications/unified/UnifiedApplicationSteps'
import { getGroupTitle } from '../../../../../GeneralApplication/Application/groupTitles'
import { Spacer } from '../../../../../../ui/atoms'
import {
  LineOfCreditAgreement,
  VerifyEmailAlert,
} from '../../../../../GeneralApplication/Application/components'
import { getUnifiedApplicationEditor } from '../../getUnifiedApplicationEditor'

export const ApplicationPreview = observer(() => {
  const { t } = useTranslation('application')

  const store = useUnifiedApplication()

  const { showVerifyEmail, setShowVerifyEmail } = ApplicationStore

  // TOD: VK: Unified: move
  // Auto-save draft on any change, debounced
  useEffect(() => {
    const debouncedSave = debounce(() => {
      store.saveDraft().catch((error) => console.error(error))
    }, 500)

    const disposer = reaction(
      () => toJS(store.draft),
      () => debouncedSave(),
    )
    return () => {
      disposer()
      debouncedSave.cancel()
    }
  }, [store])

  const groups = store
    .getFlowGroups()
    .filter((group) => group !== Groups.review)

  return (
    <>
      {groups.map((group) => {
        const steps = store.getGroupSteps(group)
        const groupTitle = getGroupTitle(group)

        return (
          <View key={group}>
            <Spacer height={22} />

            {/* Group title */}
            <View style={styles.groupTitleWrapper}>
              <BtPlainText style={styles.sectionTitle} key={groupTitle}>
                {t(groupTitle as any)}
              </BtPlainText>
              <BtLink
                title={t('Review.Edit')}
                textStyle={styles.linkText}
                disabled={store.isSubmitting}
                onPress={() => store.editGroup(group)}
              />
            </View>

            {/* individual editors */}

            {group === Groups.businessOwner ? (
              <OwnerReview />
            ) : (
              steps.map((step) => {
                const editor = getUnifiedApplicationEditor(step)
                return (
                  <>
                    <Spacer height={24} />
                    <editor.component />
                  </>
                )
              })
            )}
          </View>
        )
      })}

      <VerifyEmailAlert close={showVerifyEmail} setClose={setShowVerifyEmail} />
      {store.type === ApplicationType.Credit && <LineOfCreditAgreement />}
    </>
  )
})

const styles = StyleSheet.create({
  groupTitleWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  sectionTitle: {
    fontWeight: '600',
    fontSize: 20,
    lineHeight: 26,
  },
  linkText: {
    color: commonColors.accentText,
    fontWeight: '600',
    fontSize: 16,
    lineHeight: 24,
  },
})
