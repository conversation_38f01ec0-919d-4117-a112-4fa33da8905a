import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { StyleSheet, View, Text, TouchableOpacity } from 'react-native'
import { IconInfoBlack } from '../../../../../../assets/icons'
import { WhatIsPenaltyModal } from '../../../LoanSchedule/WhatIsPenaltyModal'
import { composeStyle } from '@linqpal/common-frontend/src/helpers'
import { currencyMask } from '../../../../../../utils/helpers/masking'

interface PenaltyInterestProps {
  isMobile: boolean
  amount: number
}

export const PenaltyInterest: React.FC<PenaltyInterestProps> = ({
  isMobile,
  amount,
}) => {
  const { t } = useTranslation('tradeCredit')
  const [visible, setVisible] = useState<boolean>(false)

  const titleStyle = isMobile
    ? composeStyle(styles.text, styles.mobileText)
    : styles.text

  const subTitleStyle = isMobile
    ? composeStyle(styles.message, styles.mobileMessage)
    : styles.message

  const amountStyle = isMobile
    ? composeStyle(styles.amount, styles.mobileAmount)
    : styles.amount
  return (
    <View style={styles.layoutContainer}>
      <View style={styles.headerView}>
        <View style={styles.penaltyTitleContainer}>
          <Text style={titleStyle}>
            {t('tradeCredit.drawDetails.activitySummary.penaltyInterestTitle')}
          </Text>
          <TouchableOpacity
            style={isMobile ? styles.mobileInfo : styles.info}
            onPress={() => setVisible(true)}
          >
            <IconInfoBlack style={{ width: 18, height: 18 }} />
          </TouchableOpacity>
        </View>
        <View style={styles.amountContainer}>
          <Text style={amountStyle}>{currencyMask(amount)}</Text>
        </View>
      </View>
      <View style={styles.infoContainer}>
        <Text style={subTitleStyle}>
          {t('tradeCredit.drawDetails.activitySummary.penaltyInterestInfo')}
        </Text>
      </View>

      <WhatIsPenaltyModal visible={visible} setVisible={setVisible} />
    </View>
  )
}

const styles = StyleSheet.create({
  layoutContainer: {
    flexDirection: 'column',
    paddingHorizontal: 16,
    marginBottom: 12,
    minWidth: 343,
    backgroundColor: '#F8F9F9',
    border: 0,
    borderRadius: 8,
    paddingVertical: 16,
  },
  headerView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  penaltyTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  amountContainer: {
    alignItems: 'flex-end',
  },
  infoContainer: {
    flexDirection: 'row',
  },
  text: {
    fontWeight: '700',
    fontFamily: 'Inter',
    fontSize: 20,
    letterSpacing: -0.1,
    lineHeight: 20,
    color: '#172B4D',
  },
  mobileText: {
    fontSize: 14,
  },
  mobileMessage: {
    fontSize: 12,
    lineHeight: 18,
  },
  mobileAmount: {
    fontSize: 16,
    lineHeight: 20,
  },
  message: {
    fontWeight: '500',
    fontSize: 14,
    fontFamily: 'Inter',
    lineHeight: 20,
    color: '#172B4D',
  },
  amount: {
    fontWeight: '700',
    fontFamily: 'Inter',
    fontSize: 20,
    lineHeight: 24,
    color: '#FF7926',
  },
  info: {
    marginLeft: 11,
    marginTop: 1,
  },
  mobileInfo: {
    marginLeft: 8,
    marginBottom: 1,
  },
})
