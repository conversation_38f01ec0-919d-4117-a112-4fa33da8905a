import { FlowController } from '../FlowController'

// prettier-ignore
const StepDefinition = {
  businessInfo: [
    'email',
    'category',
    'businessName',
    'trade',
    'businessPhone',
    'businessAddress',
    'startDate',
    'type',
    'ein',
  ],
  finance: [
    'revenue',
    'debt',
    'creditLimit',
    'arAdvanceRequestedLimit'
  ],
  businessOwner: [
    'isOwner',
    'ownershipPercentage',
    'isAuthorized',
    'authorizedDetails',
    'address',
    'birthdate',
    'ssn',
  ],
  coOwners: [
    'coOwners'
  ],
  bank: [
    'primaryAccount'
  ],
  review: [
    'review'
  ],
} as const

export const Steps = FlowController.createStepConstants(StepDefinition)
export const Groups = FlowController.createGroupConstants(StepDefinition)
