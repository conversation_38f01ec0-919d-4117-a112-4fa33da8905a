import { CustomerAccount } from '@linqpal/common-backend'
import { parsePhoneNumber } from 'libphonenumber-js'
import mongoose from 'mongoose'
import { ControllerItem } from 'src/routes/controllerItem'
import { inviteCustomerAccount } from './inviteCustomerAccount'
import { Request } from 'express'
import { ICustomerAccount } from '@linqpal/common-backend/src/models/types'
import {
  getCustomersData,
  prepareDataAndUpsertCustomers,
} from '@linqpal/common-backend/src/services/customer/customer-status.service'

function filterPhoneNumbers(
  req: Request,
  items: { phone: string | undefined }[],
  invited = false,
): Partial<ICustomerAccount>[] {
  const phoneNumbers = new Set()
  return items
    .map((item) => {
      let { phone } = item
      if (phone) {
        try {
          const phoneNumber = parsePhoneNumber(phone, 'US')
          phone = phoneNumber.number.toString()
          if (!phoneNumbers.has(phone)) {
            phoneNumbers.add(phone)
            return {
              ...item,
              phone,
              company_id: req.company!._id.toString(),
              invited,
            } as Partial<ICustomerAccount>
          }
        } catch {}
      }

      return undefined
    })
    .filter(Boolean) as Partial<ICustomerAccount>[]
}

export default {
  post: async (req, res) => {
    const items = filterPhoneNumbers(req, req.body.items, req.body.invite)
    const phoneNumbers = items.map((item) => item.phone)
    if (phoneNumbers.length > 0) {
      const accounts = await CustomerAccount.find({
        company_id: req.company!._id,
        phone: mongoose.trusted({ $in: phoneNumbers }),
      })
      if (accounts?.length > 0) {
        const duplicatePhoneNumbers = accounts.map((a) => a.phone)
        res.status(400).send({
          message: `Customer accounts with phone number${
            duplicatePhoneNumbers.length > 1 ? 's' : ''
          } ${duplicatePhoneNumbers.join(', ')} already exists`,
          duplicatePhoneNumbers,
        })
        return
      }
    }
    if (items.length > 0) {
      await CustomerAccount.collection.insertMany(
        items.map((item) => ({
          ...item,
          _id: undefined,
        })),
      )
      const result = await CustomerAccount.aggregate([
        {
          $match: {
            company_id: req.company!._id,
            phone: mongoose.trusted({ $in: phoneNumbers }),
          },
        },
        {
          $group: {
            _id: null,
            ids: { $push: '$_id' },
          },
        },
        {
          $project: {
            _id: 0,
            ids: 1,
          },
        },
      ])

      const customerIds = result[0]?.ids
      if (customerIds?.length) {
        const customers = await getCustomersData(customerIds)
        await prepareDataAndUpsertCustomers(customers)
      }
    }
    if (req.body.invite) {
      const accounts = await CustomerAccount.find({
        company_id: req.company!._id,
        phone: mongoose.trusted({ $in: phoneNumbers }),
      })
      await Promise.all(
        accounts.map((a) =>
          inviteCustomerAccount({
            req,
            customerAccountId: a.id,
            phone: a.phone,
            name: a.name,
            role: 'Owner',
          }),
        ),
      )
    }
    res.send({ result: 'ok' })
  },
} as ControllerItem
