import React, { FC } from 'react'
import { PatternFormat } from 'react-number-format'
import { NumberFormatValues } from 'react-number-format/types/types'
import { BtEncryptedInput, BtEncryptedInputProps } from './BtEncryptedInput'

export interface BtSocialSecurityNumberInputProps
  extends BtEncryptedInputProps {
  showBadge?: boolean
  style?: any
}

export const BtSocialSecurityNumberInput: FC<BtSocialSecurityNumberInputProps> =
  ({
    size = 'large',
    label,
    value,
    validate,
    validationError,
    required,
    onChangeText,
    testID,
    showBadge,
    style,
  }) => {
    const handleValueChange = (formatValues: NumberFormatValues) => {
      if (onChangeText) {
        onChangeText(formatValues.value)
      }
    }

    const handleValidate = (text: string) => {
      if (validationError) {
        return value?.length !== 9 ? validationError : ''
      } else {
        return validate && validate(text)
      }
    }

    return (
      <PatternFormat
        format="###-##-####"
        placeholder="***********"
        size={size}
        label={label}
        value={value}
        required={required}
        customInput={BtEncryptedInput}
        keyboardType="number-pad"
        allowEmptyFormatting={false}
        validate={handleValidate}
        onValueChange={handleValueChange}
        valueIsNumericString
        showBadge={showBadge}
        style={style}
        testID={testID}
      />
    )
  }
