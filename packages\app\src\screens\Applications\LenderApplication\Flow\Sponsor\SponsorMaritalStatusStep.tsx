import React, { FC } from 'react'
import { BtRadioGroup } from '@linqpal/components/src/ui'
import { observer } from 'mobx-react'
import { useTranslation } from 'react-i18next'
import { useLenderApplication } from '../../LenderApplicationContext'
import { MaritalStatus } from '@linqpal/models/src/applications/lender/ILenderApplicationDraft'
import { runInAction } from 'mobx'
import { ILenderApplicationEditor } from '../getLenderApplicationEditor'
import { editorStyles } from '../editorStyles'

const SponsorMaritalStatusEditor: FC = () => {
  const { t } = useTranslation('application')
  const store = useLenderApplication()

  const handleChange = (option: MaritalStatus) => {
    runInAction(() => {
      store.draft.data.sponsor.maritalStatus = option
    })
  }

  return (
    <BtRadioGroup
      value={store.draft.data.sponsor.maritalStatus}
      options={[
        {
          label: t('LenderApplication.Flow.Sponsor.MaritalStatus.Single'),
          value: MaritalStatus.Single,
        },
        {
          label: t('LenderApplication.Flow.Sponsor.MaritalStatus.Married'),
          value: MaritalStatus.Married,
        },
      ]}
      onChange={handleChange}
      labelStyle={editorStyles.radioLabel}
      groupStyle={editorStyles.radioGroupVertical}
      testID="LenderApplication.Sponsor.MaritalStatus"
    />
  )
}

export const SponsorMaritalStatusStep: ILenderApplicationEditor = {
  options: {
    title: 'LenderApplication.Flow.Sponsor.MaritalStatus.Title',
    canSkip: false,
  },
  component: observer(SponsorMaritalStatusEditor),
}
