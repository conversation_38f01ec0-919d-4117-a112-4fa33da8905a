import {
  BankAccount,
  Company,
  CustomerAccount,
  Invoice,
  LoanApplication,
  Settings,
  User,
  UserRole,
} from '@linqpal/common-backend'
import { parsePhoneNumber } from 'libphonenumber-js'
import { escapeRegExp } from 'lodash'
import {
  exceptions,
  IHouseCreditInfoModel,
  SupplierAccount,
  SupplierAccountHouseCreditInfo,
} from '@linqpal/models'
import Middlewares from './middlewares'
import tran from '../../../services/transactional.service'
import moment from 'moment'
import { inviteCustomerAccount } from './inviteCustomerAccount'
import { saveBillingContacts } from './saveBillingContacts'
import { ControllerItem } from 'src/routes/controllerItem'
import { PipelineStage, Types } from 'mongoose'
import { ErpSystems } from '@linqpal/models/src/dictionaries/ErpSystems'
import TradeCreditService from '@linqpal/common-backend/src/services/tradeCredit/tradeCredit.service'
import {
  getCustomersData,
  prepareDataAndUpsertCustomers,
} from '@linqpal/common-backend/src/services/customer/customer-status.service'
import { invoiceStatus } from '@linqpal/models/src/dictionaries'
import { CustomerAccountType } from '@linqpal/models/src/dictionaries/customerAccountType'

const getHouseCreditInfo = (
  customerType: CustomerAccountType = CustomerAccountType.TradeCredit,
  hci: IHouseCreditInfoModel | null | undefined,
): SupplierAccountHouseCreditInfo | undefined => {
  if (!hci) {
    return undefined
  }

  if (customerType === CustomerAccountType.IHC) {
    return {
      account_open_date: hci.account_open_date?.toString() || null,
      ar_forward_terms: hci.ar_forward_terms?.toString() || null,
      ar_forward_available_credit:
        hci.ar_forward_available_credit?.toString() || null,
      processing_payments: hci.processing_payments?.toString() || null,
      current_balance: hci.current_balance?.toString() || null,
      max_credit_limit: hci.max_credit_limit?.toString() || null,
      payment_history: hci.payment_history?.toString() || null,
      avg_days_to_pay: hci.avg_days_to_pay?.toString() || null,
      open_a_r: hci.open_a_r?.toString() || null,
      highest_credit: hci.highest_credit?.toString() || null,
      billing_past_due_1: hci.billing_past_due_1?.toString() || null,
      billing_past_due_2: hci.billing_past_due_2?.toString() || null,
      billing_past_due_3: hci.billing_past_due_3?.toString() || null,
      billing_past_due_more: null,
      total_past_due: hci.total_past_due?.toString() || null,
      average_invoices: hci.average_invoices?.toString() || null,
      rejected_ach_count: null,
      ein_ssn: hci.ein_ssn,
    }
  }

  return {
    account_open_date: hci.account_open_date?.toString() || null,
    ar_forward_terms: null,
    ar_forward_available_credit: null,
    processing_payments: null,
    max_credit_limit: hci.max_credit_limit?.toString() || null,
    payment_history: hci.payment_history?.toString() || null,
    avg_days_to_pay: hci.avg_days_to_pay?.toString() || null,
    open_a_r: hci.open_a_r?.toString() || null,
    current_balance: hci.current_balance?.toString() || null,
    highest_credit: hci.highest_credit?.toString() || null,
    billing_past_due_1: hci.billing_past_due_1?.toString() || null,
    billing_past_due_2: hci.billing_past_due_2?.toString() || null,
    billing_past_due_3: hci.billing_past_due_3?.toString() || null,
    billing_past_due_more: hci.billing_past_due_more?.toString() || null,
    total_past_due: hci.total_past_due?.toString() || null,
    average_invoices: hci.average_invoices?.toString() || null,
    rejected_ach_count: hci.rejected_ach_count?.toString() || null,
    ein_ssn: hci.ein_ssn,
  }
}

export default {
  middlewares: { pre: [...Middlewares.pre, ...tran.pre], post: [...tran.post] },
  delete: async (req, res) => {
    const { id } = req.query
    const companyId = req.company!.id

    await CustomerAccount.findByIdAndUpdate(id, { isDeleted: true })
    await Invoice.updateMany(
      {
        customer_account_id: id,
        company_id: companyId,
        status: invoiceStatus.draft,
      },
      {
        $set: { customer_account_id: null },
      },
    )

    res.send({ id })
  },
  get: async (req, res) => {
    const page = parseInt((req.query?.page as string) || '1')
    const pageSize = parseInt((req.query?.pageSize as string) || '25')
    const paginationPipeline: PipelineStage.FacetPipelineStage[] = []
    if (page !== -1) paginationPipeline.push({ $skip: (page - 1) * pageSize })
    if (pageSize !== -1) paginationPipeline.push({ $limit: pageSize })
    paginationPipeline.push(getLoansQuery as PipelineStage.FacetPipelineStage) // Getting the loans after pagination (required to calculate TradeCreditStatus) to fix size exceeded error

    if (req.query.id) {
      const ca = await CustomerAccount.findOne({
        _id: req.query.id,
        company_id: req.company!._id,
      }).populate('bankAccounts')
      if (ca) {
        res.send({ ...ca.toObject(), id: ca._id.toString() })
      } else {
        res.send({ id: req.query.id }).status(404)
      }
    } else {
      const pipeline: PipelineStage[] = [
        {
          $match: {
            company_id: req.company!.id,
            isDeleted: { $in: [false, null] },
            parent_id: { $in: ['', null] },
          },
        },
        {
          $addFields: {
            email: { $ifNull: ['$email', ''] },
            phone: { $ifNull: ['$phone', ''] },
          },
        },
        {
          $lookup: {
            from: User.collection.name,
            let: { email: '$email', phone: '$phone' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $cond: {
                      if: { $ne: ['$$email', ''] },
                      then: {
                        $eq: ['$email', '$$email'],
                      },
                      else: {
                        $or: [
                          { $eq: ['$login', '$$email'] },
                          { $eq: ['$login', '$$phone'] },
                        ],
                      },
                    },
                  },
                },
              },
            ],
            as: 'users',
          },
        },
        { $unwind: { path: '$users', preserveNullAndEmptyArrays: true } },
        {
          $lookup: {
            from: UserRole.collection.name,
            let: { sub: '$users.sub' },
            pipeline: [{ $match: { $expr: { $eq: ['$sub', '$$sub'] } } }],
            as: 'roles',
          },
        },
        { $unwind: { path: '$roles', preserveNullAndEmptyArrays: true } },

        {
          $addFields: {
            customer_id: { $toObjectId: '$roles.company_id' },
          },
        },
        {
          $lookup: {
            from: Company.collection.name,
            as: 'customer',
            localField: 'customer_id',
            foreignField: '_id',
          },
        },
        { $unwind: { path: '$customer', preserveNullAndEmptyArrays: true } },
        {
          $unset: ['users', 'roles', 'customer.onBoarding'],
        },
        {
          $lookup: {
            from: User.collection.name,
            as: 'salesRepUserInfo',
            let: {
              salesRepObjectId: {
                $convert: {
                  input: '$salesRepId',
                  to: 'objectId',
                  onError: null,
                },
              },
            },
            pipeline: [
              { $match: { $expr: { $eq: ['$_id', '$$salesRepObjectId'] } } },
              {
                $project: {
                  fullName: { $concat: ['$firstName', ' ', '$lastName'] },
                  _id: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$salesRepUserInfo',
            preserveNullAndEmptyArrays: true,
          },
        },
        /**
         * If Company (customer) has business name set that, else show Customer Account name (name).
         */
        {
          $set: {
            name: {
              $cond: {
                if: {
                  $ne: [{ $ifNull: ['$customer.name', ''] }, ''],
                },
                then: '$customer.name',
                else: '$name',
              },
            },
          },
        },
        /**
         * If House Credit Info is null, set {}
         */
        {
          $set: {
            house_credit_info: {
              $ifNull: ['$house_credit_info', {}],
            },
          },
        },
        //
        {
          $lookup: {
            from: BankAccount.collection.name,
            as: 'primaryAccount',
            let: {
              ids: { $ifNull: ['$customer.bankAccounts', []] },
            },
            pipeline: [
              {
                $match: {
                  $and: [
                    {
                      $expr: {
                        $in: ['$_id', '$$ids'],
                      },
                    },
                    {
                      $expr: {
                        $in: ['$status', ['verified', 'manualverified']],
                      },
                    },
                    {
                      isDeactivated: { $in: [false, null] },
                    },
                    {
                      $or: [
                        { isPrimaryForCredit: true },
                        {
                          $and: [
                            {
                              $or: [
                                { isPrimaryForCredit: null },
                                { isPrimaryForCredit: { $exists: false } },
                              ],
                            },
                            { isPrimary: true },
                          ],
                        },
                      ],
                    },
                  ],
                },
              },
            ],
          },
        },
        { $set: { primaryAccount: { $last: '$primaryAccount' } } },
        {
          $addFields: {
            invoice_import_enabled: {
              $cond: {
                if: {
                  $and: [
                    { $eq: ['$contact_source', ErpSystems.QuickBooks] },
                    { $ifNull: ['$connector', false] },
                  ],
                },
                then: {
                  $cond: {
                    if: {
                      $eq: [
                        '$connector.quickBooksSettings.invoice_import_enabled',
                        false,
                      ],
                    },
                    then: false,
                    else: true, // QB import is enabled by default for old records without this setting
                  },
                },
                else: null,
              },
            },
          },
        },
      ]

      if (req.query.status) {
        pipeline.push({
          $match: {
            status: req.query.status,
          },
        })
      }

      if (req.query.isConnectorParent) {
        if (req.query.isConnectorParent === 'false') {
          pipeline.push({
            $match: {
              isConnectorParent: { $in: [false, null] },
            },
          })
        }
      }

      if (req.query.isImportEnabled) {
        const isImportEnabled =
          req.query.isImportEnabled === 'none'
            ? null
            : req.query.isImportEnabled === 'true'

        pipeline.push({
          $match: { invoice_import_enabled: isImportEnabled },
        })
      }

      if (req.company?.settings?.supplierCanPay) {
        pipeline.push({
          $lookup: {
            from: BankAccount.collection.name,
            localField: 'bankAccounts',
            foreignField: '_id',
            as: 'bankAccounts',
            pipeline: [
              {
                $match: {
                  isDeactivated: { $in: [false, null] },
                },
              },
              {
                $project: {
                  name: 1,
                  accountholderName: { $ifNull: ['$accountholderName', ''] },
                  routingNumber: 1,
                  accountNumber: '$accountNumber.display',
                  isManualEntry: 1,
                  paymentMethodType: 1,
                  'cardMetadata.accountId': 1,
                  'cardMetadata.type': 1,
                  billingAddress: 1,
                  isPrimary: 1,
                  accountType: 1,
                  finicity: 1,
                  status: 1,
                  _id: 1,
                  id: '$_id',
                },
              },
            ],
          },
        })
      }

      const { search = 'build', sortDirection = 'asc' } = req.query
      const sortColumn = req.query?.sortColumn as string

      if (search) {
        pipeline.push({
          $addFields: {
            full_name: {
              $concat: ['$first_name', ' ', '$last_name'],
            },
            id: {
              $toString: '$_id',
            },
          },
        })

        pipeline.push({
          $match: {
            $or: [
              'id',
              'name',
              'customer_id',
              'phone',
              'first_name',
              'last_name',
              'full_name',
              'display_name',
              'email',
            ].map((field) => ({
              [field]: {
                $regex: escapeRegExp(search as string),
                $options: 'i',
              },
            })),
          },
        })
      }

      let _sortColumn = sortColumn ? sortColumn : 'createdAt'

      switch (_sortColumn) {
        case 'Contact':
          pipeline.push({
            $addFields: {
              contact: {
                $concat: [
                  { $ifNull: ['$first_name', ''] },
                  ' ',
                  { $ifNull: ['$last_name', ''] },
                ],
              },
            },
          })
          pipeline.push({
            $addFields: {
              lower_contact: {
                $cond: {
                  if: {
                    $and: [
                      { $ifNull: ['$contact', false] },
                      { $ne: [{ $trim: { input: '$contact' } }, ''] },
                    ],
                  },
                  then: { $toLower: '$contact' },
                  else: {
                    $cond: {
                      if: {
                        $and: [
                          { $ifNull: ['$display_name', false] },
                          { $ne: [{ $trim: { input: '$display_name' } }, ''] },
                        ],
                      },
                      then: { $toLower: '$display_name' },
                      else: { $toLower: '$name' },
                    },
                  },
                },
              },
            },
          })
          _sortColumn = 'lower_contact'
          break
        case 'Contact Info':
          _sortColumn = 'phone'
          break
        case 'Status':
          _sortColumn = 'status'
          break
        case 'Last purchase date':
          pipeline.push({
            $addFields: {
              last_purchase_date_time: { $toDate: '$last_purchase_date' },
            },
          })
          _sortColumn = 'last_purchase_date_time'
          break
        case 'QuickBooks Import':
          _sortColumn = 'invoice_import_enabled'
          break
        case 'name':
          pipeline.push({
            $addFields: {
              lower_name: { $toLower: '$name' },
            },
          })
          _sortColumn = 'lower_name'
          break
      }

      pipeline.push({
        $sort: {
          [_sortColumn]: sortDirection === 'desc' ? -1 : 1,
        },
      })

      const [result, tradeCreditPeriod] = await Promise.all([
        CustomerAccount.aggregate([
          ...pipeline,
          {
            $facet: {
              total: [{ $count: 'count' }],
              items: [...paginationPipeline],
            },
          },
        ]),
        Settings.findOne({ key: 'trade_credit_period' }),
      ])
      const { items, total } = result[0]

      const finalItems = items.map((account: any) => {
        const { status, validTill } = TradeCreditService.calculateStatus(
          account.loans,
          account.primaryAccount,
          tradeCreditPeriod?.value || 90,
        )

        return {
          id: account._id,
          ...account,
          credit_status: status,
          credit_status_valid_till: validTill,
          customer_credit_limit: account.customer?.credit?.limit || 0,
          bankAccounts: req.company?.settings?.supplierCanPay
            ? account.bankAccounts
            : undefined,
          name: account.name,
          contacts: [],
          dateAdded: moment(account.createdAt).format('MM/DD/YYYY'),
          customer: account.customer
            ? {
                id: account.customer._id,
                ...account.customer,
              }
            : null,
          house_credit_info: getHouseCreditInfo(
            account.type,
            account.house_credit_info,
          ),
          loans: undefined,
          primaryAccount: undefined,
        } as SupplierAccount
      })

      res.send({
        items: finalItems,
        count: total[0]?.count ?? 0,
      })
    }
  },
  post: async (req, res, next) => {
    let {
      account: { id, phone, contacts = [], email, house_credit_info, ...data },
      sendInvite,
    } = req.body

    if (phone) {
      try {
        phone = parsePhoneNumber(phone, 'US').number
        if (data.business_phone) {
          data.business_phone = parsePhoneNumber(
            data.business_phone,
            'US',
          ).number
        }
      } catch {
        throw new exceptions.LogicalError('Invalid phone number')
      }
    }

    //const customer_event = id ? 'Updated' : 'Created'
    if (id) {
      const ca = await CustomerAccount.findOne({
        _id: id,
        company_id: req.company!._id,
      }).session(req.session)
      if (ca) {
        await ca
          .updateOne({
            ...data,
            phone,
            email,
            bankAccounts: ca.bankAccounts,
            contacts: ca.contacts,
            house_credit_info,
          })
          .session(req.session)
      }
    } else {
      if (!phone) {
        throw new exceptions.LogicalError('Phone number is required')
      }
      const query = Object.assign(
        {},
        phone ? { phone } : {},
        email ? { email } : {},
      )

      const found = await CustomerAccount.findOne({
        company_id: req.company!._id,
        ...query,
        isDeleted: false,
      })
      if (found && (!id || id !== found._id.toString()) && !found.parent_id) {
        throw new exceptions.LogicalError('Customer already exists')
      }

      const [ca] = await CustomerAccount.create(
        [
          {
            ...data,
            company_id: req.company!._id,
            phone,
            email,
            house_credit_info,
            settings: {
              acceptAchPayment: true,
              sendFinalPaymentWhenLoanIsPaid:
                !!req.company?.settings.sendFinalPaymentWhenLoanIsPaid,
            },
          },
        ],
        { session: req.session },
      )
      id = ca._id
    }

    // updating customer status
    const customerId = new Types.ObjectId(id)
    const customer = await getCustomersData([customerId], req.session)
    await prepareDataAndUpsertCustomers(customer, req.session)

    contacts = contacts.filter(
      (c: any) => !!c.first_name && !!c.last_name && !!c.phone && !!c.email,
    )
    if (contacts.length > 0) {
      await saveBillingContacts({
        companyId: req.company!.id,
        customerAccountId: id,
        contacts: contacts,
        req: req,
      })
    }

    let invitation
    if (sendInvite) {
      invitation = await inviteCustomerAccount({
        customerAccountId: id,
        req,
        phone,
        name: data.name,
        role: 'Owner',
      })
    }

    //await emitCustomerEvent(id, customer_event)

    res.locals.result = { id, invitation }
    next()
  },
} as ControllerItem

/*
async function emitCustomerEvent(
  customerId: string,
  eventType: 'Created' | 'Updated',
) {
  try {
    await AwsService.sendSQSMessage(
      'customer-syncback',
      JSON.stringify({ customerId, eventType }),
    )
  } catch (e) {
    console.error(e)
  }
}*/

/*const addStatusesQuery = (pipeline: PipelineStage[]) => {
  pipeline.push(
    getLoansQuery,
    {
      $addFields: {
        loan_statuses: {
          $map: {
            input: '$loans',
            in: '$$this.status',
          },
        },
      },
    },
    { $unset: ['loans'] },
    {
      $addFields: {
        status: {
          $switch: {
            branches: [
              {
                case: { $eq: [{ $size: '$loan_statuses' }, 0] },
                then: {
                  $switch: {
                    branches: [
                      {
                        case: {
                          $gte: [{ $toString: '$customer_id' }, ' '],
                        },
                        then: customerStatus.active,
                      },
                      {
                        case: { $eq: ['$invited', true] },
                        then: customerStatus.invited,
                      },
                      {
                        case: {
                          $or: [
                            { $gte: ['$email', ' '] },
                            { $gte: ['$phone', ' '] },
                            { $gte: ['$business_phone', ' '] },
                          ],
                        },
                        then: customerStatus.saved,
                      },
                    ],
                    default: customerStatus.draft,
                  },
                },
              },
              {
                case: {
                  $or: APPROVED_STATUSES.map((status) => ({
                    $in: [status, '$loan_statuses'],
                  })),
                },
                then: customerStatus.tradeCredit,
              },
            ],
            default: customerStatus.active,
          },
        },
      },
    },
    {
      $addFields: {
        customer_credit_limit: {
          $ifNull: ['$customer.credit.limit', 0],
        },
      },
    },
    { $unset: ['loan_statuses'] },
  )
}*/

export const getLoansQuery: PipelineStage = {
  $lookup: {
    from: LoanApplication.collection.name,
    let: { loans_customer_id: '$customer_id' },
    pipeline: [
      {
        $match: {
          $expr: {
            $eq: ['$company_id', { $toString: '$$loans_customer_id' }],
          },
        },
      },
      {
        $sort: { createdAt: 1, updatedAt: 1 },
      },
      {
        $addFields: {
          outputs: {
            $filter: {
              input: '$outputs',
              as: 'output',
              cond: {
                $or: [
                  { $eq: ['$$output.step', 'ProcessManualData'] },
                  { $eq: ['$$output.step', 'ProcessFinicityData'] },
                ],
              },
            },
          },
        },
      },
      {
        $project: {
          status: 1,
          approvedAmount: 1,
          invoiceDetails: 1,
          outputs: 1,
          issueDate: 1,
          decisionDate: 1,
          createdAt: 1,
        },
      },
    ],
    as: 'loans',
  },
}
