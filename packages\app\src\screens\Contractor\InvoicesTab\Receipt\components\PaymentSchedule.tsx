import React from 'react'
import { observer } from 'mobx-react'
import { useTranslation } from 'react-i18next'
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native'
import { Spacer } from '@linqpal/components/src/ui'
import {
  LOAN_REPAYMENT_STATUS,
  LOAN_REPAYMENT_TYPE,
} from '@linqpal/models/src/dictionaries/loanStatuses'
import {
  PaymentTimelineContext,
  PaymentTimelineItem,
} from '../../../../../ui/organisms'

export const PaymentSchedule = observer(
  ({ loanReceivables, isMobile, loading }) => {
    const { t } = useTranslation('global')

    const plan = loanReceivables?.length
      ? [...loanReceivables]
          .filter(
            (receivable) =>
              receivable.status !== LOAN_REPAYMENT_STATUS.CANCELED &&
              receivable.type !== LOAN_REPAYMENT_TYPE.PENALTY_INTEREST_FEE,
          )
          .sort(
            (a, b) =>
              new Date(a.expectedDate).getTime() -
              new Date(b.expectedDate).getTime(),
          )
      : []

    if (loading) {
      return (
        <View style={{ paddingLeft: 5 }}>
          <Text style={styles.title}>
            {t('PaymentHistoryInvoice.paymentScheduleSummary')}
          </Text>
          <Spacer height={20} />
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#00A0F3" />
          </View>
        </View>
      )
    }

    return (
      <>
        <View style={{ paddingLeft: 5 }}>
          <Text style={styles.title}>
            {t('PaymentHistoryInvoice.paymentScheduleSummary')}
          </Text>
          <Spacer height={20} />

          <View style={styles.paymentsLayout}>
            {plan.length > 0 ? (
              plan.map((p, idx) => (
                <PaymentTimelineItem
                  key={idx}
                  installment={p}
                  context={PaymentTimelineContext.IHC_INVOICE}
                />
              ))
            ) : (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>
                  {t('PaymentHistoryInvoice.noPaymentSchedule')}
                </Text>
              </View>
            )}
          </View>
        </View>
      </>
    )
  },
)

const styles = StyleSheet.create({
  title: {
    fontFamily: 'Inter',
    fontWeight: '700',
    fontSize: 16,
    lineHeight: 28,
    color: '#19262F',
  },
  paymentsLayout: {
    marginBottom: 30,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontFamily: 'Inter',
    fontSize: 14,
    color: '#668598',
    textAlign: 'center',
  },
})
