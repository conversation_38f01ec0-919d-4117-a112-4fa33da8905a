type FlowStepSelector<TDocument = any, TOptions = any> = (
  document: TDocument,
  options: TOptions,
) => string

type FlowStep<TDocument = any, TOptions = any> =
  | string
  | { path: string; selector: FlowStepSelector<TDocument, TOptions> }

// Simple array-based flow structure
export type Flow<TDocument = any, TOptions = any> = FlowStep<
  TDocument,
  TOptions
>[]

export class FlowController<TDocument = any, TOptions = any> {
  private readonly _flow: Flow<TDocument, TOptions>

  constructor(flow: Flow<TDocument, TOptions>) {
    this._flow = flow
    this.ensureGroupsOrder()
  }

  getFlowGroups(document: TDocument, options: TOptions): string[] {
    // we need to consider only groups that are available based on current flow definition, so cannot take just keys from _flow
    const steps = this.getFlowSteps(document, options)

    const groups = steps.map((step) => {
      const [group] = step.split('.')
      return group
    })

    const distinctGroups = new Set(groups)

    return Array.from(distinctGroups)
  }

  getFirstStep(document: TDocument, options: TOptions): string {
    const firstStep = this._flow[0]

    if (typeof firstStep === 'string') {
      return firstStep
    } else {
      // Handle conditional jumps (function-based navigation)
      const gotoPath = firstStep.selector(document, options)
      return this.tryHandleConditionalJumps(gotoPath, document, options)
    }
  }

  getFlowSteps(document: TDocument, options: TOptions): string[] {
    const steps: string[] = []

    let currentStep = this.getFirstStep(document, options)

    while (currentStep) {
      steps.push(currentStep)

      const nextStep = this.findNextStep(currentStep, document, options)
      if (!nextStep || steps.includes(nextStep)) break

      currentStep = nextStep
    }

    return steps
  }

  getGroupSteps(
    groupName: string,
    document: TDocument,
    options: TOptions,
  ): string[] {
    const flowSteps = this.getFlowSteps(document, options)

    const groupSteps = flowSteps.filter((step) =>
      step.startsWith(`${groupName}.`),
    )

    return groupSteps
  }

  getFirstGroupStep(
    groupName: string,
    document: TDocument,
    options: TOptions,
  ): string {
    const groupSteps = this.getGroupSteps(groupName, document, options)
    if (groupSteps.length === 0) return ''

    const firstGroupStep = groupSteps[0]

    return this.tryHandleConditionalJumps(firstGroupStep, document, options)
  }

  findNextStep(path: string, document: TDocument, options: TOptions): string {
    // Find the current step in the flow array
    const currentStepIndex = this._flow.findIndex((step) => {
      if (typeof step === 'string') {
        return step === path
      } else {
        return step.path === path
      }
    })

    if (currentStepIndex === -1 || currentStepIndex >= this._flow.length - 1) {
      return ''
    }

    const nextStep = this._flow[currentStepIndex + 1]

    if (typeof nextStep === 'string') {
      return nextStep
    } else {
      // Handle conditional jumps (function-based navigation)
      const gotoPath = nextStep.selector(document, options)
      return this.tryHandleConditionalJumps(gotoPath, document, options)
    }
  }

  findSkipStep(path: string, document: TDocument, options: TOptions): string {
    // Find the current step in the flow array
    // TODO: VK: Unified: Review - handle skip path like in old version
    // const currentStep = this._flow.find((step) => {
    //   if (typeof step === 'string') {
    //     return step === path
    //   } else {
    //     return step.path === path
    //   }
    // })

    // For now, we don't have skipPath in the new structure
    // If no skipPath defined, fall back to normal next step behavior
    return this.findNextStep(path, document, options)
  }

  getNextStep(
    currentStep: string,
    document: TDocument,
    options: TOptions,
  ): string {
    const nextStep = this.findNextStep(currentStep, document, options)
    return nextStep
  }

  getPreviousStep(
    currentStep: string,
    document: TDocument,
    options: TOptions,
  ): string {
    // We cannot just get a previous step because it can be conditional step pointing to some other step (or current, causing infinite loop)
    // Also we could go to the current step by conditional jump from the very beginning of the flow, so we cannot get previous non-conditional step
    // So here we get all steps in the flow with regards to conditional jumps, and just take precalculated previous one
    const flowSteps = this.getFlowSteps(document, options)
    const currentStepIndex = flowSteps.indexOf(currentStep)

    if (currentStepIndex > 0) {
      return flowSteps[currentStepIndex - 1]
    } else {
      // If we're at the first step, return current step
      return currentStep
    }
  }

  // region Path constants generator
  static createStepConstants<TGroups extends Record<string, readonly string[]>>(
    groups: TGroups,
  ) {
    // Create string constants for steps based on step definitions
    // These can be used directly as strings wherever string is acceptable
    //
    // Steps.businessInfo.email -> 'businessInfo.email'

    const result: {
      [GroupName in keyof TGroups]: {
        [StepName in TGroups[GroupName][number]]: string
      }
    } = {} as any

    for (const groupName in groups) {
      const steps = groups[groupName]
      const group: any = {}

      for (const step of steps) {
        group[step] = `${groupName}.${step}`
      }

      result[groupName] = group
    }

    return result
  }

  static createGroupConstants<
    TGroups extends Record<string, readonly string[]>,
  >(groups: TGroups) {
    // Create string constants for group names only
    // These can be used directly as strings wherever string is acceptable
    //
    // Groups.businessInfo -> 'businessInfo'

    const result: {
      [GroupName in keyof TGroups]: string
    } = {} as any

    for (const groupName in groups) {
      result[groupName] = groupName
    }

    return result
  }
  // endregion Path constants generator

  private tryHandleConditionalJumps(
    stepPath: string,
    document: TDocument,
    options: TOptions,
  ): string {
    // Find the step in the flow array
    const step = this._flow.find((flowStep) => {
      if (typeof flowStep === 'string') {
        return flowStep === stepPath
      } else {
        return flowStep.path === stepPath
      }
    })

    if (step && typeof step === 'object' && 'selector' in step) {
      const gotoPath = step.selector(document, options)
      return this.tryHandleConditionalJumps(gotoPath, document, options)
    } else {
      return stepPath
    }
  }

  private ensureGroupsOrder(): void {
    // ensure that there is no error in step sequence like groupA.step1 -> groupB.step2 -> groupA.step3
    let currentGroup: string | null = null
    const visitedGroups: string[] = []

    for (const step of this._flow) {
      const path = typeof step === 'string' ? step : step.path
      const [group] = path.split('.')

      if (currentGroup === undefined) {
        // very first group - init
        currentGroup = group
        visitedGroups.push(group)
      } else if (group !== currentGroup) {
        // group changed
        if (visitedGroups.includes(group)) {
          // group already appeared before — invalid
          throw new Error(
            `Flow validation error: step "${path}" from group "${group}" appears after group "${currentGroup}", but "${group}" was already used earlier in the flow.`,
          )
        } else {
          // encounter a new group - ok
          currentGroup = group
          visitedGroups.push(group)
        }
      }
    }
  }
}
