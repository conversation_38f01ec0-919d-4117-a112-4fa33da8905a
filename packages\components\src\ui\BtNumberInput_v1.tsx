import React, { FC } from 'react'
import { PatternFormat } from 'react-number-format'
import {
  NumberFormatValues,
  PatternFormatProps,
} from 'react-number-format/types/types'
import { BtInputBaseProps } from './BtInputBase'
import { BtInput_v1 } from './BtInput_v1'

export type BtNumberInputProps = BtInputBaseProps &
  Pick<PatternFormatProps, 'format'>

export const BtNumberInput_v1: FC<BtNumberInputProps> = ({
  format,
  placeholder,
  label,
  value,
  disabled,
  validate,
  required,
  onChangeText,
  onBlur,
  testID,
}) => {
  const handleValueChange = (formatValues: NumberFormatValues) => {
    if (onChangeText) {
      onChangeText(formatValues.value)
    }
  }

  return (
    <PatternFormat
      format={format}
      placeholder={placeholder}
      label={label}
      value={value}
      disabled={disabled}
      required={required}
      customInput={BtInput_v1}
      keyboardType="number-pad"
      allowEmptyFormatting={false}
      validate={validate}
      onValueChange={handleValueChange}
      onBlur={(e: any) => onBlur && onBlur(e)}
      valueIsNumericString
      testID={testID}
    />
  )
}
