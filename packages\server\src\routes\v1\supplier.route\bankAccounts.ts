import { ControllerItem } from '../../controllerItem'
import { authRequired } from '../../../services/auth.service'
import transactional from '../../../services/transactional.service'
import { bankAccountsService, CustomerAccount } from '@linqpal/common-backend'

export default {
  middlewares: {
    pre: [authRequired(), ...transactional.pre],
    post: transactional.post,
  },
  get: async (req, res, next) => {
    const { id, customerId } = req.query
    if (!id || typeof id !== 'string') {
      res.status(400).send({ error: 'Id not provided' })
      return
    }

    if (!customerId || typeof customerId !== 'string') {
      res.status(400).send({ error: 'Customer Id not provided' })
      return
    }

    const bankAccount = await bankAccountsService.getBankAccount(
      id as string,
      req.company!.id,
      customerId,
    )
    res.locals.result = { bankAccount: bankAccount }
    next()
  },
  post: async (req, res, next) => {
    const customerAccount = await CustomerAccount.findOne({
      _id: req.query.id,
      company_id: req.company!._id,
    })
    if (!customerAccount) {
      res.status(404).send({ error: 'Customer account not found' })
      return
    }

    const { _id, status, ...data } = req.body

    const id = await bankAccountsService.addOrUpdateBankAccount(
      null,
      customerAccount,
      _id,
      data,
      req.user!,
      req.session,
    )

    res.locals.result = { id: id }

    next()
  },
  delete: async (req, res, next) => {
    const { id, customerId } = req.query
    if (!id || typeof id !== 'string') {
      res.status(400).send({ error: 'Id not provided' })
      return
    }

    if (!customerId || typeof customerId !== 'string') {
      res.status(400).send({ error: 'Customer Id not provided' })
      return
    }

    await bankAccountsService.deleteBankAccount(id, req.company!.id, customerId)
    next()
  },
} as ControllerItem
