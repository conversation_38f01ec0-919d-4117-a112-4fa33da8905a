import { observer } from 'mobx-react-lite'
import { useTranslation } from 'react-i18next'
import { SidebarDetailItem } from '../../../../../ui/molecules'
import React from 'react'
import { IReceivableLineProps } from './IReceivableLineProps'
import intersection from 'lodash/intersection'
import { toJS } from 'mobx'
import {
  invoiceSchemaStatus,
  supplierServiceCategories,
} from '@linqpal/models/src/dictionaries'
import { currencyMask } from '../../../../../utils/helpers/masking'
import { useStore } from '../../../../../store'
import { BtText } from '@linqpal/components/src/ui'
import { EInvoiceType } from '@linqpal/models'
import { StyleSheet, View } from 'react-native'
import { colors } from '@linqpal/common-frontend/src/theme/colors'
import {
  PaidInvoiceStatuses,
  PaymentProcessingInvoiceStatuses,
} from '../../../../Contractor/PayablesTab/enums'
import { getReceivableLocalization } from '../../receivableLocalization'
import { Spacer } from '../../../../../ui/atoms'
import { round } from 'lodash'

export const AmountLine = observer(({ receivable }: IReceivableLineProps) => {
  const {
    userStore: { businessCategory },
  } = useStore()

  const { t } = useTranslation('global')
  const localization = getReceivableLocalization(receivable.type, t)

  const paidAmount = receivable.totalPaidAmount
    ? receivable.totalPaidAmount
    : PaidInvoiceStatuses.includes(receivable.status)
    ? receivable.total_amount
    : 0

  const processingAmount = receivable.totalProcessingAmount
    ? receivable.totalProcessingAmount
    : PaymentProcessingInvoiceStatuses.includes(receivable.status)
    ? receivable.total_amount
    : 0

  const remainingAmount =
    PaymentProcessingInvoiceStatuses.includes(receivable.status) ||
    PaidInvoiceStatuses.includes(receivable.status)
      ? 0
      : receivable.totalRemainingAmount

  const customerFee = receivable.customerFee || 0
  const lateFee = receivable.lateFee || 0

  const invoiceAmount =
    parseFloat(receivable?.total_amount.replace(/[^-\d.]/g, '')) || 0

  const subtotal = invoiceAmount + customerFee + lateFee

  const invoiceTotal = round(
    subtotal + parseFloat(receivable?.tax_amount) || 0,
    2,
  )

  if (
    intersection(toJS(businessCategory), supplierServiceCategories).length !== 0
  )
    return null

  const taxAmount = parseFloat(receivable?.tax_amount)
    ? currencyMask(receivable?.tax_amount)
    : 'Tax Exempted'

  return (
    <>
      {receivable.type === EInvoiceType.QUOTE &&
      receivable.status === invoiceSchemaStatus.authorized ? (
        <SidebarDetailItem bold label={t('InvoiceViewSidebar.total-label')}>
          <View style={styles.amountContainer}>
            <BtText style={styles.amountText}>
              {currencyMask(receivable.total_amount)}
            </BtText>
            <BtText>
              {t('Receivables.edit-authorization.edit-amount-hint')}
            </BtText>
          </View>
        </SidebarDetailItem>
      ) : (
        <>
          <SidebarDetailItem
            bold
            label={localization.amount}
            content={currencyMask(receivable.total_amount)}
          />
          {!!customerFee && (
            <SidebarDetailItem
              bold
              label={t('InvoiceViewSidebar.customer-fees')}
              content={currencyMask(customerFee)}
            />
          )}
          {!!lateFee && (
            <SidebarDetailItem
              bold
              label={t('InvoiceViewSidebar.late-fee')}
              content={currencyMask(lateFee)}
            />
          )}
          {subtotal !== invoiceAmount && (
            <SidebarDetailItem
              bold
              label={t('InvoiceViewSidebar.subtotal-label')}
              content={currencyMask(subtotal)}
            />
          )}
          <SidebarDetailItem
            bold
            label={t('InvoiceViewSidebar.tax-label')}
            content={taxAmount}
          />
          <SidebarDetailItem
            bold
            label={t('InvoiceViewSidebar.total-label')}
            content={currencyMask(invoiceTotal)}
          />

          <Spacer height={32} />

          {receivable.type === EInvoiceType.INVOICE &&
            receivable.status !== invoiceSchemaStatus.draft && (
              <>
                <SidebarDetailItem
                  bold
                  label={t('InvoiceViewSidebar.paid-label')}
                  content={currencyMask(paidAmount)}
                />
                {!!(remainingAmount || processingAmount) && (
                  <>
                    <SidebarDetailItem
                      bold
                      label={t('InvoiceViewSidebar.processing-label')}
                      content={currencyMask(processingAmount)}
                    />
                    <SidebarDetailItem
                      bold
                      label={t('InvoiceViewSidebar.remaining-label')}
                      content={currencyMask(remainingAmount)}
                    />
                  </>
                )}
              </>
            )}
        </>
      )}
    </>
  )
})

const styles = StyleSheet.create({
  amountContainer: {
    flexDirection: 'column',
  },
  amountText: {
    color: colors.primary.dark,
    fontSize: 16,
    fontWeight: '700',
    marginTop: -1,
    marginBottom: 5,
  },
})
