import { Instance, types } from 'mobx-state-tree'
import moment from 'moment-timezone'
import numbro from 'numbro'
import {
  LOAN_REPAYMENT_SCHEDULE_STATUS,
  LOAN_REPAYMENT_STATUS,
  LOAN_REPAYMENT_TYPE,
} from '@linqpal/models/src/dictionaries/loanStatuses'
import { routes } from '@linqpal/models'
import { ILoan } from './LoanStatusDetailsStore'
import { MomentDate } from '@linqpal/models/src/types/Moment'
import { IReceivablesRescheduleItem } from '@linqpal/models/src/types/routes'

export enum PaymentFrequency {
  Weekly = 'Weekly',
  Monthly = 'Monthly',
  Custom = 'Custom',
}

const DATE_FORMAT = 'YYYY-MM-DD'

const LoanInstallment = types
  .model({
    date: types.maybeNull(MomentDate),
    minDate: types.optional(MomentDate, moment().local().startOf('day')),
    maxDate: types.optional(
      MomentDate,
      moment().local().add(6, 'months').endOf('day'),
    ),
    amount: types.number,
    key: types.string,
  })
  .views((self) => {
    return {
      isValidDate() {
        if (!self.date) return true
        return self.minDate <= self.date && self.date <= self.maxDate
      },
    }
  })
  .actions((self) => {
    return {
      setDate(date: moment.Moment) {
        self.date = date
      },
      setAmount(amount: number) {
        self.amount = amount
      },
    }
  })

interface ILoanInstallment extends Instance<typeof LoanInstallment> {}

export const LoanCustomPaymentPlan = types
  .model({
    loanId: types.optional(types.string, ''),
    amount: types.optional(types.number, 0),
    frequency: types.optional(
      types.enumeration(Object.values(PaymentFrequency)),
      PaymentFrequency.Weekly,
    ),
    paymentsCount: types.optional(types.number, 0),
    startDate: types.maybeNull(MomentDate),
    equalPayments: types.optional(types.boolean, true),
    useExtensionFee: types.optional(types.boolean, true),
    extensionFeeDate: types.maybeNull(MomentDate),
    extensionFeeAmount: types.optional(types.number, 0),
    reschedulingReason: types.optional(types.string, ''),
    installments: types.optional(types.array(LoanInstallment), []),
  })
  .views((self) => {
    return {
      minDate() {
        return moment().local().startOf('day')
      },
      maxDate() {
        return moment().local().add(6, 'months').endOf('day')
      },
      isValidStartDate() {
        return (
          !self.startDate ||
          (this.minDate() <= self.startDate && self.startDate <= this.maxDate())
        )
      },
      isValidExtensionFeeDate() {
        return (
          !self.extensionFeeDate ||
          (this.minDate() <= self.extensionFeeDate &&
            self.extensionFeeDate <= this.maxDate())
        )
      },
      totalInstallments() {
        return self.installments
          .map((installment) => installment.amount)
          .reduce((a, b) => a + b)
      },
      amountDue() {
        const amountDue = self.amount - this.totalInstallments()
        return Math.round(amountDue * 100) / 100
      },
      amountDueFormatted() {
        return numbro(this.amountDue()).formatCurrency({
          mantissa: 2,
          thousandSeparated: true,
        })
      },
      canSubmit() {
        const validFee =
          !self.useExtensionFee ||
          (self.extensionFeeAmount && self.extensionFeeDate)

        const validInstallments =
          self.installments.length &&
          self.installments.every((item, index) => {
            if (!item.amount || !item.date) return false

            const minDate = self.installments[index - 1]?.date || this.minDate()

            return minDate <= item.date && item.date <= this.maxDate()
          })

        return (
          validFee &&
          validInstallments &&
          self.reschedulingReason &&
          this.isValidExtensionFeeDate() &&
          this.amountDue() === 0
        )
      },
    }
  })
  .actions((self) => {
    return {
      generateInstallments: function () {
        const generateUniformAmounts = () => {
          const result: number[] = []

          const totalCents = self.amount * 100
          const centsPerPayment = Math.floor(totalCents / self.paymentsCount)
          const remainder = totalCents - centsPerPayment * self.paymentsCount

          for (let i = 0; i < self.paymentsCount; i++) {
            const amount =
              i < self.paymentsCount - 1
                ? centsPerPayment / 100
                : (centsPerPayment + remainder) / 100

            result.push(Math.round(amount * 100) / 100)
          }

          return result
        }

        const generateUniformDates = () => {
          const result: moment.Moment[] = []

          if (self.startDate) {
            const useEndOfMonth =
              self.startDate.date() === self.startDate.daysInMonth()

            let currentDate = self.startDate.clone()

            for (let i = 0; i < self.paymentsCount; i++) {
              result.push(currentDate)
              currentDate = currentDate.clone()

              if (self.frequency === PaymentFrequency.Weekly) {
                currentDate.add(1, 'week')
              } else if (self.frequency === PaymentFrequency.Monthly) {
                currentDate.add(1, 'month')
                if (useEndOfMonth) {
                  currentDate.endOf('month')
                }
              }
            }
          }

          return result
        }

        const generateCustomAmounts = () => {
          // preserve already entered amounts if any
          const existingValues = self.installments
            .map((i) => i.amount)
            .slice(0, self.paymentsCount)

          if (existingValues.length < self.paymentsCount) {
            const currentCount = existingValues.length
            existingValues.length = self.paymentsCount

            return existingValues.fill(0, currentCount, self.paymentsCount)
          } else {
            return existingValues
          }
        }

        const generateCustomDates = () => {
          // preserve already entered dates if any
          const existingValues = self.installments
            .map((i) => i.date)
            .slice(0, self.paymentsCount)

          if (existingValues.length < self.paymentsCount) {
            const currentCount = existingValues.length
            existingValues.length = self.paymentsCount

            return existingValues.fill(null, currentCount, self.paymentsCount)
          } else {
            return existingValues
          }
        }

        if (self.startDate || self.frequency === PaymentFrequency.Custom) {
          const dates =
            self.frequency !== PaymentFrequency.Custom
              ? generateUniformDates()
              : generateCustomDates()

          const amounts = self.equalPayments
            ? generateUniformAmounts()
            : generateCustomAmounts()

          this.clearInstallments()

          dates.forEach((date, index) => {
            this.addInstallment(date, amounts[index], index)
          })

          this.setDateBounds()
        } else {
          this.clearInstallments()
        }
      },
      setDateBounds() {
        let minDate = self.minDate().clone()

        self.installments.forEach((installment, index) => {
          installment.minDate = minDate

          if (installment.date) {
            minDate = moment.min(
              installment.date.clone().add(1, 'days').startOf('day'),
              self.maxDate().clone(),
            )
          }

          const nextItem = self.installments.find(
            (item, ind) => ind > index && item.date,
          )

          if (nextItem && nextItem.date) {
            const lastDateBeforeNext = nextItem.date
              .clone()
              .subtract(1, 'day')
              .endOf('day')

            installment.maxDate = moment.min(
              lastDateBeforeNext,
              self.maxDate().clone(),
            )
          } else {
            installment.maxDate = self.maxDate().clone()
          }
        })
      },
      addInstallment(date: moment.Moment, amount: number, index: number) {
        self.installments.push({
          date: date,
          amount: amount,
          // unique id to use as React key
          key: index + new Date().getTime().toString(),
        })
      },
      clearInstallments() {
        self.installments.clear()
      },
      async submit(
        receivables: ILoan[] | undefined,
        loanId: string,
        userId: string,
        onSuccess: () => void,
      ) {
        let newReceivables: IReceivablesRescheduleItem[] =
          self.installments.map((installment) => ({
            receivableType: LOAN_REPAYMENT_TYPE.INSTALLMENT,
            newExpectedDate: installment.date?.format(DATE_FORMAT) || '',
            newExpectedAmount: installment.amount,
            newScheduleStatus: LOAN_REPAYMENT_SCHEDULE_STATUS.CURRENT,
          }))

        if (self.useExtensionFee) {
          newReceivables.push({
            receivableType: LOAN_REPAYMENT_TYPE.EXTENSION_FEE,
            newExpectedDate: self.extensionFeeDate?.format(DATE_FORMAT) || '',
            newExpectedAmount: self.extensionFeeAmount,
            newScheduleStatus: LOAN_REPAYMENT_SCHEDULE_STATUS.CURRENT,
          })
        }

        const nonReschedulableStatuses = [
          LOAN_REPAYMENT_STATUS.CANCELED,
          LOAN_REPAYMENT_STATUS.PAID,
          LOAN_REPAYMENT_STATUS.PROCESSING,
        ]

        newReceivables = newReceivables.concat(
          receivables?.flatMap((receivable) => {
            const newReceivable = {
              id: receivable.id,
              receivableType: receivable.type,
              newExpectedDate: receivable.expectedDate,
              newExpectedAmount: receivable.expectedAmount,
              newScheduleStatus: receivable.scheduleStatus,
            }

            if (
              nonReschedulableStatuses.includes(receivable.status) ||
              receivable.scheduleStatus !==
                LOAN_REPAYMENT_SCHEDULE_STATUS.CURRENT
            ) {
              return newReceivable
            }

            const isLate = moment().isAfter(
              receivable.expectedDate?.toString(),
              'days',
            )

            switch (receivable.type) {
              case LOAN_REPAYMENT_TYPE.INSTALLMENT:
                // discard non fully paid installments completely
                return {
                  ...newReceivable,
                  newScheduleStatus: isLate
                    ? LOAN_REPAYMENT_SCHEDULE_STATUS.POSTPONED
                    : LOAN_REPAYMENT_SCHEDULE_STATUS.RESCHEDULED,
                }
              case LOAN_REPAYMENT_TYPE.LOAN_FEE:
              case LOAN_REPAYMENT_TYPE.LATE_FEE:
              case LOAN_REPAYMENT_TYPE.MANUAL_LATE_FEE:
              case LOAN_REPAYMENT_TYPE.EXTENSION_FEE:
                let newAmount =
                  receivable.expectedAmount -
                  receivable.paidAmount +
                  receivable.adjustAmount
                newAmount = Math.round(newAmount * 100) / 100

                // reschedule unpaid fees at the first new installment date
                return [
                  {
                    ...newReceivable,
                    newScheduleStatus: isLate
                      ? LOAN_REPAYMENT_SCHEDULE_STATUS.POSTPONED
                      : LOAN_REPAYMENT_SCHEDULE_STATUS.RESCHEDULED,
                  },
                  {
                    ...newReceivable,
                    id: undefined,
                    newExpectedAmount: newAmount,
                    newExpectedDate:
                      self.installments[0]?.date?.format(DATE_FORMAT) || '',
                  },
                ]
              default:
                return newReceivable
            }
          }) || [],
        )

        const response = await routes.admin.rescheduleLoanReceivables(
          loanId,
          newReceivables,
          self.reschedulingReason,
          userId,
        )

        if (response.result === 'ok') {
          this.cleanup()
          onSuccess()
        }
      },
      cleanup() {
        self.frequency = PaymentFrequency.Weekly
        self.paymentsCount = 0
        self.startDate = null
        self.equalPayments = true
        self.useExtensionFee = false
        self.extensionFeeAmount = 0
        self.extensionFeeDate = null
        self.reschedulingReason = ''
        this.clearInstallments()
      },
    }
  })
  .actions((self) => {
    return {
      setLoanId(loanId: string) {
        if (self.loanId !== loanId) {
          self.cleanup()
          self.loanId = loanId
        }
      },
      setAmount(amount: number) {
        self.amount = amount
      },
      setFrequency(frequency: PaymentFrequency) {
        self.frequency = frequency
        // preserve existing payments (if any)
        // when switching to Custom frequency
        if (
          self.frequency !== PaymentFrequency.Custom ||
          !self.installments.length
        ) {
          self.generateInstallments()
        }
      },
      setPaymentsCount(count: number) {
        self.paymentsCount = count
        self.generateInstallments()
      },
      setStartDate(date: string) {
        const startDate = moment(date, DATE_FORMAT).startOf('day')
        self.startDate = startDate
        self.generateInstallments()
      },
      setEqualPayments(value: boolean) {
        self.equalPayments = value
        // preserve existing payments (if any)
        // when disabling Equal Payments option
        if (self.equalPayments) {
          self.generateInstallments()
        }
      },
      setUseExtensionFee(value: boolean) {
        self.useExtensionFee = value
        if (!self.extensionFeeDate) {
          self.extensionFeeDate =
            self.startDate?.clone() || moment().local().startOf('day')
        }
      },
      setExtensionFeeDate(date: string) {
        self.extensionFeeDate = moment(date, DATE_FORMAT).startOf('day')
      },
      setExtensionFeeAmount(amount: number) {
        self.extensionFeeAmount = amount
      },
      setReschedulingReason(reason: string) {
        self.reschedulingReason = reason
      },
      setInstallmentDate(installment: ILoanInstallment, date: string) {
        installment.setDate(moment(date, DATE_FORMAT).startOf('day'))
        self.setDateBounds()
      },
    }
  })
